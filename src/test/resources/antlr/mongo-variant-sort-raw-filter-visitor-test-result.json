{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$switch": {"branches": [{"case": {"$isArray": "$$variant.colorInfo.colors"}, "then": {"$or": [{"$in": ["red", "$$variant.colorInfo.colors"]}]}}], "default": {"$or": [{"$eq": ["$$variant.colorInfo.colors", "red"]}]}}}, {"$or": [{"$switch": {"branches": [{"case": {"$isArray": "$$variant.sizes"}, "then": {"$or": [{"$in": ["XL", "$$variant.sizes"]}]}}], "default": {"$or": [{"$eq": ["$$variant.sizes", "XL"]}]}}}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.brands"}, "then": {"$or": [{"$in": ["Nike", "$$variant.brands"]}, {"$in": ["<PERSON><PERSON>", "$$variant.brands"]}]}}], "default": {"$or": [{"$eq": ["$$variant.brands", "Nike"]}, {"$eq": ["$$variant.brands", "<PERSON><PERSON>"]}]}}}]}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.colorInfo.colorFamilies"}, "then": {"$or": [{"$in": ["black", "$$variant.colorInfo.colorFamilies"]}]}}], "default": {"$or": [{"$eq": ["$$variant.colorInfo.colorFamilies", "black"]}]}}}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.priceInfo.currencyCode"}, "then": {"$or": [{"$in": ["USD", "$$variant.priceInfo.currencyCode"]}]}}], "default": {"$or": [{"$eq": ["$$variant.priceInfo.currencyCode", "USD"]}]}}}]}, {"$not": {"$switch": {"branches": [{"case": {"$isArray": "$$variant.priceInfo.cost"}, "then": {"$and": [{"$gt": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.priceInfo.cost"}}, 0]}, 0.0]}, {"$lte": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$variant.priceInfo.cost"}}, 0]}, 10.0]}]}}], "default": {"$and": [{"$gt": ["$$variant.priceInfo.cost", 0.0]}, {"$lte": ["$$variant.priceInfo.cost", 10.0]}]}}}}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.priceInfo.price"}, "then": {"$and": [{"$gt": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.priceInfo.price"}}, 0]}, 0.0]}, {"$lte": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$variant.priceInfo.price"}}, 0]}, 10.0]}]}}], "default": {"$and": [{"$gt": ["$$variant.priceInfo.price", 0.0]}, {"$lte": ["$$variant.priceInfo.price", 10.0]}]}}}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.priceInfo.originalPrice"}, "then": {"$and": [{"$gt": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.priceInfo.originalPrice"}}, 0]}, 0.0]}, {"$lte": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$variant.priceInfo.originalPrice"}}, 0]}, 10.0]}]}}], "default": {"$and": [{"$gt": ["$$variant.priceInfo.originalPrice", 0.0]}, {"$lte": ["$$variant.priceInfo.originalPrice", 10.0]}]}}}]}, {"$or": [{"$or": [{"$or": [{"$switch": {"branches": [{"case": {"$isArray": "$$variant.rating.averageRating"}, "then": {"$in": [10.0, "$$variant.rating.averageRating"]}}], "default": {"$or": {"$eq": ["$$variant.rating.averageRating", 10.0]}}}}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.rating.averageRating"}, "then": {"$gte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.rating.averageRating"}}, 0]}, 3.0]}}], "default": {"$gte": ["$$variant.rating.averageRating", 3.0]}}}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.rating.ratingCount"}, "then": {"$lte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.rating.ratingCount"}}, 0]}, 100.0]}}], "default": {"$lte": ["$$variant.rating.ratingCount", 100.0]}}}]}, {"$not": {"$switch": {"branches": [{"case": {"$isArray": "$$variant.rating.ratingHistogram"}, "then": {"$in": [5.0, "$$variant.rating.ratingHistogram"]}}], "default": {"$or": {"$eq": ["$$variant.rating.ratingHistogram", 5.0]}}}}}]}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.attributes.category"}, "then": {"$or": [{"$in": ["CAT_1", "$$variant.attributes.category"]}]}}], "default": {"$or": [{"$eq": ["$$variant.attributes.category", "CAT_1"]}]}}}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.attributes.totalSalesCount"}, "then": {"$and": [{"$gt": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.attributes.totalSalesCount"}}, 0]}, 0.0]}, {"$lte": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$variant.attributes.totalSalesCount"}}, 0]}, 10.0]}]}}], "default": {"$and": [{"$gt": ["$$variant.attributes.totalSalesCount", 0.0]}, {"$lte": ["$$variant.attributes.totalSalesCount", 10.0]}]}}}]}, {"$or": [{"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.priceInfo.price"}, "then": {"$and": [{"$gte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$lv.priceInfo.price"}}, 0]}, 10.0]}, {"$lt": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$lv.priceInfo.price"}}, 0]}, 200.0]}]}}], "default": {"$and": [{"$gte": ["$$lv.priceInfo.price", 10.0]}, {"$lt": ["$$lv.priceInfo.price", 200.0]}]}}}]}}}}, {"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.priceInfo.originalPrice"}, "then": {"$in": [200.0, "$$lv.priceInfo.originalPrice"]}}], "default": {"$or": {"$eq": ["$$lv.priceInfo.originalPrice", 200.0]}}}}]}}}}]}]}, {"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.attributes.category"}, "then": {"$or": [{"$in": ["CAT_1", "$$lv.attributes.category"]}]}}], "default": {"$or": [{"$eq": ["$$lv.attributes.category", "CAT_1"]}]}}}]}}}}]}, {"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.attributes.totalSalesCount"}, "then": {"$and": [{"$gt": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$lv.attributes.totalSalesCount"}}, 0]}, 0.0]}, {"$lte": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$lv.attributes.totalSalesCount"}}, 0]}, 10.0]}]}}], "default": {"$and": [{"$gt": ["$$lv.attributes.totalSalesCount", 0.0]}, {"$lte": ["$$lv.attributes.totalSalesCount", 10.0]}]}}}]}}}}]}