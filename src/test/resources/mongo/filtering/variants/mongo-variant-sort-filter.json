{"filter": [{"$and": [{"$and": [{"$switch": {"branches": [{"case": {"$isArray": "$$variant.attributes.totalSalesCount"}, "then": {"$and": [{"$gte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.attributes.totalSalesCount"}}, 0]}, 0.0]}, {"$lt": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$variant.attributes.totalSalesCount"}}, 0]}, 100.0]}]}}], "default": {"$and": [{"$gte": ["$$variant.attributes.totalSalesCount", 0.0]}, {"$lt": ["$$variant.attributes.totalSalesCount", 100.0]}]}}}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.colorInfo.colorFamilies"}, "then": {"$or": [{"$in": ["Red", "$$variant.colorInfo.colorFamilies"]}]}}], "default": {"$or": [{"$eq": ["$$variant.colorInfo.colorFamilies", "Red"]}]}}}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.attributes.category"}, "then": {"$or": [{"$in": ["CAT1", "$$variant.attributes.category"]}]}}], "default": {"$or": [{"$eq": ["$$variant.attributes.category", "CAT1"]}]}}}, {"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.attributes.totalSalesCount"}, "then": {"$and": [{"$gte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$lv.attributes.totalSalesCount"}}, 0]}, 0.0]}, {"$lt": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$lv.attributes.totalSalesCount"}}, 0]}, 1.0]}]}}], "default": {"$and": [{"$gte": ["$$lv.attributes.totalSalesCount", 0.0]}, {"$lt": ["$$lv.attributes.totalSalesCount", 1.0]}]}}}]}}}}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.brands"}, "then": {"$or": [{"$in": ["Nike", "$$variant.brands"]}]}}], "default": {"$or": [{"$eq": ["$$variant.brands", "Nike"]}]}}}, {"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.attributes.category"}, "then": {"$or": [{"$in": ["Nike", "$$lv.attributes.category"]}]}}], "default": {"$or": [{"$eq": ["$$lv.attributes.category", "Nike"]}]}}}]}}}}, {"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.priceInfo.originalPrice"}, "then": {"$and": [{"$gte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$lv.priceInfo.originalPrice"}}, 0]}, 0.0]}, {"$lt": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$lv.priceInfo.originalPrice"}}, 0]}, 1.0]}]}}], "default": {"$and": [{"$gte": ["$$lv.priceInfo.originalPrice", 0.0]}, {"$lt": ["$$lv.priceInfo.originalPrice", 1.0]}]}}}]}}}}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.priceInfo.originalPrice"}, "then": {"$and": [{"$gte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.priceInfo.originalPrice"}}, 0]}, 0.0]}, {"$lt": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$variant.priceInfo.originalPrice"}}, 0]}, 100.0]}]}}], "default": {"$and": [{"$gte": ["$$variant.priceInfo.originalPrice", 0.0]}, {"$lt": ["$$variant.priceInfo.originalPrice", 100.0]}]}}}]}, {"$not": {"$switch": {"branches": [{"case": {"$isArray": "$$variant.id"}, "then": {"$or": [{"$in": ["4", "$$variant.id"]}, {"$in": ["5", "$$variant.id"]}, {"$in": ["6", "$$variant.id"]}]}}], "default": {"$or": [{"$eq": ["$$variant.id", "4"]}, {"$eq": ["$$variant.id", "5"]}, {"$eq": ["$$variant.id", "6"]}]}}}}]}, {"$or": [{"$and": [{"$switch": {"branches": [{"case": {"$isArray": "$$variant.attributes.totalSalesCount"}, "then": {"$and": [{"$gte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.attributes.totalSalesCount"}}, 0]}, 0.0]}, {"$lt": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$variant.attributes.totalSalesCount"}}, 0]}, 100.0]}]}}], "default": {"$and": [{"$gte": ["$$variant.attributes.totalSalesCount", 0.0]}, {"$lt": ["$$variant.attributes.totalSalesCount", 100.0]}]}}}, {"$not": {"$switch": {"branches": [{"case": {"$isArray": "$$variant.attributes.totalSalesCount"}, "then": {"$in": [1.0, "$$variant.attributes.totalSalesCount"]}}], "default": {"$or": {"$eq": ["$$variant.attributes.totalSalesCount", 1.0]}}}}}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.attributes.category"}, "then": {"$or": [{"$in": ["CAT1", "$$variant.attributes.category"]}]}}], "default": {"$or": [{"$eq": ["$$variant.attributes.category", "CAT1"]}]}}}, {"$not": {"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.attributes.totalSalesCount"}, "then": {"$in": [1.0, "$$lv.attributes.totalSalesCount"]}}], "default": {"$or": {"$eq": ["$$lv.attributes.totalSalesCount", 1.0]}}}}]}}}}}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.priceInfo.price"}, "then": {"$in": [10.0, "$$variant.priceInfo.price"]}}], "default": {"$or": {"$eq": ["$$variant.priceInfo.price", 10.0]}}}}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.brands"}, "then": {"$or": [{"$in": ["<PERSON><PERSON>", "$$variant.brands"]}]}}], "default": {"$or": [{"$eq": ["$$variant.brands", "<PERSON><PERSON>"]}]}}}, {"$not": {"$switch": {"branches": [{"case": {"$isArray": "$$variant.sizes"}, "then": {"$or": [{"$in": ["XL", "$$variant.sizes"]}]}}], "default": {"$or": [{"$eq": ["$$variant.sizes", "XL"]}]}}}}, {"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.priceInfo.originalPrice"}, "then": {"$and": [{"$gte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$lv.priceInfo.originalPrice"}}, 0]}, 0.0]}, {"$lt": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$lv.priceInfo.originalPrice"}}, 0]}, 100.0]}]}}], "default": {"$and": [{"$gte": ["$$lv.priceInfo.originalPrice", 0.0]}, {"$lt": ["$$lv.priceInfo.originalPrice", 100.0]}]}}}]}}}}, {"$not": {"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.priceInfo.originalPrice"}, "then": {"$in": [1.0, "$$lv.priceInfo.originalPrice"]}}], "default": {"$or": {"$eq": ["$$lv.priceInfo.originalPrice", 1.0]}}}}]}}}}}, {"$not": {"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.attributes.category"}, "then": {"$or": [{"$in": ["CAT2", "$$lv.attributes.category"]}]}}], "default": {"$or": [{"$eq": ["$$lv.attributes.category", "CAT2"]}]}}}]}}}}}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.priceInfo.originalPrice"}, "then": {"$and": [{"$gte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.priceInfo.originalPrice"}}, 0]}, 0.0]}, {"$lt": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$variant.priceInfo.originalPrice"}}, 0]}, 100.0]}]}}], "default": {"$and": [{"$gte": ["$$variant.priceInfo.originalPrice", 0.0]}, {"$lt": ["$$variant.priceInfo.originalPrice", 100.0]}]}}}, {"$not": {"$switch": {"branches": [{"case": {"$isArray": "$$variant.priceInfo.originalPrice"}, "then": {"$in": [10.0, "$$variant.priceInfo.originalPrice"]}}], "default": {"$or": {"$eq": ["$$variant.priceInfo.originalPrice", 10.0]}}}}}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.id"}, "then": {"$or": [{"$in": ["1", "$$variant.id"]}, {"$in": ["2", "$$variant.id"]}, {"$in": ["3", "$$variant.id"]}]}}], "default": {"$or": [{"$eq": ["$$variant.id", "1"]}, {"$eq": ["$$variant.id", "2"]}, {"$eq": ["$$variant.id", "3"]}]}}}]}, {"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$and": [{"$switch": {"branches": [{"case": {"$isArray": "$$variant.colorInfo.colors"}, "then": {"$or": [{"$in": ["red", "$$variant.colorInfo.colors"]}]}}], "default": {"$or": [{"$eq": ["$$variant.colorInfo.colors", "red"]}]}}}, {"$or": [{"$switch": {"branches": [{"case": {"$isArray": "$$variant.sizes"}, "then": {"$or": [{"$in": ["XL", "$$variant.sizes"]}]}}], "default": {"$or": [{"$eq": ["$$variant.sizes", "XL"]}]}}}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.brands"}, "then": {"$or": [{"$in": ["Nike", "$$variant.brands"]}, {"$in": ["<PERSON><PERSON>", "$$variant.brands"]}]}}], "default": {"$or": [{"$eq": ["$$variant.brands", "Nike"]}, {"$eq": ["$$variant.brands", "<PERSON><PERSON>"]}]}}}]}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.colorInfo.colorFamilies"}, "then": {"$or": [{"$in": ["black", "$$variant.colorInfo.colorFamilies"]}]}}], "default": {"$or": [{"$eq": ["$$variant.colorInfo.colorFamilies", "black"]}]}}}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.priceInfo.currencyCode"}, "then": {"$or": [{"$in": ["USD", "$$variant.priceInfo.currencyCode"]}]}}], "default": {"$or": [{"$eq": ["$$variant.priceInfo.currencyCode", "USD"]}]}}}]}, {"$not": {"$switch": {"branches": [{"case": {"$isArray": "$$variant.priceInfo.cost"}, "then": {"$and": [{"$gt": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.priceInfo.cost"}}, 0]}, 0.0]}, {"$lte": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$variant.priceInfo.cost"}}, 0]}, 10.0]}]}}], "default": {"$and": [{"$gt": ["$$variant.priceInfo.cost", 0.0]}, {"$lte": ["$$variant.priceInfo.cost", 10.0]}]}}}}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.priceInfo.price"}, "then": {"$and": [{"$gt": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.priceInfo.price"}}, 0]}, 0.0]}, {"$lte": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$variant.priceInfo.price"}}, 0]}, 10.0]}]}}], "default": {"$and": [{"$gt": ["$$variant.priceInfo.price", 0.0]}, {"$lte": ["$$variant.priceInfo.price", 10.0]}]}}}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.priceInfo.originalPrice"}, "then": {"$and": [{"$gt": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.priceInfo.originalPrice"}}, 0]}, 0.0]}, {"$lte": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$variant.priceInfo.originalPrice"}}, 0]}, 10.0]}]}}], "default": {"$and": [{"$gt": ["$$variant.priceInfo.originalPrice", 0.0]}, {"$lte": ["$$variant.priceInfo.originalPrice", 10.0]}]}}}]}, {"$or": [{"$or": [{"$or": [{"$switch": {"branches": [{"case": {"$isArray": "$$variant.rating.averageRating"}, "then": {"$in": [10.0, "$$variant.rating.averageRating"]}}], "default": {"$or": {"$eq": ["$$variant.rating.averageRating", 10.0]}}}}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.rating.averageRating"}, "then": {"$gte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.rating.averageRating"}}, 0]}, 3.0]}}], "default": {"$gte": ["$$variant.rating.averageRating", 3.0]}}}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.rating.ratingCount"}, "then": {"$lte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.rating.ratingCount"}}, 0]}, 100.0]}}], "default": {"$lte": ["$$variant.rating.ratingCount", 100.0]}}}]}, {"$not": {"$switch": {"branches": [{"case": {"$isArray": "$$variant.rating.ratingHistogram"}, "then": {"$in": [5.0, "$$variant.rating.ratingHistogram"]}}], "default": {"$or": {"$eq": ["$$variant.rating.ratingHistogram", 5.0]}}}}}]}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.attributes.category"}, "then": {"$or": [{"$in": ["CAT_1", "$$variant.attributes.category"]}]}}], "default": {"$or": [{"$eq": ["$$variant.attributes.category", "CAT_1"]}]}}}]}, {"$switch": {"branches": [{"case": {"$isArray": "$$variant.attributes.totalSalesCount"}, "then": {"$and": [{"$gt": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.attributes.totalSalesCount"}}, 0]}, 0.0]}, {"$lte": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$variant.attributes.totalSalesCount"}}, 0]}, 10.0]}]}}], "default": {"$and": [{"$gt": ["$$variant.attributes.totalSalesCount", 0.0]}, {"$lte": ["$$variant.attributes.totalSalesCount", 10.0]}]}}}]}, {"$or": [{"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.priceInfo.price"}, "then": {"$and": [{"$gte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$lv.priceInfo.price"}}, 0]}, 10.0]}, {"$lt": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$lv.priceInfo.price"}}, 0]}, 200.0]}]}}], "default": {"$and": [{"$gte": ["$$lv.priceInfo.price", 10.0]}, {"$lt": ["$$lv.priceInfo.price", 200.0]}]}}}]}}}}, {"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.priceInfo.originalPrice"}, "then": {"$in": [200.0, "$$lv.priceInfo.originalPrice"]}}], "default": {"$or": {"$eq": ["$$lv.priceInfo.originalPrice", 200.0]}}}}]}}}}]}]}, {"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.attributes.category"}, "then": {"$or": [{"$in": ["CAT_1", "$$lv.attributes.category"]}]}}], "default": {"$or": [{"$eq": ["$$lv.attributes.category", "CAT_1"]}]}}}]}}}}]}, {"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "50"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.attributes.totalSalesCount"}, "then": {"$and": [{"$gt": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$lv.attributes.totalSalesCount"}}, 0]}, 0.0]}, {"$lte": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$lv.attributes.totalSalesCount"}}, 0]}, 10.0]}]}}], "default": {"$and": [{"$gt": ["$$lv.attributes.totalSalesCount", 0.0]}, {"$lte": ["$$lv.attributes.totalSalesCount", 10.0]}]}}}]}}}}]}]}