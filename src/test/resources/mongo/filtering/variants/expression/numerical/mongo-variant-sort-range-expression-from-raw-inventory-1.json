{"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "place"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.priceInfo.price"}, "then": {"$and": [{"$gte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$lv.priceInfo.price"}}, 0]}, 1.0]}, {"$lt": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$lv.priceInfo.price"}}, 0]}, 2.0]}]}}], "default": {"$and": [{"$gte": ["$$lv.priceInfo.price", 1.0]}, {"$lt": ["$$lv.priceInfo.price", 2.0]}]}}}]}}}}