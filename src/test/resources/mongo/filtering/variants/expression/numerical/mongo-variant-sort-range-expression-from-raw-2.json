{"$switch": {"branches": [{"case": {"$isArray": "$$variant.priceInfo.price"}, "then": {"$and": [{"$gte": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$variant.priceInfo.price"}}, 0]}, 1.0]}, {"$lt": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$variant.priceInfo.price"}}, 0]}, 2.0]}]}}], "default": {"$and": [{"$gte": ["$$variant.priceInfo.price", 1.0]}, {"$lt": ["$$variant.priceInfo.price", 2.0]}]}}}