{"$anyElementTrue": {"$map": {"input": "$$variant.localInventories", "as": "lv", "in": {"$and": [{"$eq": ["$$lv.placeId", "place"]}, {"$switch": {"branches": [{"case": {"$isArray": "$$lv.priceInfo.price"}, "then": {"$and": [{"$gt": [{"$arrayElemAt": [{"$minN": {"n": 1, "input": "$$lv.priceInfo.price"}}, 0]}, 1.0]}, {"$lte": [{"$arrayElemAt": [{"$maxN": {"n": 1, "input": "$$lv.priceInfo.price"}}, 0]}, 2.0]}]}}], "default": {"$and": [{"$gt": ["$$lv.priceInfo.price", 1.0]}, {"$lte": ["$$lv.priceInfo.price", 2.0]}]}}}]}}}}