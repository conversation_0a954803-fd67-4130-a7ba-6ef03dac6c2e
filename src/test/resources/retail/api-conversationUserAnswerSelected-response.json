{"id": "bb93d94d-dd9c-4a7c-805d-82a0a83c0002", "area": "Production", "query": "dress", "correctedQuery": "This was corrected", "biasingProfile": "White", "filter": "(colorFamilies:ANY(\"White\")) AND (attributes.colors:ANY(\"yellow\"))", "originalRequest": {"query": "dress", "area": "Production", "collection": "productsClothing", "siteFilter": "", "refinements": [{"navigationName": "attributes.colors", "type": "Value", "value": "yellow", "or": true}], "conversationalSearchConfig": {"followupConversationRequested": true, "conversationId": "c154d073-87f5-4edb-accd-587eabe014ff", "userAnswer": {"selectedAnswer": {"productAttributeValue": {"name": "attributes.colors", "value": "yellow"}}}}}, "records": [], "totalRecordCount": 0, "metadata": {"searchAttributionToken": "NtQKDAjYrrGEBhCWs_v3AhABGiQ2MDlhNjA5Yy0wMDAwLTI2ZDctODQ0OS1mNGY1ZTgwODc1YjQ", "cached": false}, "pageInfo": {"recordStart": 0, "recordEnd": 0}, "availableNavigation": [], "selectedNavigation": [{"name": "attributes.colors", "displayName": "colors", "type": "Value", "refinements": [{"type": "Value", "value": "yellow", "pinned": false}], "or": true, "pinned": false}], "rewrites": [], "empty": true, "warnings": [], "includeExpandedResults": false, "facetLimit": 300, "redirectMetadata": [], "conversationalSearchResult": {"conversationId": "c154d073-87f5-4edb-accd-587eabe014ff", "refinedQuery": "dress", "followupQuestion": "What is the occasion?", "suggestedAnswers": [{"name": "attribute.occasion", "value": "wedding"}, {"name": "attribute.occasion", "value": "party"}]}}