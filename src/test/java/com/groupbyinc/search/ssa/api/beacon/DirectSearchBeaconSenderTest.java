package com.groupbyinc.search.ssa.api.beacon;

import com.groupbyinc.search.ssa.api.dto.RecordDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SearchResponseDto;
import com.groupbyinc.search.ssa.api.dto.TemplateDto;
import com.groupbyinc.search.ssa.api.dto.ZoneDto;
import com.groupbyinc.search.ssa.api.dto.ZoneDtoType;
import com.groupbyinc.search.ssa.api.event.SearchSuccessEvent;
import com.groupbyinc.search.ssa.beacon.DirectSearchBeaconSender;
import com.groupbyinc.search.ssa.beacon.client.DirectSearchBeaconRequest;
import com.groupbyinc.search.ssa.beacon.client.SearchBeaconClient;
import com.groupbyinc.search.ssa.beacon.config.DirectSearchBeaconConfiguration;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.RecordSponsoredInfo;
import com.groupbyinc.search.ssa.core.SponsoredRecordsRequest;
import com.groupbyinc.search.ssa.core.trigger.TriggerSet;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.pubsub.PubsubWriterService;
import com.groupbyinc.search.ssa.topsort.model.AuctionResponse;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micronaut.http.client.exceptions.HttpClientException;
import org.assertj.core.api.ThrowableAssert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Flux;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.beacon.DirectSearchBeaconSender.META_FIELDS;
import static com.groupbyinc.search.ssa.beacon.config.DirectSearchBeaconConfiguration.EVENT_TYPE;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_PUBSUB_DIRECT_SEARCH;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.DEFAULT_CONTEXT;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyIterable;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@DisplayName("DirectSearchBeaconSender Tests")
class DirectSearchBeaconSenderTest {

    private static final SponsoredRecordsRequest SPONSORED_REQUEST_STUB = new SponsoredRecordsRequest(-1, List.of());
    private static final Comparator<RecordDto> SPONSORED_RECORD_COMPARATOR = (o1, o2) -> {
        assertThat(o1.getSponsoredInfo()).isNotNull();
        assertThat(o1.getSponsoredInfo().topsort()).isNotNull();
        assertThat(o2.getSponsoredInfo()).isNotNull();
        assertThat(o2.getSponsoredInfo().topsort()).isNotNull();
        return o1.getSponsoredInfo().topsort().rank().compareTo(o2.getSponsoredInfo().topsort().rank());
    };

    private DirectSearchBeaconSender directSearchBeaconSender;
    private SearchBeaconClient searchBeaconClient;
    private PubsubWriterService pubsubService;
    private FeaturesManager featuresManager;

    @BeforeEach
    void setUp() {
        var directSearchBeaconConfiguration = mock(DirectSearchBeaconConfiguration.class);
        searchBeaconClient = mock(SearchBeaconClient.class);
        var meterRegistry = mock(MeterRegistry.class);
        pubsubService = mock(PubsubWriterService.class);
        featuresManager = mock(FeaturesManager.class);
        directSearchBeaconSender = new DirectSearchBeaconSender(
            featuresManager,
            pubsubService,
            searchBeaconClient,
            directSearchBeaconConfiguration
        );

        given(directSearchBeaconConfiguration.getAuthToken()).willReturn("auth token");
        given(meterRegistry.counter(eq("direct.search.beacon.count"), anyIterable())).willReturn(mock(Counter.class));
    }

    @Test
    @DisplayName("Exceptions are swallowed")
    void exceptionsAreSwallowed() {
        var searchRequestDto = SearchRequestDto.builder()
            .pageSize(1)
            .sponsoredRecords(SPONSORED_REQUEST_STUB)
            .build();

        var searchResponseDto = SearchResponseDto
            .builder()
            .id("search id")
            .originalRequest(searchRequestDto)
            .build();

        when(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_PUBSUB_DIRECT_SEARCH))).thenReturn(false);

        given(searchBeaconClient.sendDirectBeacon(any(), any(), any())).willReturn(Flux.error(new HttpClientException("BOOM")));

        ThrowableAssert.ThrowingCallable onSearchSuccessEvent = () -> directSearchBeaconSender.onSearchSuccessEvent(
            new SearchSuccessEvent(DEFAULT_CONTEXT, searchResponseDto)
        );

        assertThatNoException().isThrownBy(onSearchSuccessEvent);
    }

    @Test
    @DisplayName("Direct search beacons can be sent via http to Wisdom when pageSize > 0 and pubsub feature flag is disabled")
    void httpDirectSearchBeaconsCanBeSentToWisdom() {
        var searchRequestDto = SearchRequestDto.builder()
            .pageSize(1)
            .query("test")
            .sponsoredRecords(SPONSORED_REQUEST_STUB)
            .build();

        var searchResponseDto = SearchResponseDto
            .builder()
            .id("search id")
            .originalRequest(searchRequestDto)
            .build();

        var directBeaconSearchResponseDto = searchResponseDto.toBuilder()
            .originalQuery(searchRequestDto.getQuery())
            .warnings(List.of())
            .records(List.of())
            .sponsoredRecords(List.of())
            .build();

        when(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_PUBSUB_DIRECT_SEARCH))).thenReturn(false);

        given(searchBeaconClient.sendDirectBeacon(any(), any(), any())).willReturn(Flux.empty());

        directSearchBeaconSender.onSearchSuccessEvent(
            new SearchSuccessEvent(DEFAULT_CONTEXT, searchResponseDto)
        );

        then(searchBeaconClient).should().sendDirectBeacon(
            DEFAULT_CONTEXT.getMerchandiser().merchandiserId(),
            "auth token",
            new DirectSearchBeaconRequest<>(
                DEFAULT_CONTEXT.getMerchandiser().merchandiserId(),
                "search id",
                directBeaconSearchResponseDto,
                EVENT_TYPE,
                directBeaconSearchResponseDto.getExperiments()
            )
        );
    }

    @Test
    @DisplayName("Direct search beacons can be sent via Pubsub to Wisdom when pageSize > 0 and pubsub feature flag is enabled")
    void pubsubDirectSearchBeaconsCanBeSentToWisdom() {
        var searchRequestDto = SearchRequestDto.builder()
            .pageSize(1)
            .query("test")
            .sponsoredRecords(SPONSORED_REQUEST_STUB)
            .build();

        var searchResponseDto = SearchResponseDto
            .builder()
            .id("search id")
            .originalRequest(searchRequestDto)
            .build();

        var directBeaconSearchResponseDto = searchResponseDto.toBuilder()
            .originalQuery(searchRequestDto.getQuery())
            .records(List.of())
            .sponsoredRecords(List.of())
            .warnings(List.of())
            .build();

        when(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_PUBSUB_DIRECT_SEARCH))).thenReturn(true);

        given(pubsubService.sendDirectBeacon(any())).willReturn(true);

        directSearchBeaconSender.onSearchSuccessEvent(
            new SearchSuccessEvent(DEFAULT_CONTEXT, searchResponseDto)
        );

        then(pubsubService).should().sendDirectBeacon(
            new DirectSearchBeaconRequest<>(
                DEFAULT_CONTEXT.getMerchandiser().merchandiserId(),
                "search id",
                directBeaconSearchResponseDto,
                EVENT_TYPE,
                directBeaconSearchResponseDto.getExperiments()
            )
        );
    }

    @Test
    @DisplayName("Direct beacons can be sent via Pubsub with sponsored records merged")
    void pubsubDirectSearchBeaconsCanBeSentToWisdomWithSponsoredRecords() {
        var searchRequestDto = SearchRequestDto.builder()
            .pageSize(10)
            .query("test")
            .sponsoredRecords(new SponsoredRecordsRequest(-1, List.of(0, 2)))
            .build();

        var records = Stream.of("1", "2", "3", "4", "5")
            .map(id -> recordFor(id, null))
            .toList();

        //id - rank
        var sponsoredRecords = Map.of("2", 1, "3", 2).entrySet().stream()
            .map(e -> recordFor(e.getKey(), e.getValue()))
            .sorted(SPONSORED_RECORD_COMPARATOR)
            .toList();

        var searchResponseDto = SearchResponseDto.builder()
            .id("search id")
            .records(records)
            .sponsoredRecords(sponsoredRecords)
            .originalRequest(searchRequestDto)
            .build();

        var expectedDirectPayload = searchResponseDto.toBuilder()
            .sponsoredRecords(List.of())
            .records(cleanUpRecords(
                List.of(
                    sponsoredRecords.getFirst(),
                    records.getFirst(),
                    sponsoredRecords.get(1),
                    records.get(3),
                    records.get(4)
                )
            ))
            .warnings(List.of())
            .originalQuery(searchRequestDto.getQuery())
            .build();

        when(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_PUBSUB_DIRECT_SEARCH))).thenReturn(true);
        given(pubsubService.sendDirectBeacon(any())).willReturn(true);

        directSearchBeaconSender.onSearchSuccessEvent(
            new SearchSuccessEvent(DEFAULT_CONTEXT, searchResponseDto)
        );

        then(pubsubService).should().sendDirectBeacon(new DirectSearchBeaconRequest<>(
            DEFAULT_CONTEXT.getMerchandiser().merchandiserId(),
            "search id",
            expectedDirectPayload,
            EVENT_TYPE,
            searchResponseDto.getExperiments()
        ));
    }

    @Test
    @DisplayName("Direct beacon with sponsored records merged, with some records pushed out")
    void pubsubDirectSearchBeaconsCanBeSentToWisdomWithSponsoredRecordsWithRegularRecordsPushedFromResults() {
        var searchRequestDto = SearchRequestDto.builder()
            .pageSize(3)
            .query("test")
            .sponsoredRecords(new SponsoredRecordsRequest(-1, List.of(0, 2)))
            .build();

        var records = Stream.of("1", "2", "3", "4", "5")
            .map(id -> recordFor(id, null))
            .toList();

        //id - rank
        var sponsoredRecords = Map.of("2", 1, "3", 2).entrySet().stream()
            .map(e -> recordFor(e.getKey(), e.getValue()))
            .sorted(SPONSORED_RECORD_COMPARATOR)
            .toList();

        var searchResponseDto = SearchResponseDto.builder()
            .id("search id")
            .records(records)
            .sponsoredRecords(sponsoredRecords)
            .originalRequest(searchRequestDto)
            .build();

        var expectedDirectPayload = searchResponseDto.toBuilder()
            .sponsoredRecords(List.of())
            .records(cleanUpRecords(
                List.of(
                    sponsoredRecords.getFirst(),
                    records.getFirst(),
                    sponsoredRecords.get(1)
                )
            ))
            .warnings(List.of())
            .originalQuery(searchRequestDto.getQuery())
            .build();

        when(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_PUBSUB_DIRECT_SEARCH))).thenReturn(true);
        given(pubsubService.sendDirectBeacon(any())).willReturn(true);

        directSearchBeaconSender.onSearchSuccessEvent(
            new SearchSuccessEvent(DEFAULT_CONTEXT, searchResponseDto)
        );

        then(pubsubService).should().sendDirectBeacon(new DirectSearchBeaconRequest<>(
            DEFAULT_CONTEXT.getMerchandiser().merchandiserId(),
            "search id",
            expectedDirectPayload,
            EVENT_TYPE,
            searchResponseDto.getExperiments()
        ));
    }

    @Test
    @DisplayName("Direct beacons with sponsored records merged, with invalid indexes ignored")
    void pubsubDirectSearchBeaconsCanBeSentToWisdomWithSponsoredRecordsPositionedOutOfBounds() {
        var searchRequestDto = SearchRequestDto.builder()
            .pageSize(10)
            .query("test")
            .sponsoredRecords(new SponsoredRecordsRequest(-1, List.of(-1, 2, 9999)))
            .build();

        var records = Stream.of("1", "2", "3", "4", "5")
            .map(id -> recordFor(id, null))
            .toList();

        //id - rank
        var sponsoredRecords = Map.of("2", 1, "3", 2, "5", 3).entrySet().stream()
            .map(e -> recordFor(e.getKey(), e.getValue()))
            .sorted(SPONSORED_RECORD_COMPARATOR)
            .toList();

        var searchResponseDto = SearchResponseDto.builder()
            .id("search id")
            .records(records)
            .sponsoredRecords(sponsoredRecords)
            .originalRequest(searchRequestDto)
            .build();

        var expectedDirectPayload = searchResponseDto.toBuilder()
            .sponsoredRecords(List.of())
            .records(cleanUpRecords(
                List.of(
                    records.getFirst(),
                    records.get(2),
                    sponsoredRecords.getFirst(),
                    records.get(3),
                    records.get(4)
                )
            ))
            .warnings(List.of())
            .originalQuery(searchRequestDto.getQuery())
            .build();

        when(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_PUBSUB_DIRECT_SEARCH))).thenReturn(true);
        given(pubsubService.sendDirectBeacon(any())).willReturn(true);

        directSearchBeaconSender.onSearchSuccessEvent(
            new SearchSuccessEvent(DEFAULT_CONTEXT, searchResponseDto)
        );

        then(pubsubService).should().sendDirectBeacon(new DirectSearchBeaconRequest<>(
            DEFAULT_CONTEXT.getMerchandiser().merchandiserId(),
            "search id",
            expectedDirectPayload,
            EVENT_TYPE,
            searchResponseDto.getExperiments()
        ));
    }

    @Test
    @DisplayName("Direct beacons with sponsored records merged, with positions shifted left")
    void pubsubDirectSearchBeaconsCanBeSentToWisdomWithSponsoredRecordsWithPositionsShifted() {
        var searchRequestDto = SearchRequestDto.builder()
            .pageSize(10)
            .query("test")
            .sponsoredRecords(new SponsoredRecordsRequest(-1, List.of(0, 7, 9)))
            .build();

        var records = Stream.of("1", "2", "3", "4", "5")
            .map(id -> recordFor(id, null))
            .toList();

        //id - rank
        var sponsoredRecords = Map.of("2", 1, "3", 2, "5", 3).entrySet().stream()
            .map(e -> recordFor(e.getKey(), e.getValue()))
            .sorted(SPONSORED_RECORD_COMPARATOR)
            .toList();

        var searchResponseDto = SearchResponseDto.builder()
            .id("search id")
            .records(records)
            .sponsoredRecords(sponsoredRecords)
            .originalRequest(searchRequestDto)
            .build();

        var expectedDirectPayload = searchResponseDto.toBuilder()
            .sponsoredRecords(List.of())
            .records(cleanUpRecords(
                List.of(
                    sponsoredRecords.getFirst(),
                    records.getFirst(),
                    records.get(3),
                    sponsoredRecords.get(1),
                    sponsoredRecords.get(2)
                )
            ))
            .warnings(List.of())
            .originalQuery(searchRequestDto.getQuery())
            .build();

        when(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_PUBSUB_DIRECT_SEARCH))).thenReturn(true);
        given(pubsubService.sendDirectBeacon(any())).willReturn(true);

        directSearchBeaconSender.onSearchSuccessEvent(
            new SearchSuccessEvent(DEFAULT_CONTEXT, searchResponseDto)
        );

        then(pubsubService).should().sendDirectBeacon(new DirectSearchBeaconRequest<>(
            DEFAULT_CONTEXT.getMerchandiser().merchandiserId(),
            "search id",
            expectedDirectPayload,
            EVENT_TYPE,
            searchResponseDto.getExperiments()
        ));
    }

    @Test
    @DisplayName("Search direct beacon clears fields that should not be published")
    void pubsubDirectSearchBeaconsClearsFieldsThatShouldNotBePublished() {
        var searchRequestDto = SearchRequestDto.builder()
            .pageSize(10)
            .query("test")
            .sponsoredRecords(new SponsoredRecordsRequest(-1, List.of(0, 7, 9)))
            .build();

        var records = Stream.of("1", "2", "3", "4", "5")
            .map(id -> recordFor(id, null))
            .toList();

        //id - rank
        var sponsoredRecords = Map.of("2", 1, "3", 2, "5", 3).entrySet().stream()
            .map(e -> recordFor(e.getKey(), e.getValue()))
            .sorted(SPONSORED_RECORD_COMPARATOR)
            .toList();

        var ruleId = 1;
        var template = new TemplateDto("test name", "test rule name", ruleId, new TriggerSet());
        template.getZones().add(ZoneDto.ofContent("test name", ZoneDtoType.Content, "some content"));

        var searchResponseDto = SearchResponseDto
            .builder()
            .id("search id")
            .records(records)
            .sponsoredRecords(sponsoredRecords)
            .originalRequest(searchRequestDto)
            .template(template)
            .warnings(List.of("test warning"))
            .build();

        when(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_PUBSUB_DIRECT_SEARCH))).thenReturn(true);
        given(pubsubService.sendDirectBeacon(any())).willReturn(true);

        directSearchBeaconSender.onSearchSuccessEvent(
            new SearchSuccessEvent(DEFAULT_CONTEXT, searchResponseDto)
        );

        verify(pubsubService, times(1)).sendDirectBeacon(argThat(searchResponseDtoDirectSearchBeaconRequest -> {
            var event = searchResponseDtoDirectSearchBeaconRequest.event();
            var noZoneContent = event.getTemplate().getZones().stream().allMatch(
                zoneDto -> zoneDto.richContent().isEmpty() && zoneDto.content().isEmpty() && zoneDto.html().isEmpty()
            );
            var noDebug = event.getDebug() == null;
            var noWarnings = event.getWarnings() == null || event.getWarnings().isEmpty();
            return noZoneContent && noDebug && noWarnings;
        }));
    }

    @Test
    @DisplayName("Direct search beacons will fall back to http if pubsub fails")
    void pubsubDirectSearchBeaconsFallbackToHttp() {
        var searchRequestDto = SearchRequestDto.builder()
            .pageSize(1)
            .query("test")
            .sponsoredRecords(SPONSORED_REQUEST_STUB)
            .build();

        var searchResponseDto = SearchResponseDto
            .builder()
            .id("search id")
            .originalRequest(searchRequestDto)
            .build();

        var directBeaconSearchResponseDto = searchResponseDto.toBuilder()
            .originalQuery(searchRequestDto.getQuery())
            .warnings(List.of())
            .records(List.of())
            .sponsoredRecords(List.of())
            .build();

        when(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_PUBSUB_DIRECT_SEARCH))).thenReturn(true);

        given(pubsubService.sendDirectBeacon(any())).willReturn(false);
        given(searchBeaconClient.sendDirectBeacon(any(), any(), any())).willReturn(Flux.empty());

        directSearchBeaconSender.onSearchSuccessEvent(
            new SearchSuccessEvent(DEFAULT_CONTEXT, searchResponseDto)
        );

        then(searchBeaconClient).should().sendDirectBeacon(
            DEFAULT_CONTEXT.getMerchandiser().merchandiserId(),
            "auth token",
            new DirectSearchBeaconRequest<>(
                DEFAULT_CONTEXT.getMerchandiser().merchandiserId(),
                "search id",
                directBeaconSearchResponseDto,
                EVENT_TYPE,
                directBeaconSearchResponseDto.getExperiments()
            )
        );
    }

    private static RecordDto recordFor(String id, Integer rank) {
        RecordSponsoredInfo sponsoredInfo = null;
        if (rank != null) {
            sponsoredInfo = new RecordSponsoredInfo(
                new AuctionResponse.Winner(rank, "product", id, "bid%s".formatted(id), "vendor%s".formatted(id))
            );
        }
        return RecordDto.fromDomain(
            Record.of(
                APPAREL_MERCHANDISER,
                PRODUCTS_CLOTHING,
                id,
                id,
                "title%s".formatted(id),
                null,
                null,
                null,
                sponsoredInfo != null,
                sponsoredInfo
            )
        );
    }

    private List<RecordDto> cleanUpRecords(List<RecordDto> records) {
        return records.stream().map(r -> {
                r.getMetadata().keySet().removeIf(k -> !(META_FIELDS.contains(k)));
                return new RecordDto(
                    r.getId(),
                    r.getUrl(),
                    r.getTitle(),
                    r.getCollection(),
                    r.getMetadata(),
                    null,
                    null,
                    null,
                    r.getPrimaryProductId(),
                    r.getLabels()
                );
            }

        ).toList();
    }

}
