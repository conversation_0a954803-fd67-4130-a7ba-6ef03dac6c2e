package com.groupbyinc.search.ssa.api.resolvers;

import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.authz.Identity;
import com.groupbyinc.search.ssa.core.authz.Role;
import com.groupbyinc.search.ssa.fixture.AuthFixture;

import io.micronaut.http.HttpAttributes;
import io.micronaut.http.HttpRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Set;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("IdentityArgumentResolver Tests")
class IdentityArgumentResolverTest {

    private final IdentityArgumentResolver resolver = new IdentityArgumentResolver();

    @Test
    @DisplayName("Resolves identity when request is authenticated with a proper claim set")
    void resolvesWhenRequestIsAuthenticatedWithClaimSet() {

        var authFixture = AuthFixture.merchandiserDefaultRole("valid");

        var httpRequest = HttpRequest.GET("/test")
            .setAttribute(
                HttpAttributes.PRINCIPAL,
                authFixture.getInternalHttpRequestPrincipalAttribute()
            );

        assertThat(resolver.bind(null, httpRequest).get())
            .isEqualTo(authFixture.getIdentity());
    }

    @Test
    @DisplayName("Returns unsatisfied if authentication is not present")
    void returnsUnsatisfiedIfAuthenticationNotPresent() {

        var httpRequest = HttpRequest.GET("/test");

        assertThat(resolver.bind(null, httpRequest).isSatisfied())
            .isFalse();
    }

    @ParameterizedTest(name = "identity = {0}, merchandiser = {1}")
    @DisplayName("Authorizes performing searches properly")
    @MethodSource("performSearchCases")
    void authorizesPerformingSearchesProperly(Identity identity, Merchandiser merchandiser, boolean authorized) {

        assertThat(identity.canPerformSearchFor(merchandiser))
            .isEqualTo(authorized);
    }

    @SuppressWarnings("SpellCheckingInspection")
    static Stream<Arguments> performSearchCases() {

        var bjsId = "bjswholesale";
        var bjsMerchandiser = Merchandiser.of(bjsId);
        var bjsIdentity = new Identity(bjsId, bjsId, Set.of(Role.CUSTOMER_DEFAULT));

        var cvsId = "cvshealth";
        var cvsMerchandiser = Merchandiser.of(cvsId);
        var cvsIdentity = new Identity(cvsId, cvsId, Set.of(Role.CUSTOMER_DEFAULT));

        var roleLackingIdentity1 = new Identity("cvshealth", "cvshealth", Set.of());
        var roleLackingIdentity2 = new Identity("cvshealth", "cvshealth", Set.of(Role.of("ADMIN")));

        var groupById = "groupbyby";
        var superUserIdentity = new Identity(groupById, groupById, Set.of(Role.SUPERUSER));

        return Stream.of(
            Arguments.of(cvsIdentity, cvsMerchandiser, true),
            Arguments.of(bjsIdentity, bjsMerchandiser, true),
            Arguments.of(cvsIdentity, bjsMerchandiser, false),
            Arguments.of(bjsIdentity, cvsMerchandiser, false),
            Arguments.of(roleLackingIdentity1, cvsMerchandiser, false),
            Arguments.of(roleLackingIdentity2, cvsMerchandiser, false),
            Arguments.of(superUserIdentity, bjsMerchandiser, true),
            Arguments.of(superUserIdentity, cvsMerchandiser, true)
        );
    }

}
