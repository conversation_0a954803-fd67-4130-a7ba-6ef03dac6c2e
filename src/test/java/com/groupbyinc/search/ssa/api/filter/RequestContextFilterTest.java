package com.groupbyinc.search.ssa.api.filter;

import com.groupbyinc.search.ssa.api.dto.FacetSearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.validation.context.RequestContextValidator;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.core.RequestOptions;
import com.groupbyinc.search.ssa.core.navigation.Facet;
import com.groupbyinc.search.ssa.features.FeaturesManager;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micronaut.core.propagation.MutablePropagatedContext;
import io.micronaut.core.propagation.PropagatedContext;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.MutableHttpRequest;
import jakarta.validation.ValidationException;
import jakarta.validation.Validator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static com.groupbyinc.search.ssa.stub.TestData.APPAREL;
import static com.groupbyinc.search.ssa.stub.TestData.COLLECTION;
import static com.groupbyinc.search.ssa.stub.TestData.LOGIN_ID;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTION;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;
import static com.groupbyinc.search.ssa.stub.TestData.REQUEST_ID;
import static com.groupbyinc.search.ssa.stub.TestData.TEST_ENVIRONMENT;
import static com.groupbyinc.search.ssa.stub.TestData.VISITOR_ID;
import static com.groupbyinc.search.ssa.util.Constants.GROUPBY_CUSTOMER_ID_HEADER;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@DisplayName("RequestContextFilter Tests")
class RequestContextFilterTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    private FeaturesManager featuresManager;

    @BeforeEach
    void setUp() {
        featuresManager = mock(FeaturesManager.class);
        when(featuresManager.getLdContextEnvironment()).thenReturn(TEST_ENVIRONMENT);
    }

    @Test
    @DisplayName("Resolves Context when request method is GET and env is single tenant")
    void resolvesContextForRequestMethodGetSingleTenant() {
        var expectedContext = new RequestContext(
            Merchandiser.of("gbiqa"),
            REQUEST_ID,
            PRODUCTS_CLOTHING,
            TEST_ENVIRONMENT,
            RequestOptions.EMPTY
        );

        var httpRequest = createGetRequest();

        var filter = new RequestContextFilter(objectMapper, featuresManager, new RequestContextValidator(List.of()), null, null);
        filter.setSingleTenant(true);
        filter.setTenantName("gbiqa");

        var body = new byte[0];
        var propagatedContext = MutablePropagatedContext.of(PropagatedContext.getOrEmpty());

        filter.addContextToRequest(httpRequest, body, propagatedContext);
        var actualContext = propagatedContext.getContext();

        assertNotNull(actualContext);
        assertThat(actualContext.get(RequestContext.class))
            .usingRecursiveComparison()
            .isEqualTo(expectedContext);
    }

    @Test
    @DisplayName("Resolves Context when request method is GET and env is multi tenant")
    void resolvesContextForRequestMethodGetMultiTenant() {
        var expectedContext = new RequestContext(
            Merchandiser.of(APPAREL),
            REQUEST_ID,
            PRODUCTS_CLOTHING,
            TEST_ENVIRONMENT,
            RequestOptions.EMPTY
        );

        var httpRequest = createGetRequest();

        var filter = new RequestContextFilter(objectMapper, featuresManager, new RequestContextValidator(List.of()), null, null);
        filter.setSingleTenant(false);

        var body = new byte[0];
        var propagatedContext = MutablePropagatedContext.of(PropagatedContext.getOrEmpty());

        filter.addContextToRequest(httpRequest, body, propagatedContext);
        var actualContext = propagatedContext.getContext();

        assertNotNull(actualContext);
        assertThat(actualContext.get(RequestContext.class))
            .usingRecursiveComparison()
            .isEqualTo(expectedContext);
    }

    @Test
    @DisplayName("Resolves Context when request method is POST request is facet and env is single tenant")
    void resolvesContextForRequestMethodPostForFacetSingleTenant() {
        var loginId = "AlVACH1BQn58pLpMu4drZtdWsxrEto9W6HY4MSYLmMc=";

        var expectedContext = new RequestContext(
            Merchandiser.of("gbiqa"),
            REQUEST_ID,
            PRODUCTION,
            PRODUCTS_CLOTHING,
            VISITOR_ID,
            loginId,
            null,
            TEST_ENVIRONMENT,
            RequestOptions.EMPTY
        );

        var httpRequest = createPostRequest(true);

        var filter = new RequestContextFilter(objectMapper, featuresManager, new RequestContextValidator(List.of()), null, null);
        filter.setSingleTenant(true);
        filter.setTenantName("gbiqa");
        filter.setSalt("f9c977ea-b423-4b3e-9952-d38cec720b70");

        var body = requestBodyBytes(true);
        var propagatedContext = MutablePropagatedContext.of(PropagatedContext.getOrEmpty());

        filter.addContextToRequest(httpRequest, body, propagatedContext);
        var actualContext = propagatedContext.getContext();

        assertNotNull(actualContext);
        assertThat(actualContext.get(RequestContext.class))
            .usingRecursiveComparison()
            .isEqualTo(expectedContext);
    }

    @Test
    @DisplayName("Resolves Context when request method is POST request is facet and env is multi tenant")
    void resolvesContextForRequestMethodPostForFacetMultiTenant() {
        var loginId = "AlVACH1BQn58pLpMu4drZtdWsxrEto9W6HY4MSYLmMc=";

        var expectedContext = new RequestContext(
            Merchandiser.of(APPAREL),
            REQUEST_ID,
            PRODUCTION,
            PRODUCTS_CLOTHING,
            VISITOR_ID,
            loginId,
            null,
            TEST_ENVIRONMENT,
            RequestOptions.EMPTY
        );

        var httpRequest = createPostRequest(true);

        var filter = new RequestContextFilter(objectMapper, featuresManager, new RequestContextValidator(List.of()), null, null);
        filter.setSingleTenant(false);
        filter.setSalt("f9c977ea-b423-4b3e-9952-d38cec720b70");

        var body = requestBodyBytes(true);
        var propagatedContext = MutablePropagatedContext.of(PropagatedContext.getOrEmpty());

        filter.addContextToRequest(httpRequest, body, propagatedContext);
        var actualContext = propagatedContext.getContext();

        assertNotNull(actualContext);
        assertThat(actualContext.get(RequestContext.class))
            .usingRecursiveComparison()
            .isEqualTo(expectedContext);
    }

    @Test
    @DisplayName("Resolves Context when request method is POST request is search and env is single tenant")
    void resolvesContextForRequestMethodPostForSearchSingleTenant() {
        var loginId = "AlVACH1BQn58pLpMu4drZtdWsxrEto9W6HY4MSYLmMc=";

        var expectedContext = new RequestContext(
            Merchandiser.of("gbiqa"),
            REQUEST_ID,
            PRODUCTION,
            PRODUCTS_CLOTHING,
            VISITOR_ID,
            loginId,
            null,
            TEST_ENVIRONMENT,
            RequestOptions.EMPTY
        );

        var httpRequest = createPostRequest(false);

        var filter = new RequestContextFilter(objectMapper, featuresManager, new RequestContextValidator(List.of()), null, null);
        filter.setSingleTenant(true);
        filter.setTenantName("gbiqa");
        filter.setSalt("f9c977ea-b423-4b3e-9952-d38cec720b70");

        var body = requestBodyBytes(false);
        var propagatedContext = MutablePropagatedContext.of(PropagatedContext.getOrEmpty());

        filter.addContextToRequest(httpRequest, body, propagatedContext);
        var actualContext = propagatedContext.getContext();

        assertNotNull(actualContext);
        assertThat(actualContext.get(RequestContext.class))
            .usingRecursiveComparison()
            .isEqualTo(expectedContext);
    }

    @Test
    @DisplayName("Resolves Context when request method is POST request is search and env is multi tenant")
    void resolvesContextForRequestMethodPostForSearchMultiTenant() {
        var loginId = "AlVACH1BQn58pLpMu4drZtdWsxrEto9W6HY4MSYLmMc=";

        var expectedContext = new RequestContext(
            Merchandiser.of(APPAREL),
            REQUEST_ID,
            PRODUCTION,
            PRODUCTS_CLOTHING,
            VISITOR_ID,
            loginId,
            null,
            TEST_ENVIRONMENT,
            RequestOptions.EMPTY
        );

        var httpRequest = createPostRequest(false);

        var filter = new RequestContextFilter(objectMapper, featuresManager, new RequestContextValidator(List.of()), null, null);
        filter.setSingleTenant(false);
        filter.setSalt("f9c977ea-b423-4b3e-9952-d38cec720b70");

        var body = requestBodyBytes(false);
        var propagatedContext = MutablePropagatedContext.of(PropagatedContext.getOrEmpty());

        filter.addContextToRequest(httpRequest, body, propagatedContext);
        var actualContext = propagatedContext.getContext();

        assertNotNull(actualContext);
        assertThat(actualContext.get(RequestContext.class))
            .usingRecursiveComparison()
            .isEqualTo(expectedContext);
    }

    private byte[] requestBodyBytes(boolean isFacet) {
        try {
            return objectMapper.writeValueAsBytes(requestBody(isFacet));
        } catch (JsonProcessingException e) {
            throw new ValidationException(e);
        }
    }

    private Object requestBody(boolean isFacet) {
        var searchRequestDto = SearchRequestDto.builder()
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .visitorId(VISITOR_ID)
            .loginId(LOGIN_ID)
            .build();


        if (isFacet) {
            var facet = Facet.builder().navigationName("test").build();
            new FacetSearchRequestDto(facet, searchRequestDto);
        }

        return searchRequestDto;
    }

    private HttpRequest<?> createPostRequest(boolean isFacet) {
        var httpRequest = HttpRequest.POST("/test", requestBody(isFacet));
        addRequestDetails(httpRequest);

        return httpRequest;
    }

    private HttpRequest<?> createGetRequest() {
        var httpRequest = HttpRequest.GET("/test");
        addRequestDetails(httpRequest);

        return httpRequest;
    }

    private void addRequestDetails(MutableHttpRequest<?> httpRequest) {
        httpRequest.getHeaders().add(GROUPBY_CUSTOMER_ID_HEADER, APPAREL);
        httpRequest.setAttribute(REQUEST_ID, REQUEST_ID);
        httpRequest.getParameters().add(COLLECTION, PRODUCTS_CLOTHING);
    }

}
