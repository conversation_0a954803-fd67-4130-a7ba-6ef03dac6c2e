package com.groupbyinc.search.ssa.api.validation;

import com.groupbyinc.search.ssa.api.validation.context.ConfigurationResolver;
import com.groupbyinc.search.ssa.api.validation.context.RequestContextValidationException;
import com.groupbyinc.search.ssa.api.validation.context.TenantConfigurationValidator;
import com.groupbyinc.search.ssa.core.tenant.TenantConfiguration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static com.groupbyinc.search.ssa.stub.TestData.DEFAULT_CONTEXT;

import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;

@DisplayName("TenantConfigurationSearchPreCondition Tests")
class TenantConfigurationValidatorTest {

    private TenantConfigurationValidator tenantConfigurationSearchPreCondition;
    private ConfigurationResolver<TenantConfiguration> tenantConfigurationResolver;
    private TenantConfiguration tenantConfiguration;

    @BeforeEach
    @SuppressWarnings("unchecked")
    void setUp() {
        this.tenantConfiguration = mock(TenantConfiguration.class);
        this.tenantConfigurationResolver = mock(ConfigurationResolver.class);
        this.tenantConfigurationSearchPreCondition = new TenantConfigurationValidator(tenantConfigurationResolver);
    }

    @Test
    @DisplayName("Throws an exception when the tenant cannot be found")
    void throwsAnExceptionWhenTheTenantCannotBeFound() {
        given(tenantConfigurationResolver.resolve(DEFAULT_CONTEXT)).willReturn(Optional.empty());

        assertThatThrownBy(() -> tenantConfigurationSearchPreCondition.validate(DEFAULT_CONTEXT))
            .isInstanceOf(RequestContextValidationException.class)
            .hasMessage("Tenant 'apparel' could not be found.");
    }

    @Test
    @DisplayName("Throws an exception when the tenant is not active")
    void throwsAnExceptionWhenTheTenantIsNotActive() {
        given(tenantConfigurationResolver.resolve(DEFAULT_CONTEXT)).willReturn(Optional.of(tenantConfiguration));
        given(tenantConfiguration.enabled()).willReturn(false);

        assertThatThrownBy(() -> tenantConfigurationSearchPreCondition.validate(DEFAULT_CONTEXT))
            .isInstanceOf(RequestContextValidationException.class)
            .hasMessage("Tenant 'apparel' is not active.");
    }

    @Test
    @DisplayName("Allows an active tenant")
    void allowsAnActiveTenant() {
        given(tenantConfigurationResolver.resolve(DEFAULT_CONTEXT)).willReturn(Optional.of(tenantConfiguration));
        given(tenantConfiguration.enabled()).willReturn(true);

        assertThatNoException()
            .isThrownBy(() -> tenantConfigurationSearchPreCondition.validate(DEFAULT_CONTEXT));
    }

}
