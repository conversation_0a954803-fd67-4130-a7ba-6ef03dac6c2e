package com.groupbyinc.search.ssa.api.error;

import com.groupbyinc.search.ssa.application.logging.LoggingContext;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.api.validation.context.RequestContextValidationException;

import io.micronaut.core.type.Argument;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.HttpStatus;
import io.micronaut.web.router.exceptions.UnsatisfiedPathVariableRouteException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.io.IOException;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.stub.TestData.ROCKET;

import static org.assertj.core.api.Assertions.assertThat;

class GlobalApiExceptionHandlerTest {

    GlobalApiExceptionHandler exceptionHandler = new GlobalApiExceptionHandler();

    @SuppressWarnings("unused") // Jupiter uses the scenarioName argument to generate display name
    @ParameterizedTest(name = "{0} --> {2}")
    @MethodSource("exceptionsToHandle")
    @DisplayName("Transforms propagated errors to HTTP responses properly")
    void transformsPropagatedErrorsToHttpResponsesProperly(String scenarioName,
                                                           Throwable internalError,
                                                           HttpStatus expectedResponseStatus,
                                                           String expectedMessage) {
        var trackingId = UUID.randomUUID().toString();
        var httpRequest = HttpRequest.POST("/api/resource?queryString=param", Map.of("field", "value"));
        httpRequest.setAttribute(LoggingContext.REQUEST_ID, trackingId);

        var response = exceptionHandler.handle(httpRequest, internalError);

        assertThat((Object) response.getStatus()).isEqualTo(expectedResponseStatus);

        var responseBody = response.body();
        assertThat(responseBody).isNotNull();

        assertThat(responseBody.trackingId()).isEqualTo(trackingId);
        assertThat(responseBody.method()).isEqualTo(httpRequest.getMethod().name());
        assertThat(responseBody.path()).isEqualTo(httpRequest.getPath());
        assertThat(responseBody.message()).contains(expectedMessage);
    }

    static Stream<Arguments> exceptionsToHandle() {

        var badRequestExceptions = Stream.of(
            RequestContextValidationException.tenantNotActive(Merchandiser.of(ROCKET)),
            new UnsatisfiedPathVariableRouteException("id", Argument.of(String.class))
        ).map(
            e -> Arguments.of(e.getClass().getSimpleName(), e, HttpStatus.BAD_REQUEST, e.getMessage())
        );

        var forbiddenExceptions = Stream.of(
            new AuthorizationException(null, "Search is not authorized.")
        ).map(
            e -> Arguments.of(e.getClass().getSimpleName(), e, HttpStatus.FORBIDDEN, e.getMessage())
        );

        var internalErrorIOException = Stream.of(
            new IOException("Error communicating over GRPC")
        ).map(
            e -> Arguments.of(e.getClass().getSimpleName(), e, HttpStatus.INTERNAL_SERVER_ERROR, "There was an internal error processing the search request: Error communicating over GRPC")
        );

        var internalErrorNPEException = Stream.of(
            new NullPointerException()
        ).map(
            e -> Arguments.of(e.getClass().getSimpleName(), e, HttpStatus.INTERNAL_SERVER_ERROR, "Unknown error occurred.")
        );

        var internalErrorIllegalStateException = Stream.of(
            new IllegalStateException("Network error")
        ).map(
            e -> Arguments.of(e.getClass().getSimpleName(), e, HttpStatus.INTERNAL_SERVER_ERROR, "There was an internal error processing the search request: Network error")
        );

        return Stream.of(
            badRequestExceptions,
            forbiddenExceptions,
            internalErrorIOException,
            internalErrorNPEException,
            internalErrorIllegalStateException
        ).flatMap(Function.identity());
    }
}
