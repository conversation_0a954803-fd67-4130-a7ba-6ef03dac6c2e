package com.groupbyinc.search.ssa.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.groupbyinc.search.ssa.api.builders.SearchResponseBuilder;
import com.groupbyinc.search.ssa.api.dto.FacetSearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.error.AuthorizationException;
import com.groupbyinc.search.ssa.api.validation.CustomValidationMessages;
import com.groupbyinc.search.ssa.application.Clock;
import com.groupbyinc.search.ssa.application.builders.SearchParametersBuilder;
import com.groupbyinc.search.ssa.application.builders.SearchParametersPreparer;
import com.groupbyinc.search.ssa.application.core.search.SearchService;
import com.groupbyinc.search.ssa.application.core.search.processor.redirect.RedirectProcessor;
import com.groupbyinc.search.ssa.application.core.search.processor.rule.RuleProcessor;
import com.groupbyinc.search.ssa.application.core.search.processor.rule.RuleSearchParametersUpdater;
import com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityBooster;
import com.groupbyinc.search.ssa.application.core.search.strategy.SearchStrategyPriorityList;
import com.groupbyinc.search.ssa.core.authz.Identity;
import com.groupbyinc.search.ssa.core.navigation.Facet;
import com.groupbyinc.search.ssa.metrics.opentelemetry.MeterRegistry;
import com.groupbyinc.search.ssa.retail.exception.SiteSearchRetailClientException;
import com.groupbyinc.search.ssa.retail.util.v2alpha.UserAttributesHelper;
import com.groupbyinc.search.ssa.stub.StubMeterRegistry;
import io.micronaut.context.event.ApplicationEventPublisher;
import io.micronaut.core.propagation.PropagatedContext;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import io.micronaut.validation.validator.Validator;
import jakarta.inject.Inject;
import jakarta.validation.ConstraintViolation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.function.Supplier;

import static com.groupbyinc.search.ssa.stub.TestData.COLLECTION;
import static com.groupbyinc.search.ssa.stub.TestData.DEFAULT_CONTEXT;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTION;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;

@SuppressWarnings("unchecked")
@DisplayName("SearchApi Tests")
@MicronautTest
class SearchApiTest {

    @Inject
    Validator validator;

    @Inject
    ProductVisibilityBooster productVisibilityBooster;

    private Identity identity;
    private SearchApi searchApi;
    private MeterRegistry meterRegistry;

    @BeforeEach
    void setUp() {
        meterRegistry = mock(StubMeterRegistry.class);
        identity = mock(Identity.class);
        given(identity.canPerformSearchFor(any())).willReturn(true);
        given(meterRegistry.act(any(Supplier.class))).willCallRealMethod();

        createSearchApiMock();

        PropagatedContext.getOrEmpty().plus(DEFAULT_CONTEXT).propagate();
    }

    @Test
    @SuppressWarnings("ConstantConditions")
    void throwsAnAuthorizationExceptionWhenTheIdentityIsNull() {
        var searchRequestDto = SearchRequestDto.builder()
            .build();

        assertThatThrownBy(() -> searchApi.search(null, searchRequestDto))
            .isInstanceOf(AuthorizationException.class)
            .hasMessage("Search is not authorized.");
    }

    @Test
    void throwsAnAuthorizationExceptionWhenTheIdentityIsNotAuthorizedToSearch() {
        given(identity.canPerformSearchFor(any())).willReturn(false);
        given(meterRegistry.act(any(Supplier.class))).willCallRealMethod();

        var searchRequestDto = SearchRequestDto.builder()
            .build();

        assertThatThrownBy(() -> searchApi.search(identity, searchRequestDto))
            .isInstanceOf(AuthorizationException.class)
            .hasMessage("Search is not authorized.");
    }

    @Test
    void throwValidationExceptionWhenPageCategoriesSizeMoreThanOne() {
        List<String> pageCategories = new ArrayList<>();
        pageCategories.add("category1");
        pageCategories.add("category2");

        var searchRequestDto = SearchRequestDto.builder()
            .area(PRODUCTION)
            .collection(COLLECTION)
            .pageCategories(pageCategories)
            .build();

        assertThatThrownBy(() -> searchApi.search(identity, searchRequestDto))
            .isInstanceOf(SiteSearchRetailClientException.class)
            .hasMessage(CustomValidationMessages.PAGE_CATEGORY_SIZE_MESSAGE);
    }

    @Test
    void checkValidationWhenPageCategoriesContainsNull() {
        List<String> pageCategories = new ArrayList<>();
        pageCategories.add(null);

        var searchRequestDto = SearchRequestDto.builder()
            .area(PRODUCTION)
            .collection(COLLECTION)
            .pageCategories(pageCategories)
            .build();

        Set<ConstraintViolation<SearchRequestDto>> violations = validator.validate(searchRequestDto);
        assertFalse(violations.isEmpty());
        assertEquals(1, violations.size());

        String message = "Array objects should not contain 'null' values.";

        violations.stream().filter(violation -> violation.getMessage().equals(message))
            .findFirst()
            .ifPresent(violation -> assertThat(violation.getMessage()).isEqualTo(message));
    }

    @Test
    void facetSearchThrowsAnExceptionWhenFacetIsMissing() {
        var searchRequestDto = SearchRequestDto.builder()
            .area(PRODUCTION)
            .collection(COLLECTION)
            .query("test")
            .build();

        var body = new FacetSearchRequestDto(null, searchRequestDto);

        assertThatThrownBy(() -> searchApi.facetSearch(identity, body))
            .isInstanceOf(SiteSearchRetailClientException.class)
            .hasMessage(CustomValidationMessages.FACET_MISSING_MESSAGE);
    }

    @Test
    void facetSearchThrowsAnExceptionWhenFacetNavigationNameIsMissing() {
        var searchRequestDto = SearchRequestDto.builder()
            .area(PRODUCTION)
            .collection(COLLECTION)
            .query("test")
            .build();

        var body = new FacetSearchRequestDto(Facet.builder().build(), searchRequestDto);

        assertThatThrownBy(() -> searchApi.facetSearch(identity, body))
            .isInstanceOf(SiteSearchRetailClientException.class)
            .hasMessage(CustomValidationMessages.FACET_NAVIGATION_NAME_MISSING_MESSAGE);
    }


    private void createSearchApiMock() {
        var searchService = new SearchService(
            mock(Clock.class),
            mock(ObjectMapper.class),
            mock(RuleProcessor.class),
            mock(RedirectProcessor.class),
            new SearchStrategyPriorityList(List.of()),
            new SearchResponseBuilder(),
            new SearchParametersBuilder(mock(SearchParametersPreparer.class), mock(UserAttributesHelper.class)),
            productVisibilityBooster,
            new RuleSearchParametersUpdater(),
            mock(ApplicationEventPublisher.class)
        );

        searchApi = new SearchApi(searchService, meterRegistry);
    }

}
