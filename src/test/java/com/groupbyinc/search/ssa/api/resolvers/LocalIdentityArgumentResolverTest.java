package com.groupbyinc.search.ssa.api.resolvers;

import com.groupbyinc.search.ssa.core.authz.Identity;

import io.micronaut.http.HttpRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Set;

import static com.groupbyinc.search.ssa.core.authz.Role.CUSTOMER_DEFAULT;
import static com.groupbyinc.search.ssa.util.Constants.GROUPBY_CUSTOMER_ID_HEADER;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("LocalIdentityArgumentResolver Tests")
class LocalIdentityArgumentResolverTest {

    private final LocalIdentityArgumentResolver resolver = new LocalIdentityArgumentResolver(false, null);

    @Test
    @DisplayName("Resolves identity when request has the expected header")
    void resolvesWhenRequestHasExpectedHeader() {

        var httpRequest = HttpRequest.GET("/test")
            .header(GROUPBY_CUSTOMER_ID_HEADER, "valid");

        var expectedIdentity = new Identity("valid", "valid", Set.of(CUSTOMER_DEFAULT));

        assertThat(resolver.bind(null, httpRequest).get())
            .isEqualTo(expectedIdentity);
    }

    @Test
    @DisplayName("Returns unsatisfied if expected header is not present")
    void returnsUnsatisfiedIfExpectedHeaderNotPresent() {

        var httpRequest = HttpRequest.GET("/test");

        assertThat(resolver.bind(null, httpRequest).isSatisfied())
            .isFalse();
    }
}
