package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
import com.groupbyinc.search.ssa.core.rule.RuleTemplate;
import com.groupbyinc.search.ssa.core.template.Template;
import com.groupbyinc.search.ssa.core.template.zone.TemplateZoneContent;
import com.groupbyinc.search.ssa.core.template.zone.TemplateZoneHTML;
import com.groupbyinc.search.ssa.core.template.zone.TemplateZoneRichContent;
import com.groupbyinc.search.ssa.core.zone.ZoneType;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Base64;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("IdentityArgumentResolver Tests")
class TemplateDtoTest {


    @Test
    @DisplayName("Resolves identity when request is authenticated with a proper claim set")
    void testFromDomain() {
        //prepare
        var rulConfig = RuleConfiguration.builder()
            .name("rule 1")
            .template(new RuleTemplate("temple", false, Collections.emptyList()))
            .build();

        var zoneContent = new TemplateZoneContent(
            "1_content",
            ZoneType.CONTENT,
            "I'm so plain"
        );
        var zoneRich = new TemplateZoneRichContent(
            "2_rich_content",
            ZoneType.RICH_CONTENT,
            "<b>bold of you to assume I'm rich</b>"
        );

        var htmlContent = "<div>hello</div>";
        var zoneHTMLEncoded = new TemplateZoneHTML(
            "3_html_content",
            ZoneType.HTML,
            new String(Base64.getEncoder().encode(htmlContent.getBytes()))
        );
        var zoneHTMLRaw = new TemplateZoneHTML(
            "4_html_content",
            ZoneType.HTML,
            htmlContent
        );

        var template = Template.ofRule(rulConfig);
        template.addZones(List.of(zoneContent, zoneRich, zoneHTMLEncoded, zoneHTMLRaw));

        // act
        var result = TemplateDto.fromDomain(template);

        //verify
        assertThat(result).isNotNull();
        assertThat(result.getName()).isEqualTo(template.getName());
        assertThat(result.getRuleName()).isEqualTo(rulConfig.getName());

        assertThat(result.getZones()).hasSize(4);
        result.getZones().sort(Comparator.comparing(ZoneDto::name));

        assertThat(result.getZones().getFirst().name()).isEqualTo(zoneContent.getName());
        assertThat(result.getZones().getFirst().type()).isEqualTo(ZoneDtoType.Content);
        assertThat(result.getZones().getFirst().content()).isEqualTo(zoneContent.getContent());
        assertThat(result.getZones().getFirst().richContent()).isNull();
        assertThat(result.getZones().getFirst().html()).isNull();

        assertThat(result.getZones().get(1).name()).isEqualTo(zoneRich.getName());
        assertThat(result.getZones().get(1).type()).isEqualTo(ZoneDtoType.Rich_Content);
        assertThat(result.getZones().get(1).content()).isNull();
        assertThat(result.getZones().get(1).richContent()).isEqualTo(zoneRich.getRichContent());
        assertThat(result.getZones().get(1).html()).isNull();

        assertThat(result.getZones().get(2).name()).isEqualTo(zoneHTMLEncoded.getName());
        assertThat(result.getZones().get(2).type()).isEqualTo(ZoneDtoType.HTML);
        assertThat(result.getZones().get(2).content()).isNull();
        assertThat(result.getZones().get(2).richContent()).isNull();
        assertThat(result.getZones().get(2).html()).isEqualTo(zoneHTMLEncoded.getHtmlContentDecoded());
        assertThat(zoneHTMLEncoded.getHtmlContentDecoded()).isEqualTo(htmlContent);

        assertThat(result.getZones().get(3).name()).isEqualTo(zoneHTMLRaw.getName());
        assertThat(result.getZones().get(3).type()).isEqualTo(ZoneDtoType.HTML);
        assertThat(result.getZones().get(3).content()).isNull();
        assertThat(result.getZones().get(3).richContent()).isNull();
        assertThat(result.getZones().get(3).html()).isEqualTo(zoneHTMLRaw.getHtmlContentDecoded());
        assertThat(zoneHTMLRaw.getHtmlContentDecoded()).isEqualTo(htmlContent);
    }
}
