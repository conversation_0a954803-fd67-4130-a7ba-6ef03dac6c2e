package com.groupbyinc.search.ssa.api.utils;

import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.List;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

class SearchUtilsTest {

    @ParameterizedTest(name = "query = {0}, pageCategories = {1}, expect = {2}")
    @MethodSource("getRequestPageCategoriesScenarios")
    public void testGetRequestPageCategories(String query,
                                             List<String> pageCategories,
                                             List<String> expectPageCategories) {
        var sr = SearchRequestDto.builder()
            .query(query)
            .pageCategories(pageCategories)
            .build();

        assertThat(SearchUtils.getRequestPageCategories(sr)).isEqualTo(expectPageCategories);

    }

    static Stream<Arguments> getRequestPageCategoriesScenarios() {
        return Stream.of(
            Arguments.of("", null, SearchUtils.DEFAULT_PAGE_CATEGORIES),
            Arguments.of("", List.of(), SearchUtils.DEFAULT_PAGE_CATEGORIES),
            Arguments.of("", List.of(""), SearchUtils.DEFAULT_PAGE_CATEGORIES),
            Arguments.of("", List.of(" "), SearchUtils.DEFAULT_PAGE_CATEGORIES),
            Arguments.of("", List.of("CAT"), List.of("CAT")),
            Arguments.of(null, null, SearchUtils.DEFAULT_PAGE_CATEGORIES),
            Arguments.of(null, List.of(), SearchUtils.DEFAULT_PAGE_CATEGORIES),
            Arguments.of(null, List.of(""), SearchUtils.DEFAULT_PAGE_CATEGORIES),
            Arguments.of(null, List.of(" "), SearchUtils.DEFAULT_PAGE_CATEGORIES),
            Arguments.of(null, List.of("CAT"), List.of("CAT")),

            Arguments.of("red hat", null, null),
            Arguments.of("red hat", List.of(), List.of()),
            Arguments.of("red hat", List.of(""), List.of("")),
            Arguments.of("red hat", List.of(" "), List.of(" ")),
            Arguments.of("red hat", List.of("CAT"), List.of("CAT"))
        );
    }
}
