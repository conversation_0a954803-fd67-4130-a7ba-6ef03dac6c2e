package com.groupbyinc.search.ssa.api;

import com.groupbyinc.search.ssa.api.error.AuthorizationException;
import com.groupbyinc.search.ssa.application.core.pdp.ProductSearchStrategy;
import com.groupbyinc.search.ssa.core.authz.Identity;
import com.groupbyinc.search.ssa.metrics.opentelemetry.MeterRegistry;
import com.groupbyinc.search.ssa.retail.ProductGoogleSearcher;

import io.micronaut.core.propagation.PropagatedContext;
import io.opentelemetry.api.metrics.DoubleHistogram;
import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.context.ContextKey;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;

import static com.groupbyinc.search.ssa.stub.TestData.DEFAULT_CONTEXT;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;

@DisplayName("ProductApi Tests")
class ProductApiTest {

    private Identity identity;
    private ProductApi productApi;

    @BeforeEach
    void setUp() {
        var productGoogleSearcher = mock(ProductGoogleSearcher.class);
        var productSearchStrategy = mock(ProductSearchStrategy.class);
        given(productSearchStrategy.getStrategyFor(any())).willReturn(productGoogleSearcher);
        given(productGoogleSearcher.getProductDetails(any(), any())).willCallRealMethod();
        var metricLongCounters = new HashMap<ContextKey<String>, LongCounter>();
        var metricDoubleHistograms = new HashMap<ContextKey<Double>, DoubleHistogram>();
        var meterRegistry = new MeterRegistry(metricLongCounters, metricDoubleHistograms, null);

        identity = mock(Identity.class);
        given(identity.canPerformSearchFor(any())).willReturn(true);

        productApi = new ProductApi(productSearchStrategy, meterRegistry);

        PropagatedContext.getOrEmpty().plus(DEFAULT_CONTEXT).propagate();
    }

    @Test
    void throwsAnAuthorizationExceptionWhenTheIdentityIsNull() {
        assertThatThrownBy(() -> productApi.getByProductIds(null, "id-1", List.of()))
            .isInstanceOf(AuthorizationException.class)
            .hasMessage("Search is not authorized.");
    }

    @Test
    void throwsAnAuthorizationExceptionWhenTheIdentityIsNotAuthorizedToSearch() {
        given(identity.canPerformSearchFor(any())).willReturn(false);

        assertThatThrownBy(() -> productApi.getByProductIds(identity, "id-1", List.of()))
            .isInstanceOf(AuthorizationException.class)
            .hasMessage("Search is not authorized.");
    }
}
