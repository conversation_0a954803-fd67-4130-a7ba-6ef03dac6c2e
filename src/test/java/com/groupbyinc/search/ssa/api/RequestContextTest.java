package com.groupbyinc.search.ssa.api;

import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.utils.validation.ValidationException;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import static com.groupbyinc.search.ssa.stub.TestData.createContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

@DisplayName("Context Tests")
class RequestContextTest {

    @ParameterizedTest(name = "When merchandiser is ''{0}'' and area is ''{1}'' and collection is ''{2}''")
    @CsvSource(
        value = {
            "'rocket','apparel','collectionId'",
            "'ROCKET','apparel','collectionId'",
            "'ROCKET','apparel','collection-Id'",
            "'ROCKET','apparel','collectionId1'",
            "'ROCKET','apparel','collectionId1a'",
            "'ROCKET','apparel','collectionId1a2'",
        }
    )
    @DisplayName("Specified valid values for merchandiser, area and collection")
    void specifiedValidValues(String merchandiser, String area, String collection) {

        var actual = createContext(Merchandiser.of(merchandiser), collection, area);

        assertThat(actual.getMerchandiser().merchandiserId()).isEqualTo(merchandiser);
        assertThat(actual.getArea()).isEqualTo(area);
        assertThat(actual.getCollection()).isEqualTo(collection);
    }

    @ParameterizedTest(name = "When ldContextConfigEnv is ''{0}''")
    @NullAndEmptySource
    @ValueSource(strings = { "gbiqa" })
    @DisplayName("Specified environment to LaunchDarkly context config")
    void specifiedEnvToLDContext(String ldContextConfigEnv) {
        var context = createContext(ldContextConfigEnv);

        var actualEnvValue = context.getLdContext()
            .getIndividualContext("configuration")
            .getValue("environment")
            .stringValue();
        assertThat(actualEnvValue).isEqualTo(ldContextConfigEnv);
    }

    @ParameterizedTest(name = "When merchandiser is ''{0}'' and area is ''{1}'' and collection is ''{2}''")
    @CsvSource(
        value = {
            "'rocket','1','1collectionId'",    // collection must match [a-zA-Z]+[a-zA-Z0-9\-]+[a-zA-Z0-9]
            "'rocket','1',' collectionId'",    //                            "
            "'rocket','1','collectionId '",    //                            "
        }
    )
    @DisplayName("Specified invalid values for merchandiser, area and collection")
    void specifiedInvalidValues(String merchandiser, String area, String collection) {
        assertThrows(ValidationException.class, () -> createContext(Merchandiser.of(merchandiser), collection, area));
    }
}
