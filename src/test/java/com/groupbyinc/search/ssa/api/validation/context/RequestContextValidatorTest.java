package com.groupbyinc.search.ssa.api.validation.context;

import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.RequestOptions;

import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.LOGIN_ID;
import static com.groupbyinc.search.ssa.stub.TestData.REQUEST_ID;
import static com.groupbyinc.search.ssa.stub.TestData.ROCKET_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.VISITOR_ID;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@MicronautTest
@DisplayName("RequestContextValidator Tests")
public class RequestContextValidatorTest {

    @Inject
    RequestContextValidator requestContextValidator;

    @Test
    void throwsAnExceptionWhenTheCollectionNotFound() {
        var context = new RequestContext(
            APPAREL_MERCHANDISER,
            REQUEST_ID,
            "area",
            "collection",
            VISITOR_ID,
            LOGIN_ID,
            List.of(),
            "test",
            RequestOptions.EMPTY
        );

        assertThatThrownBy(() -> requestContextValidator.validate(context))
            .isInstanceOf(RequestContextValidationException.class)
            .hasMessage("Collection 'collection' could not be found.");
    }

    @Test
    void throwsAnExceptionWhenTheAreaCollectionPairInvalid() {
        var context = new RequestContext(
            APPAREL_MERCHANDISER,
            REQUEST_ID,
            "rocket",
            "productsClothing",
            VISITOR_ID,
            LOGIN_ID,
            List.of(),
            "test",
            RequestOptions.EMPTY
        );

        assertThatThrownBy(() -> requestContextValidator.validate(context))
            .isInstanceOf(RequestContextValidationException.class)
            .hasMessage(
                "Area 'rocket' could not be found or can not be used with passed collection: 'productsClothing'"
            );
    }

    @Test
    void throwsAnExceptionWhenTheTenantInactive() {
        var context = new RequestContext(
            ROCKET_MERCHANDISER,
            REQUEST_ID,
            "rocket",
            "productsClothing",
            VISITOR_ID,
            LOGIN_ID,
            List.of(),
            "test",
            RequestOptions.EMPTY
        );

        assertThatThrownBy(() -> requestContextValidator.validate(context))
            .isInstanceOf(RequestContextValidationException.class)
            .hasMessage("Tenant 'rocket' is not active.");
    }

    @Test
    void throwsAnExceptionWhenTheTenantNotFound() {
        var context = new RequestContext(
            Merchandiser.of("test"),
            REQUEST_ID,
            "rocket",
            "productsClothing",
            VISITOR_ID,
            LOGIN_ID,
            List.of(),
            "test",
            RequestOptions.EMPTY
        );

        assertThatThrownBy(() -> requestContextValidator.validate(context))
            .isInstanceOf(RequestContextValidationException.class)
            .hasMessage("Tenant 'test' could not be found.");
    }

}
