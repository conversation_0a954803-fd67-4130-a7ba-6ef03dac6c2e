package com.groupbyinc.search.ssa.api.validation;

import com.groupbyinc.search.ssa.core.area.AreaConfiguration;

import com.groupbyinc.search.ssa.api.validation.context.AreaCollectionValidator;
import com.groupbyinc.search.ssa.api.validation.context.ConfigurationResolver;
import com.groupbyinc.search.ssa.api.validation.context.ProjectConfigurationValidator;
import com.groupbyinc.search.ssa.api.validation.context.RequestContextValidationException;
import com.groupbyinc.search.ssa.core.project.ProjectConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static com.groupbyinc.search.ssa.stub.TestData.DEFAULT_CONTEXT;

import static org.assertj.core.api.Assertions.assertThatNoException;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;


@DisplayName("AreaCollectionValidatorTest Tests")
class AreaConfigurationContextValidatorTest {

    private AreaCollectionValidator areaCollectionValidator;
    private ProjectConfigurationValidator projectConfigurationValidator;
    private ConfigurationResolver<AreaConfiguration> areaConfigurationResolver;
    private ConfigurationResolver<ProjectConfiguration> projectConfigurationResolver;
    private AreaConfiguration areaConfiguration;
    private ProjectConfiguration projectConfiguration;

    @BeforeEach
    @SuppressWarnings("unchecked")
    void setUp() {
        this.areaConfiguration = mock(AreaConfiguration.class);
        this.projectConfiguration = mock(ProjectConfiguration.class);
        this.areaConfigurationResolver = mock(ConfigurationResolver.class);
        this.projectConfigurationResolver = mock(ConfigurationResolver.class);
        this.areaCollectionValidator = new AreaCollectionValidator(areaConfigurationResolver);
        this.projectConfigurationValidator = new ProjectConfigurationValidator(projectConfigurationResolver);
    }

    @Test
    @DisplayName("Throws an exception when the area cannot be found")
    void throwsAnExceptionWhenTheAreaCannotBeFound() {
        given(areaConfigurationResolver.resolve(DEFAULT_CONTEXT)).willReturn(Optional.empty());

        assertThatThrownBy(() -> areaCollectionValidator.validate(DEFAULT_CONTEXT))
            .isInstanceOf(RequestContextValidationException.class);
    }

    @Test
    @DisplayName("Throws an exception when the collection cannot be found")
    void throwsAnExceptionWhenTheCollectionCannotBeFound() {
        given(projectConfigurationResolver.resolve(DEFAULT_CONTEXT)).willReturn(Optional.empty());

        assertThatThrownBy(() -> projectConfigurationValidator.validate(DEFAULT_CONTEXT))
            .isInstanceOf(RequestContextValidationException.class)
            .hasMessage("Collection 'productsClothing' could not be found.");
    }

    @Test
    @DisplayName("Allows an area and collection that can be found")
    void allowsAnAreaThatCanBeFound() {
        given(areaConfigurationResolver.resolve(DEFAULT_CONTEXT)).willReturn(Optional.of(areaConfiguration));
        given(projectConfigurationResolver.resolve(DEFAULT_CONTEXT)).willReturn(Optional.of(projectConfiguration));

        assertThatNoException()
            .isThrownBy(() -> projectConfigurationValidator.validate(DEFAULT_CONTEXT));
    }

}
