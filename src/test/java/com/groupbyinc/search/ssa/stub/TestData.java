package com.groupbyinc.search.ssa.stub;

import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.Metadata;
import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.core.RequestOptions;
import com.groupbyinc.search.ssa.core.area.AreaConfiguration;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.biasing.Bias;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfileConfiguration;
import com.groupbyinc.search.ssa.core.filter.AttributeFilter;
import com.groupbyinc.search.ssa.core.filter.ProductIdFilter;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.project.ProjectConfiguration;
import com.groupbyinc.search.ssa.core.redirect.RedirectConfiguration;
import com.groupbyinc.search.ssa.core.rule.ExperimentVariant;
import com.groupbyinc.search.ssa.core.rule.PinnedProduct;
import com.groupbyinc.search.ssa.core.rule.PinnedRefinement;
import com.groupbyinc.search.ssa.core.rule.ProductIdsBucket;
import com.groupbyinc.search.ssa.core.rule.Refinement;
import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
import com.groupbyinc.search.ssa.core.rule.RuleTemplate;
import com.groupbyinc.search.ssa.core.rule.RuleTemplateSection;
import com.groupbyinc.search.ssa.core.rule.RuleType;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;
import com.groupbyinc.search.ssa.core.tenant.TenantConfiguration;
import com.groupbyinc.search.ssa.core.trigger.QueryPatternTrigger;
import com.groupbyinc.search.ssa.core.trigger.TriggerSet;
import com.groupbyinc.search.ssa.core.zone.ZoneConfiguration;
import com.groupbyinc.search.ssa.core.zone.ZoneType;
import com.groupbyinc.search.ssa.topsort.model.TopsortConfiguration;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.groupbyinc.search.ssa.core.navigation.NavigationType.RANGE;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.VALUE;
import static com.groupbyinc.search.ssa.core.tenant.TenantSettings.DEFAULT;

public class TestData {

    public static final String ROCKET = "rocket";
    public static final String APPAREL = "apparel";

    public static final String TEST_ENVIRONMENT = "test";
    public static final String COLLECTION = "collection";

    public static final Metadata METADATA = new Metadata("key", "value");

    // region RequestContext
    public static final String LOGIN_ID = "loginId";
    public static final String REQUEST_ID = "requestId";
    public static final String VISITOR_ID = "visitorId";

    public static final Merchandiser ROCKET_MERCHANDISER = Merchandiser.of(ROCKET);
    public static final Merchandiser APPAREL_MERCHANDISER = Merchandiser.of(APPAREL);

    public static final RequestContext DEFAULT_CONTEXT = createContext(APPAREL_MERCHANDISER);

    public static RequestContext createContext(Merchandiser merchandiser) {
        return createContext(merchandiser, PRODUCTS_CLOTHING, PRODUCTION);
    }

    public static RequestContext createContext(String environment) {
        return new RequestContext(
            APPAREL_MERCHANDISER,
            REQUEST_ID,
            PRODUCTION,
            PRODUCTS_CLOTHING,
            VISITOR_ID,
            LOGIN_ID,
            List.of(),
            environment,
            RequestOptions.EMPTY
        );
    }

    public static RequestContext createContext(Merchandiser merchandiser, String collection, String area) {
        return new RequestContext(
            merchandiser,
            REQUEST_ID,
            area,
            collection,
            VISITOR_ID,
            LOGIN_ID,
            List.of(),
            TEST_ENVIRONMENT,
            RequestOptions.EMPTY
        );
    }
    // endregion

    // region Tenant
    public static final Integer ACTIVE_TENANT_ID = 1;
    public static final Integer INACTIVE_TENANT_ID = 2;

    public static final TenantConfiguration ACTIVE_TENANT = tenant(ACTIVE_TENANT_ID, APPAREL, true);
    public static final TenantConfiguration INACTIVE_TENANT = tenant(INACTIVE_TENANT_ID, ROCKET, false);

    public static TenantConfiguration tenant(Integer id, String name, boolean enabled) {
        return TenantConfiguration.builder()
            .id(id)
            .name(name)
            .enabled(enabled)
            .settings(DEFAULT)
            .build();
    }
    // endregion

    // region ProjectConfiguration
    public static final Integer ACTIVE_COLLECTION_ID = 1;
    public static final Integer INACTIVE_COLLECTION_ID = 2;

    public static final String ACTIVE_COLLECTION_PROJECT = "111";
    public static final String INACTIVE_COLLECTION_PROJECT = "222";

    public static final String PRODUCTS_CLOTHING = "productsClothing";
    public static final ProjectConfiguration ACTIVE_COLLECTION = projectConfiguration(
        ACTIVE_COLLECTION_ID,
        PRODUCTS_CLOTHING,
        ACTIVE_TENANT_ID,
        ACTIVE_COLLECTION_PROJECT
    );
    public static final ProjectConfiguration INACTIVE_COLLECTION = projectConfiguration(
        INACTIVE_COLLECTION_ID,
        APPAREL,
        INACTIVE_TENANT_ID,
        INACTIVE_COLLECTION_PROJECT
    );
    public static ProjectConfiguration projectConfiguration(Integer id,
                                                            String collection,
                                                            Integer tenantId,
                                                            String projectId) {
        return ProjectConfiguration.builder()
            .id(id)
            .collection(collection)
            .tenantId(tenantId)
            .projectId(projectId)
            .build();
    }
    // endregion

    // region SiteFilter
    public static final Integer ACTIVE_SITE_FILTER_ID = 1;
    public static final Integer INACTIVE_SITE_FILTER_ID = 2;

    public static final String ACTIVE_SITE_FILTER_NAME = "active";
    public static final String INACTIVE_SITE_FILTER_NAME = "inactive";

    public static final String ACTIVE_SITE_FILTER_FILTER = "colorFamilies:ANY(\"White\")";
    public static final String INACTIVE_SITE_FILTER_FILTER = "availability: ANY(\"IN_STOCK\")";

    public static final SiteFilterConfiguration ACTIVE_SITE_FILTER = siteFilter(
        ACTIVE_SITE_FILTER_ID,
        ACTIVE_COLLECTION_ID,
        ACTIVE_SITE_FILTER_NAME,
        ACTIVE_SITE_FILTER_FILTER
    );
    public static final SiteFilterConfiguration INACTIVE_SITE_FILTER = siteFilter(
        INACTIVE_SITE_FILTER_ID,
        INACTIVE_COLLECTION_ID,
        INACTIVE_SITE_FILTER_NAME,
        INACTIVE_SITE_FILTER_FILTER
    );

    public static SiteFilterConfiguration siteFilter(Integer id,
                                                     Integer collectionId,
                                                     String name,
                                                     String rawFilter) {
        return SiteFilterConfiguration.builder()
            .id(id)
            .collectionId(collectionId)
            .name(name)
            .rawFilter(rawFilter)
            .build();
    }
    // endregion

    // region AreaConfiguration
    public static final Integer ACTIVE_AREA_ID = 1;
    public static final Integer INACTIVE_AREA_ID = 2;
    public static final String PRODUCTION = "Production";
    public static final AreaConfiguration ACTIVE_AREA = area(
        ACTIVE_AREA_ID,
        ACTIVE_TENANT_ID,
        ACTIVE_COLLECTION_ID,
        ACTIVE_SITE_FILTER_ID
    );
    public static final AreaConfiguration INACTIVE_AREA = area(
        INACTIVE_AREA_ID,
        INACTIVE_TENANT_ID,
        INACTIVE_COLLECTION_ID,
        INACTIVE_SITE_FILTER_ID
    );

    public static AreaConfiguration area(Integer id,
                                         Integer tenantId,
                                         Integer collectionId,
                                         Integer siteFilterId) {
        return AreaConfiguration.builder()
            .id(id)
            .name(PRODUCTION)
            .tenantId(tenantId)
            .metadata(List.of(METADATA))
            .collectionId(collectionId)
            .servingConfigName(PRODUCTION)
            .siteFilterId(siteFilterId)
            .topsortConfiguration(TopsortConfiguration.DISABLED)
            .build();
    }
    // endregion

    // region RedirectConfiguration
    public static final Integer ACTIVE_REDIRECT_ID = 1;
    public static final Integer INACTIVE_REDIRECT_ID = 2;

    public static final RedirectConfiguration ACTIVE_REDIRECT = redirect(ACTIVE_REDIRECT_ID, ACTIVE_AREA_ID);
    public static final RedirectConfiguration INACTIVE_REDIRECT = redirect(INACTIVE_REDIRECT_ID, INACTIVE_AREA_ID);

    public static RedirectConfiguration redirect(Integer id, Integer areaId) {
        return RedirectConfiguration.builder()
            .id(id)
            .areaId(areaId)
            .url("www.example.com/bacon-jeans")
            .priority(1)
            .activeHoursEnabled(false)
            .activeFrom(null)
            .activeTo(null)
            .triggers(
                List.of(
                    new QueryPatternTrigger(QueryPatternTrigger.Type.MATCHES, "bacon,cat"),
                    new QueryPatternTrigger(QueryPatternTrigger.Type.CONTAINS, "bacon,cat"),
                    new QueryPatternTrigger(QueryPatternTrigger.Type.REGEX, "\\d{3},\\d{4}")
                )
            )
            .metadata(List.of(METADATA))
            .build();
    }
    // endregion

    // region ZoneConfiguration
    public static final Integer ACTIVE_ZONE_ID = 1;
    public static final Integer INACTIVE_ZONE_ID = 2;

    public static final ZoneConfiguration ACTIVE_ZONE = zone(ACTIVE_ZONE_ID, ACTIVE_AREA_ID);
    public static final ZoneConfiguration INACTIVE_ZONE = zone(INACTIVE_ZONE_ID, INACTIVE_AREA_ID);

    public static ZoneConfiguration zone(Integer id, Integer areaId) {
        return ZoneConfiguration.builder()
            .id(id)
            .areaId(areaId)
            .name("HTML")
            .value("<h1>HTML</h1>")
            .zoneType(ZoneType.CONTENT)
            .build();
    }
    // endregion

    // region RuleConfiguration
    public static final Integer ACTIVE_RULE_ID = 1;
    public static final Integer INACTIVE_RULE_ID = 2;

    public static final RuleTemplate ACTIVE_RULE_TEMPLATE = new RuleTemplate(
        "ACTIVE_RULE_TEMPLATE",
        false,
        List.of(new RuleTemplateSection(ACTIVE_ZONE_ID, "activeSection", null, ZoneType.CONTENT))
    );
    public static final RuleTemplate INACTIVE_RULE_TEMPLATE = new RuleTemplate(
        "INACTIVE_RULE_TEMPLATE",
        false,
        List.of(new RuleTemplateSection(INACTIVE_ZONE_ID, "inactiveSection", null, ZoneType.CONTENT))
    );

    public static final RuleConfiguration ACTIVE_RULE = rule(
        ACTIVE_RULE_ID,
        "ACTIVE_RULE",
        ACTIVE_AREA_ID,
        ACTIVE_RULE_TEMPLATE
    );
    public static final RuleConfiguration INACTIVE_RULE = rule(
        INACTIVE_RULE_ID,
        "INACTIVE_RULE",
        INACTIVE_AREA_ID,
        INACTIVE_RULE_TEMPLATE
    );

    public static RuleConfiguration rule(List<ExperimentVariant> variants) {
        return RuleConfiguration.builder()
            .id(ACTIVE_RULE_ID)
            .name("ACTIVE_RULE")
            .areaId(ACTIVE_AREA_ID)
            .variants(variants)
            .type(RuleType.EXPERIMENT)
            .build();
    }

    public static RuleConfiguration ruleAttributeFilters(List<AttributeFilter> attributeFilters) {
        return RuleConfiguration.builder()
            .id(ACTIVE_RULE_ID)
            .name("ACTIVE_RULE")
            .areaId(ACTIVE_AREA_ID)
            .attributeFilters(attributeFilters)
            .type(RuleType.REGULAR)
            .build();
    }

    public static RuleConfiguration rulePinnedRefinements(List<PinnedRefinement> pinnedRefinements) {
        return RuleConfiguration.builder()
            .id(ACTIVE_RULE_ID)
            .name("ACTIVE_RULE")
            .areaId(ACTIVE_AREA_ID)
            .pinnedRefinements(pinnedRefinements)
            .type(RuleType.REGULAR)
            .build();
    }

    public static RuleConfiguration ruleProductIdFilter(ProductIdFilter productIdFilter) {
        return RuleConfiguration.builder()
            .id(ACTIVE_RULE_ID)
            .name("ACTIVE_RULE")
            .areaId(ACTIVE_AREA_ID)
            .productIdFilter(productIdFilter)
            .type(RuleType.REGULAR)
            .build();
    }

    public static RuleConfiguration rulePinnedProducts(List<PinnedProduct> pinnedProducts) {
        return RuleConfiguration.builder()
            .id(ACTIVE_RULE_ID)
            .name("ACTIVE_RULE")
            .areaId(ACTIVE_AREA_ID)
            .pinnedProducts(pinnedProducts)
            .type(RuleType.REGULAR)
            .build();
    }

    public static RuleConfiguration ruleBoostedProductBuckets(List<ProductIdsBucket> boostedProductBuckets) {
        return RuleConfiguration.builder()
            .id(ACTIVE_RULE_ID)
            .name("ACTIVE_RULE")
            .areaId(ACTIVE_AREA_ID)
            .boostedProductBuckets(boostedProductBuckets)
            .type(RuleType.REGULAR)
            .build();
    }

    public static RuleConfiguration ruleBuriedProductBuckets(List<ProductIdsBucket> buriedProductBuckets) {
        return RuleConfiguration.builder()
            .id(ACTIVE_RULE_ID)
            .name("ACTIVE_RULE")
            .areaId(ACTIVE_AREA_ID)
            .buriedProductBuckets(buriedProductBuckets)
            .type(RuleType.REGULAR)
            .build();
    }

    public static RuleConfiguration ruleBiasingProfileName(String biasingProfileName) {
        return RuleConfiguration.builder()
            .id(ACTIVE_RULE_ID)
            .name("ACTIVE_RULE")
            .areaId(ACTIVE_AREA_ID)
            .biasingProfileName(biasingProfileName)
            .type(RuleType.REGULAR)
            .build();
    }

    public static RuleConfiguration ruleIncludedNavigations(List<String> includedNavigations) {
        return RuleConfiguration.builder()
            .id(ACTIVE_RULE_ID)
            .name("ACTIVE_RULE")
            .areaId(ACTIVE_AREA_ID)
            .includedNavigations(includedNavigations)
            .type(RuleType.REGULAR)
            .build();
    }


    public static RuleConfiguration rule(Integer id,
                                         String name,
                                         Integer areaId,
                                         RuleTemplate template) {
        return RuleConfiguration.builder()
            .id(id)
            .name(name)
            .areaId(areaId)
            .priority(1)
            .activeHoursEnabled(false)
            .activeFrom(null)
            .activeTo(null)
            .triggerSets(
                Set.of(
                    new TriggerSet(
                        List.of(
                            new QueryPatternTrigger(QueryPatternTrigger.Type.MATCHES, "bacon"),
                            new QueryPatternTrigger(QueryPatternTrigger.Type.CONTAINS, "bacon, cat"),
                            new QueryPatternTrigger(QueryPatternTrigger.Type.REGEX, "\\d{3},\\d{4}")
                        ),
                        List.of(),
                        List.of()
                    )
                )
            )
            .biasingProfileName(null)
            .includedNavigations(new ArrayList<>())
            .attributeFilters(new ArrayList<>())
            .productIdFilter(null)
            .template(template)
            .boostedProductBuckets(new ArrayList<>())
            .buriedProductBuckets(new ArrayList<>())
            .pinnedRefinements(new ArrayList<>())
            .pinnedProducts(new ArrayList<>())
            .type(RuleType.REGULAR)
            .variants(new ArrayList<>())
            .build();
    }
    // endregion

    // region NavigationConfiguration
    public static final String BRANDS = "brands";
    public static final String PRICE = "price";
    public static final String COLOR_FAMILIES = "colorFamilies";
    public static final List<Range> DEFAULT_NAVIGATION_RANGES = List.of(
        new Range(0d, 25d, null),
        new Range(25d, 100d, null),
        new Range(100d, 1000d, null)
    );
    public static final Refinement BRAND_PINNED_REFINEMENT = new Refinement("Nike", 1);

    public static NavigationConfiguration valueNavigation(Integer id,
                                                          String field,
                                                          Integer areaId,
                                                          int priority) {
        return valueNavigation(id, field, areaId, priority, new ArrayList<>());
    }

    public static NavigationConfiguration valueNavigation(Integer id,
                                                          String field,
                                                          Integer areaId,
                                                          int priority,
                                                          List<Refinement> pinnedRefinements) {
        return NavigationConfiguration.builder()
            .id(id)
            .name(field)
            .field(field)
            .areaId(areaId)
            .priority(priority)
            .type(VALUE)
            .ranges(null)
            .sort(null)
            .multiSelect(true)
            .metadata(List.of(METADATA))
            .pinnedRefinements(pinnedRefinements)
            .build();
    }

    public static NavigationConfiguration rangeNavigation(Integer id,
                                                          String field,
                                                          Integer areaId,
                                                          int priority) {
        return NavigationConfiguration.builder()
            .id(id)
            .name(field)
            .field(field)
            .areaId(areaId)
            .priority(priority)
            .type(RANGE)
            .ranges(DEFAULT_NAVIGATION_RANGES)
            .sort(null)
            .multiSelect(true)
            .metadata(List.of(METADATA))
            .pinnedRefinements(new ArrayList<>())
            .build();
    }
    // endregion

    // region BiasingProfiles
    public static Bias bias(String content, String field) {
        return Bias.builder()
            .content(content)
            .field(field)
            .strength(Bias.Strength.STRONG_INCREASE)
            .build();
    }

    public static BiasingProfileConfiguration biasingProfile(Integer id,
                                                             String name,
                                                             Integer areaId,
                                                             boolean areaDefault,
                                                             List<Bias> biases) {
        return BiasingProfileConfiguration.builder()
            .id(id)
            .name(name)
            .areaId(areaId)
            .biases(biases)
            .areaDefault(areaDefault)
            .build();
    }
    // endregion

    // region Attributes
    public static AttributeConfiguration attribute(String key, Integer collectionId) {
        return AttributeConfiguration.builder()
            .key(key)
            .retrievable(true)
            .displayName(key)
            .metadata(List.of(METADATA))
            .collectionId(collectionId)
            .build();
    }
    // endregion

    // region TopSort
    public static TopsortConfiguration topSort(Integer id, Integer areaId) {
        return TopsortConfiguration.builder()
            .id(id)
            .areaId(areaId)
            .enabled(true)
            .apiKey("+ejejEHcrd8ZJ+0IuNs2+o93+RL1/Nas+Xtptsgn+SE0TQWEsguiRFJ5kv1CXUPk")
            .build();
    }
    // endregion

}
