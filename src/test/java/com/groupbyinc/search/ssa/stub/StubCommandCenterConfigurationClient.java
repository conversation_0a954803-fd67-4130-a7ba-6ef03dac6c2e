package com.groupbyinc.search.ssa.stub;

import com.groupbyinc.search.ssa.commandcenter.CommandCenterConfigurationClient;
import com.groupbyinc.search.ssa.core.AllConfigurations;
import com.groupbyinc.search.ssa.core.features.Features;

import java.util.List;

import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_AREA;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_AREA_ID;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_COLLECTION;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_COLLECTION_ID;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_REDIRECT;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_RULE;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_SITE_FILTER;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_TENANT;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_ZONE;
import static com.groupbyinc.search.ssa.stub.TestData.BRANDS;
import static com.groupbyinc.search.ssa.stub.TestData.BRAND_PINNED_REFINEMENT;
import static com.groupbyinc.search.ssa.stub.TestData.COLOR_FAMILIES;
import static com.groupbyinc.search.ssa.stub.TestData.INACTIVE_AREA;
import static com.groupbyinc.search.ssa.stub.TestData.INACTIVE_AREA_ID;
import static com.groupbyinc.search.ssa.stub.TestData.INACTIVE_COLLECTION;
import static com.groupbyinc.search.ssa.stub.TestData.INACTIVE_COLLECTION_ID;
import static com.groupbyinc.search.ssa.stub.TestData.INACTIVE_REDIRECT;
import static com.groupbyinc.search.ssa.stub.TestData.INACTIVE_RULE;
import static com.groupbyinc.search.ssa.stub.TestData.INACTIVE_SITE_FILTER;
import static com.groupbyinc.search.ssa.stub.TestData.INACTIVE_TENANT;
import static com.groupbyinc.search.ssa.stub.TestData.INACTIVE_ZONE;
import static com.groupbyinc.search.ssa.stub.TestData.PRICE;
import static com.groupbyinc.search.ssa.stub.TestData.attribute;
import static com.groupbyinc.search.ssa.stub.TestData.bias;
import static com.groupbyinc.search.ssa.stub.TestData.biasingProfile;
import static com.groupbyinc.search.ssa.stub.TestData.rangeNavigation;
import static com.groupbyinc.search.ssa.stub.TestData.topSort;
import static com.groupbyinc.search.ssa.stub.TestData.valueNavigation;

public class StubCommandCenterConfigurationClient implements CommandCenterConfigurationClient {

    @Override
    public AllConfigurations getAllConfigurations() {
        var tenants = List.of(ACTIVE_TENANT, INACTIVE_TENANT);
        var projectConfigurations = List.of(ACTIVE_COLLECTION, INACTIVE_COLLECTION);
        var siteFilters = List.of(ACTIVE_SITE_FILTER, INACTIVE_SITE_FILTER);
        var areas = List.of(ACTIVE_AREA, INACTIVE_AREA);
        var redirects = List.of(ACTIVE_REDIRECT, INACTIVE_REDIRECT);
        var zones = List.of(ACTIVE_ZONE, INACTIVE_ZONE);
        var rules = List.of(ACTIVE_RULE, INACTIVE_RULE);
        var navigations = List.of(
            valueNavigation(1, BRANDS, ACTIVE_AREA_ID, 1, List.of(BRAND_PINNED_REFINEMENT)),
            valueNavigation(2, COLOR_FAMILIES, ACTIVE_AREA_ID, 2),
            rangeNavigation(3, PRICE, ACTIVE_AREA_ID, 3),

            valueNavigation(4, BRANDS, INACTIVE_AREA_ID, 1),
            valueNavigation(5, COLOR_FAMILIES, INACTIVE_AREA_ID, 2),
            rangeNavigation(6, PRICE, INACTIVE_AREA_ID, 3)
        );
        var biasingProfiles = List.of(
            biasingProfile(1, "White", ACTIVE_AREA_ID, true, List.of(bias("White", COLOR_FAMILIES))),
            biasingProfile(2, "Black", ACTIVE_AREA_ID, false, List.of(bias("Black", COLOR_FAMILIES))),

            biasingProfile(3, "White", INACTIVE_AREA_ID, true, List.of(bias("White", COLOR_FAMILIES))),
            biasingProfile(4, "Black", INACTIVE_AREA_ID, false, List.of(bias("Black", COLOR_FAMILIES)))
        );
        var attributes = List.of(
            attribute(PRICE, ACTIVE_COLLECTION_ID),
            attribute(BRANDS, ACTIVE_COLLECTION_ID),
            attribute(COLOR_FAMILIES, ACTIVE_COLLECTION_ID),

            attribute(PRICE, INACTIVE_COLLECTION_ID),
            attribute(BRANDS,INACTIVE_COLLECTION_ID),
            attribute(COLOR_FAMILIES, INACTIVE_COLLECTION_ID)
        );
        var topSortConfigs = List.of(
            topSort(1, ACTIVE_AREA_ID),
            topSort(2, INACTIVE_AREA_ID)
        );

        return new AllConfigurations(
            rules,
            zones,
            areas,
            tenants,
            redirects,
            attributes,
            navigations,
            biasingProfiles,
            projectConfigurations,
            siteFilters,
            topSortConfigs,
            new Features()
        );
    }

}
