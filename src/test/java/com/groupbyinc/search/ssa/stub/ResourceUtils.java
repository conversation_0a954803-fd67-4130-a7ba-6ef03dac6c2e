package com.groupbyinc.search.ssa.stub;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Map;

import static java.util.Objects.requireNonNull;

/**
 * Utility class containing common methods around bundled resources in support of testing.
 */
public class ResourceUtils {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    /**
     * Reads the contents of a bundled resource and returns it as a UTF-8 string.
     *
     * @param resourceName The name of the bundled resource.
     *
     * @return The contents of the resource as a UTF-8 string, or null if the resource is not found.
     * @throws NullPointerException If the resource is not found.
     * @throws RuntimeException     If the resource cannot be read.
     */
    public static String getResourceContents(String resourceName) {

        var resourceUrl = requireNonNull(
            ResourceUtils.class.getClassLoader().getResource(resourceName)
        );

        try {
            var path = Path.of(resourceUrl.toURI());
            return Files.readString(path, StandardCharsets.UTF_8);
        } catch (URISyntaxException | IOException e) {
            throw new RuntimeException("Failed to read resource '" + resourceName + "'.", e);
        }
    }

    /**
     * Reads the contents of a bundled resource and returns it as a Map.
     *
     * @param resourceName The name of the bundled resource.
     *
     * @return The contents of the resource as a Map, or throws exception if the resource is not found or not parsable
     *
     * @throws IllegalArgumentException If the resource is not found.
     * @throws RuntimeException         If the mapping is failed.
     */
    @SneakyThrows
    public static Map<String, Object> getResourceAsMap(String resourceName) {
        var classLoader = ResourceUtils.class.getClassLoader();
        var resource = classLoader.getResource(resourceName);
        if (resource == null) {
            var errorMessage = "File `%s` is not found!".formatted(resourceName);
            throw new IllegalArgumentException(errorMessage);
        }
        return MAPPER.readValue(new File(resource.getFile()), new TypeReference<>() {});
    }

}
