package com.groupbyinc.search.ssa.stub;

import com.groupbyinc.search.ssa.metrics.opentelemetry.MeterRegistry;

import io.opentelemetry.context.ContextKey;

import java.util.HashMap;
import java.util.function.Supplier;

public class StubMeterRegistry extends MeterRegistry {

    public StubMeterRegistry() {
        super(new HashMap<>(), new HashMap<>(), null);
    }

    @Override
    public <T> T act(Supplier<T> f) {
        return f.get();
    }

    @Override
    public <T> T timer(ContextKey<Double> contextKey, Supplier<T> f) {
        return super.timer(contextKey, f);
    }

}
