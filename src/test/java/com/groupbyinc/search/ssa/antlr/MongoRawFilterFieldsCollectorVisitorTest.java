package com.groupbyinc.search.ssa.antlr;

import com.groupbyinc.search.ssa.stub.ResourceUtils;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.FIELDS_FOR_FILTERS;
import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.defaultAttributeConfigs;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.parseRawFilter;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("MongoRawFilterFieldsCollectorVisitor Tests")
class MongoRawFilterFieldsCollectorVisitorTest {

    private final MongoRawFilterFieldsCollectorVisitor visitor = new MongoRawFilterFieldsCollectorVisitor(
        defaultAttributeConfigs()
    );

    @Test
    @DisplayName("Test parsing of text expression. Collecting used attributes")
    void shouldCollectAllAttributesUsedInFilter() {
        var raw = ResourceUtils.getResourceContents("antlr/raw-filter");

        var parsed = parseRawFilter(raw, visitor);

        assertThat(parsed.size()).isEqualTo(FIELDS_FOR_FILTERS.size());
        for (var field : FIELDS_FOR_FILTERS) {
            assertThat(parsed.contains(field)).isTrue();
        }
    }

}

