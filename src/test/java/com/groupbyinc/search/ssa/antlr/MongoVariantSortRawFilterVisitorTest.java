package com.groupbyinc.search.ssa.antlr;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.defaultAttributeConfigs;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.parseRawFilter;
import static com.groupbyinc.search.ssa.stub.ResourceUtils.getResourceContents;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("MongoVariantSortRawFilterVisitor Tests")
public class MongoVariantSortRawFilterVisitorTest {


    private final MongoVariantSortRawFilterVisitor visitor = new MongoVariantSortRawFilterVisitor(
        defaultAttributeConfigs()
    );

    @Test
    @DisplayName("Test creating of variant sort filter from raw filters")
    void shouldCorrectlyCreateVariantSortFilterFromRawFilter() {
        var raw = getResourceContents("antlr/raw-filter");

        var parsed = parseRawFilter(raw, visitor);

        var expected = Document.parse(
            getResourceContents("antlr/mongo-variant-sort-raw-filter-visitor-test-result.json")
        );

        assertThat(parsed.toJson()).isEqualTo(expected.toJson());
    }

}
