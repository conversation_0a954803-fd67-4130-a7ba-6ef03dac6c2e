package com.groupbyinc.search.ssa.antlr;

import com.groupbyinc.search.ssa.stub.ResourceUtils;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.defaultAttributeConfigs;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.parseRawFilter;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("MongoSearchRawFilterVisitor Tests")
public class MongoSearchRawFilterVisitorTest {

    private final MongoSearchRawFilterVisitor visitor = new MongoSearchRawFilterVisitor(
        defaultAttributeConfigs()
    );

    @Test
    @DisplayName("Test parsing of raw filter")
    void shouldCorrectlyParseRawFilter() {
        var raw = ResourceUtils.getResourceContents("antlr/raw-filter");

        var parsed = parseRawFilter(raw, visitor);

        var expected = Document.parse(
            ResourceUtils.getResourceContents("antlr/mongo-search-raw-filter-visitor-test-result.json")
        );

        assertThat(parsed.toJson()).isEqualTo(expected.toJson());
    }

}
