package com.groupbyinc.search.ssa.partnumber;

import com.groupbyinc.search.ssa.api.error.ProcessingException;
import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngine;
import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
import com.groupbyinc.search.ssa.core.Pagination;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.RecordLabel;
import com.groupbyinc.search.ssa.core.SearchMode;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.proto.commandcenter.config.AttributeMessage;
import com.groupbyinc.search.ssa.core.navigation.Navigation;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.navigation.NavigationRefinement;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;

import io.micronaut.core.propagation.PropagatedContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvFileSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.partnumber.PartNumberSearchService.PRIMARY_RESULTS_MAX_LIMIT;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PartNumberSearchServiceTest {
    private static final String PRIMARY_SEARCH_FAILED = "Primary search failed";
    private static final String MONGO_PREFIX = "mongo";
    private static final String GOOGLE_PREFIX = "google";
    private static final String PRICE = "price";

    private final SearchResults fallbackResults = SearchResults.builder().build();

    private final SearchParameters searchParameters = SearchParameters.builder()
        .partNumberSearchableAttributes(List.of(AttributeConfiguration.builder().build()))
        .partNumberExpansionEnabled(false) // Important: disable expansion for non-expansion fallback tests
        .pagination(new Pagination(30, 0L))
        .build();

    @Mock
    private SearchEngine primarySearchEngine;

    @Mock
    private SearchEngine relevancySearchEngine;

    @Captor
    private ArgumentCaptor<SearchParameters> searchParamsCaptor;

    private PartNumberSearchService partNumberSearchService;
    private PropagatedContext.Scope scope;

    @BeforeEach
    void setUp() {
        var bucketAligner = new NumericFacetBucketAligner();
        partNumberSearchService = new PartNumberSearchService(primarySearchEngine, relevancySearchEngine, bucketAligner);

        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    // region Fallback Search Tests

    @Test
    void testSearch_whenPrimarySearchSuccess_thenNoFallback() {
        // given
        var primaryResults = SearchResults.builder()
            .records(List.of(getRecord("id")))
            .numTotalRecords(1)
            .build();

        when(primarySearchEngine.search(searchParameters)).thenReturn(primaryResults);

        // when
        var actualResults = partNumberSearchService.search(searchParameters);

        // then
        verify(primarySearchEngine).search(searchParameters);
        verify(relevancySearchEngine, never()).search(any());
        assertThat(actualResults).isEqualTo(primaryResults);
        assertFalse(actualResults.isFallbackSearchEngineUsed());
    }

    @Test
    void testSearch_whenPrimarySearchFailure_andFallbackDisabled_thenNoFallback() {
        // given
        var partNumberSearchParams = searchParameters.toBuilder()
            .partNumberFallbackEnabled(false)
            .build();

        when(primarySearchEngine.search(partNumberSearchParams)).thenThrow(new RuntimeException(PRIMARY_SEARCH_FAILED));

        // when then
        assertThatThrownBy(() -> partNumberSearchService.search(partNumberSearchParams))
            .isInstanceOf(ProcessingException.class)
            .hasMessageContaining(PRIMARY_SEARCH_FAILED);

        verify(primarySearchEngine).search(partNumberSearchParams);
        verify(relevancySearchEngine, never()).search(any());
    }

    @Test
    void testSearch_whenPrimarySearchFailure_thenFallbackSuccess() {
        // given
        when(primarySearchEngine.search(any(SearchParameters.class))).thenThrow(new RuntimeException(PRIMARY_SEARCH_FAILED));
        when(relevancySearchEngine.search(any(SearchParameters.class))).thenReturn(fallbackResults);

        // when
        var actualResults = partNumberSearchService.search(searchParameters);

        // then
        verify(primarySearchEngine).search(searchParameters);
        verify(relevancySearchEngine).search(searchParameters);
        assertTrue(actualResults.isFallbackSearchEngineUsed());

        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.contains(PRIMARY_SEARCH_FAILED))
        );
    }

    @Test
    void testSearch_whenPrimarySearchFailure_andFallbackEnabled_thenFallbackUsed() {
        // given
        var partNumberSearchParams = searchParameters.toBuilder()
            .partNumberFallbackEnabled(true)
            .build();

        when(primarySearchEngine.search(partNumberSearchParams)).thenThrow(new RuntimeException(PRIMARY_SEARCH_FAILED));
        when(relevancySearchEngine.search(partNumberSearchParams)).thenReturn(fallbackResults);

        // when
        var actualResults = partNumberSearchService.search(partNumberSearchParams);

        // then
        verify(primarySearchEngine).search(partNumberSearchParams);
        verify(relevancySearchEngine).search(partNumberSearchParams);
        assertThat(actualResults.isFallbackSearchEngineUsed()).isTrue();

        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.contains(PRIMARY_SEARCH_FAILED))
        );
    }

    @Test
    void testSearch_whenPrimarySearchNoRecords_thenFallbackSuccess() {
        // given
        var emptyResults = SearchResults.builder().build();
        when(primarySearchEngine.search(searchParameters)).thenReturn(emptyResults);
        when(relevancySearchEngine.search(searchParameters)).thenReturn(fallbackResults);

        // when
        var actualResults = partNumberSearchService.search(searchParameters);

        // then
        verify(primarySearchEngine).search(searchParameters);
        verify(relevancySearchEngine).search(searchParameters);
        assertTrue(actualResults.isFallbackSearchEngineUsed());
    }

    @Test
    void testSearch_whenPrimarySearchNoRecords_andFallbackEnabled_thenFallbackUsed() {
        // given
        var emptyResults = SearchResults.builder().build();
        var partNumberSearchParams = searchParameters.toBuilder()
            .partNumberFallbackEnabled(true)
            .build();

        when(primarySearchEngine.search(partNumberSearchParams)).thenReturn(emptyResults);
        when(relevancySearchEngine.search(partNumberSearchParams)).thenReturn(fallbackResults);

        // when
        var actualResults = partNumberSearchService.search(partNumberSearchParams);

        // then
        verify(primarySearchEngine).search(partNumberSearchParams);
        verify(relevancySearchEngine).search(partNumberSearchParams);
        assertThat(actualResults).isEqualTo(fallbackResults);
        assertThat(actualResults.isFallbackSearchEngineUsed()).isTrue();
    }

    @Test
    void testSearch_whenPrimarySearchNoRecords_andFallbackDisabled_thenEmptyResponse() {
        // given
        var partNumberSearchParams = SearchParameters.builder()
            .partNumberSearchableAttributes(List.of(AttributeConfiguration.builder().build()))
            .partNumberFallbackEnabled(false)
            .partNumberExpansionEnabled(false)
            .pagination(new Pagination(30, 0L))
            .build();

        var emptyResults = SearchResults.builder().build();

        when(primarySearchEngine.search(partNumberSearchParams)).thenReturn(emptyResults);

        // when
        var actualResults = partNumberSearchService.search(partNumberSearchParams);

        // then
        verify(primarySearchEngine).search(partNumberSearchParams);
        verify(relevancySearchEngine, never()).search(any());
        assertThat(actualResults).isEqualTo(emptyResults);
        assertFalse(actualResults.isFallbackSearchEngineUsed());
    }

    // endregion

    // region Search Expansion Tests

    @Test
    void testSearch_whenPartNumberExpansionEnabled_thenMergedResults() {
        // given
        var mongoNavigation = createNavigationWithName("mongoNav");
        var googleNavigation = createNavigationWithName("googleNav");

        var pageSize = 30;
        var skip = 0L;
        var primaryRecordsCount = 14;
        var relevancyRecordsCount = 16;

        var searchParams = SearchParameters.builder()
            .partNumberExpansionEnabled(true)
            .pagination(new Pagination(pageSize, skip))
            .build();

        var mongoRecords = createTestRecords(primaryRecordsCount, MONGO_PREFIX);
        var mongoResults = SearchResults.builder()
            .records(mongoRecords)
            .numTotalRecords(primaryRecordsCount)
            .navigations(List.of(mongoNavigation))
            .build();

        var googleRecords = createTestRecords(relevancyRecordsCount, GOOGLE_PREFIX);
        var googleResults = SearchResults.builder()
            .records(googleRecords)
            .numTotalRecords(relevancyRecordsCount)
            .navigations(List.of(googleNavigation))
            .build();

        var primarySearchParamsCaptor = ArgumentCaptor.forClass(SearchParameters.class);

        when(primarySearchEngine.search(any(SearchParameters.class))).thenReturn(mongoResults);
        when(relevancySearchEngine.search(any(SearchParameters.class))).thenReturn(googleResults);

        // when
        var actualResults = partNumberSearchService.search(searchParams);

        // then
        verify(primarySearchEngine).search(primarySearchParamsCaptor.capture());
        verify(relevancySearchEngine).search(searchParamsCaptor.capture());

        // Verify that Mongo search parameters are correctly adjusted
        assertThat(primarySearchParamsCaptor.getValue().getPagination().getSize()).isEqualTo(PRIMARY_RESULTS_MAX_LIMIT);

        // Verify that Google search parameters are correctly adjusted
        var googleSearchParams = searchParamsCaptor.getValue();
        assertThat(googleSearchParams.getPagination().getSize()).isEqualTo(relevancyRecordsCount);
        assertThat(googleSearchParams.getPagination().getOffset()).isEqualTo(0);

        // Verify product IDs from Mongo are excluded in Google search
        var excludedIds = googleSearchParams.getProductIdFilter().excludedProductIds();
        var mongoProductIds = mongoRecords.stream()
            .map(Record::getProductId)
            .collect(Collectors.toSet());
        assertThat(excludedIds.containsAll(mongoProductIds)).isTrue();

        // Verify merged results have the expected total count
        assertThat(actualResults.getNumTotalRecords()).isEqualTo(pageSize);
        assertThat(actualResults.getRecords().size()).isEqualTo(pageSize);

        assertThat(
            actualResults.getNavigations()
                .stream()
                .map(Navigation::getName)
                .toList())
            .containsExactlyInAnyOrder(mongoNavigation.getName(), googleNavigation.getName());

        assertThat(actualResults.getRecords().stream()
            .limit(primaryRecordsCount)
            .allMatch(r -> r.getLabels() != null && r.getLabels().contains(RecordLabel.PART_NUMBER))).isTrue();

        for (int i = 0; i < primaryRecordsCount; i++) {
            assertThat(actualResults.getRecords().get(i).getProductId()).startsWith(MONGO_PREFIX);
        }
        for (int i = 0; i < relevancyRecordsCount; i++) {
            assertThat(actualResults.getRecords().get(i + primaryRecordsCount).getProductId()).startsWith(GOOGLE_PREFIX);
        }
    }

    @Test
    void testSearch_whenRequestMoreThan100Results_thenResultsFromMongoAreLimited() {
        // given
        var pageSize = 120;
        var skip = 0L;

        var searchParams = SearchParameters.builder()
            .partNumberExpansionEnabled(true)
            .pagination(new Pagination(pageSize, skip))
            .build();

        var mongoRecords = createTestRecords(PRIMARY_RESULTS_MAX_LIMIT, MONGO_PREFIX);
        var mongoResults = SearchResults.builder()
            .records(mongoRecords)
            .numTotalRecords(PRIMARY_RESULTS_MAX_LIMIT)
            .build();

        var googleCount = 20;
        var googleTotalCount = 1000;
        var googleRecords = createTestRecords(googleCount, GOOGLE_PREFIX);
        var googleResults = SearchResults.builder()
            .records(googleRecords)
            .numTotalRecords(googleTotalCount)
            .build();

        var primarySearchParamsCaptor = ArgumentCaptor.forClass(SearchParameters.class);

        when(primarySearchEngine.search(any(SearchParameters.class))).thenReturn(mongoResults);
        when(relevancySearchEngine.search(any(SearchParameters.class))).thenReturn(googleResults);

        // when
        var actualResults = partNumberSearchService.search(searchParams);

        // then
        verify(primarySearchEngine).search(primarySearchParamsCaptor.capture());
        verify(relevancySearchEngine).search(searchParamsCaptor.capture());

        // Verify that Mongo search parameters are correctly adjusted
        assertThat(primarySearchParamsCaptor.getValue().getPagination().getSize()).isEqualTo(PRIMARY_RESULTS_MAX_LIMIT);

        // Verify that Google search parameters are correctly adjusted
        var googleSearchParams = searchParamsCaptor.getValue();
        assertThat(googleSearchParams.getPagination().getSize()).isEqualTo(googleCount);
        assertThat(googleSearchParams.getPagination().getOffset()).isEqualTo(0);

        assertThat(actualResults.getRecords().size()).isEqualTo(pageSize);
        assertThat(actualResults.getNumTotalRecords()).isEqualTo(PRIMARY_RESULTS_MAX_LIMIT + googleTotalCount);
    }

    @Test
    void testSearch_whenPartNumberExpansionDisabled_thenNoMergedResults() {
        // given
        var mongoRecords = List.of(getRecord("id"));
        var mongoResults = SearchResults.builder()
            .records(mongoRecords)
            .numTotalRecords(1)
            .build();

        when(primarySearchEngine.search(any(SearchParameters.class))).thenReturn(mongoResults);

        // when
        var actualResults = partNumberSearchService.search(searchParameters);

        // then
        verify(primarySearchEngine).search(searchParameters);
        verify(relevancySearchEngine, never()).search(any());
        assertThat(actualResults).isEqualTo(mongoResults);
    }

    @Test
    void testSearch_withFacetOnlyGoogleCall_thenCorrectFacetsSelected() {
        // given
        var mongoNavigation = createNavigationWithName("mongoNav");
        var googleNavigation = createNavigationWithName("googleNav");

        var searchParams = SearchParameters.builder()
            .partNumberExpansionEnabled(true)
            .pagination(new Pagination(30, 0L))
            .build();

        var mongoRecords = createTestRecords(31, MONGO_PREFIX);
        var mongoResults = SearchResults.builder()
            .records(mongoRecords)
            .numTotalRecords(31)
            .navigations(List.of(mongoNavigation))
            .build();

        var googleResults = SearchResults.builder()
            .numTotalRecords(10)
            .navigations(List.of(googleNavigation))
            .build();

        when(primarySearchEngine.search(any(SearchParameters.class))).thenReturn(mongoResults);
        when(relevancySearchEngine.search(any(SearchParameters.class))).thenReturn(googleResults);

        // when
        var actualResults = partNumberSearchService.search(searchParams);

        // then
        verify(relevancySearchEngine).search(searchParamsCaptor.capture());

        // Verify that Google search parameters are correctly adjusted
        var googleSearchParams = searchParamsCaptor.getValue();
        assertEquals(0, googleSearchParams.getPagination().getSize());
        assertEquals(SearchMode.FACETED_SEARCH, googleSearchParams.getSearchMode());

        assertThat(actualResults.getNavigations()).containsExactly(googleNavigation, mongoNavigation);
    }

    @Test
    void testSearch_withEmptyGoogleFacets_thenFallbackToMongoFacets() {
        // given
        var mongoNavigation = createNavigationWithName("mongoNav");

        var searchParams = SearchParameters.builder()
            .partNumberExpansionEnabled(true)
            .pagination(new Pagination(30, 0L))
            .build();

        var mongoRecords = createTestRecords(31, MONGO_PREFIX);
        var mongoResults = SearchResults.builder()
            .records(mongoRecords)
            .numTotalRecords(31)
            .navigations(List.of(mongoNavigation))
            .build();

        var googleResults = SearchResults.builder()
            .numTotalRecords(0)
            .build();

        when(primarySearchEngine.search(any(SearchParameters.class))).thenReturn(mongoResults);
        when(relevancySearchEngine.search(any(SearchParameters.class))).thenReturn(googleResults);

        // when
        var actualResults = partNumberSearchService.search(searchParams);

        // then
        assertThat(actualResults.getNavigations()).containsExactly(mongoNavigation);
    }

    @Test
    void testSearch_withDeepSkip_thenCorrectPagination() {
        // given
        var primaryTotalHits = 14;
        var reqPageSize = 30;
        var reqSkip = 30L;

        var searchParams = SearchParameters.builder()
            .partNumberExpansionEnabled(true)
            .pagination(new Pagination(reqPageSize, reqSkip))
            .build();

        var mongoRecords = createTestRecords(primaryTotalHits, MONGO_PREFIX);
        var mongoResults = SearchResults.builder()
            .records(mongoRecords)
            .numTotalRecords(primaryTotalHits)
            .build();

        var googleRecordsCount = 30;
        var googleRecords = createTestRecords(googleRecordsCount, GOOGLE_PREFIX);
        var googleResults = SearchResults.builder()
            .records(googleRecords)
            .numTotalRecords(googleRecordsCount)
            .build();

        when(primarySearchEngine.search(any(SearchParameters.class))).thenReturn(mongoResults);
        when(relevancySearchEngine.search(any(SearchParameters.class))).thenReturn(googleResults);

        // when
        var actualResults = partNumberSearchService.search(searchParams);

        // then
        verify(relevancySearchEngine).search(searchParamsCaptor.capture());

        var googleSearchParams = searchParamsCaptor.getValue();
        assertThat(googleSearchParams.getPagination().getSize()).isEqualTo(reqPageSize);
        assertThat(googleSearchParams.getPagination().getOffset()).isEqualTo(reqSkip - primaryTotalHits);

        assertThat(actualResults.getRecords().size()).isEqualTo(reqPageSize);
        assertThat(actualResults.getNumTotalRecords()).isEqualTo(primaryTotalHits + googleRecordsCount);

        assertThat(actualResults.getRecords().stream()
            .filter(r -> r.getProductId().startsWith(GOOGLE_PREFIX))
            .count()).isEqualTo(googleRecordsCount);
    }

    @Test
    void testSearch_whenSkipMoreThanLimit_thenResultsFromGoogle() {
        // given
        var pageSize = 20;
        var skip = 110L;

        var searchParams = SearchParameters.builder()
            .partNumberExpansionEnabled(true)
            .pagination(new Pagination(pageSize, skip))
            .build();

        var mongoRecords = createTestRecords(pageSize, MONGO_PREFIX);
        var mongoResults = SearchResults.builder()
            .records(mongoRecords)
            .numTotalRecords(1000) // should be cut off to 100
            .build();

        var googleTotalCount = 200;
        var googleRecords = createTestRecords(pageSize, GOOGLE_PREFIX);
        var googleResults = SearchResults.builder()
            .records(googleRecords)
            .numTotalRecords(googleTotalCount)
            .build();

        when(primarySearchEngine.search(any(SearchParameters.class))).thenReturn(mongoResults);
        when(relevancySearchEngine.search(any(SearchParameters.class))).thenReturn(googleResults);

        // when
        var actualResults = partNumberSearchService.search(searchParams);

        // then
        verify(relevancySearchEngine).search(searchParamsCaptor.capture());

        // Verify that Google search parameters are correctly adjusted
        var googleSearchParams = searchParamsCaptor.getValue();
        assertThat(googleSearchParams.getPagination().getSize()).isEqualTo(pageSize);
        assertThat(googleSearchParams.getPagination().getOffset()).isEqualTo(10);

        assertThat(actualResults.getRecords().size()).isEqualTo(pageSize);

        assertThat(actualResults.getRecords().stream()
            .allMatch(r -> r.getProductId().startsWith(GOOGLE_PREFIX))).isTrue();

        assertThat(actualResults.getNumTotalRecords()).isEqualTo(PRIMARY_RESULTS_MAX_LIMIT + googleTotalCount);
    }

    @Test
    void testSearch_whenExpandedResultsHaveDuplicates_thenPrimaryResultsHavePriority() {
        // given
        var searchParams = SearchParameters.builder()
            .pagination(new Pagination(10, 0L))
            .build();

        var primaryRecords = List.of(
            getRecord("prod1"),
            getRecord("prod2")
        );
        var primaryResults = SearchResults.builder()
            .records(primaryRecords)
            .numTotalRecords(primaryRecords.size())
            .build();

        var expandedRecords = List.of(
            getRecord("prod2"), // Duplicate
            getRecord("prod3"),
            getRecord("prod4")
        );
        var expandedResults = SearchResults.builder()
            .records(expandedRecords)
            .numTotalRecords(expandedRecords.size())
            .build();

        when(primarySearchEngine.search(any(SearchParameters.class))).thenReturn(primaryResults);
        when(relevancySearchEngine.search(any(SearchParameters.class))).thenReturn(expandedResults);

        // when
        SearchResults actualResults = partNumberSearchService.search(searchParams);

        // then
        verify(relevancySearchEngine).search(searchParamsCaptor.capture());

        var actualProductIds = actualResults.getRecords().stream()
            .map(Record::getProductId)
            .collect(Collectors.toList());

        assertThat(actualProductIds)
            .hasSize(4)
            .containsExactly("prod1", "prod2", "prod3", "prod4");

        var expandedSearchParams = searchParamsCaptor.getValue();
        assertThat(expandedSearchParams.getProductIdFilter()).isNotNull();
        assertThat(expandedSearchParams.getProductIdFilter().excludedProductIds())
            .containsExactlyInAnyOrder("prod1", "prod2");
    }

    @ParameterizedTest(name = "Case #{0}: reqPageSize={1}, reqSkip={2}, partTotalHits={3}, relevancyPageSize={4}, relevancySkip={5}")
    @CsvFileSource(resources = "/part-number/expanded-search-pagination.csv", numLinesToSkip = 1)
    @DisplayName("Test SearchParameters adjustment based on test cases")
    void testSearchParametersAdjustment(
        int caseNumber,
        int reqPageSize,
        long reqSkip,
        int partTotalHits,
        int relevancyPageSize,
        long relevancySkip
    ) {
        // given
        var searchParams = SearchParameters.builder()
            .partNumberExpansionEnabled(true)
            .pagination(new Pagination(reqPageSize, reqSkip))
            .build();

        List<Record> mockMongoRecords = createTestRecords(partTotalHits, MONGO_PREFIX);

        var mongoResults = SearchResults.builder()
            .records(mockMongoRecords)
            .numTotalRecords(partTotalHits)
            .build();
        var googleResults = SearchResults.builder().build();

        when(primarySearchEngine.search(any(SearchParameters.class))).thenReturn(mongoResults);
        when(relevancySearchEngine.search(any(SearchParameters.class))).thenReturn(googleResults);

        // when
        partNumberSearchService.search(searchParams);

        // Verify the relevancy search engine was called with the correct parameters
        verify(relevancySearchEngine).search(searchParamsCaptor.capture());
        var googleSearchParams = searchParamsCaptor.getValue();

        // Verify the pagination parameters match expected values from the test case
        assertThat(googleSearchParams.getPagination().getSize())
            .as("Case #" + caseNumber + ": pageSize should be " + relevancyPageSize)
            .isEqualTo(relevancyPageSize);
        assertThat(googleSearchParams.getPagination().getOffset())
            .as("Case #" + caseNumber + ": skip should be " + relevancySkip)
            .isEqualTo(relevancySkip);

        if (relevancyPageSize == 0) {
            assertThat(googleSearchParams.getSearchMode())
                .as("Case #" + caseNumber + ": searchMode should be FACETED_SEARCH when pageSize is 0")
                .isEqualTo(SearchMode.FACETED_SEARCH);
        }

        // Verify primary product IDs are excluded in the expanded search query
        if (partTotalHits > 0) {
            var excludedIds = googleSearchParams.getProductIdFilter().excludedProductIds();
            var mongoProductIds = mockMongoRecords.stream()
                .map(Record::getProductId)
                .collect(Collectors.toSet());
            assertThat(excludedIds).containsAll(mongoProductIds);
        }
    }

    // endregion

    // region Facet Merging Tests
    @Test
    @DisplayName("Test merging facets with common navigations and refinements - counts should be summed")
    void testMergeSearchResults_withCommonNavigationsAndRefinements_shouldSumCounts() {
        // given
        var commonNavName = "brands";
        var commonRefValue = "nike";
        var mongoRefValue = "adidas";
        var googleRefValue = "puma";

        // Create primary (Mongo) navigation with refinements
        var primaryRefinements = List.of(
            NavigationRefinement.valueRefinement(commonRefValue, 5, false),
            NavigationRefinement.valueRefinement(mongoRefValue, 3, false)
        );
        var primaryNav = Navigation.builder()
            .name(commonNavName)
            .field("field_" + commonNavName)
            .type(NavigationType.VALUE)
            .refinements(primaryRefinements)
            .build();

        // Create expanded (Google) navigation with some common refinements
        var expandedRefinements = List.of(
            NavigationRefinement.valueRefinement(commonRefValue, 8, false),
            NavigationRefinement.valueRefinement(googleRefValue, 4, false)
        );
        var expandedNav = Navigation.builder()
            .name(commonNavName)
            .field("field_" + commonNavName)
            .type(NavigationType.VALUE)
            .refinements(expandedRefinements)
            .build();

        // Create search results
        var primaryResults = SearchResults.builder()
            .records(List.of(getRecord("mongo1")))
            .numTotalRecords(1)
            .navigations(List.of(primaryNav))
            .build();
        var expandedResults = SearchResults.builder()
            .records(List.of(getRecord("google1")))
            .numTotalRecords(1)
            .navigations(List.of(expandedNav))
            .build();

        var searchParams = SearchParameters.builder().partNumberExpansionEnabled(true).build();
        when(primarySearchEngine.search(any())).thenReturn(primaryResults);
        when(relevancySearchEngine.search(any())).thenReturn(expandedResults);

        // when
        var mergedResults = partNumberSearchService.search(searchParams);

        // then
        // Verify the merged navigation contains all refinements
        var mergedNavs = mergedResults.getNavigations();
        assertThat(mergedNavs).hasSize(1);

        var mergedNav = mergedNavs.getFirst();
        assertThat(mergedNav.getName()).isEqualTo(commonNavName);

        var mergedRefinements = mergedNav.getRefinements();
        assertThat(mergedRefinements).hasSize(3); // nike, puma, adidas

        // Find the common refinement and verify its count is summed
        var commonRefinement = mergedRefinements.stream()
            .filter(r -> commonRefValue.equals(r.getValue()))
            .findFirst()
            .orElseThrow();
        assertThat(commonRefinement.getCount()).isEqualTo(13);

        // Verify the order of refinements (alphabetical order: adidas, nike, puma)
        assertThat(mergedRefinements.get(0).getValue()).isEqualTo(mongoRefValue); // adidas
        assertThat(mergedRefinements.get(1).getValue()).isEqualTo(commonRefValue); // nike
        assertThat(mergedRefinements.get(2).getValue()).isEqualTo(googleRefValue); // puma
    }

    @Test
    @DisplayName("Test navigation order in merged results - should be sorted by priority")
    void testMergeSearchResults_navigationOrder() {
        // given
        var googleNav1 = createNavigationWithName("googleNav1");
        var googleNav2 = createNavigationWithName("googleNav2");
        // from Rule
        var googleNav2RuleConfig = createNavigationConfigurationWithPriority(googleNav2, 1);

        var mongoNav1 = createNavigationWithName("mongoNav1");
        var mongoNav2 = createNavigationWithName("mongoNav2");

        var primaryResults = SearchResults.builder()
            .records(List.of(getRecord("mongo1")))
            .numTotalRecords(1)
            .navigations(List.of(mongoNav1, mongoNav2))
            .build();
        var expandedResults = SearchResults.builder()
            .records(List.of(getRecord("google1")))
            .numTotalRecords(1)
            .navigations(List.of(googleNav1, googleNav2))
            .build();

        var merchandiserConfig = MerchandisingConfiguration.builder()
            .navigationConfigurations(Set.of(googleNav2RuleConfig))
            .build();

        var searchParams = SearchParameters.builder()
            .partNumberExpansionEnabled(true)
            .merchandisingConfiguration(merchandiserConfig)
            // pinned in a Rule
            .includedNavigations(List.of(mongoNav2.getField(), mongoNav1.getField()))
            .build();

        when(primarySearchEngine.search(any())).thenReturn(primaryResults);
        when(relevancySearchEngine.search(any())).thenReturn(expandedResults);

        // when
        var mergedResults = partNumberSearchService.search(searchParams);

        // then
        var mergedNavs = mergedResults.getNavigations();
        assertThat(mergedNavs).hasSize(4);

        // Verify navigations are ordered by priority
        assertThat(mergedNavs.get(0).getName()).isEqualTo("mongoNav2"); // Rule priority #1
        assertThat(mergedNavs.get(1).getName()).isEqualTo("mongoNav1"); // Rule priority #2
        assertThat(mergedNavs.get(2).getName()).isEqualTo("googleNav2"); // Navigation conf priority #1
        assertThat(mergedNavs.get(3).getName()).isEqualTo("googleNav1");
    }

    @Test
    @DisplayName("Test navigations and refinements order in merged results")
    void testMergeSearchResults_withUniqueNavigations() {
        // given
        var googleOnlyNav = createNavigationWithName("googleOnly");
        var mongoOnlyNav = createNavigationWithName("mongoOnly");
        var commonNavName = "common";

        // Create common navigation with different refinements in each result
        var mongoCommonNav = Navigation.builder()
            .name(commonNavName)
            .field("field_" + commonNavName)
            .type(NavigationType.VALUE)
            .refinements(List.of(
                NavigationRefinement.valueRefinement("mongo_ref", 5, false)
            ))
            .build();

        var googleCommonNav = Navigation.builder()
            .name(commonNavName)
            .field("field_" + commonNavName)
            .type(NavigationType.VALUE)
            .refinements(List.of(
                NavigationRefinement.valueRefinement("google_ref", 8, false)
            ))
            .build();

        // Create search results
        var primaryResults = SearchResults.builder()
            .records(List.of(getRecord("mongo1")))
            .numTotalRecords(1)
            .navigations(List.of(mongoOnlyNav, mongoCommonNav))
            .build();

        var expandedResults = SearchResults.builder()
            .records(List.of(getRecord("google1")))
            .numTotalRecords(1)
            .navigations(List.of(googleOnlyNav, googleCommonNav))
            .build();

        // Mock the search engines
        var searchParams = SearchParameters.builder().partNumberExpansionEnabled(true).build();
        when(primarySearchEngine.search(any())).thenReturn(primaryResults);
        when(relevancySearchEngine.search(any())).thenReturn(expandedResults);

        // when
        var mergedResults = partNumberSearchService.search(searchParams);

        // then
        var mergedNavs = mergedResults.getNavigations();
        assertThat(mergedNavs).hasSize(3);

        // Verify all navigations are present
        var navNames = mergedNavs.stream().map(Navigation::getName).collect(Collectors.toList());
        assertThat(navNames).containsExactly("googleOnly", "common", "mongoOnly");

        // Verify the common navigation has refinements from both sources
        var mergedCommonNav = mergedNavs.stream()
            .filter(nav -> commonNavName.equals(nav.getName()))
            .findFirst()
            .orElseThrow();

        var refinementValues = mergedCommonNav.getRefinements().stream()
            .map(NavigationRefinement::getValue)
            .collect(Collectors.toList());

        assertThat(refinementValues).containsExactly("google_ref", "mongo_ref");
    }

    @Test
    @DisplayName("Test merging with range navigation type")
    void testMergeSearchResults_withRangeNavigationType() {
        // given
        var navName = "price";

        var mongoRange = new Range(0.0, 50.0);
        var mongoRefinements = List.of(
            NavigationRefinement.rangeRefinement(mongoRange, 1)
        );
        var mongoNav = Navigation.builder()
            .name(navName)
            .field(navName)
            .type(NavigationType.RANGE)
            .refinements(mongoRefinements)
            .build();

        var googleRange = new Range(0.0, 50.0);
        var googleRefinements = List.of(
            NavigationRefinement.rangeRefinement(googleRange, 8)
        );
        var googleNav = Navigation.builder()
            .name(navName)
            .field(navName)
            .type(NavigationType.RANGE)
            .refinements(googleRefinements)
            .build();

        // Create search results with records that have realistic MongoDB nested structure
        var mongoRecord = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            "mongo1",
            "mongo1",
            "Mongo Product",
            createMongoMetadataWithPrice(25.0) // Realistic nested structure
        );
        var primaryResults = SearchResults.builder()
            .records(List.of(mongoRecord))
            .numTotalRecords(1)
            .navigations(List.of(mongoNav))
            .build();

        var expandedResults = SearchResults.builder()
            .records(List.of(getRecord("google1")))
            .numTotalRecords(8)
            .navigations(List.of(googleNav))
            .build();

        // Create attribute configuration for price field
        var priceAttributeConfig = AttributeConfiguration.builder()
            .key("price")
            .path("priceInfo.price")
            .build();

        // Create merchandising configuration with attribute configurations
        var merchandisingConfig = MerchandisingConfiguration.builder()
            .attributeConfigurations(Map.of("price", priceAttributeConfig))
            .build();

        var searchParams = SearchParameters.builder()
            .partNumberExpansionEnabled(true)
            .merchandisingConfiguration(merchandisingConfig)
            .build();
        when(primarySearchEngine.search(any())).thenReturn(primaryResults);
        when(relevancySearchEngine.search(any())).thenReturn(expandedResults);

        // when
        var mergedResults = partNumberSearchService.search(searchParams);

        // then
        var mergedNavs = mergedResults.getNavigations();
        assertThat(mergedNavs).hasSize(1);

        var mergedNav = mergedNavs.getFirst();
        assertThat(mergedNav.getName()).isEqualTo(navName);
        assertThat(mergedNav.getType()).isEqualTo(NavigationType.RANGE);

        var mergedRefinements = mergedNav.getRefinements();
        assertThat(mergedRefinements).hasSize(1);

        var mergedRefinement = mergedRefinements.getFirst();
        assertThat(mergedRefinement.getNavigationType()).isEqualTo(NavigationType.RANGE);
        assertThat(mergedRefinement.getRange().low()).isEqualTo(0.0);
        assertThat(mergedRefinement.getRange().high()).isEqualTo(50.0);

        assertThat(mergedRefinement.getCount()).isEqualTo(9); // Google: 8 + Mongo: 1 record aligned with bucket alignment
    }

    @Test
    @DisplayName("Test bucket alignment for numeric facets during PartNumber Expanded Search")
    void testMergeSearchResults_withBucketAlignmentForNumericFacets() {
        // given - Google navigation with the specific bucket structure
        var googleRefinements = List.of(
            NavigationRefinement.rangeRefinement(new Range(0.0, 10.0), 100),
            NavigationRefinement.rangeRefinement(new Range(10.0, 20.0), 50),
            NavigationRefinement.rangeRefinement(new Range(20.0, 30.0), 25)
        );
        var googleNav = Navigation.builder()
            .name("price")
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(googleRefinements)
            .build();

        // Mongo navigation with overlapping and edge case ranges
        var mongoRefinements = List.of(
            NavigationRefinement.rangeRefinement(new Range(-5.0, 0.0), 5),   // Below Google minimum
            NavigationRefinement.rangeRefinement(new Range(5.0, 15.0), 30),  // Overlaps Google 0-10 and 10-20
            NavigationRefinement.rangeRefinement(new Range(25.0, 35.0), 15)  // Overlaps Google 20-30 and extends beyond
        );
        var mongoNav = Navigation.builder()
            .name("price")
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(mongoRefinements)
            .build();

        // Create search results - Mongo as primary, Google as expanded (typical PartNumber pattern)
        // Create records with realistic MongoDB nested structure and actual price values
        var mongoRecords = List.of(
            Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, "mongo1", "mongo1", "Product 1",
                createMongoMetadataWithPrice(7.5)),  // Falls in Google [0-10)
            Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, "mongo2", "mongo2", "Product 2",
                createMongoMetadataWithPrice(12.5)), // Falls in Google [10-20)
            Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, "mongo3", "mongo3", "Product 3",
                createMongoMetadataWithPrice(27.5)), // Falls in Google [20-30)
            Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, "mongo4", "mongo4", "Product 4",
                createMongoMetadataWithPrice(-2.5)), // Below Google range
            Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, "mongo5", "mongo5", "Product 5",
                createMongoMetadataWithPrice(32.5))  // Above Google range
        );
        var primaryResults = SearchResults.builder()
            .records(mongoRecords)
            .numTotalRecords(5)
            .navigations(List.of(mongoNav))
            .build();
        var expandedResults = SearchResults.builder()
            .records(List.of(getRecord("google1")))
            .numTotalRecords(1)
            .navigations(List.of(googleNav))
            .build();

        var priceAttributeConfig = AttributeConfiguration.builder()
            .key(PRICE)
            .path("priceInfo.price")
            .type(AttributeMessage.AttributeType.NUMERICAL)
            .build();
        var merchandisingConfig = MerchandisingConfiguration.builder()
            .attributeConfigurations(Map.of(PRICE, priceAttributeConfig))
            .build();
        var searchParams = SearchParameters.builder()
            .partNumberExpansionEnabled(true)
            .merchandisingConfiguration(merchandisingConfig)
            .build();
        when(primarySearchEngine.search(any())).thenReturn(primaryResults);
        when(relevancySearchEngine.search(any())).thenReturn(expandedResults);

        // when - Perform PartNumber Expanded Search
        var mergedResults = partNumberSearchService.search(searchParams);

        // then - Verify bucket alignment was applied
        var mergedNavigations = mergedResults.getNavigations();
        assertThat(mergedNavigations).hasSize(1);

        var mergedNav = mergedNavigations.getFirst();
        assertThat(mergedNav.getName()).isEqualTo("price");
        assertThat(mergedNav.getType()).isEqualTo(NavigationType.RANGE);

        var mergedRefinements = mergedNav.getRefinements();

        // Should have Google buckets and edge case buckets
        assertThat(mergedRefinements.size()).isGreaterThanOrEqualTo(3);

        // Verify Google bucket structure is preserved with aligned counts
        var firstBucket = findRefinementByRange(mergedRefinements, 0.0, 10.0);
        assertThat(firstBucket.getCount()).isEqualTo(101); // Google: 100 + Mongo: 1 record at 7.5

        var secondBucket = findRefinementByRange(mergedRefinements, 10.0, 20.0);
        assertThat(secondBucket.getCount()).isEqualTo(51); // Google: 50 + Mongo: 1 record at 12.5

        var thirdBucket = findRefinementByRange(mergedRefinements, 20.0, 30.0);
        assertThat(thirdBucket.getCount()).isEqualTo(26); // Google: 25 + Mongo: 1 record at 27.5

        // Verify that edge case buckets are created for values outside Google ranges
        assertThat(mergedRefinements.size()).isGreaterThanOrEqualTo(5); // 3 Google buckets + 2 edge case buckets

        // Check edge case buckets
        // Expected range: [-4.0, 0.0) based on reasonable rounding for record at -2.5
        var belowMinBucket = findRefinementByRange(mergedRefinements, -4.0, 0.0);
        assertThat(belowMinBucket.getCount()).isEqualTo(1); // 1 record at -2.5

        // Expected range: [30.0, 40.0) based on reasonable rounding for record at 32.5
        var aboveMaxBucket = findRefinementByRange(mergedRefinements, 30.0, 40.0);
        assertThat(aboveMaxBucket.getCount()).isEqualTo(1); // 1 record at 32.5
    }

    @Test
    @DisplayName("Test fallback to Mongo facets when Google returns no results")
    void testMergeSearchResults_withEmptyGoogleResults_shouldUsePrimaryFacets() {
        // given
        var mongoNav = createNavigationWithName("mongoNav");

        // Create search results
        var primaryResults = SearchResults.builder()
            .records(List.of(getRecord("mongo1")))
            .numTotalRecords(1)
            .navigations(List.of(mongoNav))
            .build();
        var expandedResults = SearchResults.builder()
            .numTotalRecords(0)
            .build();

        var searchParams = SearchParameters.builder().partNumberExpansionEnabled(true).build();
        when(primarySearchEngine.search(any())).thenReturn(primaryResults);
        when(relevancySearchEngine.search(any())).thenReturn(expandedResults);

        // when
        var mergedResults = partNumberSearchService.search(searchParams);

        // then
        var mergedNavs = mergedResults.getNavigations();

        // Should use Mongo facets only
        assertThat(mergedNavs).hasSize(1);
        assertThat(mergedNavs.getFirst().getName()).isEqualTo("mongoNav");
    }

    @Test
    @DisplayName("Test fallback to Mongo facets when Google returns results with no facets")
    void testMergeSearchResults_withEmptyGoogleFacets_shouldUsePrimaryFacets() {
        // given
        var mongoNav = createNavigationWithName("mongoNav");

        // Create search results
        var primaryResults = SearchResults.builder()
            .records(List.of(getRecord("mongo1")))
            .numTotalRecords(1)
            .navigations(List.of(mongoNav))
            .build();
        var expandedResults = SearchResults.builder()
            .records(List.of(getRecord("google1")))
            .numTotalRecords(1)
            .build();

        var searchParams = SearchParameters.builder().partNumberExpansionEnabled(true).build();
        when(primarySearchEngine.search(any())).thenReturn(primaryResults);
        when(relevancySearchEngine.search(any())).thenReturn(expandedResults);

        // when
        var mergedResults = partNumberSearchService.search(searchParams);

        // then
        var mergedNavs = mergedResults.getNavigations();

        // Should use Mongo facets only
        assertThat(mergedNavs).hasSize(1);
        assertThat(mergedNavs.getFirst().getName()).isEqualTo("mongoNav");
    }

    @Test
    @DisplayName("Test navigations with same name but different fields are kept separate")
    void testMergeSearchResults_withSameNameDifferentFields_shouldKeepSeparate() {
        // given
        var commonNavName = "Category";
        var categoriesField = "categories";
        var categoryDisplayField = "attributes.gbl_category_display";
        var electronicsValue = "Electronics";
        var clothingValue = "Clothing";
        var homeGardenValue = "Home & Garden";
        var sportsValue = "Sports";

        // Create primary navigation with one field
        var primaryNav = Navigation.builder()
            .name(commonNavName)
            .field(categoriesField)
            .type(NavigationType.VALUE)
            .refinements(List.of(
                NavigationRefinement.valueRefinement(electronicsValue, 5, false),
                NavigationRefinement.valueRefinement(clothingValue, 3, false)
            ))
            .build();

        // Create expanded navigation with same name but different field
        var expandedNav = Navigation.builder()
            .name(commonNavName)
            .field(categoryDisplayField)
            .type(NavigationType.VALUE)
            .refinements(List.of(
                NavigationRefinement.valueRefinement(homeGardenValue, 8, false),
                NavigationRefinement.valueRefinement(sportsValue, 4, false)
            ))
            .build();

        // Create search results
        var primaryResults = SearchResults.builder()
            .records(List.of(getRecord("mongo1")))
            .numTotalRecords(1)
            .navigations(List.of(primaryNav))
            .build();
        var expandedResults = SearchResults.builder()
            .records(List.of(getRecord("google1")))
            .numTotalRecords(1)
            .navigations(List.of(expandedNav))
            .build();

        var searchParams = SearchParameters.builder().partNumberExpansionEnabled(true).build();
        when(primarySearchEngine.search(any())).thenReturn(primaryResults);
        when(relevancySearchEngine.search(any())).thenReturn(expandedResults);

        // when
        var mergedResults = partNumberSearchService.search(searchParams);

        // then
        var mergedNavs = mergedResults.getNavigations();

        // Should have both navigations kept separate (not merged)
        assertThat(mergedNavs).hasSize(2);

        // Both should have the same name but different fields
        assertThat(mergedNavs.stream().map(Navigation::getName).distinct().count()).isEqualTo(1);
        assertThat(mergedNavs.stream().map(Navigation::getField).distinct().count()).isEqualTo(2);

        // Verify the fields are correct
        var fields = mergedNavs.stream().map(Navigation::getField).collect(Collectors.toSet());
        assertThat(fields).containsExactlyInAnyOrder(categoriesField, categoryDisplayField);

        // Verify refinements are not merged (each navigation should have its original refinements)
        var expandedNavInResult = mergedNavs.stream()
            .filter(nav -> categoryDisplayField.equals(nav.getField()))
            .findFirst()
            .orElseThrow();
        assertThat(expandedNavInResult.getRefinements()).hasSize(2);
        assertThat(expandedNavInResult.getRefinements().stream().map(NavigationRefinement::getValue))
            .containsExactlyInAnyOrder(homeGardenValue, sportsValue);

        var primaryNavInResult = mergedNavs.stream()
            .filter(nav -> categoriesField.equals(nav.getField()))
            .findFirst()
            .orElseThrow();
        assertThat(primaryNavInResult.getRefinements()).hasSize(2);
        assertThat(primaryNavInResult.getRefinements().stream().map(NavigationRefinement::getValue))
            .containsExactlyInAnyOrder(electronicsValue, clothingValue);
    }

    // endregion

    private static Record getRecord(String id) {
        return Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, id, "primaryProductId", "title", Map.of());
    }

    private static List<Record> createTestRecords(int count, String prefix) {
        return IntStream.range(0, count)
            .mapToObj(i -> Record.of(
                APPAREL_MERCHANDISER,
                PRODUCTS_CLOTHING,
                prefix + "_" + i,
                prefix + "_" + i,
                prefix + "_title_" + i,
                Map.of()
            ))
            .toList();
    }

    private static Navigation createNavigationWithName(String name) {
        List<NavigationRefinement> refinements = List.of(
            NavigationRefinement.valueRefinement(name + "_value", 10, false)
        );
        return Navigation.builder()
            .name(name)
            .field("field_" + name)
            .type(NavigationType.VALUE)
            .refinements(refinements)
            .build();
    }

    private static NavigationConfiguration createNavigationConfigurationWithPriority(Navigation navigation,
                                                                                     int priority) {
        return NavigationConfiguration.builder()
            .name(navigation.getName())
            .field(navigation.getField())
            .areaId(1)
            .type(navigation.getType())
            .priority(priority)
            .build();
    }

    private Map<String, Object> createMongoMetadataWithPrice(Double price) {
        return Map.of(
            "indexables", Map.of(
                "priceInfo", Map.of(
                    "price", List.of(price)
                )
            )
        );
    }

    private NavigationRefinement findRefinementByRange(List<NavigationRefinement> refinements,
                                                       Double low, Double high) {
        return refinements.stream()
            .filter(r -> {
                var range = r.getRange();
                var rangeLow = range.low();
                var rangeHigh = range.high();
                return (Objects.equals(rangeLow, low)) &&
                    (Objects.equals(rangeHigh, high));
            })
            .findFirst()
            .orElseThrow(() -> new AssertionError("Expected Range [" + low + ", " + high + "] not found"));
    }
}
