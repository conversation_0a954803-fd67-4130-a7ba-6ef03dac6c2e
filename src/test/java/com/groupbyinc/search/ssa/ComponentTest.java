package com.groupbyinc.search.ssa;

import com.groupbyinc.search.ssa.fixture.AuthFixture;
import com.groupbyinc.search.ssa.fixture.DirectSearchBeaconFixture;
import com.groupbyinc.search.ssa.stub.ResourceUtils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import io.micronaut.context.ApplicationContext;
import io.micronaut.http.HttpHeaders;
import io.micronaut.http.MediaType;
import io.micronaut.http.client.HttpClient;
import io.micronaut.runtime.server.EmbeddedServer;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.TestInstance;

import java.util.HashMap;
import java.util.Map;


import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options;
import static org.junit.jupiter.api.TestInstance.Lifecycle.PER_CLASS;

@TestInstance(PER_CLASS)
public abstract class ComponentTest {

    public static final String CONFIG_PATH = "/config/site-search";

    /** The embedded server running this Micronaut application in the test environment. */
    protected EmbeddedServer server;

    /**
     * An HTTP client that can be used to communicate with the embedded server (the external HTTP API of this app)
     * in the test environment.
     */
    protected HttpClient httpClient;

    /** Object mapper configured by Micronaut. */
    protected ObjectMapper objectMapper;

    /** WireMock server to fake JWKS responses. */
    private WireMockServer mockServer;

    protected final DirectSearchBeaconFixture directSearchBeaconFixture = new DirectSearchBeaconFixture();

    @BeforeAll
    void beforeAll() {
        var appContext = ApplicationContext.builder()
            .properties(getProperties())
            .build();

        registerSingletons(appContext);
        setupEmbeddedServerAndHttpClient(appContext);
        directSearchBeaconFixture.stubSuccess();
    }

    @AfterAll
    void afterAll() {
        shutdownApplication();
        cleanupMockServers();
    }

    /**
     * Gets additional properties to add into the application context.
     * <p>
     * Sub-classes can override this method, calling super, to add further test specific properties.
     *
     * @return Properties to set.
     */
    protected Map<String, Object> getProperties() {

        var properties = new HashMap<String, Object>();
        setupMockServer();
        properties.put(
            "micronaut.security.token.jwt.signatures.jwks.groupby.url",
            mockServer.url(AuthFixture.JWKS_PATH)
        );
        properties.put("commandcenter.client.url", mockServer.url(CONFIG_PATH));
        properties.put("micronaut.http.services.beacon.url", directSearchBeaconFixture.getBaseUrl());
        properties.put("direct-search-beacon.auth-token", DirectSearchBeaconFixture.AUTH_TOKEN);
        properties.put("jackson.serialization-inclusion", "non_null");
        return properties;
    }

    /**
     * Register any singletons that should be present or replaces in the application context.
     *
     * @param appContext Application context for the singletons to get registered with.
     */
    protected abstract void registerSingletons(ApplicationContext appContext);

    /**
     * Creates a new WireMock server on a random available port to fake the JWKS
     * response of the authentication service
     */
    private void setupMockServer() {
        mockServer = new WireMockServer(options().dynamicPort());

        mockServer.givenThat(
            get(urlEqualTo(AuthFixture.JWKS_PATH))
                .willReturn(aResponse()
                    .withStatus(200)
                    .withBody(AuthFixture.JWKS_RESPONSE))
        );

        mockServer.givenThat(
            get(urlEqualTo(CONFIG_PATH))
                .willReturn(aResponse().withStatus(200)
                    .withHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                    .withBody(ResourceUtils.getResourceContents("config/config.json")))
        );

        mockServer.start();
    }

    /**
     * Starts the embedded server (running this Micronaut application) with the provided application context,
     * and sets up the HTTP client for communicating with the running server.
     *
     * @param appContext The application context.
     */
    private void setupEmbeddedServerAndHttpClient(ApplicationContext appContext) {

        server = appContext.start().getBean(EmbeddedServer.class)
            .start();
        httpClient = HttpClient.create(server.getURL());
        objectMapper = appContext.getBean(ObjectMapper.class);
    }

    /**
     * Shuts down all WireMock servers.
     */
    private void cleanupMockServers() {
        mockServer.stop();
        directSearchBeaconFixture.stop();
    }

    /**
     * Shuts down the embedded server and closes the application context.
     */
    private void shutdownApplication() {
        server.stop()
            .getApplicationContext()
            .stop();
    }

}
