package com.groupbyinc.search.ssa.beacon;

import com.groupbyinc.search.ssa.beacon.client.MerchandiserSubDomainFilter;

import io.micronaut.http.HttpMethod;
import io.micronaut.http.MutableHttpRequest;
import io.micronaut.http.filter.ClientFilterChain;
import io.micronaut.http.simple.SimpleHttpRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentCaptor;

import static com.groupbyinc.search.ssa.util.Constants.GROUPBY_CUSTOMER_ID_HEADER;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.TestInstance.Lifecycle.PER_CLASS;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.mock;

@TestInstance(PER_CLASS)
@SuppressWarnings("all")
@DisplayName("MerchandiserSubDomainFilter Tests")
class MerchandiserSubDomainFilterTest {

    private MerchandiserSubDomainFilter merchandiserSubDomainFilter;
    private ClientFilterChain filterChain;

    @BeforeEach
    void setUp() {
        merchandiserSubDomainFilter = new MerchandiserSubDomainFilter();
        filterChain = mock(ClientFilterChain.class);
    }

    @Test
    @DisplayName("Does not modify the request if the 'merchandiser-id' request attribute has not been set")
    void doesNotModifyTheRequestIfTheMerchandiserIdAttributeIsNotProvided() {

        // Given: A http request with no merchandiserId request attribute
        var simpleHttpRequest = new SimpleHttpRequest<>(HttpMethod.GET, "/hello", null)
                    .header(GROUPBY_CUSTOMER_ID_HEADER, "");
        var simpleHttpRequestClone = new SimpleHttpRequest<>(HttpMethod.GET, "/hello", null);

        // When: The filter processes the request
        merchandiserSubDomainFilter.doFilter(simpleHttpRequestClone, filterChain);

        // Then: The filter proceeds
        ArgumentCaptor<MutableHttpRequest<?>> argumentCaptor = ArgumentCaptor.forClass(MutableHttpRequest.class);
        then(filterChain).should().proceed(argumentCaptor.capture());

        // And: The request is unchanged
        assertThat(argumentCaptor.getValue())
            .usingRecursiveComparison()
            .isEqualTo(simpleHttpRequest);
    }

    @Test
    @DisplayName("Replaces the '__merchandiser-id__' template in the Uri when the 'merchandiser-id' request attribute has been set")
    void replacesTheMerchandiserIdTemplatesInTheRequestUri() {
        var merchandiserId = "harrods";
        var templatedUrl =  "http://__merchandiser-id__.groupbycloud.com/hello";
        var actualUrl = "http://%s.groupbycloud.com/hello".formatted(merchandiserId);

        // Given: A http request with the merchandiserId request attribute and the template string in the Uri
        var simpleHttpRequest = new SimpleHttpRequest<>(HttpMethod.GET, templatedUrl, null)
            .setAttribute("merchandiser-id", merchandiserId);

        // When: The filter processes the request
        merchandiserSubDomainFilter.doFilter(simpleHttpRequest, filterChain);

        // Then: The filter proceeds
        ArgumentCaptor<MutableHttpRequest<?>> argumentCaptor = ArgumentCaptor.forClass(MutableHttpRequest.class);
        then(filterChain).should().proceed(argumentCaptor.capture());

        // And: The '__customer-id__' template in the Uri has been replaced
        assertThat(argumentCaptor.getValue())
            .usingRecursiveComparison()
            .isEqualTo(
                new SimpleHttpRequest<>(HttpMethod.GET, actualUrl, null)
                    .header(GROUPBY_CUSTOMER_ID_HEADER, merchandiserId)
                    .setAttribute("merchandiser-id", merchandiserId)
            );
    }
}
