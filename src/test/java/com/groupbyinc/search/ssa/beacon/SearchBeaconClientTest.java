package com.groupbyinc.search.ssa.beacon;

import com.groupbyinc.search.ssa.api.dto.Experiment;
import com.groupbyinc.search.ssa.beacon.client.DirectSearchBeaconRequest;
import com.groupbyinc.search.ssa.beacon.client.SearchBeaconClient;
import com.groupbyinc.search.ssa.fixture.DirectSearchBeaconFixture;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import io.micronaut.test.support.TestPropertyProvider;
import jakarta.inject.Inject;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.groupbyinc.search.ssa.beacon.config.DirectSearchBeaconConfiguration.EVENT_TYPE;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.TestInstance.Lifecycle.PER_CLASS;

@MicronautTest
@TestInstance(PER_CLASS)
@DisplayName("SearchBeaconClient Tests")
class SearchBeaconClientTest implements TestPropertyProvider {

    @Inject
    private SearchBeaconClient searchBeaconClient;

    private final DirectSearchBeaconFixture directSearchBeaconFixture = new DirectSearchBeaconFixture();

    @NonNull
    @Override
    public Map<String, String> getProperties() {
        return Map.of(
            "micronaut.http.services.beacon.url", directSearchBeaconFixture.getBaseUrl()
        );
    }

    @AfterAll
    void afterAll() {
        directSearchBeaconFixture.stop();
    }

    @Test
    @SuppressWarnings("ConstantConditions")
    @DisplayName("Can send a direct search beacon")
    void canSendADirectSearchBeacon() {
        directSearchBeaconFixture.stubSuccess();

        var directSearchBeaconRequest = new DirectSearchBeaconRequest<>(
            APPAREL,
            "28b5f6ea-9850-49ec-897b-6da034fce7d1",
            Map.of("hello", "world"),
            EVENT_TYPE,
            List.of(new Experiment("search_rule_test", "variant B"))
        );

        var response = searchBeaconClient.sendDirectBeacon(
            APPAREL,
            DirectSearchBeaconFixture.AUTH_TOKEN,
            directSearchBeaconRequest
        ).blockFirst();
        assertThat(Optional.ofNullable(response.getStatus())).isNotNull();

        directSearchBeaconFixture.assertDirectSearchBeaconRequestWasSent("""
            {
                "customerId":"apparel",
                "responseId":"28b5f6ea-9850-49ec-897b-6da034fce7d1",
                "experiments":[
                    {
                        "experimentId":"search_rule_test",
                        "experimentVariant":"variant B"
                    }
                ],
                "event":{
                    "hello":"world"
                },
                "eventType":"search"
            }
        """);
    }

}
