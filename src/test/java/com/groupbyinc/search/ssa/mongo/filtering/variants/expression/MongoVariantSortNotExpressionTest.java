package com.groupbyinc.search.ssa.mongo.filtering.variants.expression;

import com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortComparisonExpression;
import com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortRangeExpression;
import com.groupbyinc.search.ssa.mongo.filtering.variants.expression.text.MongoVariantSortTextExpression;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static com.groupbyinc.search.ssa.stub.ResourceUtils.getResourceContents;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("MongoVariantSortNotExpression Tests")
public class MongoVariantSortNotExpressionTest {

    @Test
    @DisplayName("MongoVariantSortTextExpression single value (NOT) Test")
    public void mongoVariantSortTextExpressionSingleValueTest() {
        var result = new MongoVariantSortNotExpression(
            new MongoVariantSortTextExpression("sizes", List.of("XL"))
        ).toFilter();

        var expected = getResourceContents(
            "mongo/filtering/variants/expression/mongo-variant-sort-text-expression-single-value.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortTextExpression multiple values (NOT) Test")
    public void mongoVariantSortTextExpressionMultipleValuesTest() {
        var result = new MongoVariantSortNotExpression(
            new MongoVariantSortTextExpression("sizes", List.of("Xl", "L"))
        ).toFilter();

        var expected = getResourceContents(
            "mongo/filtering/variants/expression/mongo-variant-sort-text-expression-multiple-values.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchTextExpression single value Inventory (NOT) Test")
    public void mongoVariantSortTextExpressionSingleValueInventoryTest() {
        var result = new MongoVariantSortNotExpression(
            new MongoVariantSortTextExpression("sizes", "place", List.of("XL"))
        ).toFilter();

        var expected = getResourceContents(
            "mongo/filtering/variants/expression/mongo-variant-sort-text-expression-single-value-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchTextExpression multiple values Inventory (NOT) Test")
    public void mongoVariantSortTextExpressionMultipleValuesInventoryTest() {
        var result = new MongoVariantSortNotExpression(
            new MongoVariantSortTextExpression("sizes", "place", List.of("Xl", "L"))
        ).toFilter();

        var expected = getResourceContents(
            "mongo/filtering/variants/expression/mongo-variant-sort-text-expression-multiple-values-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortRangeExpression From Raw (NOT) Tests")
    public void mongoVariantSortRangeExpressionFromRawTest() {
        var result = new MongoVariantSortNotExpression(
            MongoVariantSortRangeExpression.of("priceInfo.price", null, 1.0, 2.0)
        ).toFilter();

        var expected = getResourceContents(
            "mongo/filtering/variants/expression/mongo-variant-sort-range-expression-from-raw.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortRangeExpression From Raw (Inventory) (NOT) Tests")
    public void mongoVariantSortRangeExpressionFromRawInventoryTest() {
        var result = new MongoVariantSortNotExpression(
            MongoVariantSortRangeExpression.of("priceInfo.price", "place", 1.0, 2.0)
        ).toFilter();

        var expected = getResourceContents(
            "mongo/filtering/variants/expression/mongo-variant-sort-range-expression-from-raw-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression not equal (NOT) Test")
    public void mongoVariantSortComparisonExpressionNotEqualTest() {
        var result = new MongoVariantSortNotExpression(
            new MongoVariantSortComparisonExpression("priceInfo.price", "!=", 1.0)
        ).toFilter();

        var expected = getResourceContents(
            "mongo/filtering/variants/expression/mongo-variant-sort-comparison-expression-not-equal.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression not equal (Inventory) (NOT) Test")
    public void mongoVariantSortComparisonExpressionNotEqualInventoryTest() {
        var result = new MongoVariantSortNotExpression(
            new MongoVariantSortComparisonExpression("priceInfo.price", "place", "!=", 1.0)
        ).toFilter();

        var expected = getResourceContents(
            "mongo/filtering/variants/expression/mongo-variant-sort-comparison-expression-not-equal-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

}
