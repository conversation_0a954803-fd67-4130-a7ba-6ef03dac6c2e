package com.groupbyinc.search.ssa.mongo.filtering.search.expression;

import com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical.MongoSearchComparisonExpression;
import com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical.MongoSearchRangeExpression;
import com.groupbyinc.search.ssa.mongo.filtering.search.expression.text.MongoSearchTextExpression;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static com.groupbyinc.search.ssa.stub.ResourceUtils.getResourceContents;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("MongoSearchNotExpression Tests")
public class MongoSearchNotExpressionTest {

    @Test
    @DisplayName("MongoSearchTextExpression single value (NOT) Test")
    public void mongoSearchTextExpressionSingleValueTest() {
        var result = new MongoSearchNotExpression(
            new MongoSearchTextExpression("sizes", List.of("XL"))
        ).toFilter();

        var expected = """
            {
              "compound": {
                "mustNot": [
                  {
                    "equals": {
                      "value": "XL",
                      "path": "indexables.sizes"
                    }
                  }
                ]
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchTextExpression multiple values (NOT) Test")
    public void mongoSearchTextExpressionMultipleValuesTest() {
        var result = new MongoSearchNotExpression(
            new MongoSearchTextExpression("sizes", List.of("Xl", "L"))
        ).toFilter();

        var expected = """
            {
              "compound": {
                "mustNot": [
                  {
                    "in": {
                      "value": [
                        "Xl",
                        "L"
                      ],
                      "path": "indexables.sizes"
                    }
                  }
                ]
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchTextExpression single value Inventory (NOT) Test")
    public void mongoSearchTextExpressionSingleValueInventoryTest() {
        var result = new MongoSearchNotExpression(
            new MongoSearchTextExpression("localInventories.sizes", "place", List.of("XL"))
        ).toFilter();

        var expected = getResourceContents(
            "mongo/filtering/search/expression/mongo-search-not-expression-single-value-inventory-text.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchTextExpression multiple values Inventory (NOT) Test")
    public void mongoSearchTextExpressionMultipleValuesInventoryTest() {
        var result = new MongoSearchNotExpression(
            new MongoSearchTextExpression("localInventories.sizes", "place", List.of("Xl", "L"))
        ).toFilter();

        var expected = getResourceContents(
            "mongo/filtering/search/expression/mongo-search-not-expression-multiple-value-inventory-text.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchRangeExpression From Raw (NOT) Tests")
    public void mongoSearchRangeExpressionFromRawTest() {
        var result = new MongoSearchNotExpression(
            MongoSearchRangeExpression.of("priceInfo.price", null, 1.0, 2.0)
        ).toFilter();

        var expected = """
            {
              "compound": {
                "mustNot": [
                  {
                    "range": {
                      "path": "indexables.priceInfo.price",
                      "gte": 1.0,
                      "lt": 2.0
                    }
                  }
                ]
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchRangeExpression From Raw (Inventory) (NOT) Tests")
    public void mongoSearchRangeExpressionFromRawInventoryTest() {
        var result = new MongoSearchNotExpression(
            MongoSearchRangeExpression.of("localInventories.priceInfo.price", "place", 1.0, 2.0)
        ).toFilter();

        var expected = getResourceContents(
            "mongo/filtering/search/expression/mongo-search-not-expression-from-raw-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression not equal (NOT) Test")
    public void mongoSearchComparisonExpressionNotEqualTest() {
        var result = new MongoSearchNotExpression(
            new MongoSearchComparisonExpression("priceInfo.price", "!=", 1.0)
        ).toFilter();

        var expected = """
            {
              "compound": {
                "mustNot": [
                  {
                    "compound": {
                      "mustNot": [
                        {
                          "equals": {
                            "value": 1.0,
                            "path": "indexables.priceInfo.price"
                          }
                        }
                      ]
                    }
                  }
                ]
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression not equal (Inventory) (NOT) Test")
    public void mongoSearchComparisonExpressionNotEqualInventoryTest() {
        var result = new MongoSearchNotExpression(
            new MongoSearchComparisonExpression("localInventories.priceInfo.price", "place", "!=", 1.0)
        ).toFilter();

        var expected = getResourceContents(
            "mongo/filtering/search/expression/mongo-search-not-expression-not-equal-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

}
