package com.groupbyinc.search.ssa.mongo.facet.category;

import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.navigation.Range;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import io.micronaut.core.propagation.PropagatedContext;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.BUCKETS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.CATEGORY;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.COUNT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.DEFAULT_NUM_RANGES;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.ID_UNDERSCORE;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTION;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;

import static java.util.List.of;
import static java.util.stream.Collectors.toMap;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CategoryBucketsProviderTest {
    private static final String TENANT = "tenant";
    private static final String COLLECTION = "collection";

    private CategoryBucketsProvider provider;

    @Mock
    private MongoClient mongoClient;
    @Captor
    ArgumentCaptor<Bson> projectionCaptor;

    private PropagatedContext.Scope scope;

    @BeforeEach
    void setUp() {
        this.provider = new CategoryBucketsProvider(mongoClient, new CategoryBucketsCache(Duration.ofMinutes(60)));

        scope = PropagatedContext
            .getOrEmpty()
            .plus(createContext(new Merchandiser(TENANT), COLLECTION, PRODUCTION))
            .propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    @Test
    void resolveNumRanges_shouldReturnDefaultRanges() {
        //given
        var fields = of("f1", "f2");
        var sp = params(null, Map.of());

        //when
        var result = provider.resolveNumRanges(sp, fields);

        //then
        assertThat(result).hasSize(2);
        assertThat(result.get("f1")).isEqualTo(DEFAULT_NUM_RANGES);
        assertThat(result.get("f2")).isEqualTo(DEFAULT_NUM_RANGES);
    }

    @Test
    void resolveNumRanges_shouldReturnRangesFromSubcategory() {
        //given
        var field1 = "price";
        var field2 = "rating";
        var field3 = "clicks";

        var attrMap = Map.of(
            field1, "priceInfo.price",
            field2, "rating.rating",
            field3, "clicks"
        );

        var cat1 = "Bed & Bath";
        var cat2 = "Bed & Bath > Shop all Bath";
        var cat3 = "Bed & Bath > Shop all Bath > Bath Towels";

        var cat1Count = 100;
        var cat2Count = 50;
        var cat3Count = 20;

        var cat1Field1Buckets = of(of(0d, 5d), of(5d, 10d), of(10d, 20d));
        var cat1Field2Buckets = of(of(0d, 2d), of(2d, 4d), of(4d, 5d));
        var cat1Field3Buckets = List.<List<Double>>of();

        var cat2Field1Buckets = of(of(0d, 10d), of(10d, 20d), of(20d, 30d));
        var cat2Field2Buckets = of(of(0d, 1d), of(1d, 4d), of(4d, 5d));
        var cat2Field3Buckets = List.<List<Double>>of();

        var cat3Field1Buckets = of(of(0d, 50d), of(50d, 100d), of(100d, 200d));
        var cat3Field2Buckets = of(of(0d, 3d), of(3d, 4d), of(4d, 5d));
        var cat3Field3Buckets = List.<List<Double>>of();

        var categories = of(cat1, cat2, cat3);
        mockCategoryCall(
            categories,
            of(
                categoryBson(
                    cat1, cat1Count, Map.of(
                        field1, cat1Field1Buckets,
                        field2, cat1Field2Buckets,
                        field3, cat1Field3Buckets
                    )
                ),
                categoryBson(
                    cat2, cat2Count, Map.of(
                        field1, cat2Field1Buckets,
                        field2, cat2Field2Buckets,
                        field3, cat2Field3Buckets
                    )
                ),
                categoryBson(
                    cat3, cat3Count, Map.of(
                        field1, cat3Field1Buckets,
                        field2, cat3Field2Buckets,
                        field3, cat3Field3Buckets
                    )
                )
            )
        );

        //when
        var result = provider.resolveNumRanges(params(categories, attrMap), of(field1, field2, field3));

        //then
        assertThat(result).hasSize(3);
        assertThat(result.get(field1)).isEqualTo(toSSRanges(cat3Field1Buckets));
        assertThat(result.get(field2)).isEqualTo(toSSRanges(cat3Field2Buckets));
        assertThat(result.get(field3)).isEqualTo(DEFAULT_NUM_RANGES);
        assertProjection(attrMap);
    }

    @Test
    void resolveNumRanges_shouldReturnRangesFromHighestCountCategory() {
        //given
        var field1 = "price";
        var field2 = "rating";
        var field3 = "clicks";

        var attrMap = Map.of(
            field1, "priceInfo.price",
            field2, "rating.rating",
            field3, "clicks"
        );

        var cat1 = "Bed & Bath";
        var cat2 = "Bed & Bath > Shop all Bath";
        var cat3 = "Bed & Bath > Shop all Sinks";

        var cat1Count = 100;
        var cat2Count = 50;
        var cat3Count = 20;

        var cat1Field1Buckets = of(of(0d, 5d), of(5d, 10d), of(10d, 20d));
        var cat1Field2Buckets = of(of(0d, 2d), of(2d, 4d), of(4d, 5d));
        var cat1Field3Buckets = List.<List<Double>>of();

        var cat2Field1Buckets = of(of(0d, 10d), of(10d, 20d), of(20d, 30d));
        var cat2Field2Buckets = of(of(0d, 1d), of(1d, 4d), of(4d, 5d));
        var cat2Field3Buckets = List.<List<Double>>of();

        var cat3Field1Buckets = of(of(0d, 50d), of(50d, 100d), of(100d, 200d));
        var cat3Field2Buckets = of(of(0d, 3d), of(3d, 4d), of(4d, 5d));
        var cat3Field3Buckets = List.<List<Double>>of();

        var categories = of(cat1, cat2, cat3);
        mockCategoryCall(
            categories,
            of(
                categoryBson(
                    cat1, cat1Count, Map.of(
                        field1, cat1Field1Buckets,
                        field2, cat1Field2Buckets,
                        field3, cat1Field3Buckets
                    )
                ),
                categoryBson(
                    cat2, cat2Count, Map.of(
                        field1, cat2Field1Buckets,
                        field2, cat2Field2Buckets,
                        field3, cat2Field3Buckets
                    )
                ),
                categoryBson(
                    cat3, cat3Count, Map.of(
                        field1, cat3Field1Buckets,
                        field2, cat3Field2Buckets,
                        field3, cat3Field3Buckets
                    )
                )
            )
        );

        //when
        var result = provider.resolveNumRanges(params(categories, attrMap), of(field1, field2, field3));

        //then
        assertThat(result).hasSize(3);
        assertThat(result.get(field1)).isEqualTo(toSSRanges(cat1Field1Buckets));
        assertThat(result.get(field2)).isEqualTo(toSSRanges(cat1Field2Buckets));
        assertThat(result.get(field3)).isEqualTo(DEFAULT_NUM_RANGES);
        assertProjection(attrMap);
    }

    @Test
    void resolveNumRanges_shouldReturnRangesFromSubcategory_cached() {
        //given
        var field1 = "price";
        var field2 = "rating";

        var attrMap = Map.of(
            field1, "priceInfo.price",
            field2, "rating.rating"
        );

        var cat1 = "Bed & Bath";
        var cat2 = "Bed & Bath > Shop all Bath";

        var cat1Count = 100;
        var cat2Count = 50;

        var cat1Field1Buckets = of(of(0d, 5d), of(5d, 10d), of(10d, 20d));
        var cat1Field2Buckets = of(of(0d, 2d), of(2d, 4d), of(4d, 5d));

        var cat2Field1Buckets = of(of(0d, 10d), of(10d, 20d), of(20d, 30d));
        var cat2Field2Buckets = of(of(0d, 1d), of(1d, 4d), of(4d, 5d));


        // round 1
        mockCategoryCall(
            of(cat1),
            of(
                categoryBson(
                    cat1, cat1Count, Map.of(
                        field1, cat1Field1Buckets,
                        field2, cat1Field2Buckets
                    )
                )
            )
        );

        //when
        var result = provider.resolveNumRanges(params(of(cat1), attrMap), of(field1, field2));

        //then
        assertThat(result).hasSize(2);
        assertThat(result.get(field1)).isEqualTo(toSSRanges(cat1Field1Buckets));
        assertThat(result.get(field2)).isEqualTo(toSSRanges(cat1Field2Buckets));
        assertProjection(attrMap);

        // round 2
        projectionCaptor = ArgumentCaptor.forClass(Bson.class);
        mockCategoryCall(
            of(cat2),
            of(
                categoryBson(
                    cat2, cat2Count, Map.of(
                        field1, cat2Field1Buckets,
                        field2, cat2Field2Buckets
                    )
                )
            )
        );

        //when
        result = provider.resolveNumRanges(params(of(cat1, cat2), attrMap), of(field1, field2));

        //then
        assertThat(result).hasSize(2);
        assertThat(result.get(field1)).isEqualTo(toSSRanges(cat2Field1Buckets));
        assertThat(result.get(field2)).isEqualTo(toSSRanges(cat2Field2Buckets));
        assertProjection(attrMap);
    }

    @SuppressWarnings("unchecked")
    private void mockCategoryCall(List<String> categories, List<Document> result) {
        var db = Mockito.mock(MongoDatabase.class);
        var coll = Mockito.mock(MongoCollection.class);
        var findIter = Mockito.mock(FindIterable.class);

        when(mongoClient.getDatabase(TENANT)).thenReturn(db);
        when(db.getCollection("%s_search_buckets".formatted(COLLECTION))).thenReturn(coll);
        when(coll.find(Filters.in(CATEGORY, categories))).thenReturn(findIter);
        when(findIter.projection(projectionCaptor.capture())).thenReturn(findIter);
        when(findIter.into(any())).thenReturn(new ArrayList<>(result));
    }

    private void assertProjection(Map<String, String> fields) {
        assertThat(projectionCaptor.getAllValues()).hasSize(1);
        var projection = projectionCaptor.getValue().toBsonDocument();
        assertThat(projection.getInt32(ID_UNDERSCORE).getValue()).isEqualTo(0);
        assertThat(projection.getInt32(CATEGORY).getValue()).isEqualTo(1);
        assertThat(projection.getInt32(COUNT).getValue()).isEqualTo(1);
        assertThat(projection.containsKey(BUCKETS)).isTrue();
        var buckets = projection.getDocument(BUCKETS);
        fields.forEach((f, p) ->
            assertThat(buckets.getString(f).getValue()).isEqualTo("$fields.%s.buckets".formatted(p))
        );
    }

    private static Document categoryBson(String name, Integer count, Map<String, List<List<Double>>> fields) {
        var bucketsBson = new Document();
        fields.forEach(bucketsBson::append);

        return new Document()
            .append(CATEGORY, name)
            .append(COUNT, count)
            .append(BUCKETS, bucketsBson);
    }

    private static SearchParameters params(List<String> categories, Map<String, String> attributes) {
        return SearchParameters.builder()
            .merchandisingConfiguration(MerchandisingConfiguration.builder()
                .attributeConfigurations(
                    attributes.entrySet().stream()
                        .map(e -> AttributeConfiguration.builder()
                            .key(e.getKey())
                            .path(e.getValue())
                            .build())
                        .collect(toMap(AttributeConfiguration::key, Function.identity()))
                ).build()
            ).pageCategories(categories)
            .build();
    }

    private static List<Range> toSSRanges(List<List<Double>> ranges) {
        return ranges.stream().map(b -> new Range(b.getFirst(), b.getLast())).toList();
    }
}
