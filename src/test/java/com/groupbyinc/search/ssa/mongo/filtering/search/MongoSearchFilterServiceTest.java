package com.groupbyinc.search.ssa.mongo.filtering.search;

import com.groupbyinc.search.ssa.core.SearchParameters;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.getAttributeFilters;
import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.getMerchandisingConfiguration;
import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.getProductIdFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.getSelectedRefinements;
import static com.groupbyinc.search.ssa.stub.ResourceUtils.getResourceContents;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("MongoSearchFilterService Tests")
public class MongoSearchFilterServiceTest {

    @Test
    @DisplayName("Build full search filter")
    public void mongoSearchFilterServiceBuildSearchFilterTest() {
        var expected = getResourceContents("mongo/filtering/search/mongo-search-filter.json");

        var searchParameters = SearchParameters
            .builder()
            .preFilter(getResourceContents("antlr/raw-filter"))
            .merchandisingConfiguration(getMerchandisingConfiguration())
            .attributeFilters(getAttributeFilters())
            .refinements(getSelectedRefinements())
            .productIdFilter(getProductIdFilter())
            .build();

        var filter = new MongoSearchFilterService().createFilter(searchParameters);

        assertThat(filter.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

}
