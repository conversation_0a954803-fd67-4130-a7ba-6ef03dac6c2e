package com.groupbyinc.search.ssa.mongo;

import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.area.AreaConfiguration;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.features.Features;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.mongo.filtering.search.MongoSearchFilterService;

import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Supplier;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.FILTER;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.IN;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.PATH;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.VALUE;
import static com.groupbyinc.search.ssa.mongo.facet.MongoFacetConverter.getActiveMultiSelectFields;
import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("Multi-Select Navigation Facet Filtering Tests for PartNumber Expansion Search")
class MongoSearchEngineMultiSelectTest {

    // Field names used throughout tests
    private static final String BRAND_FIELD = "attributes.brand";
    private static final String COLOR_FIELD = "attributes.color";
    private static final String SIZE_FIELD = "attributes.size";
    private static final String DYNAMIC_TEST_FIELD = "attributes.dynamicTest";

    // Common values used in refinements
    private static final String NIKE_VALUE = "Nike";
    private static final String RED_VALUE = "Red";
    private static final String LARGE_VALUE = "Large";
    private static final String TEST_VALUE = "TestValue";

    // Product IDs
    private static final String PRODUCT_1 = "product1";
    private static final String PRODUCT_2 = "product2";
    private static final String PRODUCT_3 = "product3";

    // Display names for attributes
    private static final String BRAND_DISPLAY_NAME = "Brand";
    private static final String COLOR_DISPLAY_NAME = "Color";
    private static final String SIZE_DISPLAY_NAME = "Size";
    private static final String DYNAMIC_TEST_DISPLAY_NAME = "Dynamic Test";

    private static final String TEST_QUERY = "test query";
    private static final String FILTER_KEY = "filter";

    private static final Supplier<Instant> INSTANT_SUPPLIER = () ->
        Instant.now().plusMillis(53);

    private MerchandisingConfiguration merchandisingConfiguration;
    private MongoSearchFilterService mongoSearchFilterService;

    @BeforeEach
    void setUp() {
        merchandisingConfiguration = new MerchandisingConfiguration(
            AreaConfiguration.builder().build(),
            new TreeSet<>(),
            Set.of(
                NavigationConfiguration.builder()
                    .id(1)
                    .name(BRAND_DISPLAY_NAME)
                    .field(BRAND_FIELD)
                    .type(NavigationType.VALUE)
                    .priority(1)
                    .multiSelect(true) // Multi-select navigation
                    .build(),
                NavigationConfiguration.builder()
                    .id(2)
                    .name(COLOR_DISPLAY_NAME)
                    .field(COLOR_FIELD)
                    .type(NavigationType.VALUE)
                    .priority(2)
                    .multiSelect(true) // Multi-select navigation
                    .build(),
                NavigationConfiguration.builder()
                    .id(3)
                    .name(SIZE_DISPLAY_NAME)
                    .field(SIZE_FIELD)
                    .type(NavigationType.VALUE)
                    .priority(3)
                    .multiSelect(false) // Single-select navigation
                    .build()
            ),
            new TreeSet<>(),
            List.of(),
            Map.of(
                BRAND_FIELD, AttributeConfiguration.builder()
                    .key(BRAND_FIELD)
                    .path(BRAND_FIELD)
                    .displayName(BRAND_DISPLAY_NAME)
                    .indexable(true)
                    .retrievable(true)
                    .build(),
                COLOR_FIELD, AttributeConfiguration.builder()
                    .key(COLOR_FIELD)
                    .path(COLOR_FIELD)
                    .displayName(COLOR_DISPLAY_NAME)
                    .indexable(true)
                    .retrievable(true)
                    .build(),
                SIZE_FIELD, AttributeConfiguration.builder()
                    .key(SIZE_FIELD)
                    .path(SIZE_FIELD)
                    .displayName(SIZE_DISPLAY_NAME)
                    .indexable(true)
                    .retrievable(true)
                    .build()
            ),
            INSTANT_SUPPLIER,
            null,
            List.of(),
            new Features()
        );

        mongoSearchFilterService = new MongoSearchFilterService();
    }

    @Test
    @DisplayName("Test multi-select navigation identification in PN expansion")
    void testMultiSelectNavigationIdentification() {
        // given
        var refinements = List.of(
            SelectedRefinement.builder()
                .field(BRAND_FIELD)
                .type(NavigationType.VALUE)
                .value(NIKE_VALUE)
                .or(false)
                .build(),
            SelectedRefinement.builder()
                .field(COLOR_FIELD)
                .type(NavigationType.VALUE)
                .value(RED_VALUE)
                .or(false)
                .build()
        );

        var searchParameters = SearchParameters.builder()
            .merchandisingConfiguration(merchandisingConfiguration)
            .query(TEST_QUERY)
            .refinements(refinements)
            .partNumberSearchEnabled(true)
            .build();

        // when
        var multiSelectNavigations = getActiveMultiSelectFields(searchParameters);

        // then
        assertThat(multiSelectNavigations).containsExactlyInAnyOrder(BRAND_FIELD, COLOR_FIELD);
    }

    @Test
    @DisplayName("Test creation of filter for multi-select facet")
    @SuppressWarnings("unchecked")
    void testCreateFilterForMultiSelectFacet() {
        // given
        var refinements = List.of(
            SelectedRefinement.builder()
                .field(BRAND_FIELD)
                .type(NavigationType.VALUE)
                .value(NIKE_VALUE)
                .or(false)
                .build(),
            SelectedRefinement.builder()
                .field(COLOR_FIELD)
                .type(NavigationType.VALUE)
                .value(RED_VALUE)
                .or(false)
                .build(),
            SelectedRefinement.builder()
                .field(SIZE_FIELD)
                .type(NavigationType.VALUE)
                .value(LARGE_VALUE)
                .or(false)
                .build()
        );

        var originalParams = SearchParameters.builder()
            .merchandisingConfiguration(merchandisingConfiguration)
            .refinements(refinements)
            .build();

        // when
        Document result = mongoSearchFilterService.createFilterForMultiSelectFacet(
            originalParams,
            BRAND_FIELD,
            Set.of(PRODUCT_1, PRODUCT_2, PRODUCT_3)
        );

        // then
        assertThat(result).isNotNull();
        assertThat(result).containsKey(FILTER_KEY);


        // Verify the filter structure contains the expected elements
        var filters = (List<Document>) result.get(FILTER);
        assertThat(filters).isNotEmpty();

        // Verify product ID filter is present with the "in" operator
        boolean hasProductIdFilter = filters.stream()
            .anyMatch(filter -> filter.containsKey(IN) &&
                filter.get(IN, Document.class).containsKey(PATH) &&
                filter.get(IN, Document.class).containsKey(VALUE));
        assertThat(hasProductIdFilter)
            .withFailMessage("Filter should contain product ID filter with 'in' operator")
            .isTrue();

        // Verify the product ID filter contains the expected product IDs
        var productIdFilter = filters.stream()
            .filter(filter -> filter.containsKey(IN))
            .map(filter -> filter.get(IN, Document.class))
            .filter(inClause -> inClause.containsKey(VALUE))
            .findFirst();

        assertThat(productIdFilter).isPresent();

        var productIds = (List<String>) productIdFilter.get().get(VALUE);
        assertThat(productIds).containsExactlyInAnyOrder(PRODUCT_1, PRODUCT_2, PRODUCT_3);
    }

    @Test
    @DisplayName("Test dynamic facets handling - multiSelect behavior")
    void testDynamicFacetsMultiSelectBehavior() {
        // given
        var dynamicAttribute = AttributeConfiguration.builder()
            .key(DYNAMIC_TEST_FIELD)
            .path(DYNAMIC_TEST_FIELD)
            .displayName(DYNAMIC_TEST_DISPLAY_NAME)
            .indexable(true)
            .retrievable(true)
            .dynamicFacetable(true)
            .build();

        // Create an updated merchandising configuration with the dynamic attribute
        var existingAttributes = new HashMap<>(merchandisingConfiguration.attributeConfigurations());
        existingAttributes.put(DYNAMIC_TEST_FIELD, dynamicAttribute);

        var updatedMerchandisingConfig = merchandisingConfiguration.toBuilder()
            .attributeConfigurations(existingAttributes)
            .build(
            );

        var searchParameters = SearchParameters.builder()
            .merchandisingConfiguration(updatedMerchandisingConfig)
            .query(TEST_QUERY)
            .refinements(List.of(
                SelectedRefinement.builder()
                    .field(DYNAMIC_TEST_FIELD)
                    .type(NavigationType.VALUE)
                    .value(TEST_VALUE)
                    .or(false)
                    .build()
            ))
            .partNumberSearchEnabled(true)
            .build();

        // when
        Set<String> activeMultiSelectFields = getActiveMultiSelectFields(searchParameters);

        // then
        assertThat(activeMultiSelectFields).containsExactly(DYNAMIC_TEST_FIELD);
    }

}
