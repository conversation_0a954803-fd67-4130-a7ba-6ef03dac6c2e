package com.groupbyinc.search.ssa.mongo.filtering;

import com.groupbyinc.search.ssa.antlr.MongoSearchRawFilterVisitor;
import com.groupbyinc.search.ssa.api.error.ProcessingException;
import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;
import com.groupbyinc.search.ssa.mongo.filtering.search.expression.text.MongoSearchTextExpression;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_MAX_N;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.COMPOUND;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.EQUALS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.FILTER;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.GT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.GTE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.IN;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.LT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.LTE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.SHOULD;
import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.defaultAttributeConfigs;
import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.getMerchandisingConfiguration;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.buildRawFilters;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.caseBodyWithIsArrayCheck;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.createDocumentForRangeSearchFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.createDocumentForSearchFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.createInventoryFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.createInventorySortFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.getBoundOperatorForArray;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.getFieldToPathFunction;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.getInternalFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.getInternalShould;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.hasInternalFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.hasInternalShould;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.hasRawFilters;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.parseInventoryIfExists;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.parseRange;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.switchWithOneCase;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.wrapWithMustNotBasedOnInternalStructure;
import static com.groupbyinc.search.ssa.stub.ResourceUtils.getResourceContents;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

@DisplayName("MongoFilterUtils Tests")
public class MongoFilterUtilsTest {

    @Test
    @DisplayName("Test for getFieldToPathFunction function")
    public void getFieldToPathFunctionShouldConvertFiltersToPathInObject() {
        var searchParameters = SearchParameters.builder()
            .merchandisingConfiguration(getMerchandisingConfiguration())
            .build();

        assertTransform(searchParameters, "price", "priceInfo.price");
        assertTransform(searchParameters, "originalPrice", "priceInfo.originalPrice");
        assertTransform(searchParameters, "currencyCode", "priceInfo.currencyCode");
        assertTransform(searchParameters, "cost", "priceInfo.cost");
        assertTransform(searchParameters, "colorFamilies", "colorInfo.colorFamilies");
        assertTransform(searchParameters, "colors", "colorInfo.colors");
        assertTransform(searchParameters, "rating", "rating.averageRating");
        assertTransform(searchParameters, "averageRating", "rating.averageRating");
        assertTransform(searchParameters, "ratingCount", "rating.ratingCount");
        assertTransform(searchParameters, "ratingHistogram", "rating.ratingHistogram");
        assertTransform(
            searchParameters,
            "inventory(5, original_price)", "inventory(5, localInventories.priceInfo.originalPrice)"
        );
        assertTransform(
            searchParameters,
            "inventory(5, price)", "inventory(5, localInventories.priceInfo.price)"
        );
        assertTransform(
            searchParameters,
            "inventory(5, attributes.category)", "inventory(5, localInventories.attributes.category)"
        );
        assertTransform(
            searchParameters,
            "inventory(5, attributes.totalSalesCount)", "inventory(5, localInventories.attributes.totalSalesCount)"
        );
    }

    private void assertTransform(SearchParameters searchParameters, String given, String expected) {
        assertEquals(expected, getFieldToPathFunction(searchParameters).apply(given));
    }

    @Test
    @DisplayName("Test raw filter building (No filters)")
    public void rawFilterBuildingNoFiltersTest() {
        var visitor = new MongoSearchRawFilterVisitor(defaultAttributeConfigs());

        var result = buildRawFilters(Optional.empty(), visitor, mock(Logger.class));

        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Test raw filter building")
    public void rawFilterBuildingFilterTest() {
        var visitor = new MongoSearchRawFilterVisitor(defaultAttributeConfigs());
        var expected = """
            {
              "compound": {
                "filter": [
                  {
                    "equals": {
                      "value": "red",
                      "path": "indexables.colorInfo.colorFamilies"
                    }
                  }
                ]
              }
            }
            """;

        var result = buildRawFilters(Optional.of("colorFamilies: ANY(\"red\")"), visitor, mock(Logger.class));

        assertTrue(result.isPresent());
        assertThat(result.get().toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("Test hasRawFilters method")
    public void hasRawFiltersTests() {
        var searchParameters = SearchParameters
            .builder()
            .merchandisingConfiguration(getMerchandisingConfiguration())
            .build();

        var result = hasRawFilters(searchParameters);
        assertFalse(result);

        searchParameters = searchParameters.toBuilder().preFilter("colorFamilies: ANY(\"red\")").build();
        result = hasRawFilters(searchParameters);
        assertTrue(result);

        searchParameters = searchParameters
            .toBuilder()
            .merchandisingConfiguration(
                new MerchandisingConfiguration(
                    null,
                    Set.of(),
                    Set.of(),
                    Set.of(),
                    List.of(),
                    defaultAttributeConfigs(),
                    null,
                    new SiteFilterConfiguration(1, 1, "name", "colorFamilies: ANY(\"red\")", null),
                    List.of(),
                    null
                )
            )
            .preFilter(null)
            .build();
        result = hasRawFilters(searchParameters);
        assertTrue(result);

        searchParameters = searchParameters
            .toBuilder()
            .preFilter("colorFamilies: ANY(\"red\")")
            .build();
        result = hasRawFilters(searchParameters);
        assertTrue(result);
    }

    @Test
    @DisplayName("Test parseRange method")
    public void parseRangeTests() {
        var parsed = List.of(
            parseRange("1", "2", GTE, GT, LT, LTE),   //0
            parseRange("1i", "2", GTE, GT, LT, LTE),  //1
            parseRange("1", "2i", GTE, GT, LT, LTE),  //2
            parseRange("1e", "2", GTE, GT, LT, LTE),  //3
            parseRange("1", "2e", GTE, GT, LT, LTE),  //4
            parseRange("1i", "2i", GTE, GT, LT, LTE), //5
            parseRange("1e", "2e", GTE, GT, LT, LTE), //6
            parseRange("1e", "2i", GTE, GT, LT, LTE), //7
            parseRange("1i", "2e", GTE, GT, LT, LTE), //8
            parseRange("*", "2", GTE, GT, LT, LTE),   //9
            parseRange("*", "2i", GTE, GT, LT, LTE),  //10
            parseRange("*", "2e", GTE, GT, LT, LTE),  //11
            parseRange("1", "*", GTE, GT, LT, LTE),   //12
            parseRange("1i", "*", GTE, GT, LT, LTE),  //13
            parseRange("1e", "*", GTE, GT, LT, LTE)   //14
        );

        var expected = List.of(
            new MongoFilterUtils.ParsedRange(1.0, 2.0, GTE, LT),                 //0
            new MongoFilterUtils.ParsedRange(1.0, 2.0, GTE, LT),                 //1
            new MongoFilterUtils.ParsedRange(1.0, 2.0, GTE, LTE),                //2
            new MongoFilterUtils.ParsedRange(1.0, 2.0, GT, LT),                  //3
            new MongoFilterUtils.ParsedRange(1.0, 2.0, GTE, LT),                 //4
            new MongoFilterUtils.ParsedRange(1.0, 2.0, GTE, LTE),                //5
            new MongoFilterUtils.ParsedRange(1.0, 2.0, GT, LT),                  //6
            new MongoFilterUtils.ParsedRange(1.0, 2.0, GT, LTE),                 //7
            new MongoFilterUtils.ParsedRange(1.0, 2.0, GTE, LT),                 //8
            new MongoFilterUtils.ParsedRange(Double.POSITIVE_INFINITY, 2.0, GTE, LT),  //9
            new MongoFilterUtils.ParsedRange(Double.POSITIVE_INFINITY, 2.0, GTE, LTE), //10
            new MongoFilterUtils.ParsedRange(Double.POSITIVE_INFINITY, 2.0, GTE, LT),  //11
            new MongoFilterUtils.ParsedRange(1.0, Double.POSITIVE_INFINITY, GTE, LT),  //12
            new MongoFilterUtils.ParsedRange(1.0, Double.POSITIVE_INFINITY, GTE, LT),  //13
            new MongoFilterUtils.ParsedRange(1.0, Double.POSITIVE_INFINITY, GT, LT)    //14
        );

        for (var i = 0; i < parsed.size(); i++) {
            assertThat(parsed.get(i))
                .withFailMessage(parsed.get(i) + " not equal to: " + expected.get(i) + " i: " + i)
                .isEqualTo(expected.get(i));
        }
    }

    @Test
    @DisplayName("Test createDocumentForSearchFilter method")
    public void createDocumentForSearchFilterTests() {
        var result = createDocumentForSearchFilter(EQUALS, "sizes", "XL");
        var expected = "{\"equals\": {\"value\": \"XL\",\"path\": \"sizes\"}}";

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = createDocumentForSearchFilter(IN, "sizes", List.of("XL", "L"));
        expected = "{\"in\": {\"value\": [\"XL\", \"L\"],\"path\": \"sizes\"}}";

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("Test createDocumentForRangeSearchFilter method")
    public void createDocumentForRangeSearchFilterTests() {
        var result = createDocumentForRangeSearchFilter("price", 10, null, GT, null);
        var expected = Document.parse("{\"range\": {\"path\": \"price\", \"gt\": 10}}");

        assertThat(result.toJson()).isEqualTo(expected.toJson());

        result = createDocumentForRangeSearchFilter("price", 10, null, LT, null);
        expected = Document.parse("{\"range\": {\"path\": \"price\", \"lt\": 10}}");

        assertThat(result.toJson()).isEqualTo(expected.toJson());

        result = createDocumentForRangeSearchFilter("price", 10, 1200, LT, GTE);
        expected = Document.parse("{\"range\": {\"path\": \"price\", \"lt\": 10, \"gte\": 1200}}");

        assertThat(result.toJson()).isEqualTo(expected.toJson());
    }

    @Test
    @DisplayName("Test hasInternalFilter method")
    public void hasInternalFilterTests() {
        var doc = new Document(
            COMPOUND,
            new Document(
                FILTER,
                List.of(new Document())
            )
        );

        var result = hasInternalFilter(doc);
        assertTrue(result);

        doc = new Document(
            COMPOUND,
            new Document(
                SHOULD,
                List.of(new Document())
            )
        );

        result = hasInternalFilter(doc);
        assertFalse(result);

        doc = new Document(
            COMPOUND,
            new Document(
                EQUALS,
                "test"
            )
        );

        result = hasInternalFilter(doc);
        assertFalse(result);
    }

    @Test
    @DisplayName("Test hasInternalShould method")
    public void hasInternalShouldTests() {
        var doc = new Document(
            COMPOUND,
            new Document(
                SHOULD,
                List.of(new Document())
            )
        );

        var result = hasInternalShould(doc);
        assertTrue(result);

        doc = new Document(
            COMPOUND,
            new Document(
                FILTER,
                List.of(new Document())
            )
        );

        result = hasInternalShould(doc);
        assertFalse(result);

        doc = new Document(
            COMPOUND,
            new Document(
                EQUALS,
                "test"
            )
        );

        result = hasInternalShould(doc);
        assertFalse(result);
    }

    @Test
    @DisplayName("Test getInternalFilter method")
    public void getInternalFilterTests() {
        var doc = new Document(
            COMPOUND,
            new Document(
                FILTER,
                List.of(new Document("key",  "value"))
            )
        );

        var result = getInternalFilter(doc);
        assertThat(result.size()).isEqualTo(1);
        assertThat(result.getFirst().toJson()).isEqualTo("{\"key\": \"value\"}");


        try {
            doc = new Document(
                COMPOUND,
                new Document(
                    EQUALS,
                    "test"
                )
            );
            getInternalFilter(doc);
        } catch (ProcessingException e) {
            assertThat(e).isInstanceOf(ProcessingException.class);
        }
    }

    @Test
    @DisplayName("Test getInternalShould method")
    public void getInternalShouldTests() {
        var doc = new Document(
            COMPOUND,
            new Document(
                SHOULD,
                List.of(new Document("key",  "value"))
            )
        );

        var result = getInternalShould(doc);
        assertThat(result.size()).isEqualTo(1);
        assertThat(result.getFirst().toJson()).isEqualTo("{\"key\": \"value\"}");


        try {
            doc = new Document(
                COMPOUND,
                new Document(
                    EQUALS,
                    "test"
                )
            );
            getInternalShould(doc);
        } catch (ProcessingException e) {
            assertThat(e).isInstanceOf(ProcessingException.class);
        }
    }

    @Test
    @DisplayName("Test wrapWithMustNotBasedOnInternalStructure method")
    public void wrapWithMustNotBasedOnInternalStructureTests() {
        var should = new Document(
            COMPOUND,
            new Document(
                SHOULD,
                List.of(new Document("key",  "value"))
            )
        );
        var filter = new Document(
            COMPOUND,
            new Document(
                FILTER,
                List.of(new Document("key",  "value"))
            )
        );
        var equals = new Document(
            COMPOUND,
            new Document(
                EQUALS,
                "test"
            )
        );

        var result = wrapWithMustNotBasedOnInternalStructure(should);
        assertThat(result.toJson())
            .isEqualTo(Document.parse("{\"compound\":{\"mustNot\":[{\"key\":\"value\"}]}}").toJson());

        result = wrapWithMustNotBasedOnInternalStructure(filter);
        assertThat(result.toJson())
            .isEqualTo(Document.parse("{\"compound\":{\"mustNot\":[{\"key\":\"value\"}]}}").toJson());

        result = wrapWithMustNotBasedOnInternalStructure(equals);
        assertThat(result.toJson())
            .isEqualTo(Document.parse("{\"compound\":{\"mustNot\":[{\"compound\":{\"equals\":\"test\"}}]}}").toJson());
    }

    @Test
    @DisplayName("Test createInventoryFilter method")
    public void createInventoryFilterTests() {
        var filter = new MongoSearchTextExpression("localInventories.sizes", "10", List.of("XL", "X", "L")).toFilter();
        var expected = getResourceContents("mongo/filtering/mongo-filtering-utils-create-inventory-filter.json");

        var result = createInventoryFilter("10", filter);

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("Test caseBodyWithIsArrayCheck method")
    public void caseBodyWithIsArrayCheckTests() {
        var filter = new MongoSearchTextExpression("localInventories.sizes", "10", List.of("XL", "X", "L")).toFilter();
        var expected = getResourceContents(
            "mongo/filtering/mongo-filtering-utils-case-body-with-is-array-check.json"
        );

        var result = caseBodyWithIsArrayCheck("indexables.sizes", filter);

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("Test switchWithOneCase method")
    public void switchWithOneCaseTests() {
        var caseFilter = new MongoSearchTextExpression("sizes", List.of("XL", "X", "L")).toFilter();
        var defaultFilter = new MongoSearchTextExpression("brands", List.of("Puma")).toFilter();

        var expected = """
           {
             "$switch": {
               "branches": [
                 {
                   "in": {
                     "value": [
                       "XL",
                       "X",
                       "L"
                     ],
                     "path": "indexables.sizes"
                   }
                 }
               ],
               "default": {
                 "equals": {
                   "value": "Puma",
                   "path": "indexables.brands"
                 }
               }
             }
           }
        """;

        var result = switchWithOneCase(caseFilter, defaultFilter);

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("Test getBoundOperatorForArray method")
    public void getBoundOperatorForArrayTests() {
        var expected = """
           {
             "$arrayElemAt": [
               {
                 "$maxN": {
                   "n": 1,
                   "input": "indexables.brands"
                 }
               },
               0
             ]
           }
        """;

        var result = getBoundOperatorForArray("indexables.brands", $_MAX_N);

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("Test createInventorySortFilter method")
    public void createInventorySortFilterTests() {
        var filter = new MongoSearchTextExpression("localInventories.sizes", "10", List.of("XL", "X", "L")).toFilter();

        var expected = getResourceContents(
            "mongo/filtering/mongo-filtering-utils-create-inventory-sort-filter.json"
        );

        var result = createInventorySortFilter(filter, "10");

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("Test parseInventoryIfExists method")
    public void parseInventoryIfExistsTests() {
        var result = parseInventoryIfExists("brands", true);

        assertThat(result.field()).isEqualTo("brands");
        assertNull(result.placeId());

        result = parseInventoryIfExists("10.inventories.brands", true);

        assertThat(result.field()).isEqualTo("brands");
        assertThat(result.placeId()).isEqualTo("10");

        result = parseInventoryIfExists("inventory(10, brands)", true);

        assertThat(result.field()).isEqualTo("brands");
        assertThat(result.placeId()).isEqualTo("10");
    }

}
