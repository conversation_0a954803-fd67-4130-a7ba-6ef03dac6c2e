package com.groupbyinc.search.ssa.mongo.facet;

import com.groupbyinc.proto.commandcenter.config.AttributeMessage;
import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.navigation.Navigation;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.navigation.NavigationRefinement;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.groupbyinc.search.ssa.core.navigation.NavigationType.RANGE;

import static org.junit.jupiter.api.Assertions.assertEquals;

@DisplayName("MongoFacetConverterTest Tests")
class MongoFacetConverterTest {

    @Test
    @DisplayName("Refinements are reduced when Navigation.isMultiSelect = false in config")
    void convertFacetToNavigationWithMultiSelectFalse() {
        var navigationConfiguration = NavigationConfiguration
            .builder()
            .name("Price")
            .field("price")
            .multiSelect(false)
            .build();

        var searchParameters = buildSearchParameters(
            List.of(
                new SelectedRefinement(
                    "price",
                    RANGE,
                    new Range(10.0, 100.0),
                    null,
                    false,
                    true
                )
            ),
            Set.of(navigationConfiguration),
            Map.of(
                "price",
                AttributeConfiguration
                    .builder()
                    .key("price")
                    .displayName("price")
                    .type(AttributeMessage.AttributeType.NUMERICAL)
                    .build()
            )
        );

        var refinement1 = NavigationRefinement.rangeRefinement(new Range(10.0, 100.0), 112);
        var expectedNavigation = buildNavigation(navigationConfiguration, List.of(refinement1));

        var mongoFacetConverter = new MongoFacetConverter(searchParameters);
        var facetValue = buildFacet();

        var actualNavigation = mongoFacetConverter
            .convertFacetToNavigation(searchParameters, "price", facetValue)
            .orElse(null);
        assertEquals(expectedNavigation, actualNavigation);
    }

    @Test
    @DisplayName("Refinements are NOT reduced when Navigation.isMultiSelect = true in config")
    void convertFacetToNavigationWithMultiSelectTrue() {
        var navigationConfiguration = NavigationConfiguration
            .builder()
            .name("Price")
            .field("price")
            .multiSelect(true)
            .build();

        var searchParameters = buildSearchParameters(
            List.of(
                new SelectedRefinement(
                    "price",
                    RANGE,
                    new Range(10.0, 100.0),
                    null,
                    false,
                    true
                )
            ),
            Set.of(navigationConfiguration),
            Map.of(
                "price",
                AttributeConfiguration
                    .builder()
                    .key("price")
                    .displayName("price")
                    .type(AttributeMessage.AttributeType.NUMERICAL)
                    .build()
            )
        );
        var refinement1 = NavigationRefinement.rangeRefinement(new Range(10.0, 100.0), 112);
        var refinement2 = NavigationRefinement.rangeRefinement(new Range(100.0, 300.0), 50);
        var refinement3 = NavigationRefinement.rangeRefinement(new Range(300.0, null), 1);
        var expectedNavigation = buildNavigation(
            navigationConfiguration,
            List.of(refinement1, refinement2, refinement3)
        );

        var mongoFacetConverter = new MongoFacetConverter(searchParameters);
        var facetValue = buildFacet();

        var actualNavigation = mongoFacetConverter
            .convertFacetToNavigation(searchParameters, "price", facetValue)
            .orElse(null);
        assertEquals(expectedNavigation, actualNavigation);
    }

    private SearchParameters buildSearchParameters(List<SelectedRefinement> refinements,
                                                   Set<NavigationConfiguration> navigationConfigurations,
                                                   Map<String, AttributeConfiguration> attributeConfigurations) {
        return SearchParameters
            .builder()
            .merchandisingConfiguration(
                MerchandisingConfiguration
                    .builder()
                    .navigationConfigurations(navigationConfigurations)
                    .attributeConfigurations(attributeConfigurations)
                    .build()
            )
            .refinements(refinements)
            .build();
    }

    private Document buildFacet() {
        var buckets = Arrays.asList(
            new Document("_id", 10.0).append("count", 112L),
            new Document("_id", 100.0).append("count", 50L),
            new Document("_id", 300.0).append("count", 1L)
        );
        return new Document("buckets", buckets);
    }

    private Navigation buildNavigation(NavigationConfiguration configuration, List<NavigationRefinement> refinements) {
        return Navigation
            .builder()
            .name(configuration.name())
            .field(configuration.field())
            .type(RANGE)
            .refinements(refinements)
            .multiSelect(configuration.multiSelect())
            .metadata(null)
            .pinned(false)
            .build();
    }
}
