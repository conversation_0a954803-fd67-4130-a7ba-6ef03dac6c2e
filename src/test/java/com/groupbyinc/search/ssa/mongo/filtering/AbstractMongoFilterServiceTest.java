package com.groupbyinc.search.ssa.mongo.filtering;

import com.groupbyinc.search.ssa.core.SearchParameters;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.FIELDS_FOR_FILTERS;
import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.getAttributeFilters;
import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.getMerchandisingConfiguration;
import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.getSelectedRefinements;
import static com.groupbyinc.search.ssa.stub.ResourceUtils.getResourceContents;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("AbstractMongoFilterService Tests")
public class AbstractMongoFilterServiceTest {

    @Test
    @DisplayName("Test parsing of text expression. Collecting used attributes")
    public void shouldCollectAllAttributesUsedInFilter() {
        var raw = getResourceContents("antlr/raw-filter");

        var searchParameters = SearchParameters.builder()
            .preFilter(raw)
            .merchandisingConfiguration(getMerchandisingConfiguration())
            .attributeFilters(getAttributeFilters())
            .refinements(getSelectedRefinements())
            .build();

        var parsed = AbstractMongoFilterService.getUsedFields(searchParameters);

        assertThat(parsed.size()).isEqualTo(FIELDS_FOR_FILTERS.size());
        for (var field : FIELDS_FOR_FILTERS) {
            assertThat(parsed.contains(field)).isTrue();
        }
    }

}
