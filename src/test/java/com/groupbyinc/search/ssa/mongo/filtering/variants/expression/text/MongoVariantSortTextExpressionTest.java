package com.groupbyinc.search.ssa.mongo.filtering.variants.expression.text;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static com.groupbyinc.search.ssa.stub.ResourceUtils.getResourceContents;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("MongoVariantSortTextExpression Tests")
class MongoVariantSortTextExpressionTest {

    private static final String EXPECTED_FILE_PATH = "mongo/filtering/variants/expression/text/";

    @Test
    @DisplayName("MongoVariantSortTextExpression single value Test")
    public void mongoVariantSortTextExpressionSingleValueTest() {
        var result = new MongoVariantSortTextExpression("sizes", List.of("XL")).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH + "mongo-variant-sort-text-expression-single-value.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortTextExpression multiple values Test")
    public void mongoVariantSortTextExpressionMultipleValuesTest() {
        var result = new MongoVariantSortTextExpression("sizes", List.of("Xl", "L")).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH + "mongo-variant-sort-text-expression-multiple-values.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortTextExpression single value Inventory Test")
    public void mongoVariantSortTextExpressionSingleValueInventoryTest() {
        var result = new MongoVariantSortTextExpression("sizes", "place", List.of("XL")).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH +"mongo-variant-sort-text-expression-single-value-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortTextExpression multiple values Inventory Test")
    public void mongoVariantSortTextExpressionMultipleValuesInventoryTest() {
        var result = new MongoVariantSortTextExpression("sizes", "place", List.of("Xl", "L")).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH + "mongo-variant-sort-text-expression-multiple-values-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

}
