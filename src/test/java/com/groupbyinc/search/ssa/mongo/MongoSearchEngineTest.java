//package com.groupbyinc.search.ssa.mongo;
//
//import com.groupbyinc.search.ssa.application.cache.Cache;
//import com.groupbyinc.search.ssa.application.cache.CacheOperations;
//import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
//import com.groupbyinc.search.ssa.core.Order;
//import com.groupbyinc.search.ssa.core.Pagination;
//import com.groupbyinc.search.ssa.core.SearchParameters;
//import com.groupbyinc.search.ssa.core.SearchResults;
//import com.groupbyinc.search.ssa.core.Sort;
//import com.groupbyinc.search.ssa.core.area.AreaConfiguration;
//import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
//import com.groupbyinc.search.ssa.core.biasing.Bias;
//import com.groupbyinc.search.ssa.core.biasing.BiasingProfile;
//import com.groupbyinc.search.ssa.core.biasing.NumericContent;
//import com.groupbyinc.search.ssa.core.features.Features;
//import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
//import com.groupbyinc.search.ssa.core.navigation.NavigationType;
//import com.groupbyinc.search.ssa.core.navigation.Range;
//import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
//import com.groupbyinc.search.ssa.features.FeaturesManager;
//import com.groupbyinc.search.ssa.mongo.converter.MongoBoostFactor;
//import com.groupbyinc.search.ssa.mongo.converter.MongoRequestConverter;
//import com.groupbyinc.search.ssa.mongo.converter.MongoResponseConverter;
//import com.groupbyinc.search.ssa.mongo.facet.MongoFacetProvider;
//import com.groupbyinc.search.ssa.mongo.filtering.search.MongoSearchFilterService;
//import com.groupbyinc.search.ssa.mongo.filtering.variants.MongoVariantsSortFilterService;
//import com.groupbyinc.search.ssa.mongo.settings.MongoFeatureSettings;
//import com.groupbyinc.search.ssa.mongo.settings.MongoSettingsStorage;
//import com.groupbyinc.search.ssa.redis.CacheResponse;
//import com.groupbyinc.search.ssa.redis.key.browse.BrowseCacheConfig;
//import com.groupbyinc.search.ssa.redis.key.mongo.MongoCacheKeyGenerator;
//import com.groupbyinc.search.ssa.stub.ResourceUtils;
//import com.groupbyinc.search.ssa.util.PrefixTreeMap;
//
//import com.mongodb.client.AggregateIterable;
//import com.mongodb.client.MongoClient;
//import com.mongodb.client.MongoCollection;
//import com.mongodb.client.MongoDatabase;
//import org.bson.BsonArray;
//import org.bson.BsonDocument;
//import org.bson.BsonValue;
//import org.bson.Document;
//import org.bson.conversions.Bson;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Test;
//import org.mockito.ArgumentCaptor;
//
//import java.time.Instant;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.Optional;
//import java.util.Set;
//import java.util.TreeSet;
//import java.util.concurrent.TimeUnit;
//import java.util.function.Supplier;
//
//import static com.groupbyinc.search.ssa.core.navigation.NavigationType.RANGE;
//import static com.groupbyinc.search.ssa.core.navigation.NavigationType.VALUE;
//import static com.groupbyinc.search.ssa.stub.TestData.DEFAULT_CONTEXT;
//
//import static org.assertj.core.api.Assertions.assertThat;
//import static org.junit.jupiter.api.Assertions.assertFalse;
//import static org.junit.jupiter.api.Assertions.assertNotNull;
//import static org.junit.jupiter.api.Assertions.assertTrue;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anyList;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.times;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.when;
//
//@SuppressWarnings({ "unchecked", "rawtypes" })
//@DisplayName("MongoSearchEngine Tests")
//class MongoSearchEngineTest {
//
//    private static final Supplier<Instant> INSTANT_SUPPLIER = () ->
//        Instant.parse("2021-03-15T15:34:05.183275Z").plusMillis(53);
//    private static final MerchandisingConfiguration MERCHANDISING_DEFAULT_CONFIGURATION
//    = new MerchandisingConfiguration(
//        AreaConfiguration.builder().build(),
//        new TreeSet<>(),
//        new TreeSet<>(),
//        new TreeSet<>(),
//        List.of(),
//        new HashMap<>(),
//        INSTANT_SUPPLIER,
//        null,
//        List.of(),
//        new Features()
//    );
//    private static final MerchandisingConfiguration MERCHANDISING_CONFIGURATION = new MerchandisingConfiguration(
//        AreaConfiguration.builder().build(),
//        new TreeSet<>(),
//        Set.of(
//            NavigationConfiguration.builder()
//                .id(1)
//                .name("attributes.brandId")
//                .field("attributes.brandId")
//                .type(VALUE)
//                .priority(1)
//                .multiSelect(false)
//                .build(),
//            NavigationConfiguration.builder()
//                .id(2)
//                .name("attributes.promotion_type")
//                .field("inventory(200,attributes.promotion_type):ANY(\"PROMO TALON\")")
//                .type(VALUE)
//                .priority(1)
//                .multiSelect(false)
//                .build(),
//            NavigationConfiguration.builder()
//                .id(3)
//                .name("attributes.discountPercentage")
//                .field("attributes.discountPercentage")
//                .type(RANGE)
//                .priority(2)
//                .multiSelect(false)
//                .build(),
//            NavigationConfiguration.builder()
//                .id(4)
//                .name("attributes.weight")
//                .field("attributes.weight")
//                .type(RANGE)
//                .priority(3)
//                .multiSelect(false)
//                .build()
//        ),
//        new TreeSet<>(),
//        List.of(),
//        Map.of("attributes.discountPercentage", AttributeConfiguration.builder()
//                .key("attributes.discountPercentage")
//                .path("attributes.discountPercentage")
//                .displayName("attributes.discountPercentage")
//                .indexable(true)
//                .retrievable(true)
//                .build(),
//            "attributes.brandId", AttributeConfiguration.builder()
//                .key("attributes.brandId")
//                .path("attributes.brandId")
//                .displayName("attributes.brandId")
//                .indexable(true)
//                .retrievable(true)
//                .build(),
//            "attributes.promotion_type", AttributeConfiguration.builder()
//                .key("attributes.promotion_type")
//                .path("attributes.promotion_type")
//                .displayName("attributes.promotion_type")
//                .indexable(true)
//                .retrievable(true)
//                .build(),
//            "attributes.weight", AttributeConfiguration.builder()
//                .key("attributes.weight")
//                .path("attributes.weight")
//                .displayName("attributes.weight")
//                .indexable(true)
//                .retrievable(true)
//                .build(),
//            "attributes.size", AttributeConfiguration.builder()
//                .key("attributes.size")
//                .path("attributes.size")
//                .displayName("attributes.size")
//                .indexable(true)
//                .retrievable(true)
//                .build(),
//            "price", AttributeConfiguration.builder()
//                .key("price")
//                .path("priceInfo.price")
//                .displayName("price")
//                .indexable(true)
//                .retrievable(true)
//                .build()
//        ),
//        INSTANT_SUPPLIER,
//        null,
//        List.of(),
//        new Features()
//    );
//
//    private MongoSearchEngine mongoSearchEngine;
//    private MongoCollection mongoCollection;
//
//    @BeforeEach
//    void setUp() {
//        var mongoClient = mock(MongoClient.class);
//        var browse = mock(Cache.class);
//        var featuresManager = mock(FeaturesManager.class);
//        var baseCacheOperations = mock(CacheOperations.class);
//        var mongoCacheKeyGenerator = mock(MongoCacheKeyGenerator.class);
//        var mongoFacetProvider = mock(MongoFacetProvider.class);
//        var mongoSettingsStorage = mock(MongoSettingsStorage.class);
//
//        var mongoRequestConvertor = new MongoRequestConverter(
//            mongoFacetProvider,
//            mongoSettingsStorage,
//            new MongoSearchFilterService(),
//            new MongoVariantsSortFilterService()
//        );
//        var mongoResponseConverter = new MongoResponseConverter();
//
//        var mongoDatabase = mock(MongoDatabase.class);
//        when(mongoClient.getDatabase(any())).thenReturn(mongoDatabase);
//
//        mongoCollection = mock(MongoCollection.class);
//        when(mongoDatabase.getCollection(any())).thenReturn(mongoCollection);
//
//        var mongoIterable = mock(AggregateIterable.class);
//        when(mongoCollection.aggregate(any())).thenReturn(mongoIterable);
//        when(mongoIterable.maxAwaitTime(1, TimeUnit.SECONDS)).thenReturn(mongoIterable);
//        when(mongoIterable.map(any())).thenReturn(mongoIterable);
//        when(mongoIterable.into(anyList())).thenAnswer(invocation -> {
//            List<Document> target = invocation.getArgument(0);
//            target.add(Document.parse(ResourceUtils.getResourceContents(
//                  "mongo/document-with-search-records-and-meta.json"))
//            );
//            return target;
//        });
//
//        when(
//            featuresManager.getObjectFlagConfiguration(any(), any(), any(), any())
//        ).thenReturn(MongoFeatureSettings.DEFAULT);
//
//        when(baseCacheOperations.getFromCache(any(), any(), any(), any(), any(), any()))
//            .thenReturn(new CacheResponse<>(BrowseCacheConfig.DEFAULT, Optional.empty(), Optional.empty()));
//
//        mongoSearchEngine = new MongoSearchEngine(
//            mongoClient,
//            browse,
//            baseCacheOperations,
//            mongoFacetProvider,
//            mongoSettingsStorage,
//            mongoRequestConvertor,
//            mongoResponseConverter,
//            mongoCacheKeyGenerator
//        );
//    }
//
//    @Test
//    @DisplayName("Invokes Mongo search with default search query")
//    void queryFromMongoWithDefaultSearchQuery() {
//        var searchParameters = SearchParameters.builder()
//            .context(DEFAULT_CONTEXT)
//            .merchandisingConfiguration(MERCHANDISING_DEFAULT_CONFIGURATION)
//            .prefixTreeMap(PrefixTreeMap.fromList(List.of("*")))
//            .debug(true)
//            .build();
//
//        var response = mongoSearchEngine.search(searchParameters);
//
//        ArgumentCaptor<List<Bson>> captor = ArgumentCaptor.forClass(List.class);
//        verify(mongoCollection).aggregate(captor.capture());
//
//        var queryPipeline = captor.getValue();
//        assertThat(queryPipeline).hasSize(6);
//
//        assertDefaultSearchStage(queryPipeline);
//        assertLimitStage(queryPipeline, 10);
//        assertAddFieldsStage(queryPipeline);
//        assertProjectStage(queryPipeline);
//        assertFacetStageOnIndex(queryPipeline);
//        assertSetStageOnIndex(queryPipeline);
//
//        assertResponse(response);
//    }
//
//    @Test
//    @DisplayName("Invokes Mongo search with default search query and pagination options")
//    void queryFromMongoWithDefaultSearchQueryAndPaginationOptions() {
//        var searchParameters = SearchParameters.builder()
//            .context(DEFAULT_CONTEXT)
//            .merchandisingConfiguration(MERCHANDISING_DEFAULT_CONFIGURATION)
//            .prefixTreeMap(PrefixTreeMap.fromList(List.of("*")))
//            .pagination(new Pagination(20, 50L))
//            .debug(true)
//            .build();
//
//        var response = mongoSearchEngine.search(searchParameters);
//
//        ArgumentCaptor<List<Bson>> captor = ArgumentCaptor.forClass(List.class);
//        verify(mongoCollection).aggregate(captor.capture());
//
//        var queryPipeline = captor.getValue();
//        assertThat(queryPipeline).hasSize(7);
//
//        assertDefaultSearchStage(queryPipeline);
//        assertSkipStage(queryPipeline);
//        assertLimitStage(queryPipeline, 20);
//        assertAddFieldsStage(queryPipeline);
//        assertProjectStage(queryPipeline);
//        assertFacetStageOnIndex(queryPipeline);
//        assertSetStageOnIndex(queryPipeline);
//
//        assertResponse(response);
//    }
//
//    @Test
//    @DisplayName("Invokes Mongo search with search query and refinements")
//    void queryFromMongoWithDefaultSearchQueryAndRefinements() {
//        var refinements = List.of(
//            SelectedRefinement.builder()
//                .field("count")
//                .type(NavigationType.RANGE)
//                .range(new Range(0.0, 100.0, "boundaries"))
//                .or(false)
//                .build(),
//            SelectedRefinement.builder()
//                .field("title")
//                .type(VALUE)
//                .value("Product Title")
//                .or(false)
//                .build(),
//            SelectedRefinement.builder()
//                .field("color")
//                .type(VALUE)
//                .value("Red")
//                .or(false)
//                .build());
//        var searchParameters = SearchParameters.builder()
//            .context(DEFAULT_CONTEXT)
//            .merchandisingConfiguration(MERCHANDISING_DEFAULT_CONFIGURATION)
//            .prefixTreeMap(PrefixTreeMap.fromList(List.of("*")))
//            .refinements(refinements)
//            .debug(true)
//            .build();
//
//        var response = mongoSearchEngine.search(searchParameters);
//
//        ArgumentCaptor<List<Bson>> captor = ArgumentCaptor.forClass(List.class);
//        verify(mongoCollection).aggregate(captor.capture());
//
//        var queryPipeline = captor.getValue();
//        assertThat(queryPipeline).hasSize(6);
//
//        assertSearchStageWithRefinementsAndNoFacets(queryPipeline, refinements);
//        assertLimitStage(queryPipeline, 10);
//        assertAddFieldsStage(queryPipeline);
//        assertProjectStage(queryPipeline);
//        assertFacetStageOnIndex(queryPipeline);
//        assertSetStageOnIndex(queryPipeline);
//
//        assertResponse(response);
//    }
//
//    @Test
//    @DisplayName("Invokes Mongo search with search query, refinements and biases")
//    void queryFromMongoWithDefaultSearchQueryAndBiasingProfile() {
//        var includedNavigations = List.of("attributes.discountPercentage", "attributes.brandId");
//        var biasingProfile = BiasingProfile.builder()
//            .biases(List.of(
//                Bias.builder()
//                    .field("attributes.size")
//                    .content("Size")
//                    .strength(Bias.Strength.WEAK_DECREASE)
//                    .type(Bias.Type.TEXTUAL)
//                    .build(),
//                Bias.builder()
//                    .field("attributes.supplierId")
//                    .content("12345")
//                    .strength(Bias.Strength.MEDIUM_INCREASE)
//                    .type(Bias.Type.TEXTUAL)
//                    .build(),
//                Bias.builder()
//                    .field("price")
//                    .numericContent(NumericContent.builder()
//                        .ranges(List.of(new Range(100.0, 1000.0, "")))
//                        .build())
//                    .strength(Bias.Strength.MEDIUM_INCREASE)
//                    .type(Bias.Type.NUMERIC)
//                    .build()
//            ))
//            .build();
//        var searchParameters = SearchParameters.builder()
//            .context(DEFAULT_CONTEXT)
//            .merchandisingConfiguration(MERCHANDISING_CONFIGURATION)
//            .prefixTreeMap(PrefixTreeMap.fromList(List.of("*")))
//            .includedNavigations(includedNavigations)
//            .biasingProfile(biasingProfile)
//            .debug(true)
//            .build();
//
//        var response = mongoSearchEngine.search(searchParameters);
//
//        ArgumentCaptor<List<Bson>> captor = ArgumentCaptor.forClass(List.class);
//        verify(mongoCollection).aggregate(captor.capture());
//
//        var queryPipeline = captor.getValue();
//        assertThat(queryPipeline).hasSize(6);
//
//        assertSearchStageWithBiasingProfile(queryPipeline, biasingProfile);
//        assertLimitStage(queryPipeline, 10);
//        assertAddFieldsStage(queryPipeline);
//        assertProjectStage(queryPipeline);
//        assertFacetStageOnIndex(queryPipeline);
//        assertSetStageOnIndex(queryPipeline);
//
//        assertResponse(response);
//    }
//
//    @Test
//    @DisplayName("Invokes Mongo search with search query and included navigations")
//    void queryFromMongoWithDefaultSearchQueryAndIncludedNavigations() {
//        var includedNavigations = List.of("attributes.discountPercentage", "attributes.brandId");
//        var searchParameters = SearchParameters.builder()
//            .context(DEFAULT_CONTEXT)
//            .merchandisingConfiguration(MERCHANDISING_CONFIGURATION)
//            .prefixTreeMap(PrefixTreeMap.fromList(List.of("*")))
//            .includedNavigations(includedNavigations)
//            .debug(true)
//            .build();
//
//        var response = mongoSearchEngine.search(searchParameters);
//
//        ArgumentCaptor<List<Bson>> captor = ArgumentCaptor.forClass(List.class);
//        verify(mongoCollection).aggregate(captor.capture());
//
//        var queryPipeline = captor.getValue();
//        assertThat(queryPipeline).hasSize(6);
//
//        assertSearchStageWithIncludedNavigations(queryPipeline, includedNavigations);
//        assertLimitStage(queryPipeline, 10);
//        assertAddFieldsStage(queryPipeline);
//        assertProjectStage(queryPipeline);
//        assertFacetStageOnIndex(queryPipeline);
//        assertSetStageOnIndex(queryPipeline);
//
//        assertResponse(response);
//
//        // assert debug details when facets available
//        var debug = response.getDebugDetails();
//        assertThat(debug.getRawMongoFacetRequest()).isNotEmpty();
//        assertThat(debug.getRawMongoFacetResponse()).isNotEmpty();
//    }
//
//    @Test
//    @DisplayName("Invokes Mongo search with search query and included navigations present in refinements list")
//    void queryFromMongoWithDefaultSearchQueryAndIncludedNavigationsPresentInRefinementsList() {
//        var includedNavigations = List.of(
//        "attributes.discountPercentage", "attributes.brandId", "attributes.weight");
//        var refinements = List.of(
//            SelectedRefinement.builder()
//                .field("attributes.discountPercentage")
//                .type(NavigationType.RANGE)
//                .range(new Range(0.0, 100.0, "boundaries"))
//                .or(false)
//                .build(),
//            SelectedRefinement.builder()
//                .field("attributes.brandId")
//                .type(VALUE)
//                .value("Brand")
//                .or(false)
//                .build(),
//            SelectedRefinement.builder()
//                .field("inventory(200,attributes.promotion_type)")
//                .type(VALUE)
//                .value("PROMO TALON")
//                .or(false)
//                .build()
//        );
//        var searchParameters = SearchParameters.builder()
//            .context(DEFAULT_CONTEXT)
//            .merchandisingConfiguration(MERCHANDISING_CONFIGURATION)
//            .prefixTreeMap(PrefixTreeMap.fromList(List.of("*")))
//            .includedNavigations(includedNavigations)
//            .refinements(refinements)
//            .debug(true)
//            .build();
//
//        var response = mongoSearchEngine.search(searchParameters);
//
//        ArgumentCaptor<List<Bson>> captor = ArgumentCaptor.forClass(List.class);
//        verify(mongoCollection, times(3)).aggregate(captor.capture());
//
//        var queryPipelines = captor.getAllValues();
//
//        // assert $search query
//        var searchPipeline = queryPipelines.getFirst();
//        assertThat(searchPipeline).hasSize(6);
//
//        assertSearchStageWithIncludedNavigations(searchPipeline, includedNavigations);
//        assertLimitStage(searchPipeline, 10);
//        assertAddFieldsStage(searchPipeline);
//        assertProjectStage(searchPipeline);
//        assertFacetStageOnIndex(searchPipeline);
//        assertSetStageOnIndex(searchPipeline);
//
//        // assert first $searchMeta queries
//        assertSearchMetaStage(queryPipelines.get(1), "attributes.discountPercentage", refinements);
//        assertSearchMetaStage(queryPipelines.get(2), "attributes.brandId", refinements);
//
//        // assert debug details when facets available
//        var debug = response.getDebugDetails();
//        assertThat(debug.getRawMongoFacetRequest()).isNotEmpty();
//        assertThat(debug.getRawMongoFacetResponse()).isNotEmpty();
//    }
//
//    @Test
//    @DisplayName("Invokes Mongo search with default search query and refinements")
//    void queryFromMongoWithDefaultSearchQueryAndSortOptions() {
//        var sorts = List.of(
//            Sort.builder()
//                .field("price")
//                .order(Order.ASCENDING)
//                .build(),
//            Sort.builder()
//                .field("title")
//                .order(Order.DESCENDING)
//                .build());
//        var searchParameters = SearchParameters.builder()
//            .context(DEFAULT_CONTEXT)
//            .merchandisingConfiguration(MERCHANDISING_DEFAULT_CONFIGURATION)
//            .prefixTreeMap(PrefixTreeMap.fromList(List.of("*")))
//            .sorts(sorts)
//            .debug(true)
//            .build();
//
//        var response = mongoSearchEngine.search(searchParameters);
//
//        ArgumentCaptor<List<Bson>> captor = ArgumentCaptor.forClass(List.class);
//        verify(mongoCollection).aggregate(captor.capture());
//
//        var queryPipeline = captor.getValue();
//        assertThat(queryPipeline).hasSize(7);
//
//        assertDefaultSearchStage(queryPipeline);
//        assertLimitStage(queryPipeline, 10);
//        assertAddFieldsStage(queryPipeline);
//        assertProjectStage(queryPipeline);
//        assertSortStage(queryPipeline, sorts);
//        assertFacetStageOnIndex(queryPipeline);
//        assertSetStageOnIndex(queryPipeline);
//
//        assertResponse(response);
//    }
//
//    private void assertDefaultSearchStage(List<Bson> queryPipeline) {
//        assertTrue(queryPipeline.getFirst().toBsonDocument().containsKey("$search"));
//        var searchDoc = queryPipeline.getFirst().toBsonDocument().get("$search").asDocument();
//        assertThat(searchDoc.get("index").asString().getValue()).isEqualTo("productsClothing_search");
//        assertTrue(searchDoc.get("exists").asDocument().containsKey("path"));
//        assertThat(searchDoc.get("exists").asDocument().get("path").asString().getValue()).isEqualTo("id");
//        assertTrue(searchDoc.get("count").asDocument().containsKey("type"));
//        assertThat(searchDoc.get("count").asDocument().get("type").asString().getValue()).isEqualTo("total");
//    }
//
//    private void assertSearchStageWithRefinementsAndNoFacets(List<Bson> queryPipeline,
//    List<SelectedRefinement> refinements) {
//        assertTrue(queryPipeline.getFirst().toBsonDocument().containsKey("$search"));
//        var searchDoc = queryPipeline.getFirst().toBsonDocument().get("$search").asDocument();
//        assertThat(searchDoc.get("index").asString().getValue()).isEqualTo("productsClothing_search");
//        var filters = searchDoc.get("compound").asDocument().get("filter").asArray()
//            .stream()
//            .toList();
//
//        // assert included navigations
//        assertRefinements(refinements, filters, true);
//
//        assertTrue(searchDoc.get("count").asDocument().containsKey("type"));
//        assertThat(searchDoc.get("count").asDocument().get("type").asString().getValue()).isEqualTo("total");
//    }
//
//    private void assertSearchStageWithBiasingProfile(List<Bson> queryPipeline, BiasingProfile biasingProfile) {
//        assertTrue(queryPipeline.getFirst().toBsonDocument().containsKey("$search"));
//        var searchDoc = queryPipeline.getFirst().toBsonDocument().get("$search").asDocument();
//        assertThat(searchDoc.get("index").asString().getValue()).isEqualTo("productsClothing_search");
//        var biases = searchDoc
//            .get("facet").asDocument()
//            .get("operator").asDocument()
//            .get("compound").asDocument()
//            .get("should").asArray();
//
//        assertBiasingProfile(biasingProfile, biases);
//        assertTrue(searchDoc.get("count").asDocument().containsKey("type"));
//        assertThat(searchDoc.get("count").asDocument().get("type").asString().getValue()).isEqualTo("total");
//    }
//
//    private void assertSearchMetaStage(List<Bson> queryPipeline, String excludedNavigation,
//    List<SelectedRefinement> refinements) {
//        assertTrue(queryPipeline.getFirst().toBsonDocument().containsKey("$searchMeta"));
//        var searchDoc = queryPipeline.getFirst().toBsonDocument().get("$searchMeta").asDocument();
//        assertThat(searchDoc.get("index").asString().getValue()).isEqualTo("productsClothing_search");
//        var facets = searchDoc.get("facet").asDocument().get("facets").asDocument();
//
//        assertIncludedNavigations(List.of(excludedNavigation), facets);
//
//        var filters = searchDoc
//        .get("facet")
//        .asDocument().get("operator").asDocument().get("compound").asDocument().get("must").asArray()
//            .stream()
//            .toList();
//
//        var excludedRefinements = refinements
//            .stream()
//            .filter(x -> excludedNavigation.equals(x.getField()))
//            .toList();
//
//        // assert excluded navigations
//        assertRefinements(excludedRefinements, filters, false);
//    }
//
//    private void assertSearchStageWithIncludedNavigations(List<Bson> queryPipeline,
//    List<String> includedNavigations) {
//        assertTrue(queryPipeline.getFirst().toBsonDocument().containsKey("$search"));
//        var searchDoc = queryPipeline.getFirst().toBsonDocument().get("$search").asDocument();
//        assertThat(searchDoc.get("index").asString().getValue()).isEqualTo("productsClothing_search");
//        var facets = searchDoc.get("facet").asDocument().get("facets").asDocument();
//
//        assertIncludedNavigations(includedNavigations, facets);
//
//        assertThat(searchDoc.get("facet").asDocument().containsKey("operator")).isEqualTo(true);
//        assertTrue(searchDoc.get("count").asDocument().containsKey("type"));
//        assertThat(searchDoc.get("count").asDocument().get("type").asString().getValue()).isEqualTo("total");
//    }
//
//    private void assertBiasingProfile(BiasingProfile biasingProfile, BsonArray biases) {
//        for (int i = 0; i < biasingProfile.getBiases().size(); i++) {
//            var score = biases.get(i).asDocument()
//                .get("compound").asDocument()
//                .get("score").asDocument()
//                .get("boost").asDocument()
//                .get("value").asDouble().getValue();
//
//            var field = biases.get(i).asDocument()
//                .get("compound").asDocument()
//                .get("should").asArray()
//                .getFirst().asDocument();
//
//            String fieldName;
//
//            if (field.containsKey("exists")) {
//                fieldName = field.get("exists").asDocument().get("path").asString().getValue();
//            } else {
//                fieldName = "price";
//                if (field.containsKey("range")) {
//                    var paths = field.get("range").asDocument().get("path").asArray().stream()
//                        .map(bsonValue -> bsonValue.asString().getValue())
//                        .sorted()
//                        .toList();
//                    assertThat(paths).hasSize(2);
//                    assertThat(paths.get(0)).isEqualTo("priceInfo.price");
//                    assertThat(paths.get(1)).isEqualTo("variants.priceInfo.price");
//                }
//            }
//
//            var strength = Objects.requireNonNull(biasingProfile.getBiases().stream()
//                .filter(bias -> bias.getField().equals(fieldName)).findFirst().orElse(null)).getStrength();
//
//            assertThat(score).isEqualTo(MongoBoostFactor.getBiasFactor(strength));
//        }
//    }
//
//    private void assertIncludedNavigations(List<String> includedNavigations, BsonDocument facets) {
//        for (var navigation : includedNavigations) {
//            var isValue = MERCHANDISING_CONFIGURATION.navigationConfigurations()
//                .stream()
//                .filter(nav -> nav.field().equals(navigation))
//                .findFirst()
//                .map(x -> x.type() == NavigationType.VALUE)
//                .orElse(false);
//            if (facets.get(navigation) != null) {
//                var foundFacet = facets.get(navigation).asDocument();
//                if (isValue) {
//                    assertThat(foundFacet.asDocument().get("type").asString().getValue()).isEqualTo("string");
//                } else {
//                    assertThat(foundFacet.asDocument().get("type").asString().getValue()).isEqualTo("number");
//                    assertThat(foundFacet.asDocument().get("boundaries").asArray()).isNotEmpty();
//                }
//                assertThat(foundFacet.asDocument().get("path").asString().getValue()).isEqualTo(navigation);
//            }
//        }
//    }
//
//    private void assertRefinements(List<SelectedRefinement> refinements, List<BsonValue> filters,
//    boolean areIncluded) {
//        var shouldFilters = filters
//            .stream()
//            .map(filter -> filter.asDocument().get("compound"))
//            .filter(filter -> filter.asDocument().containsKey("should"))
//            .map(filter -> filter.asDocument().get("should"))
//            .toList();
//
//        var mustFilters = filters
//            .stream()
//            .map(filter -> filter.asDocument().get("compound"))
//            .filter(filter -> filter.asDocument().containsKey("must"))
//            .flatMap(filter -> filter.asDocument().get("must").asArray().stream())
//            .toList();
//
//        var allFilters = new ArrayList<BsonValue>();
//        allFilters.addAll(shouldFilters);
//        allFilters.addAll(mustFilters);
//
//        var textFilters = allFilters
//            .stream()
//            .filter(filter -> filter.asDocument().containsKey("text"))
//            .map(filter -> filter.asDocument().get("text"))
//            .flatMap(filter -> filter.asDocument().get("path").asArray().stream().map(x -> x.asString().getValue()))
//            .toList();
//        var rangeFilters = allFilters
//            .stream()
//            .filter(filter -> filter.asDocument().containsKey("range"))
//            .map(filter -> filter.asDocument().get("range"))
//            .flatMap(filter -> filter.asDocument().get("path").asArray().stream().map(x -> x.asString().getValue()))
//            .toList();
//
//        refinements.forEach(refinement -> {
//            if (refinement.getType() == VALUE) {
//                if (areIncluded) {
//                    assertTrue(
//                        textFilters.contains(refinement.getField()) ||
//                            textFilters.contains("variants." + refinement.getField()) ||
//                            textFilters.contains("localInventories." + refinement.getField()));
//                } else {
//                    assertFalse(
//                        textFilters.contains(refinement.getField()) ||
//                            textFilters.contains("variants." + refinement.getField()) ||
//                            textFilters.contains("localInventories." + refinement.getField()));
//                }
//            } else {
//                if (areIncluded) {
//                    assertTrue(
//                        rangeFilters.contains(refinement.getField()) ||
//                            rangeFilters.contains("variants." + refinement.getField()) ||
//                            rangeFilters.contains("localInventories." + refinement.getField()));
//                } else {
//                    assertFalse(
//                        rangeFilters.contains(refinement.getField()) ||
//                            rangeFilters.contains("variants." + refinement.getField()) ||
//                            rangeFilters.contains("localInventories." + refinement.getField()));
//                }
//            }
//        });
//    }
//
//    private void assertSkipStage(List<Bson> queryPipeline) {
//        assertTrue(
//            queryPipeline
//                .stream()
//                .anyMatch(bson -> {
//                    var doc = bson.toBsonDocument();
//                    return doc.containsKey("$skip") && doc.get("$skip").asInt64().getValue() == 50;
//                })
//        );
//    }
//
//    private void assertLimitStage(List<Bson> queryPipeline, int expected) {
//        assertTrue(
//            queryPipeline
//                .stream()
//                .anyMatch(bson -> {
//                    var doc = bson.toBsonDocument();
//                    return doc.containsKey("$limit") && doc.get("$limit").asInt32().getValue() == expected;
//                })
//        );
//    }
//
//    private void assertAddFieldsStage(List<Bson> queryPipeline) {
//        var addFields = queryPipeline
//            .stream()
//            .filter(bson -> bson.toBsonDocument().containsKey("$addFields"))
//            .findFirst()
//            .orElse(null);
//
//        assertNotNull(addFields);
//        var addFieldsDoc = addFields.toBsonDocument().get("$addFields").asDocument();
//        assertThat(addFieldsDoc).hasSize(1);
//
//        var sortArray = addFieldsDoc.get("variants").asDocument().get("$sortArray").asDocument();
//        var sortBy = sortArray.get("sortBy").asDocument();
//        var map = sortArray.get("input").asDocument().get("$map").asDocument();
//
//        assertTrue(sortBy.containsKey("isMatching"));
//        assertThat(sortBy.get("isMatching").asInt32().getValue()).isEqualTo(-1);
//
//        assertTrue(map.containsKey("input"));
//        assertTrue(map.containsKey("as"));
//        assertTrue(map.containsKey("in"));
//
//        var in = map.asDocument().get("in").asDocument();
//        assertTrue(in.containsKey("id"));
//        assertTrue(in.containsKey("isMatching"));
//    }
//
//    private void assertProjectStage(List<Bson> queryPipeline) {
//        var project = queryPipeline
//            .stream()
//            .filter(bson -> bson.toBsonDocument().containsKey("$project"))
//            .findFirst()
//            .orElse(null);
//
//        assertNotNull(project);
//        var projectDoc = project.toBsonDocument().get("$project").asDocument();
//        assertThat(projectDoc).hasSize(6);
//        assertThat(projectDoc.get("_id").asInt32().getValue()).isZero();
//        assertThat(projectDoc.get("id").asInt32().getValue()).isEqualTo(1);
//        assertThat(projectDoc.get("type").asInt32().getValue()).isEqualTo(1);
//        assertThat(projectDoc.get("title").asInt32().getValue()).isEqualTo(1);
//        assertThat(projectDoc.get("primaryProductId").asInt32().getValue()).isEqualTo(1);
//        assertThat(projectDoc.get("variants.id").asInt32().getValue()).isEqualTo(1);
//    }
//
//    private void assertSortStage(List<Bson> queryPipeline, List<Sort> sorts) {
//        var sortStage = queryPipeline
//            .stream()
//            .filter(bson -> bson.toBsonDocument().containsKey("$sort"))
//            .findFirst()
//            .orElse(null);
//
//        assertNotNull(sortStage);
//        for (var sort : sorts) {
//            var order = sort.getOrder() == Order.ASCENDING ? 1 : -1;
//            assertThat(
//                sortStage.toBsonDocument().get("$sort").asDocument().get(sort.getField()).asInt32().getValue()
//            ).isEqualTo(order);
//        }
//    }
//
//    private void assertFacetStageOnIndex(List<Bson> queryPipeline) {
//        var facetStage = queryPipeline
//            .stream()
//            .filter(bson -> bson.toBsonDocument().containsKey("$facet"))
//            .findFirst()
//            .orElse(null);
//
//        assertNotNull(facetStage);
//        var facetDoc = facetStage.toBsonDocument().get("$facet").asDocument();
//        assertThat(facetDoc.get("docs").asArray()).isEmpty();
//        var metaDoc = facetDoc.get("meta").asArray();
//        assertThat(metaDoc).hasSize(2);
//        assertThat(metaDoc.getFirst()
//        .asDocument().get("$replaceWith").asString().getValue()).isEqualTo("$$SEARCH_META");
//        assertThat(metaDoc.get(1).asDocument().get("$limit").asInt32().getValue()).isEqualTo(1);
//    }
//
//    private void assertSetStageOnIndex(List<Bson> queryPipeline) {
//        var setStage = queryPipeline
//            .stream()
//            .filter(bson -> bson.toBsonDocument().containsKey("$set"))
//            .findFirst()
//            .orElse(null);
//
//        assertNotNull(setStage);
//
//        var setDoc = setStage.toBsonDocument().get("$set").asDocument();
//        var arrayAtElemDoc = setDoc.get("meta").asDocument().get("$arrayElemAt").asArray();
//        assertThat(arrayAtElemDoc).hasSize(2);
//        assertThat(arrayAtElemDoc.getFirst().asString().getValue()).isEqualTo("$meta");
//        assertThat(arrayAtElemDoc.get(1).asInt32().getValue()).isZero();
//    }
//
//    // Consider asserting different cases of response based on input refinements
//    // when https://groupby.atlassian.net/browse/S4R-6356 completed.
//    private void assertResponse(SearchResults response) {
//        assertThat(response).isNotNull();
//
//        assertThat(response.getQuery()).isEmpty();
//        assertThat(response.getCorrectedQuery()).isEmpty();
//        assertThat(response.getFilter()).isNull();
//        assertThat(response.getRecords()).hasSize(2);
//        assertThat(response.getNumTotalRecords()).isEqualTo(213289);
//        assertThat(response.getMetadata().getAttributionToken()).isNotBlank();
//
//        // assert records
//        var firstRecord = response.getRecords().getFirst();
//        assertThat(firstRecord.getId()).isEqualTo("d652b7bd50befadd593fd574fbd1979f");
//        assertThat(firstRecord.getProductId()).isEqualTo("1112200041");
//        assertThat(firstRecord.getPrimaryProductId()).isEqualTo("1112200041");
//        assertThat(firstRecord.getUrl()).isEqualTo("http://apparel1productsClothing.com/1112200041");
//        assertThat(firstRecord.getTitle()).isEqualTo("Title example");
//        assertThat(firstRecord.getCollection()).isEqualTo("productsClothing");
//        var firstProductMetadata = firstRecord.getMetadata();
//
//        assertThat(firstProductMetadata).containsEntry("id", "1112200041");
//        assertThat(firstProductMetadata).containsEntry("title", "Title example");
//
//        // assert navigations
//        var priceNavigation = response.getNavigations().getFirst();
//        assertThat(priceNavigation.getName()).isEqualTo("price");
//        assertThat(priceNavigation.getField()).isEqualTo("price");
//        assertThat(priceNavigation.getSource()).isEqualTo("Dynamic");
//        assertThat(priceNavigation.isPinned()).isFalse();
//        assertThat(priceNavigation.getType()).isEqualTo(NavigationType.RANGE);
//        assertThat(priceNavigation.getRefinements().getFirst().getCount()).isZero();
//        assertThat(priceNavigation.getRefinements().getFirst().getRange().low()).isEqualTo(0.0);
//        assertThat(priceNavigation.getRefinements().getFirst().getRange().high()).isNull();
//        assertThat(priceNavigation.getRefinements().getFirst().getRange().description()).isNull();
//        assertThat(priceNavigation.getRefinements().getFirst().getRange().exclude()).isFalse();
//
//        // assert debug details
//        var debug = response.getDebugDetails();
//        assertThat(debug.getRawMongoSearchRequest()).isNotEmpty();
//        assertThat(debug.getRawMongoSearchResponse()).isNotEmpty();
//    }
//}
