package com.groupbyinc.search.ssa.mongo.converter;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;


@DisplayName("Mongo Search Query Builder Tests")
public class MongoSearchTest {

    private static final String searchIndex = "productionproduct_search_search";

    @Test
    public void shouldCreateBasicTextSearch() {
        var search = new MongoSearch().text("brands", "MASERATI");

        var jsonDoc = search.toJson();
        var expected = """
            {"$search": {"returnStoredSource": true, "text": {"path": "brands", "query": ["MASERATI"]}}}""";

        assertEquals(expected, jsonDoc);
    }

    @Test
    public void shouldCreateBasicRangeSearch() {
        var search = new MongoSearch().range(
            "priceInfo.price",
            MongoSearchRange.gte(9599),
            MongoSearchRange.lte(9600)
        );

        var jsonDoc = search.toJson();
        var expected = """
            {"$search": {"returnStoredSource": true, "range": {"path": "priceInfo.price", "gte": 9599, "lte": 9600}}}""";

        assertEquals(expected, jsonDoc);
    }

    @Test
    @DisplayName("should create compound query with text and exists operators wrapping in must")
    public void shouldCompoundWithMustTextAndExistsSearch() {
        var search = new MongoSearch();

        search.compound()
            .appendMust(MongoSearchOperators.text("brands", "MASERATI"))
            .appendMust(MongoSearchOperators.exists("brands"));

        var jsonDoc = search.toJson();
        var expected = """
            {"$search": {"returnStoredSource": true, "compound": {"must": [{"text": {"path": "brands", "query": ["MASERATI"]}}, {"exists": {"path": "brands"}}]}}}""";

        assertEquals(expected, jsonDoc);
    }

    @Test
    @DisplayName("should create compound query with text and exists operators wrapping in must and filter")
    public void shouldCompoundWithMustAndFilterTextAndExistsSearch() {
        var search = new MongoSearch(searchIndex);

        search.compound()
            .appendMust(MongoSearchOperators.text("brands", "MASERATI"))
            .appendFilter(MongoSearchOperators.exists("brands"));

        var jsonDoc = search.toJson();
        var expected = """
            {"$search": {"index": "productionproduct_search_search", "returnStoredSource": true, "compound": {"must": [{"text": {"path": "brands", "query": ["MASERATI"]}}], "filter": [{"exists": {"path": "brands"}}]}}}""";

        assertEquals(expected, jsonDoc);
    }

    @Test
    @DisplayName("should create nested compound query")
    public void shouldNestCompoundConditions() {
        var search = new MongoSearch(searchIndex);

        var compoundQuery = new MongoCompoundableSearch()
            .appendMustNot(MongoSearchOperators.text("brands", "REGINA ROMERO"));

        search.compound()
            .appendMust(MongoSearchOperators.compound(compoundQuery.toDocument()))
            .appendFilter(MongoSearchOperators.exists("brands"));

        var jsonDoc = search.toJson();
        var expected = """
            {"$search": {"index": "productionproduct_search_search", "returnStoredSource": true, "compound": {"must": [{"compound": {"mustNot": [{"text": {"path": "brands", "query": ["REGINA ROMERO"]}}]}}], "filter": [{"exists": {"path": "brands"}}]}}}""";

        assertEquals(expected, jsonDoc);
    }

    @Test
    @DisplayName("should create nested compound query too")
    public void shouldCreateNestCompoundConditionsToo() {
        var search = new MongoSearch(searchIndex);

        var compoundQuery = new MongoCompoundableSearch();
        compoundQuery.compound()
            .appendMustNot(MongoSearchOperators.text("brands", "REGINA ROMERO"));

        search.compound()
            .appendMust(compoundQuery)
            .appendFilter(MongoSearchOperators.exists("brands"));

        var jsonDoc = search.toJson();
        var expected = """
            {"$search": {"index": "productionproduct_search_search", "returnStoredSource": true, "compound": {"must": [{"compound": {"mustNot": [{"text": {"path": "brands", "query": ["REGINA ROMERO"]}}]}}], "filter": [{"exists": {"path": "brands"}}]}}}""";

        assertEquals(expected, jsonDoc);
    }

    @Test
    @DisplayName("should modify document score using constant")
    public void shouldModifyDocumentScoreUsingConstant() {
        var search = new MongoSearch(searchIndex);

        search.compound()
            .scoreConstant(1.5)
            .appendMust(MongoSearchOperators.text("brands", "MASERATI"))
            .appendFilter(MongoSearchOperators.exists("brands"));

        var jsonDoc = search.toJson();
        var expected = """
            {"$search": {"index": "productionproduct_search_search", "returnStoredSource": true, "compound": {"score": {"constant": {"value": 1.5}}, "must": [{"text": {"path": "brands", "query": ["MASERATI"]}}], "filter": [{"exists": {"path": "brands"}}]}}}""";

        assertEquals(expected, jsonDoc);
    }

    @Test
    @DisplayName("should boost specific brand using constant score")
    public void shouldBoostSpecificBrandUsingConstantScore() {
        var search = new MongoSearch(searchIndex);

        var docsBrandsField = MongoSearchOperators.exists("brands");
        var specificBrand = MongoSearchOperators.text("brands", "MASERATI");

        // query to search all documents with the brand field
        var mainQueryWithBaselineScore = new MongoCompoundableSearch();
        mainQueryWithBaselineScore.compound()
            .appendFilter(docsBrandsField)
            .appendMustNot(specificBrand) // exclude these docs to prevent compounding scores
            .scoreConstant(5.0);

        // query to boost only documents of the specified brand
        var boostedBrand = new MongoCompoundableSearch();
        boostedBrand.compound()
            .appendFilter(docsBrandsField) // include main filter to improve search performance
            .appendMust(specificBrand)
            .scoreConstant(10.0);

        // append query parts to search doc
        search.compound()
            .appendShould(boostedBrand)
            .appendShould(mainQueryWithBaselineScore);

        var jsonDoc = search.toJson();
        var expected = """
            {"$search": {"index": "productionproduct_search_search", "returnStoredSource": true, "compound": {"should": [{"compound": {"filter": [{"exists": {"path": "brands"}}], "must": [{"text": {"path": "brands", "query": ["MASERATI"]}}], "score": {"constant": {"value": 10.0}}}}, {"compound": {"filter": [{"exists": {"path": "brands"}}], "mustNot": [{"text": {"path": "brands", "query": ["MASERATI"]}}], "score": {"constant": {"value": 5.0}}}}]}}}""";

        assertEquals(expected, jsonDoc);
    }

    @Test
    @DisplayName("should perform facet search")
    public void shouldPerformFacetSearch() {
        var search = new MongoSearch(searchIndex);
        var facet = search.facet();

        facet.appendFacets(
            new Document(
                "availability",
                new Document("type", "string").append("path", "availability")
            )
        );
        facet.operator().appendShould(MongoSearchOperators.text("brands", "MASERATI"));

        var jsonDoc = search.toJson();
        var expected = """
            {"$search": {"index": "productionproduct_search_search", "returnStoredSource": true, "facet": {"facets": {"availability": {"type": "string", "path": "availability"}}, "operator": {"compound": {"should": [{"text": {"path": "brands", "query": ["MASERATI"]}}]}}}}}""";

        assertEquals(expected, jsonDoc);
    }

    @Test
    @DisplayName("Copied query should have a separate instance from original query")
    void shouldCopy() {
        var originalQuery = new MongoCompoundableSearch();
        originalQuery.compound().appendMust(MongoSearchOperators.text("brands", "MASERATI"));

        var copiedQuery = originalQuery.copy();
        copiedQuery.compound().appendShould(MongoSearchOperators.text("brands", "FakeBrand"));

        assertNotEquals(originalQuery.toJson(), copiedQuery.toJson());
    }
}
