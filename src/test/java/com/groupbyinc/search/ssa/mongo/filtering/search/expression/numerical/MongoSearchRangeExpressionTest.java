package com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;


import static com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical.MongoSearchRangeExpression.fromRaw;
import static com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical.MongoSearchRangeExpression.greaterThan;
import static com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical.MongoSearchRangeExpression.greaterThanOrEqual;
import static com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical.MongoSearchRangeExpression.lessThan;
import static com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical.MongoSearchRangeExpression.lessThanOrEqual;
import static com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical.MongoSearchRangeExpression.of;
import static com.groupbyinc.search.ssa.stub.ResourceUtils.getResourceContents;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("MongoSearchRangeExpression Tests")
class MongoSearchRangeExpressionTest {

    @Test
    @DisplayName("MongoSearchRangeExpression From Raw Tests")
    public void mongoSearchRangeExpressionFromRawTest() {
        var result = fromRaw("priceInfo.price", null, "1", "2").toFilter();
        var expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gte": 1.0,
                "lt": 2.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1i", "2").toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gte": 1.0,
                "lt": 2.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1", "2i").toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gte": 1.0,
                "lte": 2.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1e", "2").toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gt": 1.0,
                "lt": 2.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1", "2e").toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gte": 1.0,
                "lt": 2.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1i", "2i").toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gte": 1.0,
                "lte": 2.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1e", "2e").toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gt": 1.0,
                "lt": 2.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1e", "2i").toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gt": 1.0,
                "lte": 2.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1i", "2e").toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gte": 1.0,
                "lt": 2.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "*", "2").toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "lt": 2.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "*", "2i").toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "lte": 2.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "*", "2e").toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "lt": 2.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1", "*").toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gte": 1.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1i", "*").toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gte": 1.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1e", "*").toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gt": 1.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchRangeExpression From Raw (Inventory) Tests")
    public void mongoSearchRangeExpressionFromRawInventoryTest() {
        var result = fromRaw("localInventories.priceInfo.price", "place", "1", "2").toFilter();
        var expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-1.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("localInventories.priceInfo.price", "place", "1i", "2").toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-2.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("localInventories.priceInfo.price", "place", "1", "2i").toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-3.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("localInventories.priceInfo.price", "place", "1e", "2").toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-4.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("localInventories.priceInfo.price", "place", "1", "2e").toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-5.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("localInventories.priceInfo.price", "place", "1i", "2i").toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-6.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("localInventories.priceInfo.price", "place", "1e", "2e").toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-7.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("localInventories.priceInfo.price", "place", "1e", "2i").toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-8.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("localInventories.priceInfo.price", "place", "1i", "2e").toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-9.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("localInventories.priceInfo.price", "place", "*", "2").toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-10.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("localInventories.priceInfo.price", "place", "*", "2i").toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-11.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("localInventories.priceInfo.price", "place", "*", "2e").toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-12.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("localInventories.priceInfo.price", "place", "1", "*").toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-13.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("localInventories.priceInfo.price", "place", "1i", "*").toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-14.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("localInventories.priceInfo.price", "place", "1e", "*").toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mong-search-range-expression-from-raw-inventory-15.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchRangeExpression greaterThan Test")
    public void mongoSearchRangeExpressionGreaterThanTest() {
        var result = greaterThan("priceInfo.price", null, 10).toFilter();

        var expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gt": 10
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchRangeExpression greaterThan (Inventory) Tests")
    public void mongoSearchRangeExpressionGreaterThanInventoryTest() {
        var result = greaterThan("localInventories.priceInfo.price", "place", 10).toFilter();

        var expected = """
            {
              "embeddedDocument": {
                "path": "indexables.localInventories",
                "operator": {
                  "compound": {
                    "filter": [
                      {
                        "equals": {
                          "path": "indexables.localInventories.placeId",
                          "value": "place"
                        }
                      },
                      {
                        "range": {
                          "path": "indexables.localInventories.priceInfo.price",
                          "gt": 10
                        }
                      }
                    ]
                  }
                }
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchRangeExpression greaterThanOrEqual Test")
    public void mongoSearchRangeExpressionGreaterThanOrEqualTest() {
        var result = greaterThanOrEqual("priceInfo.price", null, 10).toFilter();

        var expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gte": 10
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchRangeExpression greaterThanOrEqual (Inventory) Tests")
    public void mongoSearchRangeExpressionGreaterThanOrEqualInventoryTest() {
        var result = greaterThanOrEqual("localInventories.priceInfo.price", "place", 10).toFilter();

        var expected = """
            {
              "embeddedDocument": {
                "path": "indexables.localInventories",
                "operator": {
                  "compound": {
                    "filter": [
                      {
                        "equals": {
                          "path": "indexables.localInventories.placeId",
                          "value": "place"
                        }
                      },
                      {
                        "range": {
                          "path": "indexables.localInventories.priceInfo.price",
                          "gte": 10
                        }
                      }
                    ]
                  }
                }
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchRangeExpression lessThan Test")
    public void mongoSearchRangeExpressionLessThanTest() {
        var result = lessThan("priceInfo.price", null, 10).toFilter();

        var expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "lt": 10
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchRangeExpression lessThan (Inventory) Tests")
    public void mongoSearchRangeExpressionLessThanInventoryTest() {
        var result = lessThan("localInventories.priceInfo.price", "place", 10).toFilter();

        var expected = """
            {
              "embeddedDocument": {
                "path": "indexables.localInventories",
                "operator": {
                  "compound": {
                    "filter": [
                      {
                        "equals": {
                          "path": "indexables.localInventories.placeId",
                          "value": "place"
                        }
                      },
                      {
                        "range": {
                          "path": "indexables.localInventories.priceInfo.price",
                          "lt": 10
                        }
                      }
                    ]
                  }
                }
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchRangeExpression lessThanOrEqual Test")
    public void mongoSearchRangeExpressionLessThanOrEqualTest() {
        var result = lessThanOrEqual("priceInfo.price", null, 10).toFilter();

        var expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "lte": 10
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchRangeExpression lessThanOrEqual (Inventory) Tests")
    public void mongoSearchRangeExpressionLessThanOrEqualInventoryTest() {
        var result = lessThanOrEqual("localInventories.priceInfo.price", "place", 10).toFilter();

        var expected = """
            {
              "embeddedDocument": {
                "path": "indexables.localInventories",
                "operator": {
                  "compound": {
                    "filter": [
                      {
                        "equals": {
                          "path": "indexables.localInventories.placeId",
                          "value": "place"
                        }
                      },
                      {
                        "range": {
                          "path": "indexables.localInventories.priceInfo.price",
                          "lte": 10
                        }
                      }
                    ]
                  }
                }
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchRangeExpression of Tests")
    public void mongoSearchRangeExpressionOfTest() {
        var result = of("priceInfo.price", null, 1.0, null).toFilter();
        var expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gte": 1.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = of("priceInfo.price", null, null, 2.0).toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "lt": 2.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = of("priceInfo.price", null, 1.0, 2.0).toFilter();
        expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gte": 1.0,
                "lt": 2.0
              }
            }
        """;
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchRangeExpression of (Inventory) Tests")
    public void mongoSearchRangeExpressionOfInventoryTest() {
        var result = of("localInventories.priceInfo.price", "place", 1.0, null).toFilter();
        var expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mongo-search-range-expression-of-inventory-1.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = of("localInventories.priceInfo.price", "place", null, 2.0).toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mongo-search-range-expression-of-inventory-2.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = of("localInventories.priceInfo.price", "place", 1.0, 2.0).toFilter();
        expected = getResourceContents(
            "mongo/filtering/search/expression/numerical/mongo-search-range-expression-of-inventory-3.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

}
