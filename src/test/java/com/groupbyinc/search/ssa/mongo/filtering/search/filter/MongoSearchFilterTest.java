package com.groupbyinc.search.ssa.mongo.filtering.search.filter;

import com.groupbyinc.search.ssa.mongo.filtering.search.expression.text.MongoSearchTextExpression;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static com.groupbyinc.search.ssa.stub.ResourceUtils.getResourceContents;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("Mongo search filter Tests")
public class MongoSearchFilterTest {

    @Test
    @DisplayName("Build full search filter")
    public void mongoSearchFilterServiceBuildSearchFilterTest() {
        var expected = getResourceContents("mongo/filtering/search/filter/mongo-search-filter-test-expected.json");

        var sizesXl = new MongoSearchTextExpression("sizes", List.of("XL"));
        var sizesL = new MongoSearchTextExpression("sizes", List.of("L"));
        var sizesS = new MongoSearchTextExpression("sizes", List.of("S"));
        var sizesM = new MongoSearchTextExpression("sizes", List.of("M"));

        var innerAndWithOr = new MongoSearchFilter();
        innerAndWithOr.addToOrs(List.of(sizesXl, sizesL));

        var innerAndWithAnd = new MongoSearchFilter();
        innerAndWithAnd.addToAnds(List.of(sizesS, sizesM));

        var innerAndWithOrAnd = new MongoSearchFilter();
        innerAndWithOrAnd.addToOrs(List.of(sizesXl, sizesL));
        innerAndWithOrAnd.addToAnds(List.of(sizesS, sizesM));

        var innerOrWithOr = new MongoSearchFilter();
        innerOrWithOr.addToOrs(List.of(sizesXl, sizesL));

        var innerOrWithAnd = new MongoSearchFilter();
        innerOrWithAnd.addToAnds(List.of(sizesS, sizesM));

        var innerOrWithOrAnd = new MongoSearchFilter();
        innerOrWithOrAnd.addToOrs(List.of(sizesXl, sizesL));
        innerOrWithOrAnd.addToAnds(List.of(sizesS, sizesM));

        var filter = new MongoSearchFilter();
        filter.addToOrs(List.of(innerOrWithOr, innerOrWithAnd, innerOrWithOrAnd));
        filter.addToAnds(List.of(innerAndWithOr, innerAndWithAnd, innerAndWithOrAnd));

        assertThat(filter.toFilter().toJson()).isEqualTo(Document.parse(expected).toJson());
    }

}
