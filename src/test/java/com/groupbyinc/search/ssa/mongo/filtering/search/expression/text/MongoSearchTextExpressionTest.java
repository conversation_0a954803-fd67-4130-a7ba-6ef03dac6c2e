package com.groupbyinc.search.ssa.mongo.filtering.search.expression.text;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("MongoSearchTextExpression Tests")
class MongoSearchTextExpressionTest {

    @Test
    @DisplayName("MongoSearchTextExpression single value Test")
    public void mongoSearchTextExpressionSingleValueTest() {
        var result = new MongoSearchTextExpression("sizes", List.of("XL")).toFilter();

        var expected = """
            {
              "equals" : {
                "value" : "XL",
                "path" : "indexables.sizes"
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchTextExpression multiple values Test")
    public void mongoSearchTextExpressionMultipleValuesTest() {
        var result = new MongoSearchTextExpression("sizes", List.of("Xl", "L")).toFilter();

        var expected = """
            {
              "in": {
                "value": [
                  "Xl",
                  "L"
                ],
                "path": "indexables.sizes"
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchTextExpression single value Inventory Test")
    public void mongoSearchTextExpressionSingleValueInventoryTest() {
        var result = new MongoSearchTextExpression("localInventories.sizes", "place", List.of("XL")).toFilter();

        var expected = """
            {
              "embeddedDocument": {
                "path": "indexables.localInventories",
                "operator": {
                  "compound": {
                    "filter": [
                      {
                        "equals": {
                          "path": "indexables.localInventories.placeId",
                          "value": "place"
                        }
                      },
                      {
                        "equals": {
                          "value": "XL",
                          "path": "indexables.localInventories.sizes"
                        }
                      }
                    ]
                  }
                }
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchTextExpression multiple values Inventory Test")
    public void mongoSearchTextExpressionMultipleValuesInventoryTest() {
        var result = new MongoSearchTextExpression("localInventories.sizes", "place", List.of("Xl", "L")).toFilter();

        var expected = """
            {
              "embeddedDocument": {
                "path": "indexables.localInventories",
                "operator": {
                  "compound": {
                    "filter": [
                      {
                        "equals": {
                          "path": "indexables.localInventories.placeId",
                          "value": "place"
                        }
                      },
                      {
                        "in": {
                          "value": [
                            "Xl",
                            "L"
                          ],
                          "path": "indexables.localInventories.sizes"
                        }
                      }
                    ]
                  }
                }
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

}
