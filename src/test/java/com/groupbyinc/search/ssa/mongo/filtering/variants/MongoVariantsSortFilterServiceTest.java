package com.groupbyinc.search.ssa.mongo.filtering.variants;

import com.groupbyinc.search.ssa.core.SearchParameters;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.getAttributeFilters;
import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.getMerchandisingConfiguration;
import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.getProductIdFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.getSelectedRefinements;
import static com.groupbyinc.search.ssa.stub.ResourceUtils.getResourceContents;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("MongoVariantsSortFilterService Tests")
public class MongoVariantsSortFilterServiceTest {

    @Test
    @DisplayName("Build full variant sort filter")
    public void mongoVariantsSortFilterServiceBuildVariantSortFilterTest() {
        var expected = getResourceContents("mongo/filtering/variants/mongo-variant-sort-filter.json");

        var searchParameters = SearchParameters.builder()
            .preFilter(getResourceContents("antlr/raw-filter"))
            .merchandisingConfiguration(getMerchandisingConfiguration())
            .attributeFilters(getAttributeFilters())
            .refinements(getSelectedRefinements())
            .productIdFilter(getProductIdFilter())
            .build();

        var filter = new MongoVariantsSortFilterService().createFilter(searchParameters);

        assertThat(filter.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

}
