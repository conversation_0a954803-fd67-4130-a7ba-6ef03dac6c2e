package com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortRangeExpression.fromRaw;
import static com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortRangeExpression.greaterThan;
import static com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortRangeExpression.greaterThanOrEqual;
import static com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortRangeExpression.lessThan;
import static com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortRangeExpression.lessThanOrEqual;
import static com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortRangeExpression.of;
import static com.groupbyinc.search.ssa.stub.ResourceUtils.getResourceContents;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("MongoVariantSortRangeExpression Tests")
class MongoVariantSortRangeExpressionTest {

    static final String EXPECTED_FILE_PATH_PREFIX = "mongo/filtering/variants/expression/numerical/";

    @Test
    @DisplayName("MongoVariantSortRangeExpression From Raw Tests")
    public void mongoVariantSortRangeExpressionFromRawTest() {
        var result = fromRaw("priceInfo.price", null, "1", "2").toFilter();
        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-1.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1i", "2").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-2.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1", "2i").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-3.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1e", "2").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-4.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1", "2e").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-5.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1i", "2i").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-6.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1e", "2e").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-7.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1e", "2i").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-8.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1i", "2e").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-9.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "*", "2").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-10.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "*", "2i").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-11.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "*", "2e").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-12.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1", "*").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-13.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1i", "*").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-14.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", null, "1e", "*").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-15.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortRangeExpression From Raw (Inventory) Tests")
    public void mongoVariantSortRangeExpressionFromRawInventoryTest() {
        var result = fromRaw("priceInfo.price", "place", "1", "2").toFilter();
        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-1.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", "place", "1i", "2").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-2.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", "place", "1", "2i").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-3.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", "place", "1e", "2").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-4.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", "place", "1", "2e").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-5.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", "place", "1i", "2i").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-6.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", "place", "1e", "2e").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-7.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", "place", "1e", "2i").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-8.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", "place", "1i", "2e").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-9.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", "place", "*", "2").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-10.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", "place", "*", "2i").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-11.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", "place", "*", "2e").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-12.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", "place", "1", "*").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-13.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", "place", "1i", "*").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-14.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = fromRaw("priceInfo.price", "place", "1e", "*").toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-from-raw-inventory-15.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortRangeExpression greaterThan Test")
    public void mongoVariantSortRangeExpressionGreaterThanTest() {
        var result = greaterThan("priceInfo.price", null, 10).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-greater-than.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortRangeExpression greaterThan (Inventory) Tests")
    public void mongoVariantSortRangeExpressionGreaterThanInventoryTest() {
        var result = greaterThan("priceInfo.price", "place", 10).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-greater-than-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortRangeExpression greaterThanOrEqual Test")
    public void mongoVariantSortRangeExpressionGreaterThanOrEqualTest() {
        var result = greaterThanOrEqual("priceInfo.price", null, 10).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-greater-than-or-equal.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortRangeExpression greaterThanOrEqual (Inventory) Tests")
    public void mongoVariantSortRangeExpressionGreaterThanOrEqualInventoryTest() {
        var result = greaterThanOrEqual("priceInfo.price", "place", 10).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-greater-than-or-equal-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortRangeExpression lessThan Test")
    public void mongoVariantSortRangeExpressionExpressionLessThanTest() {
        var result = lessThan("priceInfo.price", null, 10).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-expression-less-than.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortRangeExpression lessThan (Inventory) Tests")
    public void mongoVariantSortRangeExpressionLessThanInventoryTest() {
        var result = lessThan("priceInfo.price", "place", 10).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-expression-less-than-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortRangeExpression lessThanOrEqual Test")
    public void mongoVariantSortRangeExpressionLessThanOrEqualTest() {
        var result = lessThanOrEqual("priceInfo.price", null, 10).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-less-than-or-equal.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortRangeExpression lessThanOrEqual (Inventory) Tests")
    public void mongoVariantSortRangeExpressionLessThanOrEqualInventoryTest() {
        var result = lessThanOrEqual("priceInfo.price", "place", 10).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-less-than-or-equal-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortRangeExpression of Tests")
    public void mongoVariantSortRangeExpressionOfTest() {
        var result = of("priceInfo.price", null, 1.0, null).toFilter();
        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-of-1.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = of("priceInfo.price", null, null, 2.0).toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-of-2.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = of("priceInfo.price", null, 1.0, 2.0).toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-of-3.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortRangeExpression of (Inventory) Tests")
    public void mongoVariantSortRangeExpressionOfInventoryTest() {
        var result = of("priceInfo.price", "place", 1.0, null).toFilter();
        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-of-inventory-1.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = of("priceInfo.price", "place", null, 2.0).toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-of-inventory-2.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());

        result = of("priceInfo.price", "place", 1.0, 2.0).toFilter();
        expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-range-expression-of-inventory-3.json"
        );
        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

}
