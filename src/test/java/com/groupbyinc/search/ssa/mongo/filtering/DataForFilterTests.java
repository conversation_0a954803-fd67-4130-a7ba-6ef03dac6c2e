package com.groupbyinc.search.ssa.mongo.filtering;

import com.groupbyinc.proto.commandcenter.config.AttributeMessage;
import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.filter.AttributeFilter;
import com.groupbyinc.search.ssa.core.filter.ProductIdFilter;
import com.groupbyinc.search.ssa.core.filter.RangeFilter;
import com.groupbyinc.search.ssa.core.filter.ValueFilter;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.groupbyinc.proto.commandcenter.config.AttributeMessage.AttributeType.NUMERICAL;
import static com.groupbyinc.search.ssa.core.filter.ValueFilter.ValueFilterType.NUMERIC;
import static com.groupbyinc.search.ssa.core.filter.ValueFilter.ValueFilterType.TEXTUAL;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.RANGE;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.VALUE;

public class DataForFilterTests {

    public static MerchandisingConfiguration getMerchandisingConfiguration() {
        return new MerchandisingConfiguration(
            null,
            Set.of(),
            Set.of(),
            Set.of(),
            List.of(),
            defaultAttributeConfigs(),
            null,
            null,
            List.of(),
            null
        );
    }

    public static List<SelectedRefinement> getSelectedRefinements() {
        return List.of(
            new SelectedRefinement("brands", VALUE, null, "Nike", true, true),
            new SelectedRefinement("originalPrice", RANGE, new Range(0.0, 100.0, ""), null, true, true),
            new SelectedRefinement("colorFamilies", VALUE, null, "Red", true, true),
            new SelectedRefinement("attributes.category", VALUE, null, "CAT1", true, true),
            new SelectedRefinement("attributes.totalSalesCount", RANGE, new Range(0.0, 100.0, ""), null, true, true),
            new SelectedRefinement("inventory(50, original_price)", RANGE, new Range(0.0, 1.0, ""), null, true, true),
            new SelectedRefinement("inventory(50, attributes.category)", VALUE, null, "Nike", true, true),
            new SelectedRefinement(
                "inventory(50, attributes.totalSalesCount)",
                RANGE,
                new Range(0.0, 1.0, ""),
                null,
                true,
                true
            )
        );
    }

    public static List<AttributeFilter> getAttributeFilters() {
        return List.of(
            new AttributeFilter(
                List.of(
                    new ValueFilter("brands", "Puma", null, false, TEXTUAL),
                    new ValueFilter("sizes", "XL", null, true, TEXTUAL),
                    new ValueFilter("price", null, 10.0, false, NUMERIC),
                    new ValueFilter("originalPrice", null, 10.0, true, NUMERIC),
                    new ValueFilter("attributes.category", "CAT1", null, false, TEXTUAL),
                    new ValueFilter("attributes.totalSalesCount", null, 1.0, true, NUMERIC),

                    new ValueFilter("inventory(50, attributes.category)", "CAT2", null, true, TEXTUAL),
                    new ValueFilter("inventory(50, original_price)", null, 1.0, true, NUMERIC),
                    new ValueFilter("inventory(50, attributes.totalSalesCount)", null, 1.0, true, NUMERIC)
                ),
                List.of(
                    new RangeFilter("originalPrice", new Range(0.0, 100.0, "")),
                    new RangeFilter("attributes.totalSalesCount", new Range(0.0, 100.0, "")),
                    new RangeFilter("inventory(50, original_price)", new Range(0.0, 100.0, ""))
                )
            )
        );
    }

    public static ProductIdFilter getProductIdFilter() {
        return new ProductIdFilter(List.of("1", "2", "3"), List.of("4", "5", "6"));
    }

    public static final Set<String> FIELDS_FOR_FILTERS = Set.of(
        "sizes",
        "brands",
        "colorInfo.colors",
        "colorInfo.colorFamilies",
        "priceInfo.cost",
        "priceInfo.price",
        "priceInfo.originalPrice",
        "priceInfo.currencyCode",
        "rating.averageRating",
        "rating.ratingCount",
        "rating.ratingHistogram",

        "attributes.category",
        "attributes.totalSalesCount",

        "localInventories.priceInfo.price",
        "localInventories.priceInfo.originalPrice",
        "localInventories.attributes.category",
        "localInventories.attributes.totalSalesCount",

        "localInventories.placeId" // should be added if inventory attributes used
    );

    public static Map<String, AttributeConfiguration> defaultAttributeConfigs() {
        var configs = new HashMap<String, AttributeConfiguration>();
        configs.put("sizes", attribute("sizes", "sizes", AttributeMessage.AttributeType.TEXTUAL));
        configs.put("brands", attribute("brands", "brands", AttributeMessage.AttributeType.TEXTUAL));

        configs.put(
            "colors",
            attribute("colors", "colorInfo.colors", AttributeMessage.AttributeType.TEXTUAL)
        );
        configs.put(
            "colorFamilies",
            attribute("colorFamilies", "colorInfo.colorFamilies", AttributeMessage.AttributeType.TEXTUAL)
        );

        configs.put("cost", attribute("cost", "priceInfo.cost", NUMERICAL));
        configs.put("price", attribute("price", "priceInfo.price", NUMERICAL));
        configs.put("originalPrice", attribute("originalPrice", "priceInfo.originalPrice", NUMERICAL));
        configs.put(
            "currencyCode",
            attribute("currencyCode", "priceInfo.currencyCode", AttributeMessage.AttributeType.TEXTUAL)
        );


        configs.put("rating", attribute("rating", "rating.averageRating", NUMERICAL));
        configs.put("averageRating", attribute("averageRating", "rating.averageRating", NUMERICAL));
        configs.put("ratingCount", attribute("ratingCount", "rating.ratingCount", NUMERICAL));
        configs.put("ratingHistogram", attribute("ratingHistogram", "rating.ratingHistogram", NUMERICAL));

        configs.put(
            "attributes.category",
            attribute("attributes.category", "attributes.category", AttributeMessage.AttributeType.TEXTUAL)
        );
        configs.put(
            "attributes.totalSalesCount",
            attribute("attributes.totalSalesCount", "attributes.totalSalesCount", NUMERICAL)
        );

        configs.put(
            "inventories.price",
            attribute(
                "inventories.price",
                "localInventories.priceInfo.price",
                AttributeMessage.AttributeType.TEXTUAL
            )
        );
        configs.put(
            "inventories.original_price",
            attribute(
                "inventories.original_price",
                "localInventories.priceInfo.originalPrice",
                NUMERICAL
            )
        );

        configs.put(
            "inventories.attributes.category",
            attribute(
                "inventories.attributes.category",
                "localInventories.attributes.category",
                AttributeMessage.AttributeType.TEXTUAL
            )
        );
        configs.put(
            "inventories.attributes.totalSalesCount",
            attribute(
                "inventories.attributes.totalSalesCount",
                "localInventories.attributes.totalSalesCount",
                NUMERICAL
            )
        );

        return configs;
    }

    public static AttributeConfiguration attribute(String key, String path, AttributeMessage.AttributeType type) {
        return AttributeConfiguration.builder().key(key).path(path).type(type).build();
    }

}
