package com.groupbyinc.search.ssa.mongo.filtering.variants.filter;

import com.groupbyinc.search.ssa.mongo.filtering.variants.expression.text.MongoVariantSortTextExpression;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static com.groupbyinc.search.ssa.stub.ResourceUtils.getResourceContents;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("Mongo variant sort filter Tests")
public class MongoVariantSortFilterTest {

    @Test
    @DisplayName("Build full search filter")
    public void mongoSearchFilterServiceBuildSearchFilterTest() {
        var expected = getResourceContents(
            "mongo/filtering/variants/filter/mongo-variant-sort-filter-test-expected.json"
        );

        var sizesXl = new MongoVariantSortTextExpression("sizes", List.of("XL"));
        var sizesL = new MongoVariantSortTextExpression("sizes", List.of("L"));
        var sizesS = new MongoVariantSortTextExpression("sizes", List.of("S"));
        var sizesM = new MongoVariantSortTextExpression("sizes", List.of("M"));

        var innerAndWithOr = new MongoVariantSortFilter();
        innerAndWithOr.addToOrs(List.of(sizesXl, sizesL));

        var innerAndWithAnd = new MongoVariantSortFilter();
        innerAndWithAnd.addToAnds(List.of(sizesS, sizesM));

        var innerAndWithOrAnd = new MongoVariantSortFilter();
        innerAndWithOrAnd.addToOrs(List.of(sizesXl, sizesL));
        innerAndWithOrAnd.addToAnds(List.of(sizesS, sizesM));

        var innerOrWithOr = new MongoVariantSortFilter();
        innerOrWithOr.addToOrs(List.of(sizesXl, sizesL));

        var innerOrWithAnd = new MongoVariantSortFilter();
        innerOrWithAnd.addToAnds(List.of(sizesS, sizesM));

        var innerOrWithOrAnd = new MongoVariantSortFilter();
        innerOrWithOrAnd.addToOrs(List.of(sizesXl, sizesL));
        innerOrWithOrAnd.addToAnds(List.of(sizesS, sizesM));

        var filter = new MongoVariantSortFilter();
        filter.addToOrs(List.of(innerOrWithOr, innerOrWithAnd, innerOrWithOrAnd));
        filter.addToAnds(List.of(innerAndWithOr, innerAndWithAnd, innerAndWithOrAnd));

        assertThat(filter.toFilter().toJson()).isEqualTo(Document.parse(expected).toJson());
    }

}
