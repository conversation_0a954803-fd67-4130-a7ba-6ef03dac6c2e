package com.groupbyinc.search.ssa.mongo.facet.category;

import com.groupbyinc.search.ssa.core.navigation.Range;

import org.bson.Document;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.TreeMap;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

class CategoryBucketsTest {
    private static final List<List<Double>> RANGE_1 = randRanges();
    private static final List<List<Double>> RANGE_2 = randRanges();
    private static final List<List<Double>> RANGE_3 = randRanges();
    private static final List<List<Double>> RANGE_4 = randRanges();
    private static final List<List<Double>> RANGE_5 = randRanges();

    @ParameterizedTest
    @MethodSource("testScenarios")
    void fromBson_shouldParseCorrectly(Document input, CategoryBuckets expected) {
        //when
        var result = CategoryBuckets.fromBson(input);

        //then
        assertThat(result).isNotNull();
        assertThat(result.name()).isEqualTo(expected.name());
        assertThat(result.count()).isEqualTo(expected.count());
        assertThat(result.fieldRanges()).hasSameSizeAs(expected.fieldRanges());
        assertThat(new TreeMap<>(result.fieldRanges())).isEqualTo(new TreeMap<>(expected.fieldRanges()));
    }

    private static Stream<Arguments> testScenarios() {
        return Stream.of(
            Arguments.of(
                categoryBsonFor(
                    "cat1",
                    101,
                    new Document()
                        .append(
                            "priceInfo", new Document()
                                .append("price", RANGE_1)
                                .append("originalPrice", RANGE_2)
                        ).append(
                            "rating", new Document()
                                .append("averageRating", RANGE_3)
                        ).append(
                            "attributes", new Document()
                                .append("clientMetrics_productViews", RANGE_4)
                                .append("clientMetrics_customerRating", RANGE_5)
                        )
                ),
                new CategoryBuckets(
                    "cat1",
                    101,
                    Map.of(
                        "priceInfo.price", toSSRanges(RANGE_1),
                        "priceInfo.originalPrice", toSSRanges(RANGE_2),
                        "rating.averageRating", toSSRanges(RANGE_3),
                        "attributes.clientMetrics_productViews", toSSRanges(RANGE_4),
                        "attributes.clientMetrics_customerRating", toSSRanges(RANGE_5)
                    )
                )
            ),
            Arguments.of(
                categoryBsonFor(
                    "cat2",
                    102,
                    new Document()
                        .append("price", RANGE_1)
                        .append("originalPrice", RANGE_2)
                        .append("averageRating", RANGE_3)
                        .append("clientMetrics_productViews", RANGE_4)
                        .append("clientMetrics_customerRating", RANGE_5)

                ),
                new CategoryBuckets(
                    "cat2",
                    102,
                    Map.of(
                        "price", toSSRanges(RANGE_1),
                        "originalPrice", toSSRanges(RANGE_2),
                        "averageRating", toSSRanges(RANGE_3),
                        "clientMetrics_productViews", toSSRanges(RANGE_4),
                        "clientMetrics_customerRating", toSSRanges(RANGE_5)
                    )
                )
            ),
            Arguments.of(
                categoryBsonFor(
                    "cat3",
                    103,
                    new Document()
                        .append("f1", null)
                ),
                new CategoryBuckets(
                    "",
                    0,
                    Map.of()
                )
            ),
            Arguments.of(
                categoryBsonFor(
                    "cat4",
                    104,
                    new Document()
                        .append("f1", List.of())
                ),
                new CategoryBuckets(
                    "cat4",
                    104,
                    Map.of("f1", List.of())
                )
            ),
            Arguments.of(
                categoryBsonFor(
                    "cat5",
                    105,
                    new Document()
                        .append("f1", List.of(List.of(0d, 5d), List.of(5d, 10d, 15d), List.of(10d, 25d)))
                ),
                new CategoryBuckets(
                    "cat5",
                    105,
                    Map.of(
                        "f1", List.of(new Range(0d, 5d), new Range(10d, 25d))
                    )
                )
            )
        );
    }

    private static Document categoryBsonFor(String name, Integer count, Document fields) {
        return new Document()
            .append("category", name)
            .append("count", count)
            .append("buckets", fields);
    }

    private static List<List<Double>> randRanges() {
        var rand = new Random();
        var bucketCount = rand.nextInt(16) + 5;
        var buckets = new ArrayList<List<Double>>();

        double currentLow = 0.0;
        for (int i = 0; i < bucketCount; i++) {
            var increment = rand.nextInt(20) + 1;
            var currentHigh = currentLow + increment;
            var bucket = new ArrayList<Double>();

            bucket.add(currentLow);
            bucket.add(currentHigh);

            buckets.add(bucket);
            currentLow = currentHigh;
        }
        return buckets;
    }

    private static List<Range> toSSRanges(List<List<Double>> ranges) {
        return ranges.stream().map(b -> new Range(b.getFirst(), b.getLast())).toList();
    }
}
