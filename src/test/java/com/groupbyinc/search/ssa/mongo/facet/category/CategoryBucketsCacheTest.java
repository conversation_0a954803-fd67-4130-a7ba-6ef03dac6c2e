package com.groupbyinc.search.ssa.mongo.facet.category;

import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;

class CategoryBucketsCacheTest {
    private static final Duration CACHE_TTL = Duration.ofMillis(100);
    private CategoryBucketsCache cache;

    @BeforeEach
    void setUp() {
        cache = new CategoryBucketsCache(CACHE_TTL);
    }

    @Test
    void put_get_one() {
        //given
        var cat = new CategoryBuckets("1", 1, Map.of());

        //when
        cache.put(cat);
        var result = cache.get(cat.name());

        //then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(cat);

        //when
        waitCacheExpiration();
        result = cache.get(cat.name());

        //then
        assertThat(result).isNull();
    }

    @Test
    void put_get_many() {
        //given
        var cats = List.of(
            new CategoryBuckets("1", 1, Map.of()),
            new CategoryBuckets("2", 2, Map.of()),
            new CategoryBuckets("3", 3, Map.of())
        );

        //when
        cache.put(cats);

        //then
        for (var cat : cats) {
            var result = cache.get(cat.name());
            assertThat(result).isNotNull();
            assertThat(result).isEqualTo(cat);
        }

        //when
        waitCacheExpiration();

        //then
        for (var cat : cats) {
            var result = cache.get(cat.name());
            assertThat(result).isNull();
        }
    }

    @Test
    void remove() {
        //given
        var cat = new CategoryBuckets("1", 1, Map.of());

        //when
        cache.put(cat);
        var result = cache.get(cat.name());

        //then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(cat);

        //when
        cache.remove(cat.name());

        //then
        assertThat(cache.get(cat.name())).isNull();
    }

    @SneakyThrows
    private void waitCacheExpiration() {
        var ttl = CACHE_TTL.toMillis();
        TimeUnit.MILLISECONDS.sleep(ttl + ttl / 2);
    }
}
