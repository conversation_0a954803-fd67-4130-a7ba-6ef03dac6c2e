package com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("MongoComparisonExpression Tests")
class MongoSearchComparisonExpressionTest {

    @Test
    @DisplayName("MongoSearchComparisonExpression equal Test")
    public void mongoSearchComparisonExpressionEqualTest() {
        var result = new MongoSearchComparisonExpression("priceInfo.price", "=", 1.0).toFilter();

        var expected = """
            {
              "equals": {
                "value": 1.0,
                "path": "indexables.priceInfo.price"
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression greaterThan Test")
    public void mongoSearchComparisonExpressionGreaterThanTest() {
        var result = new MongoSearchComparisonExpression("priceInfo.price", ">", 1.0).toFilter();

        var expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gt": 1.0
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression greaterThanOrEqual Test")
    public void mongoSearchComparisonExpressionGreaterThanOrEqualTest() {
        var result = new MongoSearchComparisonExpression("priceInfo.price", ">=", 1.0).toFilter();

        var expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "gte": 1.0
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression lessThan Test")
    public void mongoSearchComparisonExpressionLessThanTest() {
        var result = new MongoSearchComparisonExpression("priceInfo.price", "<", 1.0).toFilter();

        var expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "lt": 1.0
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression lessThanOrEqual Test")
    public void mongoSearchComparisonExpressionLessThanOrEqualTest() {
        var result = new MongoSearchComparisonExpression("priceInfo.price", "<=", 1.0).toFilter();

        var expected = """
            {
              "range": {
                "path": "indexables.priceInfo.price",
                "lte": 1.0
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression not equal Test")
    public void mongoSearchComparisonExpressionNotEqualTest() {
        var result = new MongoSearchComparisonExpression("priceInfo.price", "!=", 1.0).toFilter();

        var expected = """
            {
              "compound": {
                "mustNot": [
                  {
                    "equals": {
                      "value": 1.0,
                      "path": "indexables.priceInfo.price"
                    }
                  }
                ]
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression unknown Test")
    public void mongoSearchComparisonExpressionUnknownTest() {
        try {
            new MongoSearchComparisonExpression("priceInfo.price", "&", 1.0).toFilter();
        } catch (UnsupportedOperationException e) {
            assertThat(e).isInstanceOf(UnsupportedOperationException.class);
        }
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression equal (Inventory) Test")
    public void mongoSearchComparisonExpressionEqualInventoryTest() {
        var result = new MongoSearchComparisonExpression("localInventories.priceInfo.price", "place", "=", 1.0)
            .toFilter();

        var expected = """
            {
              "embeddedDocument": {
                "path": "indexables.localInventories",
                "operator": {
                  "compound": {
                    "filter": [
                      {
                        "equals": {
                          "path": "indexables.localInventories.placeId",
                          "value": "place"
                        }
                      },
                      {
                        "equals": {
                          "value": 1.0,
                          "path": "indexables.localInventories.priceInfo.price"
                        }
                      }
                    ]
                  }
                }
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression greaterThan (Inventory) Test")
    public void mongoSearchComparisonExpressionGreaterThanInventoryTest() {
        var result = new MongoSearchComparisonExpression("localInventories.priceInfo.price", "place", ">", 1.0)
            .toFilter();

        var expected = """
            {
              "embeddedDocument": {
                "path": "indexables.localInventories",
                "operator": {
                  "compound": {
                    "filter": [
                      {
                        "equals": {
                          "path": "indexables.localInventories.placeId",
                          "value": "place"
                        }
                      },
                      {
                        "range": {
                          "path": "indexables.localInventories.priceInfo.price",
                          "gt": 1.0
                        }
                      }
                    ]
                  }
                }
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression greaterThanOrEqual (Inventory) Test")
    public void mongoSearchComparisonExpressionGreaterThanOrEqualInventoryTest() {
        var result = new MongoSearchComparisonExpression("localInventories.priceInfo.price", "place", ">=", 1.0)
            .toFilter();

        var expected = """
            {
              "embeddedDocument": {
                "path": "indexables.localInventories",
                "operator": {
                  "compound": {
                    "filter": [
                      {
                        "equals": {
                          "path": "indexables.localInventories.placeId",
                          "value": "place"
                        }
                      },
                      {
                        "range": {
                          "path": "indexables.localInventories.priceInfo.price",
                          "gte": 1.0
                        }
                      }
                    ]
                  }
                }
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression lessThan (Inventory) Test")
    public void mongoSearchComparisonExpressionLessThanInventoryTest() {
        var result = new MongoSearchComparisonExpression("localInventories.priceInfo.price", "place", "<", 1.0)
            .toFilter();

        var expected = """
            {
              "embeddedDocument": {
                "path": "indexables.localInventories",
                "operator": {
                  "compound": {
                    "filter": [
                      {
                        "equals": {
                          "path": "indexables.localInventories.placeId",
                          "value": "place"
                        }
                      },
                      {
                        "range": {
                          "path": "indexables.localInventories.priceInfo.price",
                          "lt": 1.0
                        }
                      }
                    ]
                  }
                }
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression lessThanOrEqual (Inventory) Test")
    public void mongoSearchComparisonExpressionLessThanOrEqualInventoryTest() {
        var result = new MongoSearchComparisonExpression("localInventories.priceInfo.price", "place", "<=", 1.0)
            .toFilter();

        var expected = """
            {
              "embeddedDocument": {
                "path": "indexables.localInventories",
                "operator": {
                  "compound": {
                    "filter": [
                      {
                        "equals": {
                          "path": "indexables.localInventories.placeId",
                          "value": "place"
                        }
                      },
                      {
                        "range": {
                          "path": "indexables.localInventories.priceInfo.price",
                          "lte": 1.0
                        }
                      }
                    ]
                  }
                }
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression not equal (Inventory) Test")
    public void mongoSearchComparisonExpressionNotEqualInventoryTest() {
        var result = new MongoSearchComparisonExpression("localInventories.priceInfo.price", "place", "!=", 1.0)
            .toFilter();

        var expected = """
            {
              "compound": {
                "mustNot": [
                  {
                    "embeddedDocument": {
                      "path": "indexables.localInventories",
                      "operator": {
                        "compound": {
                          "filter": [
                            {
                              "equals": {
                                "path": "indexables.localInventories.placeId",
                                "value": "place"
                              }
                            },
                            {
                              "equals": {
                                "value": 1.0,
                                "path": "indexables.localInventories.priceInfo.price"
                              }
                            }
                          ]
                        }
                      }
                    }
                  }
                ]
              }
            }
        """;

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoSearchComparisonExpression unknown (Inventory) Test")
    public void mongoSearchComparisonExpressionUnknownInventoryTest() {
        try {
            new MongoSearchComparisonExpression("localInventories.priceInfo.price", "place", "&", 1.0)
                .toFilter();
        } catch (UnsupportedOperationException e) {
            assertThat(e).isInstanceOf(UnsupportedOperationException.class);
        }
    }

}
