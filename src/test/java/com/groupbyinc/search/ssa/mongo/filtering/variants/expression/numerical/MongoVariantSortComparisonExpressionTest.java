package com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical;

import org.bson.Document;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortRangeExpressionTest.EXPECTED_FILE_PATH_PREFIX;
import static com.groupbyinc.search.ssa.stub.ResourceUtils.getResourceContents;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("MongoVariantSortComparisonExpression Tests")
class MongoVariantSortComparisonExpressionTest {

    @Test
    @DisplayName("MongoVariantSortComparisonExpression equal Test")
    public void mongoVariantSortComparisonExpressionEqualTest() {
        var result = new MongoVariantSortComparisonExpression("priceInfo.price", "=", 1.0).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-comparison-expression-equal.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression greaterThan Test")
    public void mongoVariantSortComparisonExpressionGreaterThanTest() {
        var result = new MongoVariantSortComparisonExpression("priceInfo.price", ">", 1.0).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-comparison-expression-greater-than.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression greaterThanOrEqual Test")
    public void mongoVariantSortComparisonExpressionGreaterThanOrEqualTest() {
        var result = new MongoVariantSortComparisonExpression("priceInfo.price", ">=", 1.0).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-comparison-expression-greater-than-or-equal.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression lessThan Test")
    public void mongoVariantSortComparisonExpressionLessThanTest() {
        var result = new MongoVariantSortComparisonExpression("priceInfo.price", "<", 1.0).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-comparison-expression-less-than.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression lessThanOrEqual Test")
    public void mongoVariantSortComparisonExpressionLessThanOrEqualTest() {
        var result = new MongoVariantSortComparisonExpression("priceInfo.price", "<=", 1.0).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-comparison-expression-less-than-or-equal.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression not equal Test")
    public void mongoVariantSortComparisonExpressionNotEqualTest() {
        var result = new MongoVariantSortComparisonExpression("priceInfo.price", "!=", 1.0).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-comparison-expression-not-equal.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression unknown Test")
    public void mongoVariantSortComparisonExpressionUnknownTest() {
        try {
            new MongoVariantSortComparisonExpression("priceInfo.price", "&", 1.0).toFilter();
        } catch (UnsupportedOperationException e) {
            assertThat(e).isInstanceOf(UnsupportedOperationException.class);
        }
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression equal (Inventory) Test")
    public void mongoVariantSortComparisonExpressionEqualInventoryTest() {
        var result = new MongoVariantSortComparisonExpression("priceInfo.price", "place", "=", 1.0).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-comparison-expression-equal-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression greaterThan (Inventory) Test")
    public void mongoVariantSortComparisonExpressionGreaterThanInventoryTest() {
        var result = new MongoVariantSortComparisonExpression("priceInfo.price", "place", ">", 1.0).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-comparison-expression-greater-than-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression greaterThanOrEqual (Inventory) Test")
    public void mongoVariantSortComparisonExpressionGreaterThanOrEqualInventoryTest() {
        var result = new MongoVariantSortComparisonExpression("priceInfo.price", "place", ">=", 1.0).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX
                + "mongo-variant-sort-comparison-expression-greater-than-or-equal-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression lessThan (Inventory) Test")
    public void mongoVariantSortComparisonExpressionLessThanInventoryTest() {
        var result = new MongoVariantSortComparisonExpression("priceInfo.price", "place", "<", 1.0).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-comparison-expression-less-than-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression lessThanOrEqual (Inventory) Test")
    public void mongoVariantSortComparisonExpressionLessThanOrEqualInventoryTest() {
        var result = new MongoVariantSortComparisonExpression("priceInfo.price", "place", "<=", 1.0).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-comparison-expression-less-than-or-equal-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression not equal (Inventory) Test")
    public void mongoVariantSortComparisonExpressionNotEqualInventoryTest() {
        var result = new MongoVariantSortComparisonExpression("priceInfo.price", "place", "!=", 1.0).toFilter();

        var expected = getResourceContents(
            EXPECTED_FILE_PATH_PREFIX + "mongo-variant-sort-comparison-expression-not-equal-inventory.json"
        );

        assertThat(result.toJson()).isEqualTo(Document.parse(expected).toJson());
    }

    @Test
    @DisplayName("MongoVariantSortComparisonExpression unknown (Inventory) Test")
    public void mongoVariantSortComparisonExpressionUnknownInventoryTest() {
        try {
            new MongoVariantSortComparisonExpression("priceInfo.price", "place", "&", 1.0).toFilter();
        } catch (UnsupportedOperationException e) {
            assertThat(e).isInstanceOf(UnsupportedOperationException.class);
        }
    }

}
