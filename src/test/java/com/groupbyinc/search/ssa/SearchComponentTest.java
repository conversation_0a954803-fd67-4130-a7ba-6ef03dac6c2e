package com.groupbyinc.search.ssa;

import com.groupbyinc.search.ssa.api.dto.FacetSearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.FacetSearchResponseDto;
import com.groupbyinc.search.ssa.api.dto.NavigationTypeDto;
import com.groupbyinc.search.ssa.api.dto.RecordDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SearchResponseDto;
import com.groupbyinc.search.ssa.api.dto.SelectedRefinementDto;
import com.groupbyinc.search.ssa.api.dto.SortDto;
import com.groupbyinc.search.ssa.api.dto.conversation.ConversationalSearchConfigDto;
import com.groupbyinc.search.ssa.api.dto.conversation.ProductAttributeValueDto;
import com.groupbyinc.search.ssa.api.dto.conversation.SelectedAnswerDto;
import com.groupbyinc.search.ssa.api.dto.conversation.UserAnswerDto;
import com.groupbyinc.search.ssa.api.dto.tiles.TilesNavigationDto;
import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngineType;
import com.groupbyinc.search.ssa.application.core.search.strategy.base.DefaultSearchStrategy;
import com.groupbyinc.search.ssa.beacon.client.DirectSearchBeaconRequest;
import com.groupbyinc.search.ssa.core.conversation.ConversationalSearchResult;
import com.groupbyinc.search.ssa.core.navigation.Facet;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.fixture.AuthFixture;
import com.groupbyinc.search.ssa.metrics.opentelemetry.MeterRegistry;
import com.groupbyinc.search.ssa.stub.ResourceUtils;

import com.google.cloud.retail.v2.SearchResponse;
import com.google.cloud.retail.v2.SearchServiceClient;
import com.google.cloud.retail.v2.SearchServiceClient.SearchPagedResponse;
import com.google.protobuf.util.JsonFormat;
import io.micronaut.context.ApplicationContext;
import io.micronaut.core.type.Argument;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.HttpStatus;
import io.micronaut.http.client.exceptions.HttpClientResponseException;
import io.opentelemetry.api.metrics.DoubleHistogram;
import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.context.ContextKey;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.beacon.DirectSearchBeaconSender.META_FIELDS;
import static com.groupbyinc.search.ssa.beacon.config.DirectSearchBeaconConfiguration.EVENT_TYPE;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTION;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;
import static com.groupbyinc.search.ssa.util.Constants.GROUPBY_CUSTOMER_ID_HEADER;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.awaitility.Durations.FIVE_HUNDRED_MILLISECONDS;
import static org.awaitility.Durations.FIVE_SECONDS;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;

/**
 * This test uses a mocked {@link SearchServiceClient} as there is currently no embedded version of retail search. This is rather undesirable, but the
 * security of having one end-to-end test should be useful even if it is rather rigid. For this reason, I would not recommend adding too many tests
 * here.
 */
@DisplayName("SearchComponent Tests")
class SearchComponentTest extends ComponentTest {

    private SearchServiceClient searchServiceClient;
    private SearchServiceClient.SearchPage searchPage;

    public static final String[] IGNORING_FIELDS = { "id",
        "originalRequest.visitorId",
        "metadata.totalTime",
        "metadata.retailTime",
        "records.metadata",
        "sponsoredRecords",
        "siteParams",
        "availableNavigation.metadata",
        "warnings",
        "searchStrategies",
        "engineSource",
        "requestServed" };

    @BeforeAll
    void waitForServerToBecomeReady() {
        await()
            .atMost(FIVE_SECONDS)
            .pollInterval(FIVE_HUNDRED_MILLISECONDS)
            .ignoreException(HttpClientResponseException.class)
            .untilAsserted(() -> {
                var httpResponse = httpClient.toBlocking().exchange("/health/readiness");
                assertThat(httpResponse)
                    .extracting(HttpResponse::getStatus)
                    .isEqualTo(HttpStatus.OK);
            });
    }

    @BeforeEach
    void setUp() {
        searchPage = mock(SearchServiceClient.SearchPage.class);
        var searchPagedResponse = mock(SearchPagedResponse.class);
        given(searchPagedResponse.getPage()).willReturn(searchPage);
        given(searchServiceClient.search(any())).willReturn(searchPagedResponse);
    }

    @Test
    @DisplayName("Can perform a search and get results")
    void canPerformASearchAndReturnResults() throws Exception {
        var authFixture = AuthFixture.merchandiserDefaultRole(APPAREL);

        var searchResponseBuilder = SearchResponse.newBuilder();
        JsonFormat.parser().merge(
            ResourceUtils.getResourceContents("retail/retail-response-with-products.json"),
            searchResponseBuilder
        );
        given(searchPage.getResponse()).willReturn(searchResponseBuilder.build());

        var httpResponse = httpClient.toBlocking().exchange(
            HttpRequest.POST(
                    "/api/search",
                    SearchRequestDto.builder()
                        .query("blue sweater")
                        .area(PRODUCTION)
                        .collection(PRODUCTS_CLOTHING)
                        .pageSize(2)
                        .sorts(List.of(new SortDto("price", SortDto.OrderDto.Descending)))
                        .pageCategories(List.of("Sales > 2017 Black Friday Deals"))
                        .preFilter("price < 100")
                        .inventoryStoreId("MTL")
                        .build()
                )
                .bearerAuth(authFixture.getToken())
                .header(GROUPBY_CUSTOMER_ID_HEADER, APPAREL),
            Argument.of(SearchResponseDto.class)
        );

        assertThat((Object) httpResponse.getStatus()).isEqualTo(HttpStatus.OK);

        var expectedSearchResponseDto = objectMapper.readValue(
            ResourceUtils.getResourceContents("retail/api-response-with-products.json"),
            SearchResponseDto.class
        );
        assertThat(httpResponse.body())
            .isNotNull()
            .usingRecursiveComparison()
            .ignoringAllOverriddenEquals()
            .ignoringFields(IGNORING_FIELDS)
            .isEqualTo(expectedSearchResponseDto);

        var expectedDirectSearchBeaconRequest = httpResponse.getBody()
            .map(searchResponseDto -> new DirectSearchBeaconRequest<>(
                    APPAREL,
                    searchResponseDto.getId(),
                    searchResponseDto
                        .toBuilder()
                        .engineSource(SearchEngineType.GOOGLE)
                        .searchStrategies(List.of(DefaultSearchStrategy.class.getSimpleName()))
                        .originalQuery(searchResponseDto.getOriginalRequest().getQuery())
                        .records(cleanUpRecords(searchResponseDto.getRecords()))
                        .build(),
                    EVENT_TYPE,
                    searchResponseDto.getExperiments()
                )
            )
            .orElseThrow();
        await()
            .atMost(FIVE_SECONDS)
            .untilAsserted(() -> directSearchBeaconFixture.assertDirectSearchBeaconRequestWasSent(
                objectMapper.writeValueAsString(expectedDirectSearchBeaconRequest)
            ));
    }

    @ParameterizedTest
    @MethodSource("tilesCases")
    @DisplayName("Can perform a search requesting tiles and get results")
    void canPerformASearchWithTilesAndReturnResults(
        boolean requestTiles,
        List<com.groupbyinc.search.ssa.api.dto.tiles.ProductAttributeValueDto>
            appliedTiles,
        String expectedRetailResponseFileName,
        String expectedApiResponseFileName
    ) throws Exception {

        var authFixture = AuthFixture.merchandiserDefaultRole(APPAREL);

        var searchResponseBuilder = SearchResponse.newBuilder();
        JsonFormat.parser().merge(
            ResourceUtils.getResourceContents("retail/" + expectedRetailResponseFileName),
            searchResponseBuilder
        );
        given(searchPage.getResponse()).willReturn(searchResponseBuilder.build());

        var httpResponse = httpClient.toBlocking().exchange(
            HttpRequest.POST(
                    "/api/search",
                    SearchRequestDto.builder()
                        .query("blue sweater")
                        .area(PRODUCTION)
                        .collection(PRODUCTS_CLOTHING)
                        .pageSize(1)
                        .inventoryStoreId("MTL")
                        .tilesNavigation(new TilesNavigationDto(requestTiles, appliedTiles))
                        .build()
                )
                .bearerAuth(authFixture.getToken())
                .header(GROUPBY_CUSTOMER_ID_HEADER, APPAREL),
            Argument.of(SearchResponseDto.class)
        );

        assertThat((Object) httpResponse.getStatus()).isEqualTo(HttpStatus.OK);

        var expectedSearchResponseDto = objectMapper.readValue(
            ResourceUtils.getResourceContents("retail/" + expectedApiResponseFileName),
            SearchResponseDto.class
        );
        assertThat(httpResponse.body())
            .isNotNull()
            .usingRecursiveComparison()
            .ignoringAllOverriddenEquals()
            .ignoringFields(
                "id",
                "originalRequest.visitorId",
                "metadata.totalTime",
                "metadata.retailTime",
                "records.metadata",
                "sponsoredRecords",
                "siteParams",
                "availableNavigation.metadata",
                "warnings",
                "searchStrategies",
                "engineSource",
                "requestServed"
            )
            .isEqualTo(expectedSearchResponseDto);

        var expectedDirectSearchBeaconRequest = httpResponse.getBody()
            .map(searchResponseDto -> new DirectSearchBeaconRequest<>(
                    APPAREL,
                    searchResponseDto.getId(),
                    searchResponseDto
                        .toBuilder()
                        .engineSource(SearchEngineType.GOOGLE)
                        .searchStrategies(List.of(DefaultSearchStrategy.class.getSimpleName()))
                        .originalQuery(searchResponseDto.getOriginalRequest().getQuery())
                        .records(cleanUpRecords(searchResponseDto.getRecords()))
                        .build(),
                    EVENT_TYPE,
                    searchResponseDto.getExperiments()
                )
            )
            .orElseThrow();
        await()
            .atMost(FIVE_SECONDS)
            .untilAsserted(() -> directSearchBeaconFixture.assertDirectSearchBeaconRequestWasSent(
                objectMapper.writeValueAsString(expectedDirectSearchBeaconRequest)
            ));
    }

    @Test
    @DisplayName("followupConversation false")
    void followupConversationFalse() throws Exception {
        var authFixture = AuthFixture.merchandiserDefaultRole(APPAREL);

        var searchResponseBuilder = SearchResponse.newBuilder();
        JsonFormat.parser().merge(
            ResourceUtils.getResourceContents("retail/retail-conversationFalse-response.json"),
            searchResponseBuilder
        );
        given(searchPage.getResponse()).willReturn(searchResponseBuilder.build());

        var httpResponse = httpClient.toBlocking().exchange(
            HttpRequest.POST(
                    "/api/search",
                    SearchRequestDto.builder()
                        .query("dress")
                        .area(PRODUCTION)
                        .collection(PRODUCTS_CLOTHING)
                        .conversationalSearchConfig(new ConversationalSearchConfigDto(false))
                        .build())
                .bearerAuth(authFixture.getToken())
                .header(GROUPBY_CUSTOMER_ID_HEADER, APPAREL),
            Argument.of(SearchResponseDto.class));

        assertThat((Object) httpResponse.getStatus()).isEqualTo(HttpStatus.OK);

        var expectedSearchResponseDto = objectMapper.readValue(
            ResourceUtils.getResourceContents("retail/api-conversationFalse-response.json"),
            SearchResponseDto.class
        );
        assertThat(httpResponse.body())
            .isNotNull()
            .usingRecursiveComparison()
            .ignoringAllOverriddenEquals()
            .ignoringFields(IGNORING_FIELDS)
            .isEqualTo(expectedSearchResponseDto);

        assertThat(httpResponse.body().getConversationalSearchResult()).isNull();
    }

    @Test
    @DisplayName("followupConversation true")
    void followupConversationTrue() throws Exception {
        var authFixture = AuthFixture.merchandiserDefaultRole(APPAREL);

        var searchResponseBuilder = SearchResponse.newBuilder();
        JsonFormat.parser().merge(
            ResourceUtils.getResourceContents("retail/retail-conversationTrue-response.json"),
            searchResponseBuilder
        );
        given(searchPage.getResponse()).willReturn(searchResponseBuilder.build());

        var httpResponse = httpClient.toBlocking().exchange(
            HttpRequest.POST(
                    "/api/search",
                    SearchRequestDto.builder()
                        .query("dress")
                        .area(PRODUCTION)
                        .collection(PRODUCTS_CLOTHING)
                        .conversationalSearchConfig(new ConversationalSearchConfigDto(true))
                        .build())
                .bearerAuth(authFixture.getToken())
                .header(GROUPBY_CUSTOMER_ID_HEADER, APPAREL),
            Argument.of(SearchResponseDto.class));

        assertThat((Object) httpResponse.getStatus()).isEqualTo(HttpStatus.OK);

        var expectedSearchResponseDto = objectMapper.readValue(
            ResourceUtils.getResourceContents("retail/api-conversationTrue-response.json"),
            SearchResponseDto.class
        );
        assertThat(httpResponse.body())
            .isNotNull()
            .usingRecursiveComparison()
            .ignoringAllOverriddenEquals()
            .ignoringFields(IGNORING_FIELDS)
            .isEqualTo(expectedSearchResponseDto);

        ConversationalSearchResult actual = httpResponse.body().getConversationalSearchResult();
        ConversationalSearchResult expected = expectedSearchResponseDto.getConversationalSearchResult();
        assertThat(actual.conversationId()).isEqualTo(expected.conversationId());
        assertThat(actual.followupQuestion()).isEqualTo(expected.followupQuestion());
        assertThat(actual.suggestedAnswers()).isEqualTo(expected.suggestedAnswers());
    }

    @Test
    @DisplayName("followupConversation UserAnswer Text")
    void followupConversationUserAnswerText() throws Exception {
        var authFixture = AuthFixture.merchandiserDefaultRole(APPAREL);

        var searchResponseBuilder = SearchResponse.newBuilder();
        JsonFormat.parser().merge(
            ResourceUtils.getResourceContents("retail/retail-conversationUserAnswerText-response.json"),
            searchResponseBuilder
        );
        given(searchPage.getResponse()).willReturn(searchResponseBuilder.build());

        var httpResponse = httpClient.toBlocking().exchange(
            HttpRequest.POST(
                    "/api/search",
                    SearchRequestDto.builder()
                        .query("dress")
                        .area(PRODUCTION)
                        .collection(PRODUCTS_CLOTHING)
                        .conversationalSearchConfig(
                            new ConversationalSearchConfigDto(true, "c154d073-87f5-4edb-accd-587eabe014ff",
                                new UserAnswerDto("lavender")))
                        .build())
                .bearerAuth(authFixture.getToken())
                .header(GROUPBY_CUSTOMER_ID_HEADER, APPAREL),
            Argument.of(SearchResponseDto.class));

        assertThat((Object) httpResponse.getStatus()).isEqualTo(HttpStatus.OK);

        var expectedSearchResponseDto = objectMapper.readValue(
            ResourceUtils.getResourceContents("retail/api-conversationUserAnswerText-response.json"),
            SearchResponseDto.class
        );
        assertThat(httpResponse.body())
            .isNotNull()
            .usingRecursiveComparison()
            .ignoringAllOverriddenEquals()
            .ignoringFields(IGNORING_FIELDS)
            .isEqualTo(expectedSearchResponseDto);

        ConversationalSearchResult actual = httpResponse.body().getConversationalSearchResult();
        ConversationalSearchResult expected = expectedSearchResponseDto.getConversationalSearchResult();
        assertThat(actual.conversationId()).isEqualTo(expected.conversationId());
        assertThat(actual.followupQuestion()).isEqualTo(expected.followupQuestion());
        assertThat(actual.suggestedAnswers()).isEqualTo(expected.suggestedAnswers());
        assertThat(httpResponse.body().getQuery()).isEqualTo("dress lavender");
        assertThat(httpResponse.body().getOriginalRequest().getQuery()).isEqualTo("dress lavender");
    }

    @Test
    @DisplayName("followupConversation UserAnswer Selected")
    void followupConversationUserAnswerSelected() throws Exception {
        var authFixture = AuthFixture.merchandiserDefaultRole(APPAREL);

        var searchResponseBuilder = SearchResponse.newBuilder();
        JsonFormat.parser().merge(
            ResourceUtils.getResourceContents("retail/retail-conversationUserAnswerSelected-response.json"),
            searchResponseBuilder
        );
        given(searchPage.getResponse()).willReturn(searchResponseBuilder.build());

        var httpResponse = httpClient.toBlocking().exchange(
            HttpRequest.POST(
                    "/api/search",
                    SearchRequestDto.builder()
                        .query("dress")
                        .area(PRODUCTION)
                        .collection(PRODUCTS_CLOTHING)
                        .conversationalSearchConfig(new ConversationalSearchConfigDto(
                            true,
                            "c154d073-87f5-4edb-accd-587eabe014ff",
                            new UserAnswerDto(new SelectedAnswerDto(new ProductAttributeValueDto("attributes.colors", "yellow")))))
                        .build())
                .bearerAuth(authFixture.getToken())
                .header(GROUPBY_CUSTOMER_ID_HEADER, APPAREL),
            Argument.of(SearchResponseDto.class));

        assertThat((Object) httpResponse.getStatus()).isEqualTo(HttpStatus.OK);

        var expectedSearchResponseDto = objectMapper.readValue(
            ResourceUtils.getResourceContents("retail/api-conversationUserAnswerSelected-response.json"),
            SearchResponseDto.class
        );
        assertThat(httpResponse.body())
            .isNotNull()
            .usingRecursiveComparison()
            .ignoringAllOverriddenEquals()
            .ignoringFields(IGNORING_FIELDS)
            .isEqualTo(expectedSearchResponseDto);

        ConversationalSearchResult actual = httpResponse.body().getConversationalSearchResult();
        ConversationalSearchResult expected = expectedSearchResponseDto.getConversationalSearchResult();
        assertThat(actual.conversationId()).isEqualTo(expected.conversationId());
        assertThat(actual.followupQuestion()).isEqualTo(expected.followupQuestion());
        assertThat(actual.suggestedAnswers()).isEqualTo(expected.suggestedAnswers());

        var actualRefinements = httpResponse.body().getOriginalRequest().getRefinements();
        var expectedRefinements = expectedSearchResponseDto.getOriginalRequest().getRefinements();
        assertThat(actualRefinements.size()).isEqualTo(expectedRefinements.size());
        assertThat(actualRefinements).isEqualTo(expectedRefinements);

        var actualSelectedNavigation = httpResponse.body().getSelectedNavigation();
        var expectedSelectedNavigation = expectedSearchResponseDto.getSelectedNavigation();
        assertThat(actualSelectedNavigation.size()).isEqualTo(expectedSelectedNavigation.size());
        assertThat(actualSelectedNavigation).isEqualTo(expectedSelectedNavigation);

        var actualFilter = httpResponse.body().getFilter();
        var expectedFilter = expectedSearchResponseDto.getFilter();
        assertThat(actualFilter).isEqualTo(expectedFilter);
    }

    @Test
    @DisplayName("followupConversation UserAnswer Selected same refinement exists")
    void followupConversationUserAnswerSelectedSameRefinementExists() throws Exception {
        var authFixture = AuthFixture.merchandiserDefaultRole(APPAREL);

        var searchResponseBuilder = SearchResponse.newBuilder();
        JsonFormat.parser().merge(
            ResourceUtils.getResourceContents("retail/retail-conversationUserAnswerSelected-response.json"),
            searchResponseBuilder
        );
        given(searchPage.getResponse()).willReturn(searchResponseBuilder.build());

        var httpResponse = httpClient.toBlocking().exchange(
            HttpRequest.POST(
                    "/api/search",
                    SearchRequestDto.builder()
                        .query("dress")
                        .area(PRODUCTION)
                        .collection(PRODUCTS_CLOTHING)
                        .refinements(List.of(new SelectedRefinementDto("attributes.colors", NavigationTypeDto.Value, "yellow", false)))
                        .conversationalSearchConfig(new ConversationalSearchConfigDto(
                            true,
                            "c154d073-87f5-4edb-accd-587eabe014ff",
                            new UserAnswerDto(new SelectedAnswerDto(new ProductAttributeValueDto("attributes.colors", "yellow")))))
                        .build())
                .bearerAuth(authFixture.getToken())
                .header(GROUPBY_CUSTOMER_ID_HEADER, APPAREL),
            Argument.of(SearchResponseDto.class));

        assertThat((Object) httpResponse.getStatus()).isEqualTo(HttpStatus.OK);

        var expectedSearchResponseDto = objectMapper.readValue(
            ResourceUtils.getResourceContents("retail/api-conversationUserAnswerSelected-response.json"),
            SearchResponseDto.class
        );
        assertThat(httpResponse.body())
            .isNotNull()
            .usingRecursiveComparison()
            .ignoringAllOverriddenEquals()
            .ignoringFields(IGNORING_FIELDS)
            .isEqualTo(expectedSearchResponseDto);

        ConversationalSearchResult actual = httpResponse.body().getConversationalSearchResult();
        ConversationalSearchResult expected = expectedSearchResponseDto.getConversationalSearchResult();
        assertThat(actual.conversationId()).isEqualTo(expected.conversationId());
        assertThat(actual.followupQuestion()).isEqualTo(expected.followupQuestion());
        assertThat(actual.suggestedAnswers()).isEqualTo(expected.suggestedAnswers());

        var actualRefinements = httpResponse.body().getOriginalRequest().getRefinements();
        var expectedRefinements = expectedSearchResponseDto.getOriginalRequest().getRefinements();
        assertThat(actualRefinements.size()).isEqualTo(expectedRefinements.size());
        // even 'or' was 'false' in the request, it should be overwritten by 'true' from 'selectedAnswer'
        assertThat(actualRefinements.getFirst().or()).isEqualTo(true);

        var actualSelectedNavigation = httpResponse.body().getSelectedNavigation();
        var expectedSelectedNavigation = expectedSearchResponseDto.getSelectedNavigation();
        assertThat(actualSelectedNavigation.size()).isEqualTo(expectedSelectedNavigation.size());
        assertThat(actualSelectedNavigation).isEqualTo(expectedSelectedNavigation);

        var actualFilter = httpResponse.body().getFilter();
        var expectedFilter = expectedSearchResponseDto.getFilter();
        assertThat(actualFilter).isEqualTo(expectedFilter);
    }

    @Test
    @DisplayName("Can perform a facet search and get results")
    void canPerformFacetSearchAndReturnResult() throws Exception {
        var authFixture = AuthFixture.merchandiserDefaultRole(APPAREL);

        var searchResponseBuilder = SearchResponse.newBuilder();
        JsonFormat.parser()
            .merge(ResourceUtils.getResourceContents("retail/retail-response-with-facet.json"),
                searchResponseBuilder);
        given(searchPage.getResponse()).willReturn(searchResponseBuilder.build());

        var httpResponse = httpClient.toBlocking().exchange(
            HttpRequest.POST(
                    "/api/search/facet",
                    new FacetSearchRequestDto(
                        Facet.builder()
                            .navigationName("categories")
                            .displayName("categories")
                            .type(NavigationType.VALUE)
                            .contains("Shoes > Boots")
                            .prefix("")
                            .build(),
                        SearchRequestDto.builder()
                            .area(PRODUCTION)
                            .collection(PRODUCTS_CLOTHING)
                            .build()
                    ))
                .bearerAuth(authFixture.getToken())
                .header(GROUPBY_CUSTOMER_ID_HEADER, APPAREL),
            Argument.of(FacetSearchResponseDto.class)
        );

        assertThat((Object) httpResponse.getStatus()).isEqualTo(HttpStatus.OK);

        var expectedFacetSearchResponseDto = objectMapper.readValue(
            ResourceUtils.getResourceContents("retail/api-response-with-facets.json"),
            FacetSearchResponseDto.class);
        assertThat(httpResponse.body())
            .isNotNull()
            .usingRecursiveComparison()
            .ignoringAllOverriddenEquals()
            .ignoringFields(
                "id",
                "originalRequest.visitorId",
                "metadata.totalTime",
                "metadata.retailTime",
                "records.metadata",
                "sponsoredRecords",
                "siteParams",
                "availableNavigation.metadata"
            )
            .isEqualTo(expectedFacetSearchResponseDto);

    }

    @Override
    protected Map<String, Object> getProperties() {

        var properties = super.getProperties();
        properties.put("direct-search-beacon.enabled", "true");
        return properties;
    }

    @Override
    protected void registerSingletons(ApplicationContext appContext) {

        searchServiceClient = mock(SearchServiceClient.class);
        Map<ContextKey<String>, LongCounter> metricLongCounters = new HashMap<>();
        Map<ContextKey<Double>, DoubleHistogram> metricDoubleHistograms = new HashMap<>();
        var meterRegistry = new MeterRegistry(metricLongCounters, metricDoubleHistograms, null);
        appContext.registerSingleton(searchServiceClient);
        appContext.registerSingleton(meterRegistry);
    }

    private List<RecordDto> cleanUpRecords(List<RecordDto> records) {
        return records.stream().map(r -> {
                r.getMetadata().keySet().removeIf(k -> !(META_FIELDS.contains(k)));
                return new RecordDto(
                    r.getId(),
                    r.getUrl(),
                    r.getTitle(),
                    r.getCollection(),
                    r.getMetadata(),
                    null,
                    null,
                    null,
                    r.getPrimaryProductId(),
                    r.getLabels()
                );
            }
        ).toList();
    }

    private static Stream<Arguments> tilesCases() {
        return Stream.of(
            Arguments.of(true,
                null,
                "retail-response-with-products-and-tiles-on.json",
                "api-response-with-products-and-tiles-on.json"),
            Arguments.of(false,
                null,
                "retail-response-with-products-and-tiles-off.json",
                "api-response-with-products-and-tiles-off.json"),
            Arguments.of(true,
                List.of(
                    new com.groupbyinc.search.ssa.api.dto.tiles.ProductAttributeValueDto("sizes", "large"),
                    new com.groupbyinc.search.ssa.api.dto.tiles.ProductAttributeValueDto(
                        "attributes.weight",
                        "light"
                    )
                ),
                "retail-response-with-products-and-tiles-applied.json",
                "api-response-with-products-and-tiles-applied.json")
        );
    }
}
