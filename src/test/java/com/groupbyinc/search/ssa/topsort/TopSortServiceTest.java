package com.groupbyinc.search.ssa.topsort;

import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.Pagination;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.RequestOptions;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.SponsoredRecordsRequest;
import com.groupbyinc.search.ssa.topsort.client.TopsortClient;
import com.groupbyinc.search.ssa.topsort.model.AuctionRequest;
import com.groupbyinc.search.ssa.topsort.model.AuctionResponse;
import com.groupbyinc.search.ssa.topsort.model.TopsortConfiguration;

import io.micronaut.core.propagation.PropagatedContext;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.client.exceptions.HttpClientResponseException;
import lombok.SneakyThrows;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.retail.filteringold.RetailFilterServiceOld.PRODUCT_ID_FIELD;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.LOGIN_ID;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTION;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;
import static com.groupbyinc.search.ssa.stub.TestData.REQUEST_ID;
import static com.groupbyinc.search.ssa.stub.TestData.TEST_ENVIRONMENT;
import static com.groupbyinc.search.ssa.stub.TestData.VISITOR_ID;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;
import static com.groupbyinc.search.ssa.topsort.TopSortService.AUTH_BEARER_FORMAT;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;

@ExtendWith(MockitoExtension.class)
class TopSortServiceTest {

    private static final int TOPSORT_MIN_PAGE_SIZE = 99;
    private static final String API_KEY = "apiKey";
    private static final String BEARER = AUTH_BEARER_FORMAT.formatted(API_KEY);

    private TopSortService topsortService;
    @Mock
    private TopsortClient topsortClient;
    @Mock
    private ConfigurationManager configurationManager;
    @Captor
    private ArgumentCaptor<AuctionRequest> requestCaptor;

    private PropagatedContext.Scope scope;

    @BeforeEach
    public void setup() {
        topsortService = new TopSortService(
            TOPSORT_MIN_PAGE_SIZE,
            "https://exampple.com",
            topsortClient,
            configurationManager
        );

        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    @Test
    void topsortApplyShouldAddSponsoredRecords() {
        given(
            configurationManager.getTopSortConfig(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCTION)
        ).willReturn(topsortConfig());

        var productIds = List.of("1", "2", "3", "4", "5");
        var records = productIds.stream()
            .map(id ->
                Record.of(
                    APPAREL_MERCHANDISER,
                    PRODUCTS_CLOTHING,
                    id,
                    id,
                    "title%s".formatted(id),
                    Map.of(PRODUCT_ID_FIELD, id)
                )
            )
            .toList();
        var sponsoredPositions = List.of(0, 1);
        var sp = SearchParameters.builder()
            .pagination(new Pagination(TOPSORT_MIN_PAGE_SIZE, 0L))
            .originalPagination(new Pagination(3, 0L))
            .sponsoredRecordsRequest(new SponsoredRecordsRequest(sponsoredPositions.size(), sponsoredPositions))
            .build();
        var sr = SearchResults.builder().records(records).build();
        var auctionIds = sr.getRecords().stream()
            .map(Record::getPrimaryProductId)
            .toList();

        var winners = List.of(
            new AuctionResponse.Winner(1, "products", records.getFirst().getProductId(), "bid1", "vendorId1"),
            new AuctionResponse.Winner(2, "products", records.get(1).getProductId(), "bid2", "vendorId2")
        );

        given(topsortClient.createAuction(requestCaptor.capture(), eq(BEARER))).willReturn(
            new AuctionResponse(List.of(new AuctionResponse.Result(winners, false)), null, null)
        );

        var result = topsortService.fillWithSponsoredProducts(sp, sr, auctionIds, false);

        assertThat(requestCaptor.getValue().auctions().getFirst().products().ids()).isEqualTo(productIds);
        assertThat(requestCaptor.getValue().auctions().getFirst().slots()).isEqualTo(sponsoredPositions.size());
        assertThat(result.getRecords()).hasSize(3);

        for (int i = 0; i < 3; i++) {
            assertThat(result.getRecords().get(i)).isEqualTo(records.get(i));
        }

        assertThat(result.getSponsoredRecords()).hasSize(2);
        assertThat(result.getSponsoredRecords().getFirst()).isNotSameAs(records.getFirst());
        assertThat(result.getSponsoredRecords().getFirst().getId()).isEqualTo(records.getFirst().getId());
        assertThat(result.getSponsoredRecords().getFirst().getPrimaryProductId())
            .isEqualTo(records.getFirst().getPrimaryProductId());
        assertThat(
            result.getSponsoredRecords().getFirst().getProductId()
        ).isEqualTo(records.getFirst().getProductId());
        assertThat(result.getSponsoredRecords().getFirst().getSponsored()).isTrue();
        assertThat(
            result.getSponsoredRecords().getFirst().getSponsoredInfo().topsort()
        ).isEqualTo(winners.getFirst());

        assertThat(result.getSponsoredRecords().get(1)).isNotSameAs(records.get(1));
        assertThat(result.getSponsoredRecords().get(1).getId()).isEqualTo(records.get(1).getId());
        assertThat(result.getSponsoredRecords().get(1).getPrimaryProductId())
            .isEqualTo(records.get(1).getPrimaryProductId());
        assertThat(result.getSponsoredRecords().get(1).getProductId()).isEqualTo(records.get(1).getProductId());
        assertThat(result.getSponsoredRecords().get(1).getSponsored()).isTrue();
        assertThat(result.getSponsoredRecords().get(1).getSponsoredInfo().topsort()).isEqualTo(winners.get(1));
    }

    @Test
    void topsortApplyShouldAddSponsoredRecordsWithBadRanking() {
        given(configurationManager.getTopSortConfig(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCTION))
            .willReturn(topsortConfig());
        var productIds = List.of("1", "2", "3", "4", "5");
        var records = productIds.stream()
            .map(id ->
                Record.of(
                    APPAREL_MERCHANDISER,
                    PRODUCTS_CLOTHING,
                    id,
                    id,
                    "title%s".formatted(id),
                    Map.of(PRODUCT_ID_FIELD, id)
                )
            )
            .toList();
        var sponsoredPositions = List.of(0, 1, 2);
        var sp = SearchParameters.builder()
            .pagination(new Pagination(TOPSORT_MIN_PAGE_SIZE, 0L))
            .originalPagination(new Pagination(3, 0L))
            .sponsoredRecordsRequest(new SponsoredRecordsRequest(sponsoredPositions.size(), sponsoredPositions))
            .build();
        var sr = SearchResults.builder().records(records).build();

        var winners = List.of(
            new AuctionResponse.Winner(2, "products", records.getFirst().getProductId(), "bid1", "vendorId1"),
            new AuctionResponse.Winner(1, "products", records.get(1).getProductId(), "bid2", "vendorId2"),
            new AuctionResponse.Winner(null, "products", records.get(2).getProductId(), "bid3", "vendorId3")
        );

        given(topsortClient.createAuction(requestCaptor.capture(), eq(BEARER))).willReturn(
            new AuctionResponse(List.of(new AuctionResponse.Result(winners, false)), null, null)
        );

        var result = topsortService.fillWithSponsoredProducts(sp, sr, productIds, false);

        assertThat(requestCaptor.getValue().auctions().getFirst().products().ids()).isEqualTo(productIds);
        assertThat(requestCaptor.getValue().auctions().getFirst().slots()).isEqualTo(sponsoredPositions.size());
        assertThat(result.getRecords()).hasSize(3);

        for (int i = 0; i < 3; i++) {
            assertThat(result.getRecords().get(i)).isEqualTo(records.get(i));
        }

        assertThat(result.getSponsoredRecords()).hasSize(3);
        assertThat(result.getSponsoredRecords().getFirst()).isNotSameAs(records.get(1));
        assertThat(result.getSponsoredRecords().getFirst().getId()).isEqualTo(records.get(1).getId());
        assertThat(result.getSponsoredRecords().getFirst().getPrimaryProductId())
            .isEqualTo(records.get(1).getPrimaryProductId());
        assertThat(result.getSponsoredRecords().getFirst().getProductId()).isEqualTo(records.get(1).getProductId());
        assertThat(result.getSponsoredRecords().getFirst().getSponsored()).isTrue();
        assertThat(result.getSponsoredRecords().getFirst().getSponsoredInfo().topsort()).isEqualTo(winners.get(1));

        assertThat(result.getSponsoredRecords().get(1)).isNotSameAs(records.getFirst());
        assertThat(result.getSponsoredRecords().get(1).getId()).isEqualTo(records.getFirst().getId());
        assertThat(result.getSponsoredRecords().get(1).getPrimaryProductId())
            .isEqualTo(records.getFirst().getPrimaryProductId());
        assertThat(result.getSponsoredRecords().get(1).getProductId()).isEqualTo(records.getFirst().getProductId());
        assertThat(result.getSponsoredRecords().get(1).getSponsored()).isTrue();
        assertThat(result.getSponsoredRecords().get(1).getSponsoredInfo().topsort()).isEqualTo(winners.getFirst());

        assertThat(result.getSponsoredRecords().get(2)).isNotSameAs(records.get(2));
        assertThat(result.getSponsoredRecords().get(2).getId()).isEqualTo(records.get(2).getId());
        assertThat(result.getSponsoredRecords().get(2).getPrimaryProductId())
            .isEqualTo(records.get(2).getPrimaryProductId());
        assertThat(result.getSponsoredRecords().get(2).getProductId()).isEqualTo(records.get(2).getProductId());
        assertThat(result.getSponsoredRecords().get(2).getSponsored()).isTrue();
        assertThat(result.getSponsoredRecords().get(2).getSponsoredInfo().topsort()).isEqualTo(winners.get(2));
    }

    @SneakyThrows
    @Test
    void topsortApplyShouldAddSponsoredRecordsWithDebugInfo() {
        scope = PropagatedContext.getOrEmpty().plus(
            new RequestContext(
                APPAREL_MERCHANDISER,
                REQUEST_ID,
                PRODUCTION,
                PRODUCTS_CLOTHING,
                VISITOR_ID,
                LOGIN_ID,
                List.of(),
                TEST_ENVIRONMENT,
                new RequestOptions(false, true)
            )
        ).propagate();

        given(configurationManager.getTopSortConfig(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCTION))
            .willReturn(topsortConfig());
        var productIds = List.of("1", "2", "3", "4", "5");
        var records = productIds.stream()
            .map(id ->
                Record.of(
                    APPAREL_MERCHANDISER,
                    PRODUCTS_CLOTHING,
                    id,
                    id,
                    "title%s".formatted(id),
                    Map.of(PRODUCT_ID_FIELD, id)
                )
            )
            .toList();
        var sponsoredPositions = List.of(0, 1);
        var sp = SearchParameters.builder()
            .pagination(new Pagination(TOPSORT_MIN_PAGE_SIZE, 0L))
            .originalPagination(new Pagination(3, 0L))
            .sponsoredRecordsRequest(new SponsoredRecordsRequest(sponsoredPositions.size(), sponsoredPositions))
            .build();
        var sr = SearchResults.builder().records(records).build();

        var winners = List.of(
            new AuctionResponse.Winner(1, "products", records.getFirst().getProductId(), "bid1", "vendorId1"),
            new AuctionResponse.Winner(2, "products", records.get(1).getProductId(), "bid2", "vendorId2")
        );
        var auctionResp = new AuctionResponse(List.of(new AuctionResponse.Result(winners, false)), null, null);

        given(topsortClient.createAuction(requestCaptor.capture(), eq(BEARER))).willReturn(auctionResp);

        var result = topsortService.fillWithSponsoredProducts(sp, sr, productIds, false);

        assertThat(requestCaptor.getValue().auctions().getFirst().products().ids()).isEqualTo(productIds);
        assertThat(requestCaptor.getValue().auctions().getFirst().slots()).isEqualTo(sponsoredPositions.size());
        assertThat(result.getRecords()).hasSize(3);
        for (int i = 0; i < 3; i++) {
            assertThat(result.getRecords().get(i)).isEqualTo(records.get(i));
        }

        assertThat(result.getSponsoredRecords()).hasSize(2);
        assertThat(result.getSponsoredRecords().getFirst()).isNotSameAs(records.getFirst());
        assertThat(result.getSponsoredRecords().getFirst().getId()).isEqualTo(records.getFirst().getId());
        assertThat(
            result.getSponsoredRecords().getFirst().getProductId()
        ).isEqualTo(records.getFirst().getProductId());
        assertThat(result.getSponsoredRecords().getFirst().getSponsored()).isTrue();
        assertThat(
            result.getSponsoredRecords().getFirst().getSponsoredInfo().topsort()
        ).isEqualTo(winners.getFirst());

        assertThat(result.getSponsoredRecords().get(1)).isNotSameAs(records.get(1));
        assertThat(result.getSponsoredRecords().get(1).getId()).isEqualTo(records.get(1).getId());
        assertThat(result.getSponsoredRecords().get(1).getProductId()).isEqualTo(records.get(1).getProductId());
        assertThat(result.getSponsoredRecords().get(1).getSponsored()).isTrue();
        assertThat(result.getSponsoredRecords().get(1).getSponsoredInfo().topsort()).isEqualTo(winners.get(1));

        assertThat(getRequestContext().getDebugDetails().getDebugInfos()).isNotEmpty();
    }

    @Test
    void topSortApplyShouldNotAddSponsoredRecordsForNoWinners() {
        given(configurationManager.getTopSortConfig(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCTION))
            .willReturn(topsortConfig());

        var productIds = List.of("1", "2", "3", "4", "5");
        var records = productIds.stream()
            .map(id ->
                Record.of(
                    APPAREL_MERCHANDISER,
                    PRODUCTS_CLOTHING,
                    id,
                    id,
                    "title%s".formatted(id),
                    Map.of(PRODUCT_ID_FIELD, id)
                )
            )
            .toList();
        var sponsoredPositions = List.of(0, 1);
        var sp = SearchParameters.builder()
            .pagination(new Pagination(TOPSORT_MIN_PAGE_SIZE, 0L))
            .originalPagination(new Pagination(3, 0L))
            .sponsoredRecordsRequest(new SponsoredRecordsRequest(sponsoredPositions.size(), sponsoredPositions))
            .build();
        var sr = SearchResults.builder().records(records).build();

        var auctionResp = new AuctionResponse(List.of(new AuctionResponse.Result(List.of(), false)), null, null);

        given(topsortClient.createAuction(requestCaptor.capture(), eq(BEARER))).willReturn(auctionResp);

        var result = topsortService.fillWithSponsoredProducts(sp, sr, productIds, false);

        assertThat(requestCaptor.getValue().auctions().getFirst().products().ids()).isEqualTo(productIds);
        assertThat(requestCaptor.getValue().auctions().getFirst().slots()).isEqualTo(sponsoredPositions.size());
        assertThat(result.getRecords()).hasSize(3);

        for (int i = 0; i < 3; i++) {
            assertThat(result.getRecords().get(i)).isEqualTo(records.get(i));
        }

        assertThat(result.getSponsoredRecords()).isEmpty();
    }

    @Test
    void topsortApplyShouldAddSponsoredRecordsWithoutSponsoredCountSpecified() {
        given(configurationManager.getTopSortConfig(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCTION))
            .willReturn(topsortConfig());

        var productIds = List.of("1", "2", "3", "4", "5");
        var records = productIds.stream()
            .map(id ->
                Record.of(
                    APPAREL_MERCHANDISER,
                    PRODUCTS_CLOTHING,
                    id,
                    id,
                    "title%s".formatted(id),
                    Map.of(PRODUCT_ID_FIELD, id)
                )
            )
            .toList();
        var sponsoredPositions = List.of(0, 1);
        var sp = SearchParameters.builder()
            .pagination(new Pagination(TOPSORT_MIN_PAGE_SIZE, 0L))
            .originalPagination(new Pagination(3, 0L))
            .sponsoredRecordsRequest(new SponsoredRecordsRequest(null, sponsoredPositions))
            .build();
        var sr = SearchResults.builder().records(records).build();

        var result = topsortService.fillWithSponsoredProducts(sp, sr, productIds, false);

        assertThat(result.getSponsoredRecords()).isEmpty();
        assertThat(result.getRecords()).hasSize(3);
        for (int i = 0; i < 3; i++) {
            assertThat(result.getRecords().get(i)).isEqualTo(records.get(i));
        }

        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.equals(
                    "Topsort. Invalid slots number. Either count or positions[] should be specified."
                ))
        );
    }

    @Test
    void topsortApplyShouldAddSponsoredRecordsWithoutSponsoredPositionsSpecified() {
        given(configurationManager.getTopSortConfig(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCTION))
            .willReturn(topsortConfig());

        var productIds = List.of("1", "2", "3", "4", "5");
        var records = productIds.stream()
            .map(id ->
                Record.of(
                    APPAREL_MERCHANDISER,
                    PRODUCTS_CLOTHING,
                    id,
                    id,
                    "title%s".formatted(id),
                    Map.of(PRODUCT_ID_FIELD, id)
                )
            )
            .toList();
        var sp = SearchParameters.builder()
            .pagination(new Pagination(TOPSORT_MIN_PAGE_SIZE, 0L))
            .originalPagination(new Pagination(3, 0L))
            .sponsoredRecordsRequest(new SponsoredRecordsRequest(2, null))
            .build();
        var sr = SearchResults.builder().records(records).build();

        var result = topsortService.fillWithSponsoredProducts(sp, sr, productIds, false);

        assertThat(result.getSponsoredRecords()).isEmpty();
        assertThat(result.getRecords()).hasSize(3);
        for (int i = 0; i < 3; i++) {
            assertThat(result.getRecords().get(i)).isEqualTo(records.get(i));
        }

        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.equals(
                    "Topsort. Invalid slots number. Either count or positions[] should be specified."
                ))
        );
    }

    @Test
    void topsortApplyShouldNotAddSponsoredRecordsWithoutSponsoredCountAndPositionsSpecified() {
        var productIds = List.of("1", "2", "3", "4", "5");
        var records = productIds.stream()
            .map(id ->
                Record.of(
                    APPAREL_MERCHANDISER,
                    PRODUCTS_CLOTHING,
                    id,
                    id,
                    "title%s".formatted(id),
                    Map.of(PRODUCT_ID_FIELD, id)
                )
            )
            .toList();
        var sp = SearchParameters.builder()
            .pagination(new Pagination(TOPSORT_MIN_PAGE_SIZE, 0L))
            .originalPagination(new Pagination(3, 0L))
            .sponsoredRecordsRequest(new SponsoredRecordsRequest(null, null))
            .build();
        var sr = SearchResults.builder().records(records).build();

        var result = topsortService.fillWithSponsoredProducts(sp, sr, productIds, false);

        for (int i = 0; i < 3; i++) {
            assertThat(result.getRecords().get(i)).isEqualTo(records.get(i));
        }

        assertThat(result.getSponsoredRecords()).isEmpty();
    }

    @Test
    void topsortApplyShouldNotAddSponsoredRecordsWithoutSponsoredRequestSpecified() {
        var productIds = List.of("1", "2", "3", "4", "5");
        var records = productIds.stream()
            .map(id ->
                Record.of(
                    APPAREL_MERCHANDISER,
                    PRODUCTS_CLOTHING,
                    id,
                    id,
                    "title%s".formatted(id),
                    Map.of(PRODUCT_ID_FIELD, id)
                )
            )
            .toList();
        var sp = SearchParameters.builder()
            .pagination(new Pagination(TOPSORT_MIN_PAGE_SIZE, 0L))
            .originalPagination(new Pagination(3, 0L))
            .sponsoredRecordsRequest(null)
            .build();
        var sr = SearchResults.builder().records(records).build();

        var result = topsortService.fillWithSponsoredProducts(sp, sr, productIds, false);

        for (int i = 0; i < 3; i++) {
            assertThat(result.getRecords().get(i)).isEqualTo(records.get(i));
        }

        assertThat(result.getSponsoredRecords()).isEmpty();
    }

    @Test
    void topsortApplyShouldNotAddSponsoredRecordsForHTTPError() {
        given(configurationManager.getTopSortConfig(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCTION))
            .willReturn(topsortConfig());
        var productIds = List.of("1", "2", "3", "4", "5");
        var records = productIds.stream()
            .map(id ->
                Record.of(
                    APPAREL_MERCHANDISER,
                    PRODUCTS_CLOTHING,
                    id,
                    id,
                    "title%s".formatted(id),
                    Map.of(PRODUCT_ID_FIELD, id)
                )
            )
            .toList();
        var sponsoredPositions = List.of(0, 1);
        var sp = SearchParameters.builder()
            .pagination(new Pagination(TOPSORT_MIN_PAGE_SIZE, 0L))
            .originalPagination(new Pagination(3, 0L))
            .sponsoredRecordsRequest(new SponsoredRecordsRequest(sponsoredPositions.size(), sponsoredPositions))
            .build();
        var sr = SearchResults.builder().records(records).build();


        given(topsortClient.createAuction(requestCaptor.capture(), eq(BEARER)))
            .willThrow(new HttpClientResponseException(
                    "heck",
                    HttpResponse.badRequest(new AuctionResponse(null, "missing_context", "your payload is corrupted"))
                )
            );

        var result = topsortService.fillWithSponsoredProducts(sp, sr, productIds, false);

        assertThat(requestCaptor.getValue().auctions().getFirst().products().ids()).isEqualTo(productIds);
        assertThat(requestCaptor.getValue().auctions().getFirst().slots()).isEqualTo(sponsoredPositions.size());
        assertThat(result.getRecords()).hasSize(3);

        for (int i = 0; i < 3; i++) {
            assertThat(result.getRecords().get(i)).isEqualTo(records.get(i));
        }

        assertThat(result.getSponsoredRecords()).isEmpty();

        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.equals(
                    "Topsort API error: missing_context: your payload is corrupted"
                ))
        );
    }

    @Test
    void topsortApplyShouldNotAddSponsoredRecordsForAuctionsError() {
        given(configurationManager.getTopSortConfig(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCTION))
            .willReturn(topsortConfig());
        var productIds = List.of("1", "2", "3", "4", "5");
        var records = productIds.stream()
            .map(id ->
                Record.of(
                    APPAREL_MERCHANDISER,
                    PRODUCTS_CLOTHING,
                    id,
                    id,
                    "title%s".formatted(id),
                    Map.of(PRODUCT_ID_FIELD, id)
                )
            )
            .toList();
        var sponsoredPositions = List.of(0, 1);
        var sp = SearchParameters.builder()
            .pagination(new Pagination(TOPSORT_MIN_PAGE_SIZE, 0L))
            .originalPagination(new Pagination(3, 0L))
            .sponsoredRecordsRequest(new SponsoredRecordsRequest(sponsoredPositions.size(), sponsoredPositions))
            .build();
        var sr = SearchResults.builder().records(records).build();

        var auctionResp = new AuctionResponse(List.of(new AuctionResponse.Result(List.of(), true)), null, null);

        given(topsortClient.createAuction(requestCaptor.capture(), eq(BEARER))).willReturn(auctionResp);

        var result = topsortService.fillWithSponsoredProducts(sp, sr, productIds, false);

        assertThat(requestCaptor.getValue().auctions().getFirst().products().ids()).isEqualTo(productIds);
        assertThat(requestCaptor.getValue().auctions().getFirst().slots()).isEqualTo(sponsoredPositions.size());
        assertThat(result.getRecords()).hasSize(3);

        for (int i = 0; i < 3; i++) {
            assertThat(result.getRecords().get(i)).isEqualTo(records.get(i));
        }

        assertThat(result.getSponsoredRecords()).isEmpty();

        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.equals(
                    "Topsort API error: auction was resolved unsuccessfully"
                ))
        );
    }

    private Optional<TopsortConfiguration> topsortConfig() {
        return Optional.of(TopsortConfiguration.builder().enabled(true).apiKey(TopSortServiceTest.API_KEY).build());
    }

}
