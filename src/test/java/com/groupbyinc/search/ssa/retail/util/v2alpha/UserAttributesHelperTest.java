package com.groupbyinc.search.ssa.retail.util.v2alpha;

import com.groupbyinc.search.ssa.api.dto.crm.UserAttributeDto;
import com.groupbyinc.utils.crypto.AesCipherStrategy;

import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;

@MicronautTest
@DisplayName("UserAttributesHelper Tests")
class UserAttributesHelperTest {

    @Inject
    UserAttributesHelper helper;

    @Inject
    @Named("ecb")
    AesCipherStrategy cipher;

    /**
     * <b>This test checks: </b><br/>
     * 1 - userAttributes keys are lowercased; <br/> 2 - userAttributes values are encrypted; <br/> 3 - empty "" / null values from userAttributes
     * values are removed; <br/> 4 - empty "" keys from userAttributes values are removed; <br/> 5 - duplicated keys in userAttributes are merged.
     */
    @Test
    @DisplayName("Merge and encrypt userAttributes")
    void mergeAndEncrypt() {
        // Given
        var input = List.of(
            new UserAttributeDto("email", List.of("<EMAIL>", "<EMAIL>")),
            new UserAttributeDto("EMAIL", Arrays.asList("<EMAIL>", "", null, "<EMAIL>")),
            new UserAttributeDto("city", List.of("New York", "Los Angeles", "")),
            new UserAttributeDto("", List.of("New York", "Los Angeles", "")),
            new UserAttributeDto("", List.of(""))
        );

        // When
        List<UserAttributeDto> encryptedAttributes = helper.mergeAndEncrypt(input);

        // Then
        assertEquals(2, encryptedAttributes.size());

        for (UserAttributeDto userAttribute : encryptedAttributes) {
            Set<String> decryptedValues = userAttribute.values().stream()
                .map(cipher::decrypt)
                .collect(Collectors.toSet());

            if (userAttribute.key().equals("email")) {
                assertEquals(Set.of("<EMAIL>", "<EMAIL>", "<EMAIL>"), decryptedValues);
            } else if (userAttribute.key().equals("city")) {
                assertEquals(Set.of("New York", "Los Angeles"), decryptedValues);
            } else {
                fail("Unexpected key: " + userAttribute.key());
            }
        }
    }
}
