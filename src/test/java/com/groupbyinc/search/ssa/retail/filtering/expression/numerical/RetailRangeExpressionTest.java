package com.groupbyinc.search.ssa.retail.filtering.expression.numerical;

import com.groupbyinc.search.ssa.application.core.search.filtering.ToFilterTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.provider.Arguments;

import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.retail.filtering.expression.RetailNotExpression.not;
import static com.groupbyinc.search.ssa.retail.filtering.expression.numerical.RetailRangeExpression.range;

@DisplayName("RetailRangeExpressionTest")
class RetailRangeExpressionTest extends ToFilterTest<String> {

    @Override
    public Stream<Arguments> canBuildAValidFilterArguments() {
        return Stream.of(
            Arguments.of(
                range("price")
                    .lowerInclusive(5.99)
                    .build(),
                "price:IN(5.99i,*)"
            ),
            Arguments.of(
                range("price")
                    .upperExclusive(10)
                    .build(),
                "price:IN(*,10e)"
            ),
            Arguments.of(
                range("price")
                    .lowerInclusive(5.99)
                    .upperExclusive(10)
                    .build(),
                "price:IN(5.99i,10e)"
            ),
            Arguments.of(
                not(
                    range("price")
                        .lowerInclusive(5.99)
                        .upperExclusive(10).build()
                ),
                "(NOT price:IN(5.99i,10e))"
            )
        );
    }

}
