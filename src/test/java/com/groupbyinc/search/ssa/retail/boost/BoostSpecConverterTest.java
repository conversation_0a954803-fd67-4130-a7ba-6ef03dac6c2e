package com.groupbyinc.search.ssa.retail.boost;

import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.biasing.Bias;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfile;
import com.groupbyinc.search.ssa.core.biasing.NumericContent;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.rule.ProductIdsBucket;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.retail.filtering.RetailFilterService;
import com.groupbyinc.search.ssa.retail.filteringold.RetailFilterServiceOld;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.retail.v2.SearchRequest;
import com.launchdarkly.sdk.server.LDClient;
import com.launchdarkly.sdk.server.LDConfig;
import io.micronaut.core.propagation.PropagatedContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

@DisplayName("BoostSpecConverter Tests")
class BoostSpecConverterTest {

    private final BoostSpecConverter converter = new BoostSpecConverter(
        new FeaturesManager(
            new ObjectMapper(),
            new LDClient("", new LDConfig.Builder().build()),
            ""
        ),
        new RetailFilterService(),
        new RetailFilterServiceOld()
    );

    private PropagatedContext.Scope scope;

    @BeforeEach
    void setUp() {
        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    @Test
    @DisplayName("Test that biasing profile with one bias converted correctly.")
    void biasingProfileWithOneBias() {
        var searchParameters = SearchParameters
            .builder()
            .query("")
            .biasingProfile(
                new BiasingProfile(
                    "name",
                    List.of(
                        new Bias("color", "red", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null)
                    )
                )
            )
            .build();

        var spec = converter.convert(searchParameters);

        var expected = SearchRequest.BoostSpec
            .newBuilder()
            .addConditionBoostSpecs(
                SearchRequest.BoostSpec.ConditionBoostSpec
                    .newBuilder()
                    .setBoost(-1.0F)
                    .setCondition("color:ANY(\"red\")")
            )
            .build();

        assertThat(spec).isEqualTo(expected);
    }

    @Test
    @DisplayName("Test that biasing profile with multiply biases converted correctly.")
    void biasingProfileWithTwoBiases() {
        var searchParameters = SearchParameters
            .builder()
            .query("")
            .biasingProfile(
                new BiasingProfile(
                    "name",
                    List.of(
                        new Bias("color", "red", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("brand", "nike", Bias.Strength.STRONG_INCREASE, null, Bias.Type.TEXTUAL, null)
                    )
                )
            )
            .build();

        var spec = converter.convert(searchParameters);

        var expected = SearchRequest.BoostSpec
            .newBuilder()
            .addConditionBoostSpecs(
                SearchRequest.BoostSpec.ConditionBoostSpec
                    .newBuilder()
                    .setBoost(-1.0F)
                    .setCondition("color:ANY(\"red\")")
            )
            .addConditionBoostSpecs(
                SearchRequest.BoostSpec.ConditionBoostSpec
                    .newBuilder()
                    .setBoost(0.8F)
                    .setCondition("brand:ANY(\"nike\")")
            )
            .build();

        assertThat(spec).isEqualTo(expected);
    }

    @Test
    @DisplayName("Test that query used when biasing profile has no content.")
    void biasingProfileUseQuery() {
        var searchParameters = SearchParameters
            .builder()
            .query("test")
            .biasingProfile(
                new BiasingProfile(
                    "name",
                    List.of(
                        new Bias("color", "", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null)
                    )
                )
            )
            .build();

        var spec = converter.convert(searchParameters);

        var expected = SearchRequest.BoostSpec
            .newBuilder()
            .addConditionBoostSpecs(
                SearchRequest.BoostSpec.ConditionBoostSpec
                    .newBuilder()
                    .setBoost(-1.0F)
                    .setCondition("color:ANY(\"test\")")
            )
            .build();

        assertThat(spec).isEqualTo(expected);
    }

    @Test
    @DisplayName("Limits the number of biases to 20")
    void limitsTheNumberOfConditionBiases() {
        var searchParameters = SearchParameters
            .builder()
            .query("test")
            .biasingProfile(
                new BiasingProfile(
                    "name",
                    List.of(
                        new Bias("color", "1", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "2", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "3", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "4", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "5", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "6", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "8", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "9", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "10", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "11", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "12", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "13", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "14", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "15", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "16", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "17", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "18", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "19", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "20", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("color", "21", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null)
                    )
                )
            )
            .build();

        var spec = converter.convert(searchParameters);

        assertThat(spec.getConditionBoostSpecsList().size()).isEqualTo(20);
    }

    @Test
    @DisplayName("Test that boosted product buckets converted.")
    void testBoostedProductBucketsConverted() {
        var searchParameters = SearchParameters
            .builder()
            .query("test")
            .boostedProductBuckets(
                List.of(
                    new ProductIdsBucket(List.of("id_1")),
                    new ProductIdsBucket(List.of("id_2")),
                    new ProductIdsBucket(List.of("id_3")),
                    new ProductIdsBucket(List.of("id_4")),
                    new ProductIdsBucket(List.of("id_5")),
                    new ProductIdsBucket(List.of("id_6")),
                    new ProductIdsBucket(List.of("id_7")),
                    new ProductIdsBucket(List.of("id_8")))
            )
            .build();

        var spec = converter.convert(searchParameters);

        var expected = SearchRequest.BoostSpec
            .newBuilder()
            .addAllConditionBoostSpecs(
                List.of(
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(1.0F)
                        .setCondition("id:ANY(\"id_1\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.9F)
                        .setCondition("id:ANY(\"id_2\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.8F)
                        .setCondition("id:ANY(\"id_3\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.7F)
                        .setCondition("id:ANY(\"id_4\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.6F)
                        .setCondition("id:ANY(\"id_5\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.5F)
                        .setCondition("id:ANY(\"id_6\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.4F)
                        .setCondition("id:ANY(\"id_7\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.3F)
                        .setCondition("id:ANY(\"id_8\")")
                        .build()
                )
            )
            .build();

        assertThat(spec).isEqualTo(expected);
    }

    @Test
    @DisplayName("Test that buried product buckets converted.")
    void testBuriedProductBucketsConverted() {
        var searchParameters = SearchParameters
            .builder()
            .query("test")
            .buriedProductBuckets(
                List.of(
                    new ProductIdsBucket(List.of("id_1"))
                )
            )
            .build();

        var spec = converter.convert(searchParameters);

        var expected = SearchRequest.BoostSpec.newBuilder()
            .addConditionBoostSpecs(
                SearchRequest.BoostSpec.ConditionBoostSpec
                    .newBuilder()
                    .setBoost(-1.0F)
                    .setCondition("id:ANY(\"id_1\")")
                    .build()
            )
            .build();

        assertThat(spec).isEqualTo(expected);
    }

    @Test
    @DisplayName("Test that id in bias ignored if already boosted or buried")
    void testIdInBiasIgnored() {
        var searchParameters = SearchParameters
            .builder()
            .query("query")
            .boostedProductBuckets(
                List.of(
                    new ProductIdsBucket(List.of("id_1")),
                    new ProductIdsBucket(List.of("id_2"))
                )
            )
            .buriedProductBuckets(List.of(new ProductIdsBucket(List.of("id_3"))))
            .biasingProfile(
                new BiasingProfile(
                    "name",
                    List.of(
                        new Bias("id", "id_1", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "id_2", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "id_3", Bias.Strength.ABSOLUTE_INCREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "id_4", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null)
                    )
                )
            )
            .build();

        var spec = converter.convert(searchParameters);

        var expected = SearchRequest.BoostSpec
            .newBuilder()
            .addAllConditionBoostSpecs(
                List.of(
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(1.0F)
                        .setCondition("id:ANY(\"id_1\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.9F)
                        .setCondition("id:ANY(\"id_2\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("id:ANY(\"id_3\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("id:ANY(\"id_4\")")
                        .build()
                )
            )
            .build();

        assertThat(spec).isEqualTo(expected);
    }

    @Test
    @DisplayName("Test that biases ignored if reach limit")
    void biasesIgnoredIfLimit() {
        var searchParameters = SearchParameters
            .builder()
            .query("test")
            .query("query")
            .boostedProductBuckets(
                List.of(
                    new ProductIdsBucket(List.of("id_1")),
                    new ProductIdsBucket(List.of("id_2")),
                    new ProductIdsBucket(List.of("id_3")),
                    new ProductIdsBucket(List.of("id_4")),
                    new ProductIdsBucket(List.of("id_5")),
                    new ProductIdsBucket(List.of("id_6")),
                    new ProductIdsBucket(List.of("id_7")),
                    new ProductIdsBucket(List.of("id_8"))
                )
            )
            .buriedProductBuckets(List.of(new ProductIdsBucket(List.of("id_9"))))
            .biasingProfile(
                new BiasingProfile(
                    "name",
                    List.of(
                        new Bias("id", "id_10", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "id_11", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "id_12", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "id_13", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "id_14", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "id_15", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "id_16", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "id_17", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "id_18", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "id_19", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "id_20", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null),
                        new Bias("id", "id_21", Bias.Strength.ABSOLUTE_DECREASE, null, Bias.Type.TEXTUAL, null)
                    )
                )
            )
            .build();

        var spec = converter.convert(searchParameters);

        var expected = SearchRequest.BoostSpec
            .newBuilder()
            .addAllConditionBoostSpecs(
                List.of(
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(1.0F)
                        .setCondition("id:ANY(\"id_1\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.9F)
                        .setCondition("id:ANY(\"id_2\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.8F)
                        .setCondition("id:ANY(\"id_3\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.7F)
                        .setCondition("id:ANY(\"id_4\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.6F)
                        .setCondition("id:ANY(\"id_5\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.5F)
                        .setCondition("id:ANY(\"id_6\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.4F)
                        .setCondition("id:ANY(\"id_7\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(0.3F)
                        .setCondition("id:ANY(\"id_8\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("id:ANY(\"id_9\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("id:ANY(\"id_10\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("id:ANY(\"id_11\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("id:ANY(\"id_12\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("id:ANY(\"id_13\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("id:ANY(\"id_14\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("id:ANY(\"id_15\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("id:ANY(\"id_16\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("id:ANY(\"id_17\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("id:ANY(\"id_18\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("id:ANY(\"id_19\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("id:ANY(\"id_20\")")
                        .build()
                )
            )
            .build();

        assertThat(spec).isEqualTo(expected);
    }

    @Test
    @DisplayName("Test that biases with values and ranges")
    public void biasingProfileWithTextualAndNumericContent() {
        var searchParameters = SearchParameters
            .builder()
            .query("test")
            .query("query")
            .biasingProfile(
                new BiasingProfile(
                    "name",
                    List.of(
                        new Bias(
                            "colorFamilies",
                            "White,Red",
                            Bias.Strength.ABSOLUTE_DECREASE,
                            null,
                            Bias.Type.TEXTUAL,
                            null
                        ),
                        new Bias(
                            "price",
                            null,
                            Bias.Strength.ABSOLUTE_DECREASE,
                            null,
                            Bias.Type.NUMERIC,
                            NumericContent.builder()
                                .values(List.of(50.0, 65.0))
                                .ranges(List.of(
                                    new Range(100.0, 1000.0, null),
                                    new Range(1000.0, 10000.0, null)
                                ))
                                .build()
                        )
                    )
                )
            )
            .build();

        var spec = converter.convert(searchParameters);

        var expected = SearchRequest.BoostSpec
            .newBuilder()
            .addAllConditionBoostSpecs(
                List.of(
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("colorFamilies:ANY(\"White\",\"Red\")")
                        .build(),
                    SearchRequest.BoostSpec.ConditionBoostSpec
                        .newBuilder()
                        .setBoost(-1.0F)
                        .setCondition("price=50.0 OR price=65.0 OR price:IN(100.0i,1000.0e) OR price:IN(1000.0i,10000.0e)")
                        .build()
                )
            )
            .build();

        assertThat(spec).isEqualTo(expected);
    }

}
