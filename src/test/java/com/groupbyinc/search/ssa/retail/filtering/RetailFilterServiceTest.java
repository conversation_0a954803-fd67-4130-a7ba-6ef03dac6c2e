package com.groupbyinc.search.ssa.retail.filtering;

import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.filter.AttributeFilter;
import com.groupbyinc.search.ssa.core.filter.RangeFilter;
import com.groupbyinc.search.ssa.core.filter.ValueFilter;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * This test class evaluates the functionality of {@link RetailFilterService} in generating accurate
 * retail search filters. Through a series of unit tests, it examines the behavior of creating boosting
 * specifications, handling of invalid input scenarios, and the correct assembly of filter strings from
 * various filtering criteria including value filters, range filters, and selected refinements.
 * <p>
 * Key tests include validation of exception handling for invalid fields and values, positive testing for
 * boosting specifications, creation of filters from textual and numeric selected refinements, and
 * comprehensive testing of mixed filter scenarios. The tests also explore the correct application of
 * pre-filters and site-filters, ensuring the service adheres to expected behaviors in retail search query
 * processing.
 * <p>
 * Through systematic testing of both simple and complex filter constructions, this class aims to ensure
 * that {@link RetailFilterService} reliably translates search parameters into effective and accurate
 * search filters, contributing to optimized search results based on specified criteria.
 */
@DisplayName("RetailFilterServiceTest")
public class RetailFilterServiceTest {

    private final RetailFilterService retailFilterService = new RetailFilterService();

    @Test
    @DisplayName("Create boosting spec condition positive tests.")
    void createBoostingSpecConditionPositive() {
        // single value
        validateTextField(List.of("value"), "field:ANY(\"value\")");

        // multiple values
        validateTextField(List.of("value1", "value2"), "field:ANY(\"value1\",\"value2\")");
    }

    @Test
    @DisplayName("Create filter from selected refinements text refinements")
    void createFilterFromTextSelectedRefinements() {
        // null selected refinements
        validateSearchParametersFromObject(searchParameters().build(), "");

        // empty selected refinements
        validateRefinements(List.of(), "" );

        // selected refinements text refinement: single value | or true
        // expected: filter build with single value.
        var refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value")
                .or(true)
                .build()
        );
        validateRefinements(refinements, "field:ANY(\"value\")");

        // selected refinements text refinement: single value | or false
        // expected: filter build with single value.
        refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value")
                .or(false)
                .build()
        );
        validateRefinements(refinements, "field:ANY(\"value\")");

        // selected refinements text refinement: multivalued | or false
        // expected: filter build with single value.
        refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value")
                .or(false)
                .build(),
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value2")
                .or(false)
                .build()
        );
        validateRefinements(refinements, "field:ANY(\"value\")");

        // selected refinements text refinement: multivalued | or false second or true
        // expected: filter build with single value.
        refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value")
                .or(false)
                .build(),
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value2")
                .or(true)
                .build()
        );
        validateRefinements(refinements, "field:ANY(\"value\")");

        // selected refinements text refinement: multivalued | or true second or false
        // expected: filter build with two values.
        refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value")
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value2")
                .or(true)
                .build()
        );
        validateRefinements(refinements, "field:ANY(\"value\",\"value2\")");

        // selected refinements text refinement: multivalued | or true
        // expected: filter build with two values.
        refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value")
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value2")
                .or(true)
                .build()
        );
        validateRefinements(refinements, "field:ANY(\"value\",\"value2\")");

        // selected refinements text refinement: multivalued | multi-field
        refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value")
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value2")
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field2")
                .value("value")
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field2")
                .value("value2")
                .or(true)
                .build()
        );
        validateRefinements(
            refinements,
            "field:ANY(\"value\",\"value2\") AND field2:ANY(\"value\",\"value2\")"
        );
    }

    @Test
    @DisplayName("Create filter from selected refinements numeric refinements")
    void createFilterFromNumericSelectedRefinements() {
        // null selected refinements
        assertThat(
            retailFilterService.createFilter(searchParameters().build())
        ).isEqualTo("");

        // empty selected refinements
        validateRefinements(List.of(), "");

        // selected refinements range refinement: single value | or true
        // expected: filter build with single range.
        var refinements = List.of(
            SelectedRefinement
                .builder()
                .field("field")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, 2.0, null))
                .or(true)
                .build()
        );
        validateRefinements(refinements, "((field:IN(1.0i,2.0e)))");

        // selected refinements range refinement: single value | or true | range without max
        // expected: filter build with single range.
        refinements = List.of(
            SelectedRefinement
                .builder()
                .field("field")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, null, null))
                .or(true)
                .build()
        );
        validateRefinements(refinements, "((field:IN(1.0i,*)))");

        // selected refinements range refinement: single value | or true | range without min
        // expected: filter build with single range.
        refinements = List.of(
            SelectedRefinement
                .builder()
                .field("field")
                .type(NavigationType.RANGE)
                .range(new Range(null, 1.0, null))
                .or(true)
                .build()
        );
        validateRefinements(refinements, "((field:IN(*,1.0e)))");


        // selected refinements range refinement: single value | or false
        // expected: filter build with single range.
        refinements = List.of(
            SelectedRefinement
                .builder()
                .field("field")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, 2.0, null))
                .or(false)
                .build()
        );
        validateRefinements(refinements, "field:IN(1.0i,2.0e)");

        // selected refinements range refinement: single value | or false
        // expected: filter build with single range.
        refinements = List.of(
            SelectedRefinement
                .builder()
                .field("field")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, 2.0, null))
                .or(false)
                .build(),
            SelectedRefinement
                .builder()
                .field("field")
                .type(NavigationType.RANGE)
                .range(new Range(10.0, 20.0, null))
                .or(false)
                .build()
        );
        validateRefinements(refinements, "field:IN(1.0i,2.0e)");

        // selected refinements range refinement: single value | or false second or true
        // expected: filter build with single range.
        refinements = List.of(
            SelectedRefinement
                .builder()
                .field("field")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, 2.0, null))
                .or(false)
                .build(),
            SelectedRefinement
                .builder()
                .field("field")
                .type(NavigationType.RANGE)
                .range(new Range(10.0, 20.0, null))
                .or(true)
                .build()
        );
        validateRefinements(refinements, "field:IN(1.0i,2.0e)");

        // selected refinements range refinement: single value | or false true or false
        // expected: filter build with two ranges.
        refinements = List.of(
            SelectedRefinement
                .builder()
                .field("field")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, 2.0, null))
                .or(false)
                .build(),
            SelectedRefinement
                .builder()
                .field("field")
                .type(NavigationType.RANGE)
                .range(new Range(10.0, 20.0, null))
                .or(true)
                .build()
        );
        validateRefinements(refinements, "field:IN(1.0i,2.0e)");

        // selected refinements range refinement: single value | or false true
        // expected: filter build with two ranges.
        refinements = List.of(
            SelectedRefinement
                .builder()
                .field("field")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, 2.0, null))
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .field("field")
                .type(NavigationType.RANGE)
                .range(new Range(10.0, 20.0, null))
                .or(true)
                .build()
        );
        validateRefinements(refinements, "((field:IN(1.0i,2.0e)) OR (field:IN(10.0i,20.0e)))");

        // selected refinement range refinement: two ranges | two field
        // expected: filter build two filters with two ranges.
        refinements = List.of(
            SelectedRefinement
                .builder()
                .field("field")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, 2.0, null))
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .field("field")
                .type(NavigationType.RANGE)
                .range(new Range(10.0, 20.0, null))
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .field("field2")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, 2.0, null))
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .field("field2")
                .type(NavigationType.RANGE)
                .range(new Range(10.0, 20.0, null))
                .or(true)
                .build()
        );
        validateRefinements(
            refinements,
            "((field:IN(1.0i,2.0e)) OR (field:IN(10.0i,20.0e))) "
                + "AND ((field2:IN(1.0i,2.0e)) OR (field2:IN(10.0i,20.0e)))"
        );
    }

    @Test
    @DisplayName("Create filter from selected refinement mixed")
    void createFilterFromSelectedRefinementMixed() {
        var refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("text")
                .value("value")
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("text")
                .value("value2")
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("text2")
                .value("value")
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("text2")
                .value("value2")
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .field("range")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, 2.0, null))
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .field("range")
                .type(NavigationType.RANGE)
                .range(new Range(10.0, 20.0, null))
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .field("range2")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, 2.0, null))
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .field("range2")
                .type(NavigationType.RANGE)
                .range(new Range(10.0, 20.0, null))
                .or(true)
                .build()
        );

        validateRefinements(
            refinements,
            "((range2:IN(1.0i,2.0e)) OR (range2:IN(10.0i,20.0e))) "
                + "AND text2:ANY(\"value\",\"value2\") AND ((range:IN(1.0i,2.0e)) OR (range:IN(10.0i,20.0e))) "
                + "AND text:ANY(\"value\",\"value2\")"
        );
    }

    @Test
    @DisplayName("Create filter from value filters (Text type)")
    void createFilterFromValueFiltersTextType() {
        // null value filters
        assertThat(
            retailFilterService.createFilter(searchParameters().build())
        ).isEqualTo("");

        // empty value filters
        validateRefinements(List.of(), "");

        // value filter | text type | exclude false.
        // expected: filter created with one expression.
        var filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value")
                .exclude(false)
                .build()
        );
        validateValueFilters(filters, "((field:ANY(\"value\")))");

        // value filter | text type | exclude true.
        // expected: filter created with one negated expression.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value")
                .exclude(true)
                .build()
        );
        validateValueFilters(filters, "(((NOT field:ANY(\"value\"))))");

        // value filter | multivalued | text type | both included
        // expected: filter created with one expression contains both values.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value")
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value2")
                .exclude(false)
                .build()
        );
        validateValueFilters(filters, "((field:ANY(\"value\",\"value2\")))");

        // value filter | multivalued | text type | first included second excluded
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value")
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value2")
                .exclude(true)
                .build()
        );
        validateValueFilters(filters, "((field:ANY(\"value\") AND (NOT field:ANY(\"value2\"))))");

        // value filter | multivalued | text type | first excluded second included
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value")
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value2")
                .exclude(false)
                .build()
        );
        validateValueFilters(filters, "((field:ANY(\"value2\") AND (NOT field:ANY(\"value\"))))");

        // value filter | multivalued | text type | both excluded
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value")
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value2")
                .exclude(true)
                .build()
        );
        validateValueFilters(filters, "(((NOT field:ANY(\"value\",\"value2\"))))");

        // value filter | multivalued | text type | both included | different fields
        // expected: filter created with one expression contains both values.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value")
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field2")
                .value("value2")
                .exclude(false)
                .build()
        );
        validateValueFilters(filters, "((field:ANY(\"value\") AND field2:ANY(\"value2\")))");

        // value filter | multivalued | text type | first included second excluded | different fields
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value")
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field2")
                .value("value2")
                .exclude(true)
                .build()
        );
        validateValueFilters(filters, "((field:ANY(\"value\") AND (NOT field2:ANY(\"value2\"))))");

        // value filter | multivalued | text type | first excluded second included | different fields
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value")
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field2")
                .value("value2")
                .exclude(false)
                .build()
        );
        validateValueFilters(filters, "(((NOT field:ANY(\"value\")) AND field2:ANY(\"value2\")))");

        // value filter | multivalued | text type | both excluded | different fields
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value")
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field2")
                .value("value2")
                .exclude(true)
                .build()
        );
        validateValueFilters(filters, "(((NOT field:ANY(\"value\")) AND (NOT field2:ANY(\"value2\"))))");

        // value filter | multivalued | text type | one excluded one included | different fields
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value")
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value2")
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field2")
                .value("value")
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field2")
                .value("value2")
                .exclude(false)
                .build()
        );
        validateValueFilters(
            filters,
            "((field:ANY(\"value2\") AND (NOT field:ANY(\"value\")) "
                + "AND field2:ANY(\"value2\") AND (NOT field2:ANY(\"value\"))))"
        );
    }

    @Test
    @DisplayName("Create filter from value filters (Numeric type)")
    void createFilterFromValueFiltersNumericType() {
        // null value filters
        validateSearchParametersFromObject(searchParameters().build(), "");

        // empty value filters
        validateRefinements(List.of(), "");

        // value filter | numeric type | exclude false.
        // expected: filter created with one expression.
        var filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(false)
                .build()
        );
        validateValueFilters(filters, "((((field=1.0))))");

        // value filter | numeric type | exclude true.
        // expected: filter created with one negated expression.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(true)
                .build()
        );
        validateValueFilters(filters, "(((NOT field=1.0)))");

        // value filter | multivalued | numeric type | both included
        // expected: filter created with one expression contains both values.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(2.0)
                .exclude(false)
                .build()
        );
        validateValueFilters(filters, "((((field=1.0) OR (field=2.0))))");

        // value filter | multivalued | numeric type | first included second excluded
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(2.0)
                .exclude(true)
                .build()
        );
        validateValueFilters(filters, "((((field=1.0)) AND (NOT field=2.0)))");

        // value filter | multivalued | numeric type | first excluded second included
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(2.0)
                .exclude(false)
                .build()
        );
        validateValueFilters(filters, "((((field=2.0)) AND (NOT field=1.0)))");

        // value filter | multivalued | numeric type | both excluded
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(2.0)
                .exclude(true)
                .build()
        );
        validateValueFilters(filters, "(((NOT field=1.0) AND (NOT field=2.0)))");

        // value filter | multivalued | numeric type | both included | different fields
        // expected: filter created with one expression contains both values.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field2")
                .numberValue(2.0)
                .exclude(false)
                .build()
        );
        validateValueFilters(filters, "((((field=1.0)) AND ((field2=2.0))))");

        // value filter | multivalued | numeric type | first included second excluded | different fields
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field2")
                .numberValue(2.0)
                .exclude(true)
                .build()
        );
        validateValueFilters(filters, "((((field=1.0)) AND (NOT field2=2.0)))");

        // value filter | multivalued | numeric type | first excluded second included | different fields
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field2")
                .numberValue(2.0)
                .exclude(false)
                .build()
        );
        validateValueFilters(filters, "(((NOT field=1.0) AND ((field2=2.0))))");

        // value filter | multivalued | numeric type | both excluded | different fields
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field2")
                .numberValue(2.0)
                .exclude(true)
                .build()
        );
        validateValueFilters(filters, "(((NOT field=1.0) AND (NOT field2=2.0)))");

        // value filter | multivalued | numeric type | one excluded one included | different fields
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(2.0)
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field2")
                .numberValue(1.0)
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field2")
                .numberValue(2.0)
                .exclude(false)
                .build()
        );
        validateValueFilters(
            filters,
            "((((field=2.0)) AND (NOT field=1.0) AND ((field2=2.0)) AND (NOT field2=1.0)))"
        );
    }

    @Test
    @DisplayName("Create filter from value filters mixed")
    void createFilterFromValueFiltersMixed() {
        var filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("text")
                .value("value")
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("text")
                .value("value2")
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("text")
                .value("value3")
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("text2")
                .value("value")
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("text2")
                .value("value2")
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("numeric")
                .numberValue(1.0)
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("numeric")
                .numberValue(2.0)
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("numeric2")
                .numberValue(1.0)
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("numeric2")
                .numberValue(2.0)
                .exclude(false)
                .build()
        );
        validateValueFilters(
            filters,
            "((text2:ANY(\"value2\") AND (NOT text2:ANY(\"value\")) AND ((numeric=2.0)) "
                + "AND (NOT numeric=1.0) AND ((numeric2=2.0)) AND (NOT numeric2=1.0) AND text:ANY(\"value3\") "
                + "AND (NOT text:ANY(\"value\",\"value2\"))))"
        );
    }

    @Test
    @DisplayName("Create filter from range filters with Exclude")
    void createFilterFromRangeFiltersWithExclude() {
        // value filter | multivalued | numeric type | first included second excluded
        // expected: filter created with one negated expression and one not negated.
        var filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(2.0)
                .exclude(true)
                .build()
        );
        validateValueFilters(filters, "((((field=1.0)) AND (NOT field=2.0)))");

        // value filter | multivalued | numeric type | first excluded second included
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(2.0)
                .exclude(false)
                .build()
        );
        validateValueFilters(filters, "((((field=2.0)) AND (NOT field=1.0)))");

        // value filter | multivalued | numeric type | first included second excluded
        // included value equals excluded value
        // expected: filter created with one negated expression and one not negated.
        filters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("field")
                .numberValue(1.0)
                .exclude(true)
                .build()
        );
        validateValueFilters(filters, "((((field=1.0)) AND (NOT field=1.0)))");

        // Single range filter with exclude
        // expected: filter build with single range.
        var ranges = List.of(
            RangeFilter
                .builder()
                .field("field")
                .range(new Range(1.0, 2.0, null, true))
                .build()
        );
        validateRangeFilters(ranges, "(((NOT field:IN(1.0i,2.0e))))");

        // value filter | multivalued | numeric type | first included second excluded
        // expected: filter created with one negated expression and one not negated.
        ranges = List.of(
            RangeFilter
                .builder()
                .field("field")
                .range(new Range(5.0, 6.0, null, false))
                .build(),
            RangeFilter
                .builder()
                .field("field")
                .range(new Range(1.0, 2.0, null, true))
                .build()
        );
        validateRangeFilters(ranges, "((((field:IN(5.0i,6.0e))) AND (NOT field:IN(1.0i,2.0e))))");

        // value filter | multivalued | numeric type | first excluded second included
        // expected: filter created with one negated expression and one not negated.
        ranges = List.of(
            RangeFilter
                .builder()
                .field("field")
                .range(new Range(5.0, 6.0, null, true))
                .build(),
            RangeFilter
                .builder()
                .field("field")
                .range(new Range(1.0, 2.0, null, false))
                .build()
        );
        validateRangeFilters(ranges, "((((field:IN(1.0i,2.0e))) AND (NOT field:IN(5.0i,6.0e))))");
    }

    @Test
    @DisplayName("Create filter from range filters")
    void createFilterFromRangeFilters() {
        // null range filters
        validateSearchParametersFromObject(searchParameters().build(), "");

        // empty range filters
        validateRefinements(List.of(), "");
        validateRangeFilters(List.of(), "");

        // Single range filter
        // expected: filter build with single range.
        var ranges = List.of(
            RangeFilter
                .builder()
                .field("field")
                .range(new Range(1.0, 2.0, null))
                .build()
        );
        validateRangeFilters(ranges, "((((field:IN(1.0i,2.0e)))))");

        // Single range filter without max
        // expected: filter build with single range.
        ranges = List.of(
            RangeFilter
                .builder()
                .field("field")
                .range(new Range(1.0, null, null))
                .build()
        );
        validateRangeFilters(ranges, "((((field:IN(1.0i,*)))))");

        // Single range filter without min
        // expected: filter build with single range.
        ranges = List.of(
            RangeFilter
                .builder()
                .field("field")
                .range(new Range(null, 1.0, null))
                .build()
        );
        validateRangeFilters(ranges, "((((field:IN(*,1.0e)))))");

        // Two range filters and same fields.
        // expected: filter build with single range.
        ranges = List.of(
            RangeFilter
                .builder()
                .field("field")
                .range(new Range(1.0, 2.0, null))
                .build(),
            RangeFilter
                .builder()
                .field("field")
                .range(new Range(10.0, 20.0, null))
                .build()
        );
        validateRangeFilters(ranges, "((((field:IN(1.0i,2.0e)) OR (field:IN(10.0i,20.0e)))))");

        // Two range filters different fields
        // expected: filter build two filters with two ranges.
        ranges = List.of(
            RangeFilter
                .builder()
                .field("field")
                .range(new Range(1.0, 2.0, null))
                .build(),
            RangeFilter
                .builder()
                .field("field2")
                .range(new Range(10.0, 20.0, null))
                .build()
        );
        validateRangeFilters(ranges, "((((field:IN(1.0i,2.0e))) AND ((field2:IN(10.0i,20.0e)))))");
    }

    @Test
    @DisplayName("Create filter from mixed additional filters")
    void createFilterFromMixedAdditionalFilters() {
        // Text selected refinement anf text value filter: same key | same value.
        var refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value")
                .or(true)
                .build()
        );
        var valueFilters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value")
                .exclude(false)
                .build()
        );

        validateSearchParameters(
            valueFilters,
            null,
            refinements,
            "field:ANY(\"value\") AND ((field:ANY(\"value\")))"
        );

         // Text selected refinement anf text value filter: same key | different values | or true
         refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value")
                .or(true)
                .build()
        );
        valueFilters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value2")
                .exclude(false)
                .build()
        );

        validateSearchParameters(
            valueFilters,
            null,
            refinements,
            "field:ANY(\"value\") AND ((field:ANY(\"value2\")))"
        );

        // Text selected refinement anf text value filter: same key | different values | or false
        refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value")
                .or(false)
                .build()
        );
        valueFilters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value2")
                .exclude(false)
                .build()
        );

        validateSearchParameters(
            valueFilters,
            null,
            refinements,
            "field:ANY(\"value\") AND ((field:ANY(\"value2\")))"
        );

        // Text selected refinement anf text value filter: same key | different values | or true | exclude true
        refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value")
                .or(true)
                .build()
        );
        valueFilters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value2")
                .exclude(true)
                .build()
        );

        validateSearchParameters(
            valueFilters,
            null,
            refinements,
            "field:ANY(\"value\") AND (((NOT field:ANY(\"value2\"))))"
        );

        // Text selected refinement anf text value filter:
        // same key | different values | or true | exclude true and false
        refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value")
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value2")
                .or(true)
                .build()
        );
        valueFilters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value3")
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value4")
                .exclude(true)
                .build()
        );

        validateSearchParameters(
            valueFilters,
            null,
            refinements,
            "field:ANY(\"value\",\"value2\") AND ((field:ANY(\"value3\") AND (NOT field:ANY(\"value4\"))))"
        );

        // Range selected refinement and value filter exclude false
        refinements = List.of(
            SelectedRefinement
                .builder()
                .field("field2")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, 2.0, null))
                .or(true)
                .build()
        );
        valueFilters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value")
                .exclude(false)
                .build()
        );

        validateSearchParameters(
            valueFilters,
            null,
            refinements,
            "((field2:IN(1.0i,2.0e))) AND ((field:ANY(\"value\")))"
        );

        // Range selected refinement and value filter exclude true
        refinements = List.of(
            SelectedRefinement
                .builder()
                .field("field2")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, 2.0, null))
                .or(true)
                .build()
        );
        valueFilters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("field")
                .value("value")
                .exclude(false)
                .build()
        );

        validateSearchParameters(
            valueFilters,
            null,
            refinements,
            "((field2:IN(1.0i,2.0e))) AND ((field:ANY(\"value\")))"
        );

        // Range selected refinement text and range filter
        refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value")
                .or(true)
                .build()
        );
        var ranges = List.of(
            RangeFilter
                .builder()
                .field("field2")
                .range(new Range(1.0, 2.0, null))
                .build()
        );

        validateSearchParameters(
            null,
            ranges,
            refinements,
            "field:ANY(\"value\") AND ((((field2:IN(1.0i,2.0e)))))"
        );

        // Selected refinement filters (Text | Numeric)
        // Value filters (Text | Numeric)
        // Range filters
        refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("text")
                .value("value")
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("text")
                .value("value2")
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("text2")
                .value("value")
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("text2")
                .value("value2")
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .field("range")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, 2.0, null))
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .field("range")
                .type(NavigationType.RANGE)
                .range(new Range(10.0, 20.0, null))
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .field("range2")
                .type(NavigationType.RANGE)
                .range(new Range(1.0, 2.0, null))
                .or(true)
                .build(),
            SelectedRefinement
                .builder()
                .field("range2")
                .type(NavigationType.RANGE)
                .range(new Range(10.0, 20.0, null))
                .or(true)
                .build()
        );
        valueFilters = List.of(
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("text")
                .value("value")
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("text")
                .value("value5")
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("text")
                .value("value3")
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("text2")
                .value("value")
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.TEXTUAL)
                .field("text2")
                .value("value2")
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("numeric")
                .numberValue(1.0)
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("numeric")
                .numberValue(2.0)
                .exclude(false)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("numeric2")
                .numberValue(1.0)
                .exclude(true)
                .build(),
            ValueFilter
                .builder()
                .type(ValueFilter.ValueFilterType.NUMERIC)
                .field("numeric2")
                .numberValue(2.0)
                .exclude(false)
                .build()
        );
        ranges = List.of(
            RangeFilter
                .builder()
                .field("field")
                .range(new Range(1.0, 2.0, null))
                .build(),
            RangeFilter
                .builder()
                .field("field2")
                .range(new Range(10.0, 20.0, null))
                .build()
        );

        validateSearchParameters(
                valueFilters,
                ranges,
                refinements,
            "((range2:IN(1.0i,2.0e)) OR (range2:IN(10.0i,20.0e))) "
                + "AND text2:ANY(\"value\",\"value2\") AND ((range:IN(1.0i,2.0e)) OR (range:IN(10.0i,20.0e))) "
                + "AND text:ANY(\"value\",\"value2\") AND ((((field:IN(1.0i,2.0e))) AND text2:ANY(\"value2\") "
                + "AND (NOT text2:ANY(\"value\")) AND ((numeric=2.0)) AND (NOT numeric=1.0) AND ((numeric2=2.0)) "
                + "AND (NOT numeric2=1.0) AND text:ANY(\"value3\") AND (NOT text:ANY(\"value\",\"value5\")) "
                + "AND ((field2:IN(10.0i,20.0e)))))"
        );
    }

    @Test
    @DisplayName("Pre ans site filters")
    void createFilterFromPreFilterAndSiteFilter() {
        // pre filter | no site filter | no additional filters
        validateSearchParametersFromObject(
            searchParameters()
                .preFilter("text:ANY(\"value\",\"value2\")")
                .build(),
            "text:ANY(\"value\",\"value2\")"
        );

        // no pre filter | site filter | no additional filters
        validateSearchParametersFromObject(
            searchParameters()
                .merchandisingConfiguration(
                    MerchandisingConfiguration
                        .builder()
                        .siteFilter(SiteFilterConfiguration
                            .builder()
                            .rawFilter("text2:ANY(\"value\",\"value2\")")
                            .build())
                        .build()
                )
                .build(),
            "text2:ANY(\"value\",\"value2\")"
        );

        // pre filter | site filter | no additional filters
        validateSearchParametersFromObject(
            searchParameters()
                .preFilter("text2:ANY(\"value\",\"value2\")")
                .merchandisingConfiguration(
                    MerchandisingConfiguration
                        .builder()
                        .siteFilter(SiteFilterConfiguration
                            .builder()
                            .rawFilter("text2:ANY(\"value\",\"value2\")")
                            .build())
                        .build()
                )
                .build(),
            "(text2:ANY(\"value\",\"value2\")) AND (text2:ANY(\"value\",\"value2\"))"
        );

        // pre filter | site filter | additional filters
        var refinements = List.of(
            SelectedRefinement
                .builder()
                .type(NavigationType.VALUE)
                .field("field")
                .value("value")
                .or(true)
                .build()
        );

        validateSearchParametersFromObject(
            searchParameters()
                .preFilter("text2:ANY(\"value\",\"value2\")")
                .merchandisingConfiguration(
                    MerchandisingConfiguration
                        .builder()
                        .siteFilter(SiteFilterConfiguration
                            .builder()
                            .rawFilter("text2:ANY(\"value\",\"value2\")")
                            .build())
                        .build()
                )
                .refinements(refinements)
                .build(),
            "((text2:ANY(\"value\",\"value2\")) AND (text2:ANY(\"value\",\"value2\"))) AND (field:ANY(\"value\"))"
        );

        // pre filter | no site filter | additional filters
        validateSearchParametersFromObject(
            searchParameters()
                .preFilter("text2:ANY(\"value\",\"value2\")")
                .refinements(List.of(
                    SelectedRefinement
                        .builder()
                        .type(NavigationType.VALUE)
                        .field("field")
                        .value("value")
                        .or(true)
                        .build()
                ))
                .build(),
            "(text2:ANY(\"value\",\"value2\")) AND (field:ANY(\"value\"))"
        );

        // no pre filter | site filter | additional filters
        validateSearchParametersFromObject(
            searchParameters()
                .merchandisingConfiguration(
                    MerchandisingConfiguration
                        .builder()
                        .siteFilter(SiteFilterConfiguration
                            .builder()
                            .rawFilter("text2:ANY(\"value\",\"value2\")")
                            .build())
                        .build()
                )
                .refinements(List.of(
                    SelectedRefinement
                        .builder()
                        .type(NavigationType.VALUE)
                        .field("field")
                        .value("value")
                        .or(true)
                        .build()
                ))
                .build(),
            "(text2:ANY(\"value\",\"value2\")) AND (field:ANY(\"value\"))"
        );
    }

    @Test
    @DisplayName("Test that filters correctly escaped.")
    void jsonEscapeTest() {
        // (") have to be replaced with (\\")
        // (/) haven't be escaped
        var refinements = List.of(SelectedRefinement.builder()
            .type(NavigationType.VALUE)
            .field("field1")
            .value("0/Bearings:\"22617")
            .or(true)
            .build());

        var valueFilters = List.of(ValueFilter.builder()
            .type(ValueFilter.ValueFilterType.TEXTUAL)
            .field("field2")
            .value("0/Bearings:\"22617")
            .exclude(false)
            .build());

        validateSearchParameters(
            valueFilters,
            null,
            refinements,
            "field1:ANY(\"0/Bearings:\\\"22617\") AND ((field2:ANY(\"0/Bearings:\\\"22617\")))"
        );

    }

    private void validateRefinements(List<SelectedRefinement> refinements, String expected) {
        validateSearchParameters(null, null, refinements, expected);
    }

    private void validateValueFilters(List<ValueFilter> values, String expected) {
        validateSearchParameters(values, null, null, expected);
    }

    private void validateRangeFilters(List<RangeFilter> ranges, String expected) {
        validateSearchParameters(
            null,
            ranges,
            null,
            expected
        );
    }

    /**
     * Validates the filter string generated by {@link RetailFilterService} from a {@link SearchParameters} object
     * against an expected outcome. This method tests the filter creation process with the "Enable Curate Results"
     * feature flag in both enabled and disabled states, but expects the same outcome in both scenarios, indicating
     * that the feature flag doesn't alter the output for the given {@link SearchParameters}.
     * <p>
     * Initially, the method simulates the scenario where the "Enable Curate Results" feature a flag is disabled and
     * asserts that the generated filter string matches the expected string. Subsequently, it simulates the feature a
     * flag being enabled and verifies that the outcome remains consistent with the expected string. This ensures
     * that the filter construction logic within {@link RetailFilterService} correctly interprets the
     * {@link SearchParameters}, independent of the feature flag's state.
     * <p>
     * This method is essential for confirming that specific {@link SearchParameters} are correctly translated into
     * filter strings under various feature configurations, thereby validating the correctness and stability of the
     * search filtering logic.
     *
     * @param searchParameters The {@link SearchParameters} object containing the criteria for filter construction.
     * @param expected The expected filter string result, which should match the output of
     *                 {@link RetailFilterService#createFilter} regardless of the "Enable Curate Results"
     *                 feature flag state.
     */
    private void validateSearchParametersFromObject(SearchParameters searchParameters, String expected) {
        assertThat(
            retailFilterService.createFilter(searchParameters)
        ).isEqualTo(expected);
    }

    /**
     * Validates the construction of a filter string from search parameters under different feature flag configurations.
     * This method tests the {@link RetailFilterService#createFilter} functionality by providing lists of value filters,
     * range filters, and selected refinements, along with expected filter string outcomes for scenarios where the
     * "Enable Curate Results" feature flag is both turned off and on.
     * <p>
     * The method first simulates the feature a flag being turned off, and asserts that the filter string created by
     * the {@link RetailFilterService} matches the expected filter string for this configuration.
     * It then simulates the feature a flag being turned on, altering the way {@link RetailFilterService} constructs
     * the filter string, and asserts that the outcome matches the expected filter string for a curated results'
     * scenario.
     * <p>
     * This dual-phase validation allows for comprehensive testing of the filter creation logic under varying feature
     * configurations, ensuring that the search functionality behaves as expected in different operational contexts.
     *
     * @param values      A list of {@link ValueFilter} instances to be included in the search parameters.
     * @param ranges      A list of {@link RangeFilter} instances to be included in the search parameters.
     * @param refinements A list of {@link SelectedRefinement} instances representing user-selected refinements.
     * @param expected    The expected filter string result
     */
    private void validateSearchParameters(List<ValueFilter> values,
                                          List<RangeFilter> ranges,
                                          List<SelectedRefinement> refinements,
                                          String expected) {
        assertThat(
            retailFilterService.createFilter(
                searchParameters()
                    .attributeFilters(
                        List.of(
                            new AttributeFilter(values, ranges)
                        )
                    )
                    .refinements(refinements)
                    .build()
            )
        ).isEqualTo(expected);
    }

    /**
     * Creates and initializes a {@link SearchParameters.SearchParametersBuilder} instance with a pre-defined
     * context. This utility method encapsulates the creation of a search parameters builder, setting up a default
     * context with predetermined values for the merchandiser, area, collection, visitor ID, request ID, and login ID.
     * <p>
     * This method is designed to streamline the initialization of {@link SearchParameters} for testing or default
     * search scenarios, where a standard context is required.
     * <p>
     * Example usage:
     * <pre>
     * SearchParameters params = searchParameters()
     *     .addAttributeFilter(...)
     *     .addRangeFilter(...)
     *     .build();
     * </pre>
     * <p>
     * This approach allows for the flexible addition of further search criteria to the builder before finalizing
     * the {@link SearchParameters} object.
     *
     * @return A {@link SearchParameters.SearchParametersBuilder} instance pre-initialized with a default context.
     */
    private SearchParameters.SearchParametersBuilder searchParameters() {
        return SearchParameters.builder();
    }

    private void validateTextField(List<String> value, String expected) {
        assertThat(
            retailFilterService.createTextFieldFilter("field", value)
        ).isEqualTo(expected);
    }

}
