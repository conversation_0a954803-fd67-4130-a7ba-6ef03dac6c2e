package com.groupbyinc.search.ssa.retail.filtering.filter;

import com.groupbyinc.search.ssa.application.core.search.filtering.ToFilterTest;
import com.groupbyinc.search.ssa.retail.filtering.expression.numerical.RetailComparisonExpression;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.provider.Arguments;

import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.retail.filtering.expression.RetailNotExpression.not;
import static com.groupbyinc.search.ssa.retail.filtering.expression.numerical.RetailRangeExpression.range;
import static com.groupbyinc.search.ssa.retail.filtering.expression.text.RetailTextExpression.contains;

@DisplayName("RetailFilterTest")
class RetailFilterTest extends ToFilterTest<String> {

    @Override
    public Stream<Arguments> canBuildAValidFilterArguments() {

        return Stream.of(
            // contains
            Arguments.of(
                RetailFilter.builder()
                    .and(contains("title", "cheap"))
                    .build(),
                "title:ANY(\"cheap\")"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .and(not(contains("title", "cheap")))
                    .build(),
                "(NOT title:ANY(\"cheap\"))"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .and(contains("title", "cheap"))
                    .and(contains("text", "test"))
                    .build(),
                "title:ANY(\"cheap\") AND text:ANY(\"test\")"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .and(not(contains("title", "cheap")))
                    .and(contains("text", "test"))
                    .build(),
                "(NOT title:ANY(\"cheap\")) AND text:ANY(\"test\")"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .and(contains("title", "cheap"))
                    .and(not(contains("text", "test")))
                    .build(),
                "title:ANY(\"cheap\") AND (NOT text:ANY(\"test\"))"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .and(not(contains("title", "cheap")))
                    .and(not(contains("text", "test")))
                    .build(),
                "(NOT title:ANY(\"cheap\")) AND (NOT text:ANY(\"test\"))"
            ),

            // comparison
            Arguments.of(
                RetailFilter.builder()
                    .and(new RetailComparisonExpression("price", 10.5))
                    .build(),
                "price=10.5"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .and(not(new RetailComparisonExpression("price", 10.5)))
                    .build(),
                "(NOT price=10.5)"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .and(new RetailComparisonExpression("price", 10.5))
                    .and(new RetailComparisonExpression("price", 1.5))
                    .build(),
                "price=10.5 AND price=1.5"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .and(not(new RetailComparisonExpression("price", 10.5)))
                    .and(new RetailComparisonExpression("price", 1.5))
                    .build(),
                "(NOT price=10.5) AND price=1.5"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .and(new RetailComparisonExpression("price", 10.5))
                    .and(not(new RetailComparisonExpression("price", 1.5)))
                    .build(),
                "price=10.5 AND (NOT price=1.5)"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .and(not(new RetailComparisonExpression("price", 10.5)))
                    .and(not(new RetailComparisonExpression("price", 1.5)))
                    .build(),
                "(NOT price=10.5) AND (NOT price=1.5)"
            ),

            // range AND
            Arguments.of(
                RetailFilter.builder()
                    .and(range("price").lowerInclusive(5.99).build())
                    .build(),
                "price:IN(5.99i,*)"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .and(not(range("price").lowerInclusive(5.99).build()))
                    .build(),
                "(NOT price:IN(5.99i,*))"
            ),

            // RANGE OR
            Arguments.of(
                RetailFilter.builder()
                    .or(range("price").lowerInclusive(5.99).build())
                    .build(),
                "((price:IN(5.99i,*)))"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .or(range("price").lowerInclusive(5.99).build())
                    .or(range("price").lowerInclusive(50.99).build())
                    .build(),
                "((price:IN(5.99i,*)) OR (price:IN(50.99i,*)))"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .or(not(range("price").lowerInclusive(5.99).build()))
                    .or(range("price").lowerInclusive(50.99).build())
                    .build(),
                "(((NOT price:IN(5.99i,*))) OR (price:IN(50.99i,*)))"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .or(range("price").lowerInclusive(5.99).build())
                    .or(not(range("price").lowerInclusive(50.99).build()))
                    .build(),
                "((price:IN(5.99i,*)) OR ((NOT price:IN(50.99i,*))))"
            ),
            Arguments.of(
                RetailFilter.builder()
                    .or(not(range("price").lowerInclusive(5.99).build()))
                    .or(not(range("price").lowerInclusive(50.99).build()))
                    .build(),
                "(((NOT price:IN(5.99i,*))) OR ((NOT price:IN(50.99i,*))))"
            ),

            // combined filter
            Arguments.of(
                RetailFilter.builder()
                    .and(contains("brand", "cheap"))
                    .and(not(contains("brand", "test")))
                    .and(not(new RetailComparisonExpression("price", 10.5)))
                    .and(new RetailComparisonExpression("price", 1.5))
                    .or(range("price").lowerInclusive(5.99).build())
                    .or(not(range("price").lowerInclusive(50.99).build()))
                    .or(new RetailComparisonExpression("price", 10))
                    .or(not(new RetailComparisonExpression("price", 15)))
                    .build(),
                "brand:ANY(\"cheap\") AND (NOT brand:ANY(\"test\")) AND (NOT price=10.5) AND price=1.5 AND " +
                    "((price:IN(5.99i,*)) OR ((NOT price:IN(50.99i,*))) OR (price=10) OR ((NOT price=15)))"
            )

        );
    }

}
