package com.groupbyinc.search.ssa.retail.filtering.expression.numerical;

import com.groupbyinc.search.ssa.application.core.search.filtering.ToFilterTest;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.provider.Arguments;

import java.util.stream.Stream;

@DisplayName("RetailComparisonExpressionTest")
class RetailComparisonExpressionTest extends ToFilterTest<String> {

    @Override
    public Stream<Arguments> canBuildAValidFilterArguments() {
        return Stream.of(
            Arguments.of(new RetailComparisonExpression("price", 10.5), "price=10.5")
        );
    }

}
