package com.groupbyinc.search.ssa.retail.filtering;

import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
import com.groupbyinc.search.ssa.core.Order;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.navigation.Facet;
import com.groupbyinc.search.ssa.core.navigation.Navigation;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.navigation.NavigationRefinement;
import com.groupbyinc.search.ssa.core.navigation.NavigationSort;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;

import com.google.cloud.retail.v2.Interval;
import com.google.cloud.retail.v2.SearchRequest.FacetSpec;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.util.StringUtils.SOURCE_DYNAMIC;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;

@DisplayName("FacetConverter Tests")
class RetailFacetConverterTest {

    private final int FACET_LIMIT = 300;

    private Set<NavigationConfiguration> navigationConfigurations;

    @BeforeEach
    void setUp() {
        navigationConfigurations = new HashSet<>();
    }

    @Test
    @DisplayName("Can create a value facet spec from a value navigation")
    void canCreateValueFacet() {
        var navigationConfiguration = mock(NavigationConfiguration.class);
        given(navigationConfiguration.field()).willReturn("brands");

        navigationConfigurations.add(navigationConfiguration);
        var searchParameters = SearchParameters
            .builder()
            .merchandisingConfiguration(
                MerchandisingConfiguration
                    .builder()
                    .navigationConfigurations(navigationConfigurations)
                    .build()
            )
            .build();
        var facetConverter = new RetailFacetConverter(searchParameters);

        var facetSpecs = facetConverter.createFacetSpecs();

        assertThat(facetSpecs).containsExactly(
            FacetSpec.newBuilder()
                .setFacetKey(
                    FacetSpec.FacetKey.newBuilder()
                        .setKey("brands")
                        .build()
                )
                .setLimit(FACET_LIMIT)
                .build()
        );
    }

    @Test
    @DisplayName("Can create a value facet for navigation and navigation facet")
    void createValueFacetForNavigationValueFacet() {
        var facet = new Facet(
            "prefix1",
            "contains1",
            "Displayed as brands",
            NavigationType.VALUE,
            "brands"
        );

        var navigationConfiguration = mock(NavigationConfiguration.class);
        given(navigationConfiguration.field()).willReturn("brands");
        navigationConfigurations.add(navigationConfiguration);

        var searchParameters = SearchParameters
            .builder()
            .facet(facet)
            .merchandisingConfiguration(
                MerchandisingConfiguration
                    .builder()
                    .navigationConfigurations(navigationConfigurations)
                    .build()
            )
            .build();

        var facetConverter = new RetailFacetConverter(searchParameters);

        var facetSpecs = facetConverter.createFacetSpecs();

        assertThat(facetSpecs).containsExactly(
            FacetSpec.newBuilder()
                .setFacetKey(
                    FacetSpec.FacetKey.newBuilder()
                        .setCaseInsensitive(true)
                        .setKey("brands")
                        .addContains("contains1")
                        .addPrefixes("prefix1")
                        .build()
                )
                .setLimit(FACET_LIMIT)
                .build()
        );
    }

    @Test
    @DisplayName("Can create a facet for provided facet only")
    void createFacetForProvidedFacetOnly() {
        var facet = new Facet(
            "prefix1",
            "contains1",
            "Displayed as brands",
            NavigationType.RANGE,
            "brands"
        );

        var searchParameters = SearchParameters
            .builder()
            .facet(facet)
            .merchandisingConfiguration(
                MerchandisingConfiguration
                    .builder()
                    .navigationConfigurations(navigationConfigurations)
                    .build()
            )
            .build();

        var facetConverter = new RetailFacetConverter(searchParameters);

        var facetSpecs = facetConverter.createFacetSpecs();

        assertThat(facetSpecs).containsExactly(
            FacetSpec.newBuilder()
                .setFacetKey(
                    FacetSpec.FacetKey.newBuilder()
                        .setCaseInsensitive(true)
                        .setKey("brands")
                        .addContains("contains1")
                        .addPrefixes("prefix1")
                        .build()
                )
                .build());
    }

    @ParameterizedTest(name = "Navigation sort {0} is converted to orderBy ''{1}''")
    @MethodSource("facetOrderByArguments")
    @DisplayName("Navigations have their sort converted to a facet order by")
    void facetOrderBy(NavigationSort navigationSort, String expectedOrderBy) {
        var navigationConfiguration = mock(NavigationConfiguration.class);
        given(navigationConfiguration.field()).willReturn("brands");
        given(navigationConfiguration.type()).willReturn(NavigationType.VALUE);
        given(navigationConfiguration.sort()).willReturn(navigationSort);
        navigationConfigurations.add(navigationConfiguration);

        var searchParameters = SearchParameters
            .builder()
            .merchandisingConfiguration(
                MerchandisingConfiguration
                    .builder()
                    .navigationConfigurations(navigationConfigurations)
                    .build()
            )
            .build();

        var facetConverter = new RetailFacetConverter(searchParameters);

        var facetSpecs = facetConverter.createFacetSpecs();

        assertThat(facetSpecs).containsExactly(
            FacetSpec.newBuilder()
                .setFacetKey(
                    FacetSpec.FacetKey.newBuilder()
                        .setKey("brands")
                        .setOrderBy(expectedOrderBy)
                        .build()
                )
                .setLimit(FACET_LIMIT)
                .build()
        );
    }

    private static Stream<Arguments> facetOrderByArguments() {
        return Stream.of(
            Arguments.of(new NavigationSort(NavigationSort.SortField.COUNT, Order.DESCENDING), "count desc"),
            Arguments.of(new NavigationSort(NavigationSort.SortField.VALUE, Order.DESCENDING), "value desc")
        );
    }

    @Test
    @DisplayName("Can create an interval facet spec from a range navigation")
    void canCreateIntervalFacet() {
        var navigationConfiguration = mock(NavigationConfiguration.class);
        given(navigationConfiguration.field()).willReturn("price");
        given(navigationConfiguration.type()).willReturn(NavigationType.RANGE);
        given(navigationConfiguration.ranges()).willReturn(List.of(
            new Range(0d, 50d, null),
            new Range(50d, 100d, null)
        ));
        navigationConfigurations.add(navigationConfiguration);

        var searchParameters = SearchParameters
            .builder()
            .merchandisingConfiguration(
                MerchandisingConfiguration
                    .builder()
                    .navigationConfigurations(navigationConfigurations)
                    .build()
            )
            .build();

        var facetConverter = new RetailFacetConverter(searchParameters);

        var facetSpecs = facetConverter.createFacetSpecs();

        assertThat(facetSpecs).containsExactly(
            FacetSpec.newBuilder()
                .setFacetKey(
                    FacetSpec.FacetKey.newBuilder()
                        .setKey("price")
                        .addAllIntervals(List.of(
                            Interval.newBuilder()
                                .setMinimum(0D)
                                .setExclusiveMaximum(50D)
                                .build(),
                            Interval.newBuilder()
                                .setMinimum(50D)
                                .setExclusiveMaximum(100D)
                                .build()
                        ))
                        .build()
                )
                .setLimit(FACET_LIMIT)
                .build()
        );
    }

    @Test
    @DisplayName("Can create a value navigation from a value facet")
    void canCreateAValueNavigation() {
        var navigationConfiguration = mock(NavigationConfiguration.class);
        given(navigationConfiguration.name()).willReturn("Brand");
        given(navigationConfiguration.field()).willReturn("brands");
        navigationConfigurations.add(navigationConfiguration);

        var searchParameters = SearchParameters
            .builder()
            .merchandisingConfiguration(
                MerchandisingConfiguration
                    .builder()
                    .navigationConfigurations(navigationConfigurations)
                    .build()
            )
            .build();

        var facetConverter = new RetailFacetConverter(searchParameters);

        var navigation = facetConverter.convertFacetToNavigation(
            com.google.cloud.retail.v2.SearchResponse.Facet.newBuilder()
                .setKey("brands")
                .addAllValues(List.of(
                    com.google.cloud.retail.v2.SearchResponse.Facet.FacetValue.newBuilder()
                        .setValue("Nike")
                        .setCount(50)
                        .build(),
                    com.google.cloud.retail.v2.SearchResponse.Facet.FacetValue.newBuilder()
                        .setValue("Puma")
                        .setCount(45)
                        .build(),
                    com.google.cloud.retail.v2.SearchResponse.Facet.FacetValue.newBuilder()
                        .setValue("Adidas")
                        .setCount(32)
                        .build()
                ))
                .build()
        );

        assertThat(navigation).isEqualTo(
            Navigation.builder()
                .field("brands")
                .name("Brand")
                .type(NavigationType.VALUE)
                .refinements(List.of(
                    NavigationRefinement.valueRefinement("Nike", 50, false),
                    NavigationRefinement.valueRefinement("Puma", 45, false),
                    NavigationRefinement.valueRefinement("Adidas", 32, false)
                ))
                .pinned(false)
                .build()
        );
    }

    @Test
    @DisplayName("Can create a range navigation from an interval facet")
    void canCreateARangeNavigation() {
        var navigationConfiguration = mock(NavigationConfiguration.class);
        given(navigationConfiguration.name()).willReturn("Price");
        given(navigationConfiguration.field()).willReturn("price");
        navigationConfigurations.add(navigationConfiguration);

        var searchParameters = SearchParameters
            .builder()
            .merchandisingConfiguration(
                MerchandisingConfiguration
                    .builder()
                    .navigationConfigurations(navigationConfigurations)
                    .build()
            )
            .build();

        var facetConverter = new RetailFacetConverter(searchParameters);

        var navigation = facetConverter.convertFacetToNavigation(
            com.google.cloud.retail.v2.SearchResponse.Facet.newBuilder()
                .setKey("price")
                .addAllValues(List.of(
                    com.google.cloud.retail.v2.SearchResponse.Facet.FacetValue.newBuilder()
                        .setInterval(
                            Interval.newBuilder()
                                .setMinimum(0)
                                .setExclusiveMaximum(50)
                                .build()
                        )
                        .setCount(50)
                        .build(),
                    com.google.cloud.retail.v2.SearchResponse.Facet.FacetValue.newBuilder()
                        .setInterval(
                            Interval.newBuilder()
                                .setMinimum(50)
                                .setExclusiveMaximum(100)
                                .build()
                        )
                        .setCount(45)
                        .build()
                ))
                .build()
        );

        assertThat(navigation).isEqualTo(
            Navigation.builder()
                .field("price")
                .name("Price")
                .type(NavigationType.RANGE)
                .refinements(List.of(
                    NavigationRefinement.rangeRefinement(new Range(0D, 50D, null), 50),
                    NavigationRefinement.rangeRefinement(new Range(50D, 100D, null), 45)
                ))
                .build()
        );
    }

    @Test
    @DisplayName("Will create a dynamic navigation for a facet that has no configuration")
    void willCreateANavigationForAFacetThatHasNoConfiguration() {
        var searchParameters = SearchParameters
            .builder()
            .merchandisingConfiguration(
                MerchandisingConfiguration
                    .builder()
                    .navigationConfigurations(navigationConfigurations)
                    .build()
            )
            .build();

        var facetConverter = new RetailFacetConverter(searchParameters);

        var navigation = facetConverter.convertFacetToNavigation(
            com.google.cloud.retail.v2.SearchResponse.Facet.newBuilder()
                .setKey("attribute.CATEGORIES")
                .addAllValues(List.of(
                    com.google.cloud.retail.v2.SearchResponse.Facet.FacetValue.newBuilder()
                        .setValue("Shoes > Running > Trail Running")
                        .setCount(50)
                        .build(),
                    com.google.cloud.retail.v2.SearchResponse.Facet.FacetValue.newBuilder()
                        .setValue("Shoes > Running > Track Running")
                        .setCount(45)
                        .build()
                ))
                .build()
        );

        assertThat(navigation).isEqualTo(Navigation.builder()
            .field("attribute.CATEGORIES")
            .name("CATEGORIES")
            .type(NavigationType.VALUE)
            .refinements(List.of(
                NavigationRefinement.valueRefinement("Shoes > Running > Trail Running", 50, false),
                NavigationRefinement.valueRefinement("Shoes > Running > Track Running", 45, false)
            ))
            .source(SOURCE_DYNAMIC)
            .multiSelect(true)
            .pinned(false)
            .build()
        );
    }

    @Test
    @DisplayName("Will not create a navigation for a facet that has no values")
    void willNotCreateANavigationForAFacetThatHasNoValues() {
        var searchParameters = SearchParameters
            .builder()
            .merchandisingConfiguration(
                MerchandisingConfiguration
                    .builder()
                    .navigationConfigurations(navigationConfigurations)
                    .build()
            )
            .build();

        var facetConverter = new RetailFacetConverter(searchParameters);

        var navigation = facetConverter.convertFacetToNavigation(
            com.google.cloud.retail.v2.SearchResponse.Facet.newBuilder()
                .setKey("categories")
                .build()
        );

        assertThat(navigation).isNull();
    }

    @Test
    @DisplayName("Limits the number of facets to 100")
    void limitsTheNumberOfFacetsToOneHundred() {
        for (int i = 0; i < 100; i++) {
            var navigationConfiguration = mock(NavigationConfiguration.class);
            given(navigationConfiguration.field()).willReturn("" + i);

            navigationConfigurations.add(navigationConfiguration);
        }

        var searchParameters = SearchParameters
            .builder()
            .merchandisingConfiguration(
                MerchandisingConfiguration
                    .builder()
                    .navigationConfigurations(navigationConfigurations)
                    .build()
            )
            .build();

        var facetConverter = new RetailFacetConverter(searchParameters);

        var facetSpecs = facetConverter.createFacetSpecs();

        for (int i = 0; i < 100; i++) {
            assertThat(facetSpecs.get(i).getLimit()).isEqualTo(FACET_LIMIT);
        }
    }

}
