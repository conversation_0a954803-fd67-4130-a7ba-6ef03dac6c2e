package com.groupbyinc.search.ssa.retail;

import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.project.ProjectConfiguration;
import com.groupbyinc.search.ssa.stub.ResourceUtils;

import com.google.cloud.retail.v2.Product;
import com.google.cloud.retail.v2.ProductServiceClient;
import com.google.protobuf.util.JsonFormat;
import io.micronaut.core.propagation.PropagatedContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.DEFAULT_CONTEXT;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.when;


@DisplayName("ProductGoogleSearcher Tests")
@ExtendWith(MockitoExtension.class)
class ProductGoogleSearcherTest {

    private final Product.Builder product = Product.newBuilder();

    @InjectMocks
    private ProductGoogleSearcher productGoogleSearcher;
    @Mock
    private ProductServiceClient productServiceClient;
    @Mock
    private ConfigurationManager configurationManager;

    @BeforeEach
    void init() throws Exception {
        given(configurationManager.getProjectConfiguration(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING))
            .willReturn(Optional.of(getProjectConfiguration()));

        JsonFormat.parser().merge(
            ResourceUtils.getResourceContents("retail/retail-response-with-single-product.json"),
            product
        );

        PropagatedContext.getOrEmpty().plus(DEFAULT_CONTEXT).propagate();
    }

    @Test
    void prioritizeWhenRequestVariantIdsAndVariantsExist() {
        var requestVariantIds = List.of("2", "3");
        var productList = getProductList();
        var expected = List.of("2", "3", "1", "4", "5");

        product.clearVariants().addAllVariants(productList);
        when(productServiceClient.getProduct(
            "projects/null/locations/global/catalogs/default_catalog/branches/default_branch/products/any"
        )).thenReturn(product.build());

        var actual = productGoogleSearcher.getProductDetails("any", requestVariantIds);

        assertThat(actual).isPresent();
        // verify ids only
        assertThat(
            actual.get()
                .getVariants()
                .stream()
                .map(com.groupbyinc.search.ssa.core.product.Product::getId)
                .toList())
            .isEqualTo(expected);
    }

    @Test
    void prioritizeWhenRequestVariantIdsAndEmpty() {
        var productList = getProductList();
        var expected = List.of("1", "2", "3", "4", "5");

        product.clearVariants().addAllVariants(productList);
        when(productServiceClient.getProduct(
            "projects/null/locations/global/catalogs/default_catalog/branches/default_branch/products/any"
        )).thenReturn(product.build());

        var actual = productGoogleSearcher.getProductDetails("any", null);

        assertThat(actual).isPresent();
        // verify ids only
        assertThat(
            actual.get().getVariants().stream().map(com.groupbyinc.search.ssa.core.product.Product::getId).toList()
        ).isEqualTo(expected);
    }

    @Test
    void prioritizeWhenVariantsEmpty() {
        var requestVariantIds = List.of("2", "3");

        product.clearVariants();
        when(productServiceClient.getProduct(
            "projects/null/locations/global/catalogs/default_catalog/branches/default_branch/products/any"
        )).thenReturn(product.build());

        var actual = productGoogleSearcher.getProductDetails("any", requestVariantIds);

        assertThat(actual).isPresent();
        // verify ids only
        assertThat(
            actual.get()
                .getVariants()
                .stream()
                .map(com.groupbyinc.search.ssa.core.product.Product::getId)
                .toList())
            .isEmpty();
    }

    @Test
    void prioritizeWhenRequestVariantIdsNotInVariants() {
        var requestVariantIds = List.of("6", "7");
        var productList = getProductList();
        var expected = List.of("1", "2", "3", "4", "5");

        product.clearVariants().addAllVariants(productList);
        when(productServiceClient.getProduct(
            "projects/null/locations/global/catalogs/default_catalog/branches/default_branch/products/any"
        )).thenReturn(product.build());

        var actual = productGoogleSearcher.getProductDetails("any", requestVariantIds);

        assertThat(actual).isPresent();
        // verify ids only
        assertThat(
            actual.get()
                .getVariants()
                .stream()
                .map(com.groupbyinc.search.ssa.core.product.Product::getId)
                .toList())
            .isEqualTo(expected);
    }


    private ProjectConfiguration getProjectConfiguration() {
        return ProjectConfiguration.builder()
            .collection(PRODUCTS_CLOTHING)
            .build();
    }

    private List<Product> getProductList() {
        return Arrays.asList(
            Product.newBuilder().setId("1").build(),
            Product.newBuilder().setId("2").build(),
            Product.newBuilder().setId("3").build(),
            Product.newBuilder().setId("4").build(),
            Product.newBuilder().setId("5").build()
        );
    }
}
