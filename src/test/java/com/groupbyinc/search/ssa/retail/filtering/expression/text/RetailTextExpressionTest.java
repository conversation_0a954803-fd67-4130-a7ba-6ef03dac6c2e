package com.groupbyinc.search.ssa.retail.filtering.expression.text;

import com.groupbyinc.search.ssa.application.core.search.filtering.ToFilterTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.provider.Arguments;

import java.util.List;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.retail.filtering.expression.text.RetailTextExpression.contains;

@DisplayName("RetailTextExpressionTest")
class RetailTextExpressionTest extends ToFilterTest<String> {

    @Override
    public Stream<Arguments> canBuildAValidFilterArguments() {

        return Stream.of(
            Arguments.of(contains("title", "cheap"), "title:ANY(\"cheap\")"),
            Arguments.of(contains("title", List.of("cheap", "fancy")), "title:ANY(\"cheap\",\"fancy\")")
        );
    }

}
