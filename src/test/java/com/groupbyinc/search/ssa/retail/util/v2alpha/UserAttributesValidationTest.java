package com.groupbyinc.search.ssa.retail.util.v2alpha;

import com.groupbyinc.search.ssa.api.dto.crm.UserAttributeDto;

import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@MicronautTest
public class UserAttributesValidationTest {

    @Inject
    Validator validator;

    @Test
    @DisplayName("Validate userAttributes")
    void validateUserAttributes() {
        var input = List.of(
            new UserAttributeDto(null, null)
        );
        var violations = input.stream()
            .flatMap(dto -> validator.validate(dto).stream())
            .collect(Collectors.toSet());

        assertEquals(2, violations.size());

        var messages = violations.stream()
            .map(ConstraintViolation::getMessage)
            .collect(Collectors.toSet());

        assertTrue(messages.contains("must not be null"), "Expected 'must not be null' violations");
    }
}
