package com.groupbyinc.search.ssa.retail.filtering;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

public class RetailFilterServiceSpecificTest {

    private final RetailFilterService retailFilterService = new RetailFilterService();

    @Test
    @DisplayName("Create boosting spec condition positive tests.")
    void createBoostingSpecConditionPositive() {
        // single value
        validateTextField(List.of("value"), "field:ANY(\"value\")");

        // multiple values
        validateTextField(List.of("value1", "value2"), "field:ANY(\"value1\",\"value2\")");
    }

    private void validateTextField(List<String> value, String expected) {
        assertThat(
            retailFilterService.createTextFieldFilter("field", value)
        ).isEqualTo(expected);
    }

}
