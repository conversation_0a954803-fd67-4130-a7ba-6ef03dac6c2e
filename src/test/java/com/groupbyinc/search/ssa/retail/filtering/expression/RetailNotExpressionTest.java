package com.groupbyinc.search.ssa.retail.filtering.expression;

import com.groupbyinc.search.ssa.application.core.search.filtering.ToFilterTest;
import com.groupbyinc.search.ssa.retail.filtering.expression.numerical.RetailComparisonExpression;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.provider.Arguments;

import java.util.List;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.retail.filtering.expression.RetailNotExpression.not;
import static com.groupbyinc.search.ssa.retail.filtering.expression.numerical.RetailRangeExpression.range;
import static com.groupbyinc.search.ssa.retail.filtering.expression.text.RetailTextExpression.contains;

@DisplayName("RetailNotExpressionTest")
class RetailNotExpressionTest extends ToFilterTest<String> {

    @Override
    public Stream<Arguments> canBuildAValidFilterArguments() {

        return Stream.of(
            Arguments.of(not(contains("field", "value")), "(NOT field:ANY(\"value\"))"),
            Arguments.of(not(contains("field", List.of("v1", "v2"))), "(NOT field:ANY(\"v1\",\"v2\"))"),
            Arguments.of(not(new RetailComparisonExpression("price", 10)), "(NOT price=10)"),
            Arguments.of(
                not(range("price").lowerInclusive(10).upperExclusive(100).build()),
                "(NOT price:IN(10i,100e))"
            )
        );
    }

}
