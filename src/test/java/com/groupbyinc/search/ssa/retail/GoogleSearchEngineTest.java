package com.groupbyinc.search.ssa.retail;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.gax.grpc.GrpcStatusCode;
import com.google.api.gax.rpc.InvalidArgumentException;
import com.google.cloud.retail.v2.Product;
import com.google.cloud.retail.v2.SearchRequest;
import com.google.cloud.retail.v2.SearchResponse;
import com.google.cloud.retail.v2.SearchServiceClient;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;

import com.groupbyinc.search.ssa.application.Clock;
import com.groupbyinc.search.ssa.application.cache.Cache;
import com.groupbyinc.search.ssa.application.cache.CacheOperations;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupValuesConverter;
import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
import com.groupbyinc.search.ssa.core.Order;
import com.groupbyinc.search.ssa.core.SearchMode;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.Sort;
import com.groupbyinc.search.ssa.core.area.AreaConfiguration;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.features.Features;
import com.groupbyinc.search.ssa.core.navigation.Navigation;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.project.ProjectConfiguration;
import com.groupbyinc.search.ssa.core.rule.PinnedProduct;
import com.groupbyinc.search.ssa.core.rule.ProductIdsBucket;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.redis.CacheResponse;
import com.groupbyinc.search.ssa.redis.key.browse.BrowseCacheConfig;
import com.groupbyinc.search.ssa.redis.key.browse.BrowseCacheKeyGenerator;
import com.groupbyinc.search.ssa.redis.key.browse.PinToTopCacheKeyGenerator;
import com.groupbyinc.search.ssa.redis.key.search.SearchCacheConfig;
import com.groupbyinc.search.ssa.redis.key.search.SearchCacheKeyGenerator;
import com.groupbyinc.search.ssa.retail.boost.BoostSpecConverter;
import com.groupbyinc.search.ssa.retail.exception.SiteSearchRetailClientException;
import com.groupbyinc.search.ssa.retail.exception.SiteSearchRetailServerException;
import com.groupbyinc.search.ssa.retail.filtering.RetailFilterService;
import com.groupbyinc.search.ssa.retail.filteringold.RetailFilterServiceOld;
import com.groupbyinc.search.ssa.util.PrefixTreeMap;
import io.grpc.Status;
import io.micronaut.core.propagation.PropagatedContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeSet;
import java.util.function.Supplier;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.core.RecordLabel.BOOSTED;
import static com.groupbyinc.search.ssa.core.RecordLabel.BURIED;
import static com.groupbyinc.search.ssa.core.RecordLabel.PINNED;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.VALUE;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_BROWSE_CACHE;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_SEARCH_CACHE;
import static com.groupbyinc.search.ssa.redis.CacheSource.BROWSE;
import static com.groupbyinc.search.ssa.redis.CacheSource.SEARCH;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@DisplayName("GoogleSearchEngineTest Tests")
class GoogleSearchEngineTest {

    private static final Supplier<Instant> INSTANT_SUPPLIER = () ->
        Instant.parse("2021-03-15T15:34:05.183275Z").plusMillis(53);

    private static final MerchandisingConfiguration EMPTY_MERCHANDISING_CONFIGURATION = new MerchandisingConfiguration(
        AreaConfiguration.builder().build(),
        new TreeSet<>(),
        new TreeSet<>(),
        new TreeSet<>(),
        List.of(),
        new HashMap<>(),
        INSTANT_SUPPLIER,
        null,
        List.of(),
        new Features()
    );

    private final ObjectMapper objectMapper = Mockito.spy(new ObjectMapper()).findAndRegisterModules();

    private SearchResponse searchResponse;
    private SearchServiceClient searchServiceClient;
    private GoogleSearchEngine googleSearchEngine;
    private final FeaturesManager featuresManager = mock(FeaturesManager.class);

    private PropagatedContext.Scope scope;

    @BeforeEach
    void setUp() {
        searchServiceClient = mock(SearchServiceClient.class);
        var configurationManager = mock(ConfigurationManager.class);
        var clockMock = mock(Clock.class);
        var retailFilterServiceOld = new RetailFilterServiceOld();
        var retailFilterService = new RetailFilterService();
        var browseCache = mock(Cache.class);
        var searchCache = mock(Cache.class);
        var baseCacheOperations = mock(CacheOperations.class);
        var browseGenerator = mock(BrowseCacheKeyGenerator.class);
        var searchGenerator = mock(SearchCacheKeyGenerator.class);
        var pinToTopGenerator = mock(PinToTopCacheKeyGenerator.class);
        var variantRollupValuesConverter = mock(VariantRollupValuesConverter.class);

        googleSearchEngine = new GoogleSearchEngine(
            clockMock,
            objectMapper,
            featuresManager,
            browseCache,
            searchCache,
            new BoostSpecConverter(
                featuresManager,
                retailFilterService,
                retailFilterServiceOld
            ),
            retailFilterServiceOld,
            retailFilterService,
            searchServiceClient,
            baseCacheOperations,
            configurationManager,
            browseGenerator,
            searchGenerator,
            pinToTopGenerator,
            variantRollupValuesConverter
        );
        var projectConfiguration = mock(ProjectConfiguration.class);

        var searchPagedResponse = mock(SearchServiceClient.SearchPagedResponse.class);
        var searchPage = mock(SearchServiceClient.SearchPage.class);
        searchResponse = mock(SearchResponse.class, RETURNS_DEEP_STUBS);

        when(clockMock.now()).thenReturn(Instant.now());
        when(searchPagedResponse.getPage()).thenReturn(searchPage);
        when(searchPage.getResponse()).thenReturn(searchResponse);
        when(searchServiceClient.search(any())).thenReturn(searchPagedResponse);

        when(configurationManager.getProjectConfiguration(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING))
            .thenReturn(Optional.of(projectConfiguration));
        when(projectConfiguration.projectId()).thenReturn("8008135");

        when(featuresManager.getObjectFlagConfiguration(any(), eq(ENABLE_BROWSE_CACHE), any(), any()))
            .thenReturn(BrowseCacheConfig.DEFAULT);

        when(featuresManager.getObjectFlagConfiguration(any(), eq(ENABLE_SEARCH_CACHE), any(), any()))
            .thenReturn(SearchCacheConfig.DEFAULT);

        when(baseCacheOperations.getFromCache(any(), any(), any(), any(), any(), eq(BROWSE)))
            .thenReturn(new CacheResponse<>(BrowseCacheConfig.DEFAULT, Optional.empty(), Optional.empty()));

        when(baseCacheOperations.getFromCache(any(), any(), any(), any(), any(), eq(SEARCH)))
            .thenReturn(new CacheResponse<>(SearchCacheConfig.DEFAULT, Optional.empty(), Optional.empty()));

        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    @ParameterizedTest(name = "Sorts of {0} is converted to an OrderBy of ''{1}''.")
    @MethodSource("convertsSortsToOrderByProperlyArguments")
    @DisplayName("Converts sorts to order by properly")
    void convertsSortsToOrderByProperly(List<Sort> sorts, String expectedOrderBy) {

        var searchParameters = SearchParameters
            .builder()
            .sorts(sorts)
            .merchandisingConfiguration(EMPTY_MERCHANDISING_CONFIGURATION)
            .searchMode(SearchMode.PRODUCT_SEARCH)
            .includeExpandedResults(true)
            .facetLimit(300)
            .build();

        googleSearchEngine.search(searchParameters);
        var captor = ArgumentCaptor.forClass(SearchRequest.class);
        verify(searchServiceClient).search(captor.capture());

        var searchRequest = captor.getValue();
        assertThat(searchRequest.getOrderBy()).isEqualTo(expectedOrderBy);
    }

    @Test
    @DisplayName("Throw SiteSearchRetailServerException with proper message")
    void throwSiteSearchRetailClientExceptionProperly() {
        var searchParameters = SearchParameters
            .builder()
            .merchandisingConfiguration(EMPTY_MERCHANDISING_CONFIGURATION)
            .searchMode(SearchMode.PRODUCT_SEARCH)
            .includeExpandedResults(true)
            .facetLimit(300)
            .build();
        when(searchServiceClient.search(any())).thenThrow(new RuntimeException("Some problems"));

        var actualMessage = assertThrows(SiteSearchRetailServerException.class, () ->
            googleSearchEngine.search(searchParameters)
        ).getMessage();

        assertTrue(actualMessage.contains("Google Cloud Retail. Can't get response. Error message: Some problems"));
    }

    @Test
    @DisplayName("Throw SiteSearchRetailClientException with proper message")
    void throwSiteSearchRetailServerExceptionProperly() {
        var searchParameters = SearchParameters
            .builder()
            .merchandisingConfiguration(EMPTY_MERCHANDISING_CONFIGURATION)
            .searchMode(SearchMode.PRODUCT_SEARCH)
            .includeExpandedResults(true)
            .facetLimit(300)
            .build();

        when(searchServiceClient.search(any())).thenThrow(
            new InvalidArgumentException(
                new RuntimeException("You are the problem"),
                GrpcStatusCode.of(Status.Code.INVALID_ARGUMENT),
                false
            )
        );

        var actualMessage = assertThrows(SiteSearchRetailClientException.class, () ->
            googleSearchEngine.search(searchParameters)
        ).getMessage();

        assertTrue(actualMessage.contains("Google Cloud Retail. Can't get response. Error message:"));
        assertTrue(actualMessage.contains("You are the problem"));
    }

    @Test
    @DisplayName("Final query is set on search results")
    void finalQueryIsSetOnSearchResults() {
        var searchParameters = SearchParameters
            .builder()
            .searchMode(SearchMode.PRODUCT_SEARCH)
            .merchandisingConfiguration(EMPTY_MERCHANDISING_CONFIGURATION)
            .query("shoes")
            .includeExpandedResults(true)
            .facetLimit(300)
            .build();

        var searchResults = googleSearchEngine.search(searchParameters);

        assertThat(searchResults.getQuery()).isEqualTo("shoes");
    }

    @Test
    @DisplayName("Adds retail time to metadata.")
    void addsRetailTime() {
        var searchParameters = SearchParameters
            .builder()
            .merchandisingConfiguration(EMPTY_MERCHANDISING_CONFIGURATION)
            .searchMode(SearchMode.PRODUCT_SEARCH)
            .includeExpandedResults(true)
            .facetLimit(300)
            .build();

        var results = googleSearchEngine.search(searchParameters);

        assertThat(results.getMetadata().getRetailTime()).isNotNull();
    }

    @SuppressWarnings("deprecation")
    @ParameterizedTest(name = "Facet value {0} converts to DynamicFacetSpec.Mode {1}")
    @MethodSource("dynamicFacetValues")
    @DisplayName("Query with dynamic facet")
    void queryWithDynamicFacet(Boolean dynamicFacet, SearchRequest.DynamicFacetSpec.Mode expected) {
        var searchParameters = SearchParameters
            .builder()
            .searchMode(SearchMode.PRODUCT_SEARCH)
            .merchandisingConfiguration(EMPTY_MERCHANDISING_CONFIGURATION)
            .dynamicFacet(dynamicFacet)
            .includeExpandedResults(true)
            .facetLimit(300)
            .build();
        googleSearchEngine.search(searchParameters);
        var captor = ArgumentCaptor.forClass(SearchRequest.class);
        verify(searchServiceClient).search(captor.capture());
        var searchRequest = captor.getValue();
        assertThat(searchRequest.getDynamicFacetSpec().getMode()).isEqualTo(expected);
    }

    @ParameterizedTest()
    @MethodSource("variantRollupKeys")
    @DisplayName("Query with variant rollup keys")
    void queryWithVariantRollupKeys(List<String> keys) {
        var searchParameters = SearchParameters
            .builder()
            .searchMode(SearchMode.PRODUCT_SEARCH)
            .merchandisingConfiguration(EMPTY_MERCHANDISING_CONFIGURATION)
            .variantRollupKeys(keys)
            .includeExpandedResults(true)
            .facetLimit(300)
            .build();
        googleSearchEngine.search(searchParameters);
        var captor = ArgumentCaptor.forClass(SearchRequest.class);
        verify(searchServiceClient).search(captor.capture());
        var searchRequest = captor.getValue();
        assertThat(searchRequest.getVariantRollupKeysList()).isEqualTo(keys);
    }

    @ParameterizedTest()
    @MethodSource("sortNavigationsResultSearchArguments")
    @DisplayName("Sorts navigations in result response")
    void sortNavigationsResultSearch(List<SearchResponse.Facet> facets, List<String> expectedSortedNavigations) {
        when(searchResponse.getFacetsList()).thenReturn(facets);

        Map<String, AttributeConfiguration> attributeConfigurationMap = new HashMap<>();
        var attributeConfiguration1 = mock(AttributeConfiguration.class);
        given(attributeConfiguration1.displayName()).willReturn("field");
        attributeConfigurationMap.put("field1", attributeConfiguration1);
        attributeConfigurationMap.put("field2", attributeConfiguration1);
        attributeConfigurationMap.put("field3", attributeConfiguration1);
        attributeConfigurationMap.put("dynamic_1", attributeConfiguration1);
        attributeConfigurationMap.put("dynamic_2", attributeConfiguration1);
        attributeConfigurationMap.put("dynamic_3", attributeConfiguration1);
        attributeConfigurationMap.put("dynamic_10", attributeConfiguration1);

        var searchParameters = SearchParameters
            .builder()
            .searchMode(SearchMode.PRODUCT_SEARCH)
            .merchandisingConfiguration(new MerchandisingConfiguration(
                AreaConfiguration.builder().build(),
                ImmutableSet.of(),
                ImmutableSet.of(
                    NavigationConfiguration.builder()
                        .id(2)
                        .name("name2")
                        .field("field2")
                        .areaId(1)
                        .priority(2)
                        .type(VALUE)
                        .build(),
                    NavigationConfiguration.builder()
                        .id(3)
                        .name("name3")
                        .field("field3")
                        .areaId(1)
                        .priority(3)
                        .type(VALUE)
                        .build(),
                    NavigationConfiguration.builder()
                        .id(1)
                        .name("name1")
                        .field("field1")
                        .areaId(1)
                        .priority(1)
                        .type(VALUE)
                        .build()),
                ImmutableSet.of(),
                ImmutableList.of(),
                attributeConfigurationMap,
                INSTANT_SUPPLIER,
                null,
                List.of(),
                new Features()
            ))
            .query("shoes")
            .includeExpandedResults(true)
            .facetLimit(300)
            .build();

        var searchResults = googleSearchEngine.search(searchParameters);

        assertThat(searchResults.getNavigations()

            .stream()
            .map(Navigation::getField)
        ).containsExactlyElementsOf(expectedSortedNavigations);
    }

    @Test
    @DisplayName("Labels records with ranking labels.")
    void labelsRecordWithRanking() {
        var pinnedId = "111";
        var boostedId = "222";
        var buriedId = "333";
        var regularId = "444";

        var searchParameters = SearchParameters
            .builder()
            .merchandisingConfiguration(EMPTY_MERCHANDISING_CONFIGURATION)
            .searchMode(SearchMode.PRODUCT_SEARCH)
            .includeExpandedResults(true)
            .pinnedProducts(List.of(new PinnedProduct(1, pinnedId, false)))
            .boostedProductBuckets(List.of(new ProductIdsBucket(List.of(boostedId))))
            .buriedProductBuckets(List.of(new ProductIdsBucket(List.of(buriedId))))
            .prefixTreeMap(PrefixTreeMap.fromList(List.of()))
            .labeledProductIds(Map.of("111", PINNED, "222", BOOSTED, "333", BURIED))
            .build();
        when(this.searchResponse.getResultsList()).thenReturn(List.of(
            SearchResponse.SearchResult.newBuilder()
                .setId(pinnedId)
                .setProduct(Product.newBuilder().setTitle(pinnedId))
                .build(),
            SearchResponse.SearchResult.newBuilder()
                .setId(boostedId)
                .setProduct(Product.newBuilder().setTitle(boostedId))
                .build(),
            SearchResponse.SearchResult.newBuilder()
                .setId(buriedId)
                .setProduct(Product.newBuilder().setTitle(buriedId))
                .build(),
            SearchResponse.SearchResult.newBuilder()
                .setId(regularId)
                .setProduct(Product.newBuilder().setTitle(regularId))
                .build()
        ));
        when(this.searchResponse.getResultsCount()).thenReturn(4);

        var results = googleSearchEngine.search(searchParameters);

        assertThat(results.getRecords()).hasSize(4);
        assertThat(results.getRecords().getFirst().getProductId()).isEqualTo(pinnedId);
        assertThat(results.getRecords().getFirst().getLabel()).isEqualTo(PINNED);
        assertThat(results.getRecords().get(1).getProductId()).isEqualTo(boostedId);
        assertThat(results.getRecords().get(1).getLabel()).isEqualTo(BOOSTED);
        assertThat(results.getRecords().get(2).getProductId()).isEqualTo(buriedId);
        assertThat(results.getRecords().get(2).getLabel()).isEqualTo(BURIED);
        assertThat(results.getRecords().get(3).getProductId()).isEqualTo(regularId);
        assertThat(results.getRecords().get(3).getLabel()).isNull();
    }

    @ParameterizedTest()
    @MethodSource("sortIncludedNavigationsResultSearchArguments")
    @DisplayName("Sorts navigations by included navigations in result response")
    void givenIncludedNavigationsExistWhenSearchVerifyNavigationsSortedInSearchResults(
        List<SearchResponse.Facet> facets,
        List<String> includedNavigations,
        List<String> expectedOrder
    ) {
        when(searchResponse.getFacetsList()).thenReturn(facets);

        Map<String, AttributeConfiguration> attributeConfigurationMap = new HashMap<>();
        var attributeConfiguration1 = mock(AttributeConfiguration.class);
        given(attributeConfiguration1.displayName()).willReturn("field");
        attributeConfigurationMap.put("field2", attributeConfiguration1);
        attributeConfigurationMap.put("field3", attributeConfiguration1);
        attributeConfigurationMap.put("dynamic_1", attributeConfiguration1);
        attributeConfigurationMap.put("field1", attributeConfiguration1);

        var searchParameters = SearchParameters
            .builder()
            .searchMode(SearchMode.PRODUCT_SEARCH)
            .includedNavigations(includedNavigations)
            .merchandisingConfiguration(new MerchandisingConfiguration(
                AreaConfiguration.builder().build(),
                ImmutableSet.of(),
                ImmutableSet.of(
                    NavigationConfiguration.builder()
                        .id(2)
                        .name("name2")
                        .field("field2")
                        .areaId(1)
                        .type(VALUE)
                        .priority(2)
                        .build(),
                    NavigationConfiguration.builder()
                        .id(3)
                        .name("name3")
                        .field("field3")
                        .areaId(1)
                        .type(VALUE)
                        .priority(3)
                        .build(),
                    NavigationConfiguration.builder()
                        .id(1)
                        .name("name1")
                        .field("field1")
                        .areaId(1)
                        .type(VALUE)
                        .priority(1)
                        .build()),
                ImmutableSet.of(),
                ImmutableList.of(),
                attributeConfigurationMap,
                INSTANT_SUPPLIER,
                null,
                List.of(),
                new Features()
            ))
            .query("shoes")
            .includeExpandedResults(true)
            .facetLimit(300)
            .build();

        var searchResults = googleSearchEngine.search(searchParameters);

        assertThat(searchResults.getNavigations()
            .stream()
            .map(Navigation::getField)
        ).containsExactlyElementsOf(expectedOrder);
    }

    private static SearchResponse.Facet buildValueFacet(String key) {
        return SearchResponse.Facet.newBuilder()
            .setKey(key)
            .addValues(SearchResponse.Facet.FacetValue.newBuilder()
                .setValue("Shoes > Running > Trail Running")
                .setCount(10))
            .build();
    }

    static Stream<Arguments> sortIncludedNavigationsResultSearchArguments() {
        return Stream.of(
            Arguments.of(
                ImmutableList.of(
                    buildValueFacet("dynamic_1"),
                    buildValueFacet("field3"),
                    buildValueFacet("field2"),
                    buildValueFacet("field1")),
                ImmutableList.of("field3", "field1", "field2"),
                ImmutableList.of(
                    "field3",
                    "field1",
                    "field2",
                    "dynamic_1"
                )
            ),
            Arguments.of(
                ImmutableList.of(
                    buildValueFacet("dynamic_1"),
                    buildValueFacet("field3"),
                    buildValueFacet("field2"),
                    buildValueFacet("field1")),
                ImmutableList.of("field3", "field2", "field1"),
                ImmutableList.of(
                    "field3",
                    "field2",
                    "field1",
                    "dynamic_1"
                )
            ),
            Arguments.of(
                ImmutableList.of(
                    buildValueFacet("dynamic_1"),
                    buildValueFacet("field3"),
                    buildValueFacet("field2"),
                    buildValueFacet("field1")),
                ImmutableList.of("field1", "field2", "field3"),
                ImmutableList.of(
                    "field1",
                    "field2",
                    "field3",
                    "dynamic_1"
                )
            ),
            //The order of navigations at the highest level:
            //Rule Pinned Nav ordering > Nav configuration prioritizing > Dynamic Nav ordering
            Arguments.of(
                ImmutableList.of(
                    buildValueFacet("dynamic_1"),
                    buildValueFacet("field2"),
                    buildValueFacet("field1"),
                    buildValueFacet("field3"),
                    buildValueFacet("dynamic_10")),
                ImmutableList.of("field4", "field3"),
                ImmutableList.of(
                    "field3",
                    "field1",
                    "field2",
                    "dynamic_1",
                    "dynamic_10"
                )
            )
        );
    }

    static Stream<Arguments> sortNavigationsResultSearchArguments() {
        return Stream.of(
            Arguments.of(
                ImmutableList.of(
                    buildValueFacet("dynamic_1"),
                    buildValueFacet("field3"),
                    buildValueFacet("dynamic_2"),
                    buildValueFacet("field2")),
                ImmutableList.of(
                    "field2",
                    "field3",
                    "dynamic_1",
                    "dynamic_2"
                )
            ),
            Arguments.of(
                ImmutableList.of(
                    buildValueFacet("field1"),
                    buildValueFacet("field3"),
                    buildValueFacet("field2")),
                ImmutableList.of(
                    "field1",
                    "field2",
                    "field3"
                )
            ),
            Arguments.of(
                ImmutableList.of(
                    buildValueFacet("dynamic_1"),
                    buildValueFacet("dynamic_2"),
                    buildValueFacet("dynamic_3"),
                    buildValueFacet("dynamic_10")),
                ImmutableList.of(
                    "dynamic_1",
                    "dynamic_2",
                    "dynamic_3",
                    "dynamic_10"
                )
            ));
    }

    static Stream<Arguments> convertsSortsToOrderByProperlyArguments() {
        return Stream.of(
            Arguments.of(
                List.of(
                    new Sort("rating", Order.DESCENDING),
                    new Sort("price", Order.ASCENDING)
                ),
                "rating desc, price"
            ),
            Arguments.of(
                List.of(),
                ""
            ),
            Arguments.of(
                List.of(
                    new Sort("rating", Order.DESCENDING)),
                "rating desc"
            )
        );
    }


    static Stream<Arguments> dynamicFacetValues() {
        return Stream.of(
            Arguments.of(
                true,
                SearchRequest.DynamicFacetSpec.Mode.ENABLED
            ),
            Arguments.of(
                false,
                SearchRequest.DynamicFacetSpec.Mode.DISABLED
            ),
            Arguments.of(
                null,
                SearchRequest.DynamicFacetSpec.Mode.MODE_UNSPECIFIED
            )
        );
    }

    private static Stream<Arguments> variantRollupKeys() {
        return Stream.of(Arguments.of(
            List.of(
                "price",
                "colorFamilies",
                "originalPrice",
                "discount",
                "attributes.sddEligibleFlag",
                "attributes.BAB_PREORDER_FLAG",
                "attributes.MAX_SHIPPING_DAYS",
                "attributes.storeOnly",
                "attributes.SHIPPING_CUTOFF_OFFSET",
                "attributes.MIN_SHIPPING_DAYS"
            ))
        );
    }

}
