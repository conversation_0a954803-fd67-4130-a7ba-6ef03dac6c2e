package com.groupbyinc.search.ssa.application.core.pintotop;

import com.groupbyinc.search.ssa.application.core.search.engine.DefaultSearchEngineSelector;
import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
import com.groupbyinc.search.ssa.core.Pagination;
import com.groupbyinc.search.ssa.core.ProductSearchResultWrapper;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.RecordLabel;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.request.RequestServed;
import com.groupbyinc.search.ssa.core.rule.PinnedProduct;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.metrics.opentelemetry.MeterRegistry;
import com.groupbyinc.search.ssa.productcatalog.ProductCatalogService;
import com.groupbyinc.search.ssa.retail.GoogleSearchEngine;
import com.groupbyinc.search.ssa.retail.filtering.RetailFilterService;
import com.groupbyinc.search.ssa.retail.filteringold.RetailFilterServiceOld;

import io.micronaut.core.propagation.PropagatedContext;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.application.core.pintotop.PinToTopService.AVAILABILITY;
import static com.groupbyinc.search.ssa.application.core.pintotop.PinToTopService.OUT_OF_STOCK;
import static com.groupbyinc.search.ssa.retail.filtering.RetailFilterService.PRODUCT_ID_FIELD;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.DEFAULT_CONTEXT;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;

import static java.lang.Math.max;
import static java.lang.Math.min;
import static java.util.stream.Collectors.toSet;
import static java.util.stream.IntStream.range;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Named.named;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

@MicronautTest
@DisplayName("PinToTopService Tests")
class PinToTopServiceTest {

    private static final List<String> MASK = List.of();

    private static final String PINNED_PRODUCT_POS_1 = "1p";
    private static final String PINNED_PRODUCT_POS_3 = "3p";
    private static final String PINNED_PRODUCT_POS_10 = "10p";
    private static final String PINNED_PRODUCT_POS_11 = "11p";
    private static final String PINNED_PRODUCT_POS_13 = "13p";
    private static final String PINNED_PRODUCT_POS_21 = "21p";
    private static final String PINNED_PRODUCT_POS_24 = "24p";
    private static final List<PinnedProduct> PINNED_PRODUCTS = List.of(
        new PinnedProduct(1, PINNED_PRODUCT_POS_1, false),
        new PinnedProduct(3, PINNED_PRODUCT_POS_3, false),
        new PinnedProduct(10, PINNED_PRODUCT_POS_10, false),
        new PinnedProduct(13, PINNED_PRODUCT_POS_13, false),
        new PinnedProduct(21, PINNED_PRODUCT_POS_21, false),
        new PinnedProduct(24, PINNED_PRODUCT_POS_24, false)
    );

    private static final String ANY_TITLE = "Title";
    private static final String IN_STOCK = "IN_STOCK";
    private static final int TOTAL_RECORDS = 35;
    private static final SearchResults SEARCH_RESULTS_NO_PAGINATION = getSearchResultsWithoutPagination();
    private static final ProductSearchResultWrapper PINNED_PRODUCTS_SEARCH_RESULT = ProductSearchResultWrapper
        .builder()
        .records(
            List.of(
                getRecordWithStock(PINNED_PRODUCT_POS_1, true),
                getRecordWithStock(PINNED_PRODUCT_POS_3, true),
                getRecordWithStock(PINNED_PRODUCT_POS_10, true),
                getRecordWithStock(PINNED_PRODUCT_POS_13, true),
                getRecordWithStock(PINNED_PRODUCT_POS_21, true),
                getRecordWithStock(PINNED_PRODUCT_POS_24, true)
            )
        )
        .servedFrom(RequestServed.FETCH)
        .build();
    private static final int NUM_TOTAL_RECORDS = PINNED_PRODUCTS_SEARCH_RESULT.records().size()
        + SEARCH_RESULTS_NO_PAGINATION.getRecords().size();

    @Inject MeterRegistry meterRegistry;
    @Inject
    RetailFilterServiceOld retailFilterServiceOld;
    @Inject
    RetailFilterService retailFilterService;

    private PinToTopService pinToTopService;
    private FeaturesManager featuresManager;
    private GoogleSearchEngine googleSearchEngine;
    private ProductCatalogService productCatalogService;

    private PropagatedContext.Scope scope;

    @BeforeEach
    void setUp() {
        featuresManager = mock(FeaturesManager.class);
        googleSearchEngine = mock(GoogleSearchEngine.class);
        var engineSelector = new DefaultSearchEngineSelector(
            featuresManager,
            null,
            googleSearchEngine,
            null
        );
        productCatalogService = mock(ProductCatalogService.class);

        pinToTopService = new PinToTopService(
            meterRegistry,
            featuresManager,
            retailFilterServiceOld,
            retailFilterService,
            productCatalogService,
            googleSearchEngine,
            engineSelector
        );

        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    @Test
    @DisplayName("Override pagination test")
    void overridePaginationTest() {
        var searchParameters = SearchParameters
            .builder()
            .pagination(new Pagination(10, 5L))
            .originalPagination(new Pagination(10, 5L))
            .build();

        var updated = pinToTopService.getSearchParametersForPinnedProducts(
            searchParameters,
            "",
            new Pagination(100, 0L)
        );

        assertThat(updated.getPagination().getSize()).isEqualTo(100);
        assertThat(updated.getPagination().getOffset()).isEqualTo(0);
    }

    @Test
    @DisplayName("Override SearchParameters for PinToTop test")
    void overrideSearchParametersByRuleForPinToTopTest() {
        var searchParameters = SearchParameters
            .builder()
            .pagination(new Pagination(10, 5L))
            .originalPagination(new Pagination(10, 5L))
            .preFilter("price: IN(100i, *)")
            .pinnedProducts(List.of(new PinnedProduct(1, "id", false)))
            .build();

        pinToTopService.overrideSearchParametersForPinToTop(
            searchParameters,
            List.of("id"),
            "price: IN(100i, *)"
        );

        assertThat(searchParameters.getPagination().getSize()).isEqualTo(11);
        assertThat(searchParameters.getPagination().getOffset()).isEqualTo(4);
        assertThat(searchParameters.getPreFilter()).isEqualTo("price: IN(100i, *)");
    }

    @DisplayName("Search with all pinned products found")
    @ParameterizedTest(name = "{index}: {0}")
    @MethodSource("provideSearchParametersAndExpectedResult")
    void testSearchWithPinnedProducts(SearchParameters searchParameters, SearchResults expected) {
        var searchResults = getRegularSearchResults(searchParameters, PINNED_PRODUCTS.size());

        given(googleSearchEngine.search(any(SearchParameters.class))).willReturn(searchResults);
        given(productCatalogService.fillProductsWithMetadata(any(), any())).willReturn(searchResults);

        given(
            googleSearchEngine.searchForProducts(any(SearchParameters.class))
        ).willReturn(PINNED_PRODUCTS_SEARCH_RESULT);

        var actualResults = pinToTopService.search(searchParameters);

        assertThat(actualResults)
            .usingRecursiveComparison()
            .isEqualTo(expected);

        verify(googleSearchEngine).search(any(SearchParameters.class));
        verify(googleSearchEngine).searchForProducts(any(SearchParameters.class));
    }

    @DisplayName("Search with all pinned products found using product catalog")
    @ParameterizedTest(name = "{index}: {0}")
    @MethodSource("provideSearchParametersAndExpectedResult")
    void testSearchWithPinnedProductsUsingProductCatalog(SearchParameters searchParameters, SearchResults expected) {
        var searchResults = getRegularSearchResults(searchParameters, PINNED_PRODUCTS.size());

        given(googleSearchEngine.search(any(SearchParameters.class))).willReturn(searchResults);
        given(productCatalogService.fillProductsWithMetadata(any(), any())).willReturn(searchResults);

        given(
            featuresManager.getBooleanFlagConfiguration(
                DEFAULT_CONTEXT.getLdContext(),
                FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_FETCH
            )
        ).willReturn(true);

        given(
            featuresManager.getBooleanFlagConfiguration(
                DEFAULT_CONTEXT.getLdContext(),
                FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_PIN_TO_TOP
            )
        ).willReturn(true);

        given(
            featuresManager.getBooleanFlagConfiguration(
                DEFAULT_CONTEXT.getLdContext(),
                FeaturesManager.FeatureFlagNames.ENABLE_PINNED_PRODUCTS_FILTERING
            )
        ).willReturn(false);

        given(
            productCatalogService.getPinnedProducts(
                any(SearchParameters.class),
                eq(searchParameters.getPinnedProducts().stream().map(PinnedProduct::productId).collect(toSet())),
                eq(false)
            )
        ).willReturn(PINNED_PRODUCTS_SEARCH_RESULT);

        var actualResults = pinToTopService.search(searchParameters);

        assertThat(actualResults)
            .usingRecursiveComparison()
            .isEqualTo(expected);

        verify(googleSearchEngine, never()).searchForProducts(any());
        verify(googleSearchEngine).search(any(SearchParameters.class));
    }

    @DisplayName("Search with all pinned products found using product catalog fallbacks to regular search")
    @ParameterizedTest(name = "{index}: {0}")
    @MethodSource("provideSearchParametersAndExpectedResult")
    void testSearchWithPinnedProducts_whenProductCatalogFallbacksToRegularSearch(SearchParameters searchParameters,
                                                                                 SearchResults expectedResults) {
        var searchResults = getRegularSearchResults(searchParameters, PINNED_PRODUCTS.size());
        given(googleSearchEngine.search(any(SearchParameters.class))).willReturn(searchResults);
        given(productCatalogService.fillProductsWithMetadata(any(), any())).willReturn(searchResults);

        given(
            featuresManager.getBooleanFlagConfiguration(
                DEFAULT_CONTEXT.getLdContext(),
                FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_FETCH
            )
        ).willReturn(true);

        given(
            featuresManager.getBooleanFlagConfiguration(
                DEFAULT_CONTEXT.getLdContext(),
                FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_PIN_TO_TOP
            )
        ).willReturn(true);

        given(
            featuresManager.getBooleanFlagConfiguration(
                DEFAULT_CONTEXT.getLdContext(),
                FeaturesManager.FeatureFlagNames.ENABLE_PINNED_PRODUCTS_FILTERING
            )
        ).willReturn(false);

        given(
            productCatalogService.getPinnedProducts(
                any(SearchParameters.class),
                eq(PINNED_PRODUCTS.stream().map(PinnedProduct::productId).collect(toSet())),
                eq(false)
            )
        ).willThrow(new RuntimeException("whoops"));

        given(
            googleSearchEngine.searchForProducts(any(SearchParameters.class))
        ).willReturn(PINNED_PRODUCTS_SEARCH_RESULT);

        var expectedResultsAdjusted = expectedResults
            .toBuilder()
            .build();

        var searchParametersAdjusted = searchParameters
            .toBuilder()
            .build();

        var actualResults = pinToTopService.search(searchParametersAdjusted);

        assertThat(actualResults)
            .usingRecursiveComparison()
            .isEqualTo(expectedResultsAdjusted);

        verify(googleSearchEngine).search(any(SearchParameters.class));
        verify(googleSearchEngine).searchForProducts(any(SearchParameters.class));
        verify(productCatalogService).getPinnedProducts(
            any(SearchParameters.class),
            eq(PINNED_PRODUCTS.stream().map(PinnedProduct::productId).collect(toSet())),
            eq(false)
        );

    }

    @Test
    @DisplayName("Some of pinned products are not found")
    void testSearchWithPinnedProducts_whenSomePinnedProductsNotFound() {
        // given
        var searchParameters = SearchParameters
            .builder()
            .originalPagination(new Pagination(12, 12L))
            .pagination(new Pagination(12, 12L))
            .pinnedProducts(PINNED_PRODUCTS)
            .build();

        var pinnedProductsSearchResults = List.of(
            getRecordWithStock(PINNED_PRODUCT_POS_1, true),
            getRecordWithStock(PINNED_PRODUCT_POS_10, true),
            getRecordWithStock(PINNED_PRODUCT_POS_13, true),
            getRecordWithStock(PINNED_PRODUCT_POS_24, true)
        );

        var productSearchResult = ProductSearchResultWrapper
            .builder()
            .records(pinnedProductsSearchResults)
            .servedFrom(RequestServed.GOOGLE)
            .build();

        var expectedResults = SearchResults
            .builder()
            .numTotalRecords(pinnedProductsSearchResults.size() + SEARCH_RESULTS_NO_PAGINATION.getRecords().size())
            .records(List.of(
                getRecordWithStock(PINNED_PRODUCT_POS_13, true),
                getRecord("11"),
                getRecord("12"),
                getRecord("13"),
                getRecord("14"),
                getRecord("15"),
                getRecord("16"),
                getRecord("17"),
                getRecord("18"),
                getRecord("19"),
                getRecord("20"),
                getRecordWithStock(PINNED_PRODUCT_POS_24, true)))
            .build();
        var searchResults = getRegularSearchResults(searchParameters, PINNED_PRODUCTS.size());

        given(googleSearchEngine.search(any(SearchParameters.class))).willReturn(searchResults);
        given(productCatalogService.fillProductsWithMetadata(any(), any())).willReturn(searchResults);

        given(googleSearchEngine.searchForProducts(any(SearchParameters.class))).willReturn(productSearchResult);

        // when
        var actualResults = pinToTopService.search(searchParameters);

        // then
        assertThat(actualResults)
            .usingRecursiveComparison()
            .isEqualTo(expectedResults);

        verify(googleSearchEngine).search(any(SearchParameters.class));
        verify(googleSearchEngine).searchForProducts(any(SearchParameters.class));
    }

    @Test
    @DisplayName("Some of pinned products are not found using product catalog")
    void testSearchWithPinnedProducts_whenSomePinnedProductsNotFoundUsingProductCatalog() {
        // given
        var searchParameters = SearchParameters
            .builder()
            .originalPagination(new Pagination(12, 12L))
            .pagination(new Pagination(12, 12L))
            .pinnedProducts(PINNED_PRODUCTS)
            .responseMask(MASK)
            .build();

        var pinnedProductsSearchResults = List.of(
            getRecordWithStock(PINNED_PRODUCT_POS_1, true),
            getRecordWithStock(PINNED_PRODUCT_POS_10, true),
            getRecordWithStock(PINNED_PRODUCT_POS_13, true),
            getRecordWithStock(PINNED_PRODUCT_POS_24, true)
        );
        var records = new ProductSearchResultWrapper(pinnedProductsSearchResults, RequestServed.FETCH);

        var expectedResults = SearchResults
            .builder()
            .numTotalRecords(pinnedProductsSearchResults.size() + SEARCH_RESULTS_NO_PAGINATION.getRecords().size())
            .records(List.of(
                getRecordWithStock(PINNED_PRODUCT_POS_13, true),
                getRecord("11"),
                getRecord("12"),
                getRecord("13"),
                getRecord("14"),
                getRecord("15"),
                getRecord("16"),
                getRecord("17"),
                getRecord("18"),
                getRecord("19"),
                getRecord("20"),
                getRecordWithStock(PINNED_PRODUCT_POS_24, true)
            ))
            .build();
        var searchResults = getRegularSearchResults(searchParameters, PINNED_PRODUCTS.size());

        given(googleSearchEngine.search(any(SearchParameters.class))).willReturn(searchResults);
        given(productCatalogService.fillProductsWithMetadata(any(), any())).willReturn(searchResults);

        given(
            featuresManager.getBooleanFlagConfiguration(
                DEFAULT_CONTEXT.getLdContext(),
                FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_FETCH
            )
        ).willReturn(true);

        given(
            featuresManager.getBooleanFlagConfiguration(
                DEFAULT_CONTEXT.getLdContext(),
                FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_PIN_TO_TOP
            )
        ).willReturn(true);

        given(
            featuresManager.getBooleanFlagConfiguration(
                DEFAULT_CONTEXT.getLdContext(),
                FeaturesManager.FeatureFlagNames.ENABLE_PINNED_PRODUCTS_FILTERING
            )
        ).willReturn(false);

        given(
            productCatalogService.getPinnedProducts(
                any(SearchParameters.class),
                eq(PINNED_PRODUCTS.stream().map(PinnedProduct::productId).collect(toSet())),
                eq(false)
            )
        ).willReturn(records);

        // when
        var actualResults = pinToTopService.search(searchParameters);

        // then
        assertThat(actualResults)
            .usingRecursiveComparison()
            .isEqualTo(expectedResults);

        verify(googleSearchEngine, never()).searchForProducts(any());
        verify(googleSearchEngine).search(any(SearchParameters.class));
    }

    @Test
    @DisplayName("Some of pinned products are not in stock")
    void testSearchWithPinnedProducts_whenSomePinnedProductsNotInStock() {
        // given
        var searchParameters = SearchParameters
            .builder()
            .originalPagination(new Pagination(12, 9L))
            .pagination(new Pagination(12, 9L))
            .pinnedProducts(PINNED_PRODUCTS)
            .build();

        var pinnedProductsSearchResults = List.of(
            getRecordWithStock(PINNED_PRODUCT_POS_1, false),
            getRecordWithStock(PINNED_PRODUCT_POS_3, true),
            getRecordWithStock(PINNED_PRODUCT_POS_10, false),
            getRecordWithStock(PINNED_PRODUCT_POS_13, true),
            getRecordWithStock(PINNED_PRODUCT_POS_21, true),
            getRecordWithStock(PINNED_PRODUCT_POS_24, false)
        );

        var productSearchResultWrapper = ProductSearchResultWrapper
            .builder()
            .records(pinnedProductsSearchResults)
            .servedFrom(RequestServed.GOOGLE)
            .build();

        var expectedResults = SearchResults
            .builder()
            // 3 pinned products are out of stock, so we expect 3 fewer records in the result
            .numTotalRecords(pinnedProductsSearchResults.size() - 3 + SEARCH_RESULTS_NO_PAGINATION.getRecords().size())
            .records(List.of(
                getRecord("9"),
                getRecord("10"),
                getRecord("11"),
                getRecordWithStock(PINNED_PRODUCT_POS_13, true),
                getRecord("12"),
                getRecord("13"),
                getRecord("14"),
                getRecord("15"),
                getRecord("16"),
                getRecord("17"),
                getRecord("18"),
                getRecordWithStock(PINNED_PRODUCT_POS_21, true)
            ))
            .build();

        var regularSearchParameters = getRegularSearchResults(searchParameters, PINNED_PRODUCTS.size());

        given(googleSearchEngine.search(any(SearchParameters.class))).willReturn(regularSearchParameters);
        given(productCatalogService.fillProductsWithMetadata(any(), any())).willReturn(regularSearchParameters);

        given(
            googleSearchEngine.searchForProducts(any(SearchParameters.class))
        ).willReturn(productSearchResultWrapper);

        // when
        var actualResults = pinToTopService.search(searchParameters);

        // then
        assertThat(actualResults)
            .usingRecursiveComparison()
            .isEqualTo(expectedResults);

        verify(googleSearchEngine).search(any(SearchParameters.class));
        verify(googleSearchEngine).searchForProducts(any(SearchParameters.class));
    }

    @Test
    @DisplayName("Error during pinned products search")
    void testSearchWithPinnedProducts_whenPinnedProductsSearchThrowsException() {
        // given
        var searchParameters = SearchParameters
            .builder()
            .originalPagination(new Pagination(10, 20L))
            .pagination(new Pagination(10, 20L))
            .pinnedProducts(PINNED_PRODUCTS)
            .build();

        var expectedResults = SearchResults
            .builder()
            .numTotalRecords(SEARCH_RESULTS_NO_PAGINATION.getRecords().size())
            .records(List.of(
                getRecord("21"),
                getRecord("22"),
                getRecord("23"),
                getRecord("24"),
                getRecord("25"),
                getRecord("26"),
                getRecord("27"),
                getRecord("28"),
                getRecord("29"),
                getRecord("30")
            ))
            .build();

        var regularSearchParameters = getRegularSearchResults(searchParameters, PINNED_PRODUCTS.size());

        given(googleSearchEngine.search(any(SearchParameters.class))).willReturn(regularSearchParameters);
        given(productCatalogService.fillProductsWithMetadata(any(), any())).willReturn(regularSearchParameters);

        given(
            googleSearchEngine.searchForProducts(any(SearchParameters.class))
        ).willThrow(new RuntimeException("Error"));

        // when
        var actualResults = pinToTopService.search(searchParameters);

        // then
        assertThat(actualResults)
            .usingRecursiveComparison()
            .isEqualTo(expectedResults);

        verify(googleSearchEngine).search(any(SearchParameters.class));
        verify(googleSearchEngine).searchForProducts(any(SearchParameters.class));
    }

    @Test
    @DisplayName("Search recall size = 3, less than the pinned products positions. Offset = 0")
    void testSearchWithPinnedProducts_whenSearchRecallSizeIsSmall() {
        // given
        var searchParameters = SearchParameters
            .builder()
            .originalPagination(new Pagination(10, 0L))
            .pagination(new Pagination(10, 0L))
            .pinnedProducts(PINNED_PRODUCTS)
            .build();

        // Search recall size is less than the following pinned products positions
        var searchRecords = SEARCH_RESULTS_NO_PAGINATION.getRecords().subList(0, 3);
        var searchResults = SearchResults
            .builder()
            .numTotalRecords(searchRecords.size())
            .records(searchRecords)
            .build();

        // We expect pinned products with the following positions to be appended to the end of the result
        var expectedResults = SearchResults
            .builder()
            .numTotalRecords(PINNED_PRODUCTS_SEARCH_RESULT.records().size() + searchRecords.size())
            .records(List.of(
                getRecordWithStock(PINNED_PRODUCT_POS_1, true),
                getRecord("1"),
                getRecordWithStock(PINNED_PRODUCT_POS_3, true),
                getRecord("2"),
                getRecord("3"),
                getRecordWithStock(PINNED_PRODUCT_POS_10, true),
                getRecordWithStock(PINNED_PRODUCT_POS_13, true),
                getRecordWithStock(PINNED_PRODUCT_POS_21, true),
                getRecordWithStock(PINNED_PRODUCT_POS_24, true)
            ))
            .build();

        given(googleSearchEngine.search(any(SearchParameters.class))).willReturn(searchResults);
        given(productCatalogService.fillProductsWithMetadata(any(), any())).willReturn(searchResults);

        given(
            googleSearchEngine.searchForProducts(any(SearchParameters.class))
        ).willReturn(PINNED_PRODUCTS_SEARCH_RESULT);

        // when
        var actualResults = pinToTopService.search(searchParameters);

        // then
        assertThat(actualResults)
            .usingRecursiveComparison()
            .isEqualTo(expectedResults);

        verify(googleSearchEngine).search(any(SearchParameters.class));
        verify(googleSearchEngine).searchForProducts(any(SearchParameters.class));
    }

    @Test
    @DisplayName("Search recall size = 14, less than the pinned products positions. Offset = 10")
    void testSearchWithPinnedProducts_whenSearchRecallSizeIsSmall_size14_offset10() {
        // given
        var pageSize = 10;
        var searchParameters = SearchParameters
            .builder()
            .originalPagination(new Pagination(pageSize, 10L))
            .pagination(new Pagination(pageSize, 10L))
            .pinnedProducts(PINNED_PRODUCTS)
            .build();

        var regularSearchResults = getRegularSearchResults(searchParameters, PINNED_PRODUCTS.size());

        // Search recall size is less than the following pinned products positions
        var numberOfRegularSearchRecordsAfterOffset = 4;
        var expectedRegularSearchNumTotalRecords = pageSize + numberOfRegularSearchRecordsAfterOffset;

        var expectedRegularSearchResults = regularSearchResults.toBuilder()
            .numTotalRecords(expectedRegularSearchNumTotalRecords)
            .records(
                regularSearchResults
                    .getRecords()
                    .subList(0, PINNED_PRODUCTS.size() + numberOfRegularSearchRecordsAfterOffset)
            )
            .build();

        // We expect pinned products with the following positions to be appended to the end of the result
        var expectedResults = SearchResults
            .builder()
            .numTotalRecords(PINNED_PRODUCTS_SEARCH_RESULT.records().size() + expectedRegularSearchNumTotalRecords)
            .records(List.of(
                getRecord("8"),
                getRecord("9"),
                getRecordWithStock(PINNED_PRODUCT_POS_13, true),
                getRecord("10"),
                getRecord("11"),
                getRecord("12"),
                getRecord("13"),
                getRecord("14"),
                getRecordWithStock(PINNED_PRODUCT_POS_21, true),
                getRecordWithStock(PINNED_PRODUCT_POS_24, true)
            ))
            .build();

        given(googleSearchEngine.search(any(SearchParameters.class))).willReturn(expectedRegularSearchResults);
        given(productCatalogService.fillProductsWithMetadata(any(), any())).willReturn(expectedRegularSearchResults);

        given(
            googleSearchEngine.searchForProducts(any(SearchParameters.class))
        ).willReturn(PINNED_PRODUCTS_SEARCH_RESULT);

        // when
        var actualResults = pinToTopService.search(searchParameters);

        // then
        assertThat(actualResults)
            .usingRecursiveComparison()
            .isEqualTo(expectedResults);

        verify(googleSearchEngine).search(any(SearchParameters.class));
        verify(googleSearchEngine).searchForProducts(any(SearchParameters.class));
    }

    @Test
    @DisplayName("Search recall size = 9, less than the pinned products positions. Offset = 0")
    void testSearchWithPinnedProducts_whenSearchRecallSizeIsSmall_size9_offset0() {
        // given
        var searchParameters = SearchParameters
            .builder()
            .originalPagination(new Pagination(10, 0L))
            .pagination(new Pagination(10, 0L))
            .pinnedProducts(
                List.of(
                    new PinnedProduct(10, PINNED_PRODUCT_POS_10, false),
                    new PinnedProduct(11, PINNED_PRODUCT_POS_11, false)
                )
            )
            .build();

        // Search recall size is less than the following pinned products positions
        var searchRecords = SEARCH_RESULTS_NO_PAGINATION.getRecords().subList(0, 9);
        var regularSearchResults = SearchResults
            .builder()
            .numTotalRecords(searchRecords.size())
            .records(searchRecords)
            .build();

        var pinnedProductsSearchResults = List.of(
            getRecordWithStock(PINNED_PRODUCT_POS_10, true),
            getRecordWithStock(PINNED_PRODUCT_POS_11, true)
        );

        var productSearchResultWrapper = ProductSearchResultWrapper
            .builder()
            .records(pinnedProductsSearchResults)
            .servedFrom(RequestServed.GOOGLE)
            .build();

        // We expect pinned products with the following positions to be appended to the end of the result
        var expectedResults = SearchResults.builder()
            .numTotalRecords(pinnedProductsSearchResults.size() + searchRecords.size())
            .records(List.of(
                getRecord("1"),
                getRecord("2"),
                getRecord("3"),
                getRecord("4"),
                getRecord("5"),
                getRecord("6"),
                getRecord("7"),
                getRecord("8"),
                getRecord("9"),
                getRecordWithStock(PINNED_PRODUCT_POS_10, true)
            ))
            .build();

        given(googleSearchEngine.search(any(SearchParameters.class))).willReturn(regularSearchResults);
        given(productCatalogService.fillProductsWithMetadata(any(), any())).willReturn(regularSearchResults);

        given(
            googleSearchEngine.searchForProducts(any(SearchParameters.class))
        ).willReturn(productSearchResultWrapper);

        // when
        var actualResults = pinToTopService.search(searchParameters);

        // then
        assertThat(actualResults)
            .usingRecursiveComparison()
            .isEqualTo(expectedResults);

        verify(googleSearchEngine).search(any(SearchParameters.class));
        verify(googleSearchEngine).searchForProducts(any(SearchParameters.class));
    }

    @Test
    @DisplayName("Search recall size = 9, less than the pinned products positions. Offset = 10")
    void testSearchWithPinnedProducts_whenSearchRecallSizeIsSmall_size9_offset10() {
        // given
        var searchParameters = SearchParameters
            .builder()
            .originalPagination(new Pagination(10, 10L))
            .pagination(new Pagination(10, 10L))
            .pinnedProducts(
                List.of(
                    new PinnedProduct(10, PINNED_PRODUCT_POS_10, false),
                    new PinnedProduct(11, PINNED_PRODUCT_POS_11, false)
                )
            )
            .build();

        var pinnedProductsSearchResults = List.of(
            getRecordWithStock(PINNED_PRODUCT_POS_10, true),
            getRecordWithStock(PINNED_PRODUCT_POS_11, true)
        );

        var productSearchResultWrapper = ProductSearchResultWrapper
            .builder()
            .records(pinnedProductsSearchResults)
            .servedFrom(RequestServed.GOOGLE)
            .build();

        var regularSearchResults = getRegularSearchResults(searchParameters, pinnedProductsSearchResults.size());

        // Search recall size is less than the following pinned products positions
        var numberOfRegularSearchRecordsAfterOffset = 0;
        var numberOfPinnedProductsAfterOffset = 1;
        var expectedRegularSearchNumTotalRecords = 9;

        var expectedRegularSearchResults = regularSearchResults
            .toBuilder()
            .numTotalRecords(expectedRegularSearchNumTotalRecords)
            .records(
                regularSearchResults
                    .getRecords()
                    .subList(0, numberOfPinnedProductsAfterOffset + numberOfRegularSearchRecordsAfterOffset)
            )
            .build();

        // We expect pinned products with the following positions to be appended to the end of the result
        var expectedResults = SearchResults
            .builder()
            .numTotalRecords(pinnedProductsSearchResults.size() + expectedRegularSearchNumTotalRecords)
            .records(List.of(getRecordWithStock(PINNED_PRODUCT_POS_11, true)))
            .build();

        given(googleSearchEngine.search(any(SearchParameters.class))).willReturn(expectedRegularSearchResults);
        given(productCatalogService.fillProductsWithMetadata(any(), any())).willReturn(expectedRegularSearchResults);

        given(
            googleSearchEngine.searchForProducts(any(SearchParameters.class))
        ).willReturn(productSearchResultWrapper);

        // when
        var actualResults = pinToTopService.search(searchParameters);

        // then
        assertThat(actualResults)
            .usingRecursiveComparison()
            .isEqualTo(expectedResults);

        verify(googleSearchEngine).search(any(SearchParameters.class));
        verify(googleSearchEngine).searchForProducts(any(SearchParameters.class));
    }

    @Test
    @DisplayName("Search recall size = 0, less than the pinned products positions. Offset = 0")
    void testSearchWithPinnedProducts_whenSearchRecallSizeIsSmall_size0_offset0() {
        // given
        var searchParameters = SearchParameters
            .builder()
            .originalPagination(new Pagination(10, 0L))
            .pagination(new Pagination(10, 0L))
            .pinnedProducts(PINNED_PRODUCTS)
            .build();

        // Search recall size is less than the following pinned products positions
        var searchResults = SearchResults
            .builder()
            .numTotalRecords(0)
            .records(List.of())
            .build();

        // We expect pinned products with the following positions to be appended to the end of the result
        var expectedResults = SearchResults
            .builder()
            .numTotalRecords(PINNED_PRODUCTS_SEARCH_RESULT.records().size())
            .records(List.of(
                getRecordWithStock(PINNED_PRODUCT_POS_1, true),
                getRecordWithStock(PINNED_PRODUCT_POS_3, true),
                getRecordWithStock(PINNED_PRODUCT_POS_10, true),
                getRecordWithStock(PINNED_PRODUCT_POS_13, true),
                getRecordWithStock(PINNED_PRODUCT_POS_21, true),
                getRecordWithStock(PINNED_PRODUCT_POS_24, true)
            ))
            .build();

        given(googleSearchEngine.search(any(SearchParameters.class))).willReturn(searchResults);
        given(productCatalogService.fillProductsWithMetadata(any(), any())).willReturn(searchResults);

        given(
            googleSearchEngine.searchForProducts(any(SearchParameters.class))
        ).willReturn(PINNED_PRODUCTS_SEARCH_RESULT);

        // when
        var actualResults = pinToTopService.search(searchParameters);

        // then
        assertThat(actualResults)
            .usingRecursiveComparison()
            .isEqualTo(expectedResults);

        verify(googleSearchEngine).search(any(SearchParameters.class));
        verify(googleSearchEngine).searchForProducts(any(SearchParameters.class));
    }

    @Test
    @DisplayName("Search recall size = 22, less than the pinned products positions. Offset = 23")
    void testSearchWithPinnedProducts_whenSearchRecallSizeIsSmall_size22_offset23() {
        // given
        var searchParameters = SearchParameters
            .builder()
            .originalPagination(new Pagination(10, 23L))
            .pagination(new Pagination(10, 23L))
            .pinnedProducts(List.of(new PinnedProduct(24, PINNED_PRODUCT_POS_24, false)))
            .build();

        var pinnedProductsSearchResults = List.of(
            getRecordWithStock(PINNED_PRODUCT_POS_24, true)
        );

        var productSearchResultWrapper = ProductSearchResultWrapper
            .builder()
            .records(pinnedProductsSearchResults)
            .servedFrom(RequestServed.GOOGLE)
            .build();

        // Search recall size is less than the following pinned products positions
        var expectedRegularSearchNumTotalRecords = 22;

        var expectedRegularSearchResults = SearchResults
            .builder()
            .numTotalRecords(expectedRegularSearchNumTotalRecords)
            .records(List.of()) // empty because the adjusted offset (22) is equal to the recall size
            .build();

        // We expect pinned products with the following positions to be appended to the end of the result
        var expectedResults = SearchResults
            .builder()
            .numTotalRecords(pinnedProductsSearchResults.size() + expectedRegularSearchNumTotalRecords)
            .records(List.of(getRecordWithStock(PINNED_PRODUCT_POS_24, true)))
            .build();

        given(googleSearchEngine.search(any(SearchParameters.class))).willReturn(expectedRegularSearchResults);
        given(productCatalogService.fillProductsWithMetadata(any(), any())).willReturn(expectedRegularSearchResults);

        given(
            googleSearchEngine.searchForProducts(any(SearchParameters.class))
        ).willReturn(productSearchResultWrapper);

        // when
        var actualResults = pinToTopService.search(searchParameters);

        // then
        assertThat(actualResults)
            .usingRecursiveComparison()
            .isEqualTo(expectedResults);

        verify(googleSearchEngine).search(any(SearchParameters.class));
        verify(googleSearchEngine).searchForProducts(any(SearchParameters.class));
    }

    private static Stream<Arguments> provideSearchParametersAndExpectedResult() {
        return Stream.of(
            arguments(
                named(
                    "Offset=1, PageSize=3",
                    SearchParameters
                        .builder()
                        .merchandisingConfiguration(MerchandisingConfiguration.EMPTY)
                        .pagination(new Pagination(3, 1L))
                        .originalPagination(new Pagination(3, 1L))
                        .pinnedProducts(PINNED_PRODUCTS)
                        .build()
                ),
                SearchResults.builder()
                    .numTotalRecords(NUM_TOTAL_RECORDS)
                    .records(
                        List.of(
                            getRecord("1"),
                            getRecordWithStock(PINNED_PRODUCT_POS_3, true),
                            getRecord("2")
                        )
                    )
                    .build()
            ),
            arguments(
                named(
                    "Offset=1, PageSize=3, with variant rollup keys",
                    SearchParameters
                        .builder()
                        .merchandisingConfiguration(MerchandisingConfiguration.EMPTY)
                        .originalPagination(new Pagination(3, 1L))
                        .pagination(new Pagination(3, 1L))
                        .pinnedProducts(PINNED_PRODUCTS)
                        .responseMask(MASK)
                        .build()
                ),
                SearchResults.builder()
                    .numTotalRecords(NUM_TOTAL_RECORDS)
                    .records(List.of(
                        getRecord("1"),
                        getRecordWithStock(PINNED_PRODUCT_POS_3, true),
                        getRecord("2"))
                    )
                    .build()
            ),
            arguments(
                named(
                    "Offset=0, PageSize=10",
                    SearchParameters
                        .builder()
                        .merchandisingConfiguration(MerchandisingConfiguration.EMPTY)
                        .originalPagination(new Pagination(10, 0L))
                        .pagination(new Pagination(10, 0L))
                        .pinnedProducts(PINNED_PRODUCTS)
                        .responseMask(MASK)
                        .build()
                ),
                SearchResults.builder()
                    .numTotalRecords(NUM_TOTAL_RECORDS)
                    .records(List.of(
                        getRecordWithStock(PINNED_PRODUCT_POS_1, true),
                        getRecord("1"),
                        getRecordWithStock(PINNED_PRODUCT_POS_3, true),
                        getRecord("2"),
                        getRecord("3"),
                        getRecord("4"),
                        getRecord("5"),
                        getRecord("6"),
                        getRecord("7"),
                        getRecordWithStock(PINNED_PRODUCT_POS_10, true)
                    ))
                    .build()
            ),
            arguments(
                named(
                    "Offset=0, PageSize=10, OriginalPageSize=5",
                    SearchParameters
                        .builder()
                        .merchandisingConfiguration(MerchandisingConfiguration.EMPTY)
                        .originalPagination(new Pagination(5, 0L))
                        .pagination(new Pagination(10, 0L))
                        .pinnedProducts(PINNED_PRODUCTS)
                        .responseMask(MASK)
                        .build()
                ),
                SearchResults.builder()
                    .numTotalRecords(NUM_TOTAL_RECORDS)
                    .records(List.of(
                        getRecordWithStock(PINNED_PRODUCT_POS_1, true),
                        getRecord("1"),
                        getRecordWithStock(PINNED_PRODUCT_POS_3, true),
                        getRecord("2"),
                        getRecord("3"),
                        getRecord("4"),
                        getRecord("5"),
                        getRecord("6"),
                        getRecord("7"),
                        getRecordWithStock(PINNED_PRODUCT_POS_10, true)
                    ))
                    .build()
            ),
            arguments(
                named(
                    "Offset=2, PageSize=12",
                    SearchParameters
                        .builder()
                        .merchandisingConfiguration(MerchandisingConfiguration.EMPTY)
                        .originalPagination(new Pagination(12, 2L))
                        .pagination(new Pagination(12, 2L))
                        .pinnedProducts(PINNED_PRODUCTS)
                        .responseMask(MASK)
                        .build()
                ),
                SearchResults.builder()
                    .numTotalRecords(NUM_TOTAL_RECORDS)
                    .records(List.of(
                        getRecordWithStock(PINNED_PRODUCT_POS_3, true),
                        getRecord("2"),
                        getRecord("3"),
                        getRecord("4"),
                        getRecord("5"),
                        getRecord("6"),
                        getRecord("7"),
                        getRecordWithStock(PINNED_PRODUCT_POS_10, true),
                        getRecord("8"),
                        getRecord("9"),
                        getRecordWithStock(PINNED_PRODUCT_POS_13, true),
                        getRecord("10")
                    ))
                    .build()
            ),
            arguments(
                named(
                    "Offset=2, PageSize=12, OriginalPageSize=6",
                    SearchParameters
                        .builder()
                        .merchandisingConfiguration(MerchandisingConfiguration.EMPTY)
                        .originalPagination(new Pagination(6, 2L))
                        .pagination(new Pagination(12, 2L))
                        .pinnedProducts(PINNED_PRODUCTS)
                        .responseMask(MASK)
                        .build()
                ),
                SearchResults.builder()
                    .numTotalRecords(NUM_TOTAL_RECORDS)
                    .records(List.of(
                        getRecordWithStock(PINNED_PRODUCT_POS_3, true),
                        getRecord("2"),
                        getRecord("3"),
                        getRecord("4"),
                        getRecord("5"),
                        getRecord("6"),
                        getRecord("7"),
                        getRecordWithStock(PINNED_PRODUCT_POS_10, true),
                        getRecord("8"),
                        getRecord("9"),
                        getRecordWithStock(PINNED_PRODUCT_POS_13, true),
                        getRecord("10")
                    ))
                    .build()
            ),
            arguments(
                named(
                    "Offset=12, PageSize=12",
                    SearchParameters
                        .builder()
                        .merchandisingConfiguration(MerchandisingConfiguration.EMPTY)
                        .originalPagination(new Pagination(12, 12L))
                        .pagination(new Pagination(12, 12L))
                        .pinnedProducts(PINNED_PRODUCTS)
                        .responseMask(MASK)
                        .build()
                ),
                SearchResults.builder()
                    .numTotalRecords(NUM_TOTAL_RECORDS)
                    .records(List.of(
                        getRecordWithStock(PINNED_PRODUCT_POS_13, true),
                        getRecord("10"),
                        getRecord("11"),
                        getRecord("12"),
                        getRecord("13"),
                        getRecord("14"),
                        getRecord("15"),
                        getRecord("16"),
                        getRecordWithStock(PINNED_PRODUCT_POS_21, true),
                        getRecord("17"),
                        getRecord("18"),
                        getRecordWithStock(PINNED_PRODUCT_POS_24, true)
                    ))
                    .build()
            ),
            arguments(
                named(
                    "Offset=24, PageSize=10",
                    SearchParameters
                        .builder()
                        .merchandisingConfiguration(MerchandisingConfiguration.EMPTY)
                        .originalPagination(new Pagination(10, 24L))
                        .pagination(new Pagination(10, 24L))
                        .pinnedProducts(PINNED_PRODUCTS)
                        .responseMask(MASK)
                        .build()
                ),
                SearchResults.builder()
                    .numTotalRecords(NUM_TOTAL_RECORDS)
                    .records(List.of(
                        getRecord("19"),
                        getRecord("20"),
                        getRecord("21"),
                        getRecord("22"),
                        getRecord("23"),
                        getRecord("24"),
                        getRecord("25"),
                        getRecord("26"),
                        getRecord("27"),
                        getRecord("28")
                    ))
                    .build()
            ),
            arguments(
                named(
                    "Offset=20, PageSize=30",
                    SearchParameters
                        .builder()
                        .merchandisingConfiguration(MerchandisingConfiguration.EMPTY)
                        .originalPagination(new Pagination(30, 20L))
                        .pagination(new Pagination(30, 20L))
                        .pinnedProducts(PINNED_PRODUCTS)
                        .responseMask(MASK)
                        .build()
                ),
                SearchResults.builder()
                    .numTotalRecords(NUM_TOTAL_RECORDS)
                    .records(List.of(
                        getRecordWithStock(PINNED_PRODUCT_POS_21, true),
                        getRecord("17"),
                        getRecord("18"),
                        getRecordWithStock(PINNED_PRODUCT_POS_24, true),
                        getRecord("19"),
                        getRecord("20"),
                        getRecord("21"),
                        getRecord("22"),
                        getRecord("23"),
                        getRecord("24"),
                        getRecord("25"),
                        getRecord("26"),
                        getRecord("27"),
                        getRecord("28"),
                        getRecord("29"),
                        getRecord("30"),
                        getRecord("31"),
                        getRecord("32"),
                        getRecord("33"),
                        getRecord("34"),
                        getRecord("35")
                    ))
                    .build()
            )
        );
    }

    /**
     * Builds {@link SearchResults} object with paginated records that would be returned by a regular search.
     * The offset of the search results should be the offset of the original customer search minus the number
     * of potential pinned products (reflects {@link  PinToTopService#search} pagination adjustment logic).
     *
     * @param searchParameters   the original search parameters used to conduct a search.
     * @param pinnedProductsSize the number of potential pinned products.
     *
     * @return expected a regular retail search result.
     */
    private static SearchResults getRegularSearchResults(SearchParameters searchParameters, int pinnedProductsSize) {
        var offset = (int) max(searchParameters.getOriginalPagination().getOffset() - pinnedProductsSize, 0);
        var pageSize = searchParameters.getOriginalPagination().getSize() + pinnedProductsSize;
        return SEARCH_RESULTS_NO_PAGINATION.toBuilder()
            .records(SEARCH_RESULTS_NO_PAGINATION.getRecords().subList(offset, min(offset + pageSize, TOTAL_RECORDS)))
            .build();
    }

    // IDs are equal to the zero-based index in the search results
    private static SearchResults getSearchResultsWithoutPagination() {
        var records = range(1, TOTAL_RECORDS + 1)
            .mapToObj(i ->
                Record.of(
                    APPAREL_MERCHANDISER,
                    PRODUCTS_CLOTHING,
                    Integer.toString(i),
                    Integer.toString(i),
                    ANY_TITLE,
                    null,
                    RecordLabel.PINNED
                )
            )
            .toList();

        return SearchResults.builder()
            .numTotalRecords(TOTAL_RECORDS)
            .records(records)
            .build();
    }

    private static Record getRecord(String id) {
        return Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, id, id, ANY_TITLE, null, RecordLabel.PINNED);
    }

    private static Record getRecordWithStock(String id, boolean inStock) {
        var availability = inStock ? IN_STOCK : OUT_OF_STOCK;

        return Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            id,
            id,
            ANY_TITLE,
            Map.of(
                AVAILABILITY, availability,
                PRODUCT_ID_FIELD, id
            ),
            RecordLabel.PINNED
        );
    }

}
