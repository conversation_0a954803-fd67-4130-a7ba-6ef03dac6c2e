package com.groupbyinc.search.ssa.application.core.search.productvisibility;

import com.groupbyinc.search.ssa.core.rule.ProductVisibilityBias;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.List;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.ALL_FALSE;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.ALL_TRUE;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.EVERY_FIFTH;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.EVERY_SECOND;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.EVERY_SECOND_OFFSET;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.ON_1_5;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.ON_1_8;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.assertScoresEqual;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.score;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.visibilities;

class ScoreTest {

    @ParameterizedTest
    @MethodSource("testScenarios")
    public void testScore(List<ProductVisibility> visibilities,
                          ProductVisibilityBias bias,
                          Score expect) {
        // when
        var result = Score.calculate(visibilities, bias);

        // assert
        assertScoresEqual(expect, result);
    }

    private static Stream<Arguments> testScenarios() {
        return Stream.of(
            Arguments.argumentSet(
                "ALL_MATCHING, multi 2, offset 0, cap 1",
                visibilities(120, ALL_TRUE),
                bias(2.0, 0.0, 1.0),
                score(2584.0, 0.0, 2.0, 1.0, 2584.0, 2584.0, 2584.0)
            ),
            Arguments.argumentSet(
                "ALL_NOT_MATCHING, multi 2, offset 0, cap 1",
                visibilities(120, ALL_FALSE),
                bias(2.0, 0.0, 1.0),
                score(0.0, 2584.0, 2.0, 1.0, 0.0, 2584.0, 0.0)
            ),
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION, multi 2, offset 0, cap 1",
                visibilities(120, EVERY_SECOND),
                bias(2.0, 0.0, 1.0),
                score(1292.0, 1292.0, 2.0, 1.0, 2220.0, 2584.0, 2220.0)
            ),
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION, multi 2, offset -.25, cap .9",
                visibilities(120, EVERY_SECOND),
                bias(2.0, -0.25, .9),
                score(1292.0, 1292.0, 1.5, .9, 2220.0, 2325.6, 1938.0)
            ),
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION, multi 2, offset .25, cap .9",
                visibilities(120, EVERY_SECOND),
                bias(2.0, 0.25, .9),
                score(1292.0, 1292.0, 2.5, .9, 2220.0, 2325.6, 2220.0)
            ),
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION, multi 2, offset -.5, cap .75",
                visibilities(120, EVERY_SECOND),
                bias(2.0, -0.5, .75),
                score(1292.0, 1292.0, 1.0, .75, 2220.0, 1938.0, 1292.0)
            ),
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION, multi 2, offset .5, cap .75",
                visibilities(120, EVERY_SECOND),
                bias(2.0, 0.5, .75),
                score(1292.0, 1292.0, 3.0, .75, 2220.0, 1938.0, 1938.0)
            ),
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION, multi 999, offset 0, cap 1",
                visibilities(120, EVERY_SECOND),
                bias(999.0, 0.0, 1.0),
                score(1292.0, 1292.0, 999.0, 1.0, 2220.0, 2584.0, 2220.0)
            ),
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION, multi 2, offset 0, cap 0.1",
                visibilities(120, EVERY_SECOND),
                bias(2.0, 0.0, 0.1),
                score(1292.0, 1292.0, 2.0, 0.1, 2220.0, 258.4, 258.4)
            ),
            // max possible score with locked
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION, multi 2, offset 0, cap 1, locked: [1-5]",
                visibilities(120, EVERY_SECOND, ON_1_5),
                bias(2.0, 0.0, 1.0),
                score(1292.0, 1292.0, 2.0, 1.0, 2038.0, 2584.0, 2038.0)
            ),
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION, multi 2, offset 0, cap 1, locked: [1-8]",
                visibilities(120, EVERY_SECOND, ON_1_8),
                bias(2.0, 0.0, 1.0),
                score(1292.0, 1292.0, 2.0, 1.0, 1856.0, 2584.0, 1856.0)
            ),
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION, multi 2, offset 0, cap 1, locked: every 2nd",
                visibilities(120, EVERY_SECOND, EVERY_SECOND),
                bias(2.0, 0.0, 1.0),
                score(1292.0, 1292.0, 2.0, 1.0, 2220.0, 2584.0, 2220.0)
            ),
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION, multi 2, offset 0, cap 1, locked: every 5th",
                visibilities(120, EVERY_SECOND, EVERY_FIFTH),
                bias(2.0, 0.0, 1.0),
                score(1292.0, 1292.0, 2.0, 1.0, 2220.0, 2584.0, 2220.0)
            ),
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION_OFFSET, multi 2, offset 0, cap 1, locked: [1-5]",
                visibilities(120, EVERY_SECOND_OFFSET, ON_1_5),
                bias(2.0, 0.0, 1.0),
                score(1292.0, 1292.0, 2.0, 1.0, 1947.0, 2584.0, 1947.0)
            ),
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION_OFFSET, multi 2, offset 0, cap 1, locked: [1-8]",
                visibilities(120, EVERY_SECOND_OFFSET, ON_1_8),
                bias(2.0, 0.0, 1.0),
                score(1292.0, 1292.0, 2.0, 1.0, 1856.0, 2584.0, 1856.0)
            ),
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION_OFFSET, multi 2, offset 0, cap 1, locked: every 2nd",
                visibilities(120, EVERY_SECOND_OFFSET, EVERY_SECOND),
                bias(2.0, 0.0, 1.0),
                score(1292.0, 1292.0, 2.0, 1.0, 1292.0, 2584.0, 1292.0)
            ),
            Arguments.argumentSet(
                "EQUAL_DISTRIBUTION_OFFSET, multi 2, offset 0, cap 1, locked: every 5th",
                visibilities(120, EVERY_SECOND_OFFSET, EVERY_FIFTH),
                bias(2.0, 0.0, 1.0),
                score(1292.0, 1292.0, 2.0, 1.0, 1788.0, 2584.0, 1788.0)
            )
        );
    }

    private static ProductVisibilityBias bias(Double multi, Double offset, Double cap) {
        return new ProductVisibilityBias("_", List.of(), List.of(), multi, offset, cap);
    }

}
