package com.groupbyinc.search.ssa.application.configuration;

import com.groupbyinc.search.ssa.core.Merchandiser;

import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_AREA_ID;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_SITE_FILTER_NAME;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTION;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@MicronautTest
@DisplayName("ConfigurationManager Tests")
class ConfigurationManagerTest {

    private static final String INVALID = "invalid";
    private static final Merchandiser INVALID_MERCHANDISER = Merchandiser.of(INVALID);

    @Inject ConfigurationManager configurationManager;

    @Test
    @DisplayName("ConfigurationManager get existing tenant")
    public void getExistingTenantTest() {
        var config = configurationManager.getTenant(APPAREL_MERCHANDISER);

        assertTrue(config.isPresent());
    }

    @Test
    @DisplayName("ConfigurationManager get non-existing tenant")
    public void getNonExistingTenantTest() {
        var config = configurationManager.getTenant(INVALID_MERCHANDISER);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get existing area")
    public void getExistingAreaTest() {
        var config = configurationManager.getArea(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCTION);

        assertTrue(config.isPresent());
    }

    @Test
    @DisplayName("ConfigurationManager get area for non-existing tenant")
    public void getAreaForNonExistingTenantTest() {
        var config = configurationManager.getArea(INVALID_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCTION);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get area for non-existing collection")
    public void getAreaForNonExistingCollectionTest() {
        var config = configurationManager.getArea(APPAREL_MERCHANDISER, INVALID, PRODUCTION);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get area for non-existing name")
    public void getAreaForNonExistingNameTest() {
        var config = configurationManager.getArea(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, INVALID);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get TopSort config")
    public void getExistingTopSortTest() {
        var config = configurationManager.getTopSortConfig(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCTION);

        assertTrue(config.isPresent());
    }

    @Test
    @DisplayName("ConfigurationManager get TopSort for non-existing tenant")
    public void getTopSortForNonExistingTenantTest() {
        var config = configurationManager.getTopSortConfig(INVALID_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCTION);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get TopSort for non-existing collection")
    public void getTopSortForNonExistingCollectionTest() {
        var config = configurationManager.getTopSortConfig(APPAREL_MERCHANDISER, INVALID, PRODUCTION);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get existing site filter")
    public void getExistingSiteFilterByNameTest() {
        var config = configurationManager.getSiteFiltersByName(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            ACTIVE_SITE_FILTER_NAME
        );

        assertTrue(config.isPresent());
    }

    @Test
    @DisplayName("ConfigurationManager get site filter for non-existing tenant")
    public void getSiteFilterByNameForNonExistingTenantTest() {
        var config = configurationManager.getSiteFiltersByName(
            INVALID_MERCHANDISER,
            PRODUCTS_CLOTHING,
            ACTIVE_SITE_FILTER_NAME
        );

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get site filter for non-existing collection")
    public void getSiteFilterByNameForNonExistingCollectionTest() {
        var config = configurationManager.getSiteFiltersByName(
            APPAREL_MERCHANDISER,
            INVALID,
            ACTIVE_SITE_FILTER_NAME
        );

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get site filter for non-existing name")
    public void getSiteFilterByNameForNonExistingNameTest() {
        var config = configurationManager.getSiteFiltersByName(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            INVALID
        );

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get existing site filter")
    public void getExistingSiteFilterByIdTest() {
        var config = configurationManager.getSiteFiltersById(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            1
        );

        assertTrue(config.isPresent());
    }

    @Test
    @DisplayName("ConfigurationManager get site filter for non-existing tenant")
    public void getSiteFilterByIdForNonExistingTenantTest() {
        var config = configurationManager.getSiteFiltersById(
            INVALID_MERCHANDISER,
            PRODUCTS_CLOTHING,
            1
        );

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get site filter for non-existing collection")
    public void getSiteFilterByIdForNonExistingCollectionTest() {
        var config = configurationManager.getSiteFiltersById(
            APPAREL_MERCHANDISER,
            INVALID,
            1
        );

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get site filter for non-existing name")
    public void getSiteFilterByIdForNonExistingNameTest() {
        var config = configurationManager.getSiteFiltersById(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            100
        );

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get project configuration")
    public void getProjectConfigurationTest() {
        var config = configurationManager.getProjectConfiguration(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING);

        assertTrue(config.isPresent());
    }

    @Test
    @DisplayName("ConfigurationManager get project configuration for non-existing tenant")
    public void getProjectConfigurationForNonExistingTenantTest() {
        var config = configurationManager.getProjectConfiguration(INVALID_MERCHANDISER, PRODUCTS_CLOTHING);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get project configuration for non-existing collection")
    public void getProjectConfigurationForNonCollectionTenantTest() {
        var config = configurationManager.getProjectConfiguration(APPAREL_MERCHANDISER, INVALID);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get rule")
    public void getRule() {
        var config = configurationManager.getRules(APPAREL_MERCHANDISER, ACTIVE_AREA_ID);

        assertFalse(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get rule for non-existing tenant")
    public void getRuleInvalidTenant() {
        var config = configurationManager.getRules(INVALID_MERCHANDISER, ACTIVE_AREA_ID);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get rule for non-existing area")
    public void getRuleInvalidArea() {
        var config = configurationManager.getRules(APPAREL_MERCHANDISER, 100);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get redirect")
    public void getRedirect() {
        var config = configurationManager.getRedirects(APPAREL_MERCHANDISER, ACTIVE_AREA_ID);

        assertFalse(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get redirect for non-existing tenant")
    public void getRedirectInvalidTenant() {
        var config = configurationManager.getRedirects(INVALID_MERCHANDISER, ACTIVE_AREA_ID);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get redirect for non-existing area")
    public void getRedirectInvalidArea() {
        var config = configurationManager.getRedirects(APPAREL_MERCHANDISER, 100);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get navigations")
    public void getNavigations() {
        var config = configurationManager.getNavigations(APPAREL_MERCHANDISER, ACTIVE_AREA_ID);

        assertFalse(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get navigations for non-existing tenant")
    public void getNavigationsInvalidTenant() {
        var config = configurationManager.getNavigations(INVALID_MERCHANDISER, ACTIVE_AREA_ID);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get navigations for non-existing area")
    public void getNavigationsInvalidArea() {
        var config = configurationManager.getNavigations(APPAREL_MERCHANDISER, 100);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get BP")
    public void getBP() {
        var config = configurationManager.getBiasingProfiles(APPAREL_MERCHANDISER, ACTIVE_AREA_ID);

        assertFalse(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get BP for non-existing tenant")
    public void getBPInvalidTenant() {
        var config = configurationManager.getBiasingProfiles(INVALID_MERCHANDISER, ACTIVE_AREA_ID);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get BP for non-existing area")
    public void getBPInvalidArea() {
        var config = configurationManager.getBiasingProfiles(APPAREL_MERCHANDISER, 100);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get zones")
    public void getZones() {
        var config = configurationManager.getZones(APPAREL_MERCHANDISER, ACTIVE_AREA_ID);

        assertFalse(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get zones for non-existing tenant")
    public void getZonesInvalidTenant() {
        var config = configurationManager.getZones(INVALID_MERCHANDISER, ACTIVE_AREA_ID);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get zones for non-existing area")
    public void getZonesInvalidArea() {
        var config = configurationManager.getZones(APPAREL_MERCHANDISER, 100);

        assertTrue(config.isEmpty());
    }


    @Test
    @DisplayName("ConfigurationManager get attributes")
    public void getAttributes() {
        var config = configurationManager.getAttributeConfiguration(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING);

        assertFalse(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get attributes for non-existing tenant")
    public void getAttributesInvalidTenant() {
        var config = configurationManager.getAttributeConfiguration(INVALID_MERCHANDISER, PRODUCTS_CLOTHING);

        assertTrue(config.isEmpty());
    }

    @Test
    @DisplayName("ConfigurationManager get attributes for non-existing collection")
    public void getAttributesInvalidArea() {
        var config = configurationManager.getAttributeConfiguration(APPAREL_MERCHANDISER, INVALID);

        assertTrue(config.isEmpty());
    }

}
