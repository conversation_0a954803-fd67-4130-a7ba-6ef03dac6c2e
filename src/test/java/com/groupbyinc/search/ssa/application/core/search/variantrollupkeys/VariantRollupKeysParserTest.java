package com.groupbyinc.search.ssa.application.core.search.variantrollupkeys;

import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;

import io.micronaut.core.propagation.PropagatedContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.application.core.search.converter.AbstractFacetConverter.INVENTORIES_PREFIX;
import static com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKey.RollupKeyType.FULFILLMENT_INFO;
import static com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKey.RollupKeyType.INVENTORY_ATTRIBUTE;
import static com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKey.RollupKeyType.VARIANT_ATTRIBUTE;
import static com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKeysParser.ALLOWED_SIMPLE_KEYS_TO_PATH;
import static com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKeysParser.FULFILLMENT_KEY_TO_TYPE;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.METADATA;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;

import static java.util.stream.Collectors.toMap;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

class VariantRollupKeysParserTest {

    private static final String PLACE_ID = "MTL";
    private static final Integer COLLECTION_ID = 1;
    private static final String NOT_USED_ATTRIBUTE = "notUsedAttribute";
    private static final String NON_EXISTENT_ATTRIBUTE = "nonExistentAttribute";
    private static final String INVALID_ATTRIBUTE = "attributes.invalidAttribute";
    private static final String CUSTOM_ATTRIBUTE = "attributes.customAttribute";
    private VariantRollupKeysParser parser;

    private PropagatedContext.Scope scope;

    @BeforeEach
    void setUp() {
        parser = new VariantRollupKeysParser();

        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    @Test
    void testValidSimpleVariantKeys() {
        List<String> keys = List.of("variantId");

        var result = parser.parseVariantRollupKeys(keys, Map.of());

        assertThat(result).hasSize(keys.size());
        result.forEach(key -> {
            assertThat(key).isNotNull();
            assertThat(key.type()).isEqualTo(VARIANT_ATTRIBUTE);
            assertThat(key.placeId()).isNull();
            assertThat(key.attribute()).isEqualTo(key.key());
            assertThat(key.path()).isEqualTo(ALLOWED_SIMPLE_KEYS_TO_PATH.get(key.key()));
        });
    }

    @Test
    void testValidVariantAttributesKeys() {
        var keysToPath = Map.of(
            "price", "priceInfo.price",
            "originalPrice", "priceInfo.originalPrice",
            "discount", "discount",
            "colorFamilies", "colorInfo.colorFamilies",
            CUSTOM_ATTRIBUTE, CUSTOM_ATTRIBUTE,
            "attributes.color", "attributes.color",
            "attributes.size", "attributes.size"
        );

        Map<String, AttributeConfiguration> attributeConfigurations = Map.of(
            "price", attribute("price", "priceInfo.price"),
            "originalPrice", attribute("originalPrice", "priceInfo.originalPrice"),
            "discount", attribute("discount", "discount"),
            "colorFamilies", attribute("colorFamilies", "colorInfo.colorFamilies"),
            CUSTOM_ATTRIBUTE, attribute(CUSTOM_ATTRIBUTE, CUSTOM_ATTRIBUTE),
            "attributes.color", attribute("attributes.color", "attributes.color"),
            "attributes.size", attribute("attributes.size", "attributes.size")
        );

        var result = parser.parseVariantRollupKeys(List.copyOf(keysToPath.keySet()), attributeConfigurations);

        assertThat(result).hasSize(keysToPath.size());
        for (VariantRollupKey variantRollupKey : result) {
            assertThat(variantRollupKey).isNotNull();
            assertThat(variantRollupKey.type()).isEqualTo(VARIANT_ATTRIBUTE);
            assertThat(variantRollupKey.placeId()).isNull();
            assertThat(variantRollupKey.attribute()).isEqualTo(variantRollupKey.key());
            assertThat(variantRollupKey.path()).isEqualTo(keysToPath.get(variantRollupKey.key()));
        }
    }

    @Test
    void testInvalidVariantAttributesKeys() {
        List<String> keys = List.of(
            NON_EXISTENT_ATTRIBUTE,
            NOT_USED_ATTRIBUTE,
            INVALID_ATTRIBUTE,
            CUSTOM_ATTRIBUTE,
            "attributes.size"
        );

        Map<String, AttributeConfiguration> attributeConfigurations = Map.of(
            CUSTOM_ATTRIBUTE, attribute(CUSTOM_ATTRIBUTE, CUSTOM_ATTRIBUTE),
            "attributes.size", attribute("attributes.size", "attributes.size")
        );

        parser.parseVariantRollupKeys(keys, attributeConfigurations);

        assertThat(
            getRequestContext().getWarnings().getFirst()
        ).startsWith("Unsupported variant rollup keys:");
        assertTrue(
            getRequestContext().getWarnings().stream().anyMatch(s -> s.contains(NON_EXISTENT_ATTRIBUTE))
        );
        assertTrue(
            getRequestContext().getWarnings().stream().anyMatch(s -> s.contains(NOT_USED_ATTRIBUTE))
        );
        assertTrue(
            getRequestContext().getWarnings().stream().anyMatch(s -> s.contains(INVALID_ATTRIBUTE))
        );
    }

    @Test
    void testValidInventoryAttributes() {
        var inventoryAttributesToPath = Map.of(
            "price", "priceInfo.price",
            "originalPrice", "priceInfo.originalPrice",
            "colors", "colorInfo.colors",
            "attributes.listprice", "attributes.scn_listprice",
            "attributes.scn_sum_inventory", "attributes.scn_sum_inventory"
        );

        List<String> variantRollupKeys = inventoryAttributesToPath.keySet()
            .stream()
            .map(attribute -> "inventory(%s,%s)".formatted(PLACE_ID, attribute))
            .toList();

        Map<String, AttributeConfiguration> attributeConfigurations = inventoryAttributesToPath.entrySet()
            .stream()
            .collect(toMap(
                a -> INVENTORIES_PREFIX + a.getKey(),
                a -> attribute(INVENTORIES_PREFIX + a.getKey(), INVENTORIES_PREFIX + a.getValue())
                ));

        var result = parser.parseVariantRollupKeys(variantRollupKeys, attributeConfigurations);

        assertThat(result).hasSize(variantRollupKeys.size());
        for (VariantRollupKey key : result) {
            assertThat(key).isNotNull();
            assertThat(key.type()).isEqualTo(INVENTORY_ATTRIBUTE);
            assertThat(key.placeId()).isEqualTo(PLACE_ID);
            assertThat(key.path()).isEqualTo(inventoryAttributesToPath.get(key.attribute()));
            assertThat(inventoryAttributesToPath.keySet()).contains(key.attribute());
        }
    }

    @Test
    void testInvalidInventoryAttributesKeys() {
        var keysToPath = Map.of(
            "price", "priceInfo.price",
            "attributes.listprice", "attributes.listprice",
            INVALID_ATTRIBUTE, INVALID_ATTRIBUTE
        );

        List<String> variantRollupKeys = keysToPath.keySet()
            .stream()
            .map(attribute -> "inventory(%s,%s)".formatted(PLACE_ID, attribute))
            .toList();

        Map<String, AttributeConfiguration> attributeConfigurations = keysToPath.entrySet()
            .stream()
            .filter(a -> !a.getKey().equals(INVALID_ATTRIBUTE))
            .collect(toMap(
                a -> INVENTORIES_PREFIX + a.getKey(),
                a -> attribute(
                    INVENTORIES_PREFIX + a.getKey(),
                    INVENTORIES_PREFIX + a.getValue()
                )
            ));

        parser.parseVariantRollupKeys(variantRollupKeys, attributeConfigurations);

        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.startsWith("Unsupported variant rollup keys:"))
        );
        assertTrue(
            getRequestContext().getWarnings().stream().anyMatch(s -> s.contains(INVALID_ATTRIBUTE))
        );
    }

    @ParameterizedTest(name = "fulfillmentInfoKey: {0}, fulfillmentInfoType: {1}")
    @MethodSource("fulfillmentInfoKeys")
    @DisplayName("validFulfillmentInfoKeys")
    void testValidFulfillmentInfoKeys(String fulfillmentInfoKey) {
        String key = "%s.%s".formatted(fulfillmentInfoKey, PLACE_ID);

        var result = parser.parseVariantRollupKeys(List.of(key), Map.of());

        assertThat(result).hasSize(1);
        var parsedKey = result.getFirst();
        assertThat(parsedKey).isNotNull();
        assertThat(parsedKey.type()).isEqualTo(FULFILLMENT_INFO);
        assertThat(parsedKey.placeId()).isEqualTo(PLACE_ID);
        assertThat(parsedKey.attribute()).isEqualTo(fulfillmentInfoKey);
        assertThat(parsedKey.path()).isEqualTo(FULFILLMENT_KEY_TO_TYPE.get(fulfillmentInfoKey));
    }

    public static Stream<Arguments> fulfillmentInfoKeys() {
        return FULFILLMENT_KEY_TO_TYPE.entrySet()
            .stream()
            .map(e -> Arguments.of(e.getKey(), e.getValue()));
    }

    @Test
    void testMixedValidKeys() {
        List<String> keys = List.of(
            "price",
            "attributes.sku",
            "inventory(MTL,price)",
            "inventory(MTL,attributes.listprice)",
            "customFulfillment1.MTL",
            "variantId"
        );

        Map<String, AttributeConfiguration> attributeConfigurations = Map.of(
            "price", attribute("price", "price.priceInfo"),
            "attributes.sku", attribute("attributes.sku", "attributes.sku"),
            "inventories.price", attribute("inventories.price", "inventories.price.priceInfo"),
            "inventories.attributes.listprice",
            attribute("inventories.attributes.listprice", "inventories.attributes.listprice")
        );

        List<VariantRollupKey> result = parser.parseVariantRollupKeys(keys, attributeConfigurations);

        assertThat(result).hasSize(keys.size());
    }

    @Test
    void testInvalidKeys() {
        List<String> keys = List.of(
            "unsupportedKey",
            "inventory(MTL)",
            "inventory(MTL,unknownAttribute)",
            "attributes.",
            "customFulfillment6.MTL",
            "inventory(MTL, )",
            "inventory(,price)",
            "inventory(MTL,price",
            "inventory(MTL,price))",
            "inventory(MTL,attributes.)"
        );

        Map<String, AttributeConfiguration> attributeConfigurations = Map.of(
            "price", attribute("price", "price.PriceInfo")
        );

        parser.parseVariantRollupKeys(keys, attributeConfigurations);

        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.startsWith("Unsupported variant rollup keys:"))
        );
    }

    @Test
    void testValidAndInvalidKeys() {
        List<String> keys = List.of(
            "price",
            "attributes.sku",
            "inventory(MTL,price)",
            "inventory(MTL,attributes.listprice)",
            "customFulfillment1.MTL",
            "variantId",
            "unsupportedKey", // invalid
            "inventory(MTL)", // invalid
            "inventory(MTL,unknownAttribute)" // invalid
        );

        Map<String, AttributeConfiguration> attributeConfigurations = Map.of(
            "price", attribute("price", "price.PriceInfo"),
            "attributes.sku", attribute("attributes.sku", "attributes.sku"),
            "inventories.price", attribute("inventories.price", "inventories.price.PriceInfo"),
            "inventories.attributes.listprice",
            attribute("inventories.attributes.listprice", "inventories.attributes.listprice")
        );

        parser.parseVariantRollupKeys(keys, attributeConfigurations);

        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.startsWith("Unsupported variant rollup keys:"))
        );
        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.contains("unsupportedKey"))
        );
        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.contains("inventory(MTL)"))
        );
        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.contains("inventory(MTL,unknownAttribute)"))
        );
    }

    @Test
    void testKeysWithWhitespaces() {
        List<String> keys = List.of(
            " price ",
            " attributes.sku ",
            " inventory( MTL , price ) ",
            "inventory( MTL , attributes.listprice )",
            " customFulfillment1.MTL "
        );

        Map<String, AttributeConfiguration> attributeConfigurations = Map.of(
            "price", attribute("price", "price.PriceInfo"),
            "attributes.sku", attribute("attributes.sku", "attributes.sku"),
            "inventories.price", attribute("inventories.price", "inventories.price.PriceInfo"),
            "inventories.attributes.listprice",
            attribute("inventories.attributes.listprice", "inventories.attributes.listprice")
        );

        var result = parser.parseVariantRollupKeys(keys, attributeConfigurations);

        assertThat(result).hasSize(keys.size());
    }

    @Test
    void testEmptyAndNullKeys() {
        List<String> keys = Arrays.asList(
            "",
            "   ",
            null
        );

        parser.parseVariantRollupKeys(keys, Map.of());

        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.startsWith("Unsupported variant rollup keys:"))
        );
    }

    private static AttributeConfiguration attribute(String key, String path) {
        return AttributeConfiguration.builder()
            .key(key)
            .path(path)
            .retrievable(true)
            .displayName(key)
            .metadata(List.of(METADATA))
            .collectionId(COLLECTION_ID)
            .build();
    }

}
