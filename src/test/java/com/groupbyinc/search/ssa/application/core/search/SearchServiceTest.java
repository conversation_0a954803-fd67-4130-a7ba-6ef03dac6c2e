package com.groupbyinc.search.ssa.application.core.search;

import com.groupbyinc.search.ssa.api.builders.SearchResponseBuilder;
import com.groupbyinc.search.ssa.api.dto.BiasDto;
import com.groupbyinc.search.ssa.api.dto.BiasingProfileDto;
import com.groupbyinc.search.ssa.api.dto.CustomParameterDto;
import com.groupbyinc.search.ssa.api.dto.NavigationTypeDto;
import com.groupbyinc.search.ssa.api.dto.PageInfoDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SearchResponseDto;
import com.groupbyinc.search.ssa.api.dto.SelectedRefinementDto;
import com.groupbyinc.search.ssa.api.dto.SortDto;
import com.groupbyinc.search.ssa.application.builders.SearchParametersBuilder;
import com.groupbyinc.search.ssa.application.builders.SearchParametersPreparer;
import com.groupbyinc.search.ssa.core.CustomParameter;
import com.groupbyinc.search.ssa.core.Metadata;
import com.groupbyinc.search.ssa.core.Order;
import com.groupbyinc.search.ssa.core.Pagination;
import com.groupbyinc.search.ssa.core.Rewrites;
import com.groupbyinc.search.ssa.core.SearchMode;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.Sort;
import com.groupbyinc.search.ssa.core.biasing.Bias;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfile;
import com.groupbyinc.search.ssa.core.navigation.Facet;
import com.groupbyinc.search.ssa.core.navigation.Navigation;
import com.groupbyinc.search.ssa.core.navigation.NavigationRefinement;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;
import com.groupbyinc.search.ssa.core.template.Template;
import com.groupbyinc.search.ssa.retail.util.v2alpha.UserAttributesHelper;

import io.micronaut.core.propagation.PropagatedContext;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.groupbyinc.search.ssa.api.utils.SearchUtils.getRequestPageCategories;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.RANGE;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.VALUE;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTION;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;
import static com.groupbyinc.search.ssa.stub.TestData.VISITOR_ID;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

@SuppressWarnings("deprecation")
@ExtendWith(MockitoExtension.class)
public class SearchServiceTest {

    private SearchParametersBuilder searchParametersBuilder;
    private final SearchResponseBuilder searchResponseBuilder = new SearchResponseBuilder();

    private PropagatedContext.Scope scope;

    @BeforeEach
    void setUp() {
        searchParametersBuilder = new SearchParametersBuilder(
            mock(SearchParametersPreparer.class),
            mock(UserAttributesHelper.class)
        );
        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    @Test
    @DisplayName("Converts a base search request DTO to search parameters with default pagination")
    void convertsBaseSearchRequestDtoToSearchParameters() {
        var searchRequestDto = SearchRequestDto.builder()
            .query("blue sweater")
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .visitorId(VISITOR_ID)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        assertThat(searchParameters.getPagination().getSize()).isEqualTo(Pagination.DEFAULT_SIZE);
        assertThat(searchParameters.getPagination().getOffset()).isEqualTo(Pagination.DEFAULT_OFFSET);
    }

    @Test
    @DisplayName("Converts a base search request DTO to search parameters with default pagination")
    void convertsBaseSearchRequestDtoToSearchParametersWithOptions() {
        var searchRequestDto = SearchRequestDto.builder()
            .query("blue sweater")
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .visitorId(VISITOR_ID)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        assertThat(searchParameters.getPagination().getSize()).isEqualTo(Pagination.DEFAULT_SIZE);
        assertThat(searchParameters.getPagination().getOffset()).isEqualTo(Pagination.DEFAULT_OFFSET);
    }

    @Test
    @DisplayName("Converts pageSize and skip to pagination with topsort disabled")
    void convertsPageSizeAndSkipToPaginationWithTopsortDisabled() {
        var reqPageSize = 50;
        var regSkip = 0L;
        var searchRequestDto = SearchRequestDto.builder()
            .pageSize(50)
            .skip(0L)
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        assertThat(searchParameters).extracting(SearchParameters::getPagination)
            .isEqualTo(new Pagination(reqPageSize, regSkip));
    }

    @Test
    @DisplayName("Converts pageSize and 3 pages skip to pagination with topsort disabled")
    void convertsPageSizeAndSkipToPagination() {
        var reqPageSize = 50;
        var regSkip = 150L;
        var searchRequestDto = SearchRequestDto.builder()
            .pageSize(reqPageSize)
            .skip(regSkip)
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        assertThat(searchParameters).extracting(SearchParameters::getPagination)
            .isEqualTo(new Pagination(reqPageSize, regSkip));
        assertThat(searchParameters).extracting(SearchParameters::getOriginalPagination)
            .isEqualTo(new Pagination(reqPageSize, regSkip));
    }

    @Test
    @DisplayName("Converts pageSize and skip to pagination with topsort enabled but not overriding page size")
    void convertsPageSizeAndSkipToPaginationWithTopsortEnabledButNotOverridingPageSize() {
        var reqPageSize = 150;
        var regSkip = 0L;
        var searchRequestDto = SearchRequestDto.builder()
            .pageSize(reqPageSize)
            .skip(regSkip)
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .enableTopsort(true)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        assertThat(searchParameters).extracting(SearchParameters::getPagination)
            .isEqualTo(new Pagination(reqPageSize, regSkip));
        assertThat(searchParameters).extracting(SearchParameters::getOriginalPagination)
            .isEqualTo(new Pagination(reqPageSize, regSkip));
    }


    @Test
    @DisplayName("Converts custom parameter DTOs to custom parameters")
    void convertsCustomParameterDtoToCustomParameters() {
        var searchRequestDto = SearchRequestDto.builder()
            .customUrlParams(
                List.of(
                    new CustomParameterDto("key", "value")
                )
            )
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        assertThat(searchParameters).extracting(SearchParameters::getCustomParameters)
            .asList()
            .containsExactly(new CustomParameter("key", "value"));
    }

    @Test
    @DisplayName("Converts biasing profile DTO to biasing profile")
    void convertsBiasingProfileDtoToBiasingProfile() {
        var searchRequestDto = SearchRequestDto.builder()
            .biasing(
                new BiasingProfileDto(
                    List.of(
                        new BiasDto("a", "a", BiasDto.StrengthDto.ABSOLUTE_INCREASE, null, null, null),
                        new BiasDto("b", "b", BiasDto.StrengthDto.STRONG_INCREASE, null, null, null),
                        new BiasDto("c", "c", BiasDto.StrengthDto.MEDIUM_INCREASE, null, null, null),
                        new BiasDto("d", "d", BiasDto.StrengthDto.WEAK_INCREASE, null, null, null),
                        new BiasDto("e", "e", BiasDto.StrengthDto.LEAVE_UNCHANGED, null, null, null),
                        new BiasDto("f", "f", BiasDto.StrengthDto.WEAK_DECREASE, null, null, null),
                        new BiasDto("g", "g", BiasDto.StrengthDto.MEDIUM_DECREASE, null, null, null),
                        new BiasDto("h", "h", BiasDto.StrengthDto.STRONG_DECREASE, null, null, null),
                        new BiasDto("i", "i", BiasDto.StrengthDto.ABSOLUTE_DECREASE, null, null, null)
                    )
                )
            )
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        assertThat(searchParameters).extracting(SearchParameters::getBiasingProfile)
            .asInstanceOf(InstanceOfAssertFactories.type(BiasingProfile.class))
            .isEqualTo(
                BiasingProfile.builder()
                    .biases(
                        List.of(
                            new Bias("a", "a", Bias.Strength.ABSOLUTE_INCREASE, null, null, null),
                            new Bias("b", "b", Bias.Strength.STRONG_INCREASE, null, null, null),
                            new Bias("c", "c", Bias.Strength.MEDIUM_INCREASE, null, null, null),
                            new Bias("d", "d", Bias.Strength.WEAK_INCREASE, null, null, null),
                            new Bias("e", "e", Bias.Strength.LEAVE_UNCHANGED, null, null, null),
                            new Bias("f", "f", Bias.Strength.WEAK_DECREASE, null, null, null),
                            new Bias("g", "g", Bias.Strength.MEDIUM_DECREASE, null, null, null),
                            new Bias("h", "h", Bias.Strength.STRONG_DECREASE, null, null, null),
                            new Bias("i", "i", Bias.Strength.ABSOLUTE_DECREASE, null, null, null)
                        )
                    )
                    .build()
            );
    }

    @Test
    @DisplayName("Converts selected refinement DTOs to selected refinements")
    void convertsSelectedRefinementsDtosToSelectedRefinements() {
        var searchRequestDto = SearchRequestDto.builder()
            .refinements(
                List.of(
                    new SelectedRefinementDto(
                        "brands",
                        NavigationTypeDto.Value,
                        "Nike",
                        null,
                        null,
                        null,
                        false
                    ),
                    new SelectedRefinementDto(
                        "price",
                        NavigationTypeDto.Range,
                        null,
                        0d,
                        50d,
                        null,
                        false
                    )
                )
            )
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        assertThat(searchParameters).extracting(SearchParameters::getRefinements)
            .asList()
            .containsExactly(
                brandSelectedRefinement(),
                priceSelectedRefinement()
            );
    }

    @Test
    @DisplayName("Converts sort DTOs to sorts")
    void convertsSortDtosToSorts() {
        var searchRequestDto = SearchRequestDto.builder()
            .sorts(
                List.of(
                    new SortDto("brands", SortDto.OrderDto.Ascending),
                    new SortDto("price", SortDto.OrderDto.Descending),
                    new SortDto("color", null)
                )
            )
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        assertThat(searchParameters).extracting(SearchParameters::getSorts)
            .asList()
            .containsExactly(
                new Sort("brands", Order.ASCENDING),
                new Sort("price", Order.DESCENDING),
                new Sort("color", Order.ASCENDING)
            );
    }

    @Test
    @DisplayName("Search params contains Dynamic Facet")
    void includesDynamicFacetInSearchParams() {
        var searchRequestDto = SearchRequestDto.builder()
            .dynamicFacet(true)
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        assertThat(searchParameters).extracting(SearchParameters::getDynamicFacet)
            .isEqualTo(true);
    }

    @Test
    @DisplayName("Search params contains Unspecified pageCategories")
    void includesUnspecifiedPageCategoriesInSearchParams() {
        var searchRequestDto = SearchRequestDto.builder()
            .dynamicFacet(true)
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .query("")
            .pageCategories(new ArrayList<>())
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        assertThat(searchParameters).extracting(SearchParameters::getPageCategories)
            .isEqualTo(Collections.singletonList("UNSPECIFIED"));
    }

    @Test
    @DisplayName("Browse events should use default pageCategories")
    void browseEventShouldUseDefaultPageCategories() {
        var searchRequestDto = SearchRequestDto.builder()
            .dynamicFacet(true)
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .query("")
            .pageCategories(new ArrayList<>())
            .build();

        assertThat(getRequestPageCategories(searchRequestDto)).isEqualTo(Collections.singletonList("UNSPECIFIED"));
    }

    @Test
    @DisplayName("Browse events with pageCategories should return correct value")
    void browseEventWithPageCategoryShouldReturnCorrectValue() {
        var pageCategories = List.of("foo", "bar");
        var searchRequestDto = SearchRequestDto.builder()
            .dynamicFacet(true)
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .query("")
            .pageCategories(pageCategories)
            .build();

        assertThat(getRequestPageCategories(searchRequestDto)).isEqualTo(pageCategories);
    }

    @Test
    @DisplayName("Non Browse event should return right pageCategory")
    void nonBrowseEventShouldReturnPageCategory() {
        var pageCategories = List.of("foo", "bar");
        var searchRequestDto = SearchRequestDto.builder()
            .dynamicFacet(true)
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .query("some query")
            .pageCategories(pageCategories)
            .build();

        assertThat(getRequestPageCategories(searchRequestDto)).isEqualTo(pageCategories);
    }

    @Test
    @DisplayName("Search params contains Variant Rollup Keys")
    void includeVariantRollupKeysInSearchParam() {
        var variantRollupKeys = List.of(
            "price",
            "colorFamilies",
            "originalPrice",
            "discount",
            "attributes.sddEligibleFlag",
            "attributes.BAB_PREORDER_FLAG",
            "attributes.MAX_SHIPPING_DAYS",
            "attributes.storeOnly",
            "attributes.SHIPPING_CUTOFF_OFFSET",
            "attributes.MIN_SHIPPING_DAYS"
        );

        var searchRequestDto = SearchRequestDto.builder()
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .variantRollupKeys(variantRollupKeys)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        assertThat(searchParameters).extracting(SearchParameters::getVariantRollupKeys)
            .isEqualTo(variantRollupKeys);
    }

    @Test
    @DisplayName(
        "A response that has no results should not include pagination with zeros for page other than the first one"
    )
    void aResponseThatHasNoResultsShouldIncludePaginationWithZeros() {
        var searchRequestDto = SearchRequestDto.builder()
            .pageSize(50)
            .skip(50L)
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var searchResponseDto = searchResponseBuilder.buildSearchResponseDto(
            searchParameters,
            SearchResults.builder().numTotalRecords(50).build(),
            searchRequestDto
        );

        assertThat(searchResponseDto)
            .extracting(SearchResponseDto::getPageInfo)
            .isEqualTo(new PageInfoDto(50L, 100L));
    }

    @Test
    @DisplayName("Response contains updated query from search results")
    void responseContainsUpdatedQueryFromSearchResults() {
        var searchRequestDto = SearchRequestDto.builder()
            .query("TV")
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var searchResponseDto = searchResponseBuilder.buildSearchResponseDto(
            searchParameters,
            SearchResults.builder().query("TV The \"L\" word").build(),
            searchRequestDto
        );

        assertThat(searchResponseDto)
            .extracting(SearchResponseDto::getQuery)
            .isEqualTo("TV The \"L\" word");
    }

    @Test
    @DisplayName("Response contains the description inside range")
    void responseContainsRangeDescription() {
        var searchRequestDto = SearchRequestDto.builder()
            .query("TV")
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var rangeDesc = "test desc";
        var navigation = Navigation.builder()
            .name("test")
            .field("test")
            .type(NavigationType.RANGE)
            .refinements(List.of(NavigationRefinement.rangeRefinement(
                new Range(1.0, 100.0, rangeDesc)))
            )
            .build();

        var searchResults = SearchResults.builder()
            .query("TV The \"L\" word")
            .navigations(List.of(navigation))
            .selectedNavigations(List.of(navigation))
            .build();

        var searchResponseDto = searchResponseBuilder.buildSearchResponseDto(
            searchParameters,
            searchResults,
            searchRequestDto
        );

        assertThat(searchResponseDto)
            .extracting(SearchResponseDto::getQuery)
            .isEqualTo("TV The \"L\" word");

        var searchRefinement = searchResponseDto.getSelectedNavigation().getFirst().getRefinements().getFirst();
        assertThat(searchRefinement.getDescription()).isEqualTo(rangeDesc);
    }

    @Test
    @DisplayName("Response contains the name of the biasing profile that was used")
    void responseContainsTheNameOfTheBiasingProfileThatWasUsed() {
        var searchRequestDto = SearchRequestDto.builder()
            .query("bag")
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var searchResults = SearchResults.builder()
            .biasingProfile(
                BiasingProfile.builder()
                    .name("Expensive products are better")
                    .biases(
                        List.of(
                            Bias.builder()
                                .field("price")
                                .content("1000")
                                .strength(Bias.Strength.ABSOLUTE_INCREASE)
                                .build()
                        )
                    )
                    .build()
            )
            .build();

        var searchResponseDto = searchResponseBuilder.buildSearchResponseDto(
            searchParameters,
            searchResults,
            searchRequestDto
        );

        assertThat(searchResponseDto)
            .extracting(SearchResponseDto::getBiasingProfile)
            .isEqualTo("Expensive products are better");
    }

    @Test
    @DisplayName("Response contains the provided `rewrites` field")
    void responseContainsTheProvidedRewrites() {
        var rewriteTest1 = "rewrite_test1";
        var rewriteTest2 = "rewrite_test2";
        var searchRequestDto = SearchRequestDto.builder()
            .query("bag")
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .rewrites(List.of(rewriteTest1, rewriteTest2))
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var searchResults = SearchResults.builder()
            .rewrites(List.of(Rewrites.TOPSORT.name(), Rewrites.PRODUCT_CATALOG.name()))
            .build();

        var searchResponseDto = searchResponseBuilder.buildSearchResponseDto(
            searchParameters,
            searchResults,
            searchRequestDto
        );

        assertThat(searchResponseDto.getRewrites())
            .containsExactly(rewriteTest1, rewriteTest2, Rewrites.TOPSORT.name(), Rewrites.PRODUCT_CATALOG.name());
    }

    @Test
    @DisplayName("Response contains rule id in template")
    void responseContainsRuleIdInTemplate() {
        var searchRequestDto = SearchRequestDto.builder()
            .query("bag")
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequestDto,
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );
        var expectedRule = RuleConfiguration.builder()
            .name("TestRule")
            .id(123)
            .build();

        var searchResults = SearchResults.builder()
            .template(Template.ofRule(expectedRule))
            .build();

        var searchResponseDto = searchResponseBuilder.buildSearchResponseDto(
            searchParameters,
            searchResults,
            searchRequestDto
        );

        assertThat(searchResponseDto.getTemplate().getRuleId())
            .isEqualTo(expectedRule.getId());
    }

    @Test
    @DisplayName("Adds siteFilter to  siteParams")
    void addsSiteFilterToSiteParams() {
        var siteFilterConfig = SiteFilterConfiguration.builder()
            .id(1)
            .name("foobar")
            .build();
        var siteParams = List.of(
            new Metadata("symphony6", "winter"),
            new Metadata("symphony8", "allegretto")
        );

        var copySiteParams = new ArrayList<>(siteParams);
        copySiteParams.add(new Metadata("siteFilter", siteFilterConfig.name()));

        assertThat(searchResponseBuilder.addSiteFilterToSiteParams(siteParams, siteFilterConfig))
            .isEqualTo(copySiteParams);
    }

    @Test
    @DisplayName("Adds siteFilter to  empty List of siteParams")
    void addSiteFilterWhenSiteParamsIsEmpty() {
        var siteFilterConfig = SiteFilterConfiguration.builder()
            .id(1)
            .name("foobar")
            .build();
        ArrayList<Metadata> siteParams = new ArrayList<>();

        var copySiteParams = new ArrayList<>(siteParams);
        copySiteParams.add(new Metadata("siteFilter", siteFilterConfig.name()));

        assertThat(searchResponseBuilder.addSiteFilterToSiteParams(siteParams, siteFilterConfig))
            .isEqualTo(copySiteParams);
    }

    @Test
    @DisplayName("Return siteFilter when siteParams is null")
    void getSiteFilterWhenSiteParamsIsNull() {
        var siteFilterConfig = SiteFilterConfiguration.builder()
            .id(1)
            .name("foobar")
            .build();

        var expectedMetadataList = List.of(new Metadata("siteFilter", siteFilterConfig.name()));

        assertThat(searchResponseBuilder.addSiteFilterToSiteParams(null, siteFilterConfig))
            .isEqualTo(expectedMetadataList);
    }

    @Test
    @DisplayName("Return siteParams when siteFilterConfig is null")
    void getSiteParamsWhenSiteFilterIsNull() {
        var siteParams = List.of(
            new Metadata("symphony6", "winter"),
            new Metadata("symphony8", "allegretto")
        );

        assertThat(searchResponseBuilder.addSiteFilterToSiteParams(siteParams, null))
            .isEqualTo(siteParams);
    }

    @Test
    @DisplayName("Return empty list when both params are null")
    void getEmptyListSiteFilterWhenAllParamsAreNull() {
        assertThat(searchResponseBuilder.addSiteFilterToSiteParams(null, null))
            .isEqualTo(List.of());
    }

    public static SelectedRefinement brandSelectedRefinement() {
        return new SelectedRefinement(
            "brands",
            VALUE,
            null,
            "Nike",
            false,
            false
        );
    }

    public static SelectedRefinement priceSelectedRefinement() {
        return new SelectedRefinement(
            "price",
            RANGE,
            new Range(0d, 50d, null),
            null,
            false,
            false
        );
    }

}
