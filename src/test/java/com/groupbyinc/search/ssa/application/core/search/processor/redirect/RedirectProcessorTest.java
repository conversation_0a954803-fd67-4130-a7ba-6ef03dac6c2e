package com.groupbyinc.search.ssa.application.core.search.processor.redirect;

import com.groupbyinc.search.ssa.application.Clock;
import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.redirect.RedirectConfiguration;
import com.groupbyinc.search.ssa.core.trigger.QueryPatternTrigger;

import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

@MicronautTest
@DisplayName("RedirectProcessorTest Tests")
public class RedirectProcessorTest {

    @Inject Clock clock;

    @Test
    @DisplayName("Return empty optional object if there no redirect configurations")
    void resolveRedirectWhenNoRedirectConfigurationsTest() {
        var searchParameters = SearchParameters.builder()
            .merchandisingConfiguration(MerchandisingConfiguration.EMPTY)
            .build();

        var redirectProcessorResult = new RedirectProcessor(clock).process(searchParameters);

        assertTrue(redirectProcessorResult.isEmpty());
    }

    @Test
    @DisplayName("Return empty optional object if there expired redirect configuration")
    void resolveRedirectWhenRedirectConfigurationExpiredTest() {
        var searchParameters = SearchParameters.builder()
            .merchandisingConfiguration(
                MerchandisingConfiguration
                    .builder()
                    .redirectConfigurations(
                        Set.of(
                            RedirectConfiguration
                                .builder()
                                .activeFrom(clock.now().minus(2, ChronoUnit.DAYS).toEpochMilli())
                                .activeTo(clock.now().minus(1, ChronoUnit.DAYS).toEpochMilli())
                                .triggers(List.of())
                                .build()
                        )
                    )
                    .build()
            )
            .build();

        var redirectProcessorResult = new RedirectProcessor(clock).process(searchParameters);

        assertTrue(redirectProcessorResult.isEmpty());
    }

    @Test
    @DisplayName("Return empty optional object if there existing redirect but trigger do not match")
    void resolveRedirectWhenRedirectConfigurationExistButNotTriggeredTest() {
        var searchParameters = SearchParameters.builder()
            .query("not triggered query")
            .merchandisingConfiguration(
                MerchandisingConfiguration
                    .builder()
                    .redirectConfigurations(
                        Set.of(
                            RedirectConfiguration
                                .builder()
                                .triggers(List.of(
                                    new QueryPatternTrigger(QueryPatternTrigger.Type.CONTAINS, "test")
                                ))
                                .build()
                        )
                    )
                    .build()
            )
            .build();

        var redirectProcessorResult = new RedirectProcessor(clock).process(searchParameters);

        assertTrue(redirectProcessorResult.isEmpty());
    }

    @Test
    @DisplayName("Return redirect response if redirect triggered")
    void resolveRedirectWhenRedirectConfigurationExistAndTriggeredTest() {
        var searchParameters = SearchParameters.builder()
            .query("triggered test query")
            .merchandisingConfiguration(
                MerchandisingConfiguration
                    .builder()
                    .redirectConfigurations(
                        Set.of(
                            RedirectConfiguration
                                .builder()
                                .url("https://test.redirect.url")
                                .triggers(List.of(
                                    new QueryPatternTrigger(QueryPatternTrigger.Type.CONTAINS, "test")
                                ))
                                .build()
                        )
                    )
                    .build()
            )
            .build();

        var redirectProcessorResult = new RedirectProcessor(clock).process(searchParameters);

        assertTrue(redirectProcessorResult.isPresent());
        assertTrue(redirectProcessorResult.get().getRedirectUrl().isPresent());
        assertThat(redirectProcessorResult.get().getRedirectUrl().get()).isEqualTo("https://test.redirect.url");
    }

}
