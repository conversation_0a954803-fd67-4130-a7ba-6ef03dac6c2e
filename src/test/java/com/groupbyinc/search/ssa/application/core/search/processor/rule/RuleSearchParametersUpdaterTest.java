package com.groupbyinc.search.ssa.application.core.search.processor.rule;

import com.groupbyinc.search.ssa.api.dto.BiasDto;
import com.groupbyinc.search.ssa.api.dto.BiasingProfileDto;
import com.groupbyinc.search.ssa.api.dto.PinnedProductDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.application.builders.SearchParametersBuilder;
import com.groupbyinc.search.ssa.core.RecordLabel;
import com.groupbyinc.search.ssa.core.SearchMode;
import com.groupbyinc.search.ssa.core.biasing.Bias;
import com.groupbyinc.search.ssa.core.filter.AttributeFilter;
import com.groupbyinc.search.ssa.core.filter.ProductIdFilter;
import com.groupbyinc.search.ssa.core.filter.RangeFilter;
import com.groupbyinc.search.ssa.core.filter.ValueFilter;
import com.groupbyinc.search.ssa.core.navigation.Facet;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.rule.ExperimentVariant;
import com.groupbyinc.search.ssa.core.rule.PinnedProduct;
import com.groupbyinc.search.ssa.core.rule.PinnedRefinement;
import com.groupbyinc.search.ssa.core.rule.ProductIdsBucket;
import com.groupbyinc.search.ssa.core.rule.Refinement;
import com.groupbyinc.search.ssa.core.rule.RuleVariant;

import io.micronaut.core.propagation.PropagatedContext;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.groupbyinc.search.ssa.api.dto.BiasDto.StrengthDto.ABSOLUTE_INCREASE;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_AREA_ID;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_RULE_ID;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_RULE_TEMPLATE;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;
import static com.groupbyinc.search.ssa.stub.TestData.rule;
import static com.groupbyinc.search.ssa.stub.TestData.ruleAttributeFilters;
import static com.groupbyinc.search.ssa.stub.TestData.ruleBiasingProfileName;
import static com.groupbyinc.search.ssa.stub.TestData.ruleBoostedProductBuckets;
import static com.groupbyinc.search.ssa.stub.TestData.ruleBuriedProductBuckets;
import static com.groupbyinc.search.ssa.stub.TestData.ruleIncludedNavigations;
import static com.groupbyinc.search.ssa.stub.TestData.rulePinnedProducts;
import static com.groupbyinc.search.ssa.stub.TestData.rulePinnedRefinements;
import static com.groupbyinc.search.ssa.stub.TestData.ruleProductIdFilter;

import static org.assertj.core.api.Assertions.assertThat;

@MicronautTest
@DisplayName("SearchParametersBuilder Tests")
public class RuleSearchParametersUpdaterTest {

    @Inject SearchParametersBuilder searchParametersBuilder;
    @Inject RuleSearchParametersUpdater ruleSearchParametersUpdater;

    private PropagatedContext.Scope scope;

    @BeforeEach
    void setUp() {
        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    @Test
    @DisplayName("Override SearchParameters no rule | area default BP test")
    void updateSearchParametersByRuleAreaDefaultBPTest() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto.builder().build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.empty());

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(searchParameters.getBiasingProfile().getName().orElse(null)).isEqualTo("White");
    }

    @Test
    @DisplayName("Override SearchParameters no rule | BP by name from request test")
    void updateSearchParametersByRuleRequestBPNameTest() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto.builder().biasingProfile("Black").build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.empty());

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(searchParameters.getBiasingProfile().getName().orElse(null)).isEqualTo("Black");
    }

    @Test
    @DisplayName("Override SearchParameters no rule | BP by name from request test")
    void updateSearchParametersByRuleRequestBPTest() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto
                .builder()
                .biasing(new BiasingProfileDto(List.of(
                    new BiasDto("color", "red", ABSOLUTE_INCREASE, null, BiasDto.TypeDto.TEXTUAL, null)
                )))
                .build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.empty());

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(searchParameters.getBiasingProfile().getBiases().getFirst().getField()).isEqualTo("color");
        assertThat(searchParameters.getBiasingProfile().getBiases().getFirst().getContent()).isEqualTo("red");
        assertThat(
            searchParameters.getBiasingProfile().getBiases().getFirst().getStrength()
        ).isEqualTo(Bias.Strength.ABSOLUTE_INCREASE);
        assertThat(searchParameters.getBiasingProfile().getBiases().getFirst().getType()).isEqualTo(Bias.Type.TEXTUAL);
    }

    @Test
    @DisplayName("Override SearchParameters BP from rule test")
    void updateSearchParametersByRuleRuleBPTest() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto.builder().build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var rule = ruleBiasingProfileName("Black");
        var triggeredRule = new TriggeredRule(
            rule,
            rule.getTriggerSets().stream().findFirst().orElse(null),
            Optional.empty()
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.of(triggeredRule));

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(searchParameters.getBiasingProfile().getName().orElse(null)).isEqualTo("Black");
    }

    @Test
    @DisplayName("Override SearchParameters BP from rule test")
    void overrideSearchParametersRequestBpOverrideRuleBPTestByRule() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto
                .builder()
                .biasing(new BiasingProfileDto(List.of(
                    new BiasDto("color", "red", ABSOLUTE_INCREASE, null, BiasDto.TypeDto.TEXTUAL, null)
                )))
                .build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var rule = ruleBiasingProfileName("Black");
        var triggeredRule = new TriggeredRule(
            rule,
            rule.getTriggerSets().stream().findFirst().orElse(null),
            Optional.empty()
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.of(triggeredRule));

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(searchParameters.getBiasingProfile().getBiases().getFirst().getField()).isEqualTo("color");
        assertThat(searchParameters.getBiasingProfile().getBiases().getFirst().getContent()).isEqualTo("red");
        assertThat(
            searchParameters.getBiasingProfile().getBiases().getFirst().getStrength()
        ).isEqualTo(Bias.Strength.ABSOLUTE_INCREASE);
        assertThat(searchParameters.getBiasingProfile().getBiases().getFirst().getType()).isEqualTo(Bias.Type.TEXTUAL);
    }

    @Test
    @DisplayName("Override SearchParameters BP from rule test")
    void updateSearchParametersByRuleRuleVariantTest() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto.builder().build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );
        List<ExperimentVariant> variants = List.of(
            ExperimentVariant.builder()
                .name("variantName")
                .ruleVariant(RuleVariant.builder().build())
                .variantTriggerPercentage(100)
                .build()
        );

        var rule = rule(variants);
        var triggeredRule = new TriggeredRule(
            rule,
            rule.getTriggerSets().stream().findFirst().orElse(null),
            Optional.of("variantName")
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.of(triggeredRule));

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(searchParameters.getRuleVariantName()).isEqualTo("variantName");
    }

    @Test
    @DisplayName("Override SearchParameters boostedProductBuckets from rule test")
    void updateSearchParametersByRuleBoostedProductBucketsFromRuleTest() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto.builder().build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );


        var rule = ruleBoostedProductBuckets(List.of(new ProductIdsBucket(List.of("id"))));
        var triggeredRule = new TriggeredRule(
            rule,
            rule.getTriggerSets().stream().findFirst().orElse(null),
            Optional.empty()
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.of(triggeredRule));

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(
            searchParameters.getBoostedProductBuckets().getFirst().getProducts().getFirst()
        ).isEqualTo("id");
    }

    @Test
    @DisplayName("Override SearchParameters buriedProductBuckets from rule test")
    void updateSearchParametersByRuleBuriedProductBucketsFromRuleTest() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto.builder().build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var rule = ruleBuriedProductBuckets(List.of(new ProductIdsBucket(List.of("id"))));
        var triggeredRule = new TriggeredRule(
            rule,
            rule.getTriggerSets().stream().findFirst().orElse(null),
            Optional.empty()
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.of(triggeredRule));

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(
            searchParameters.getBuriedProductBuckets().getFirst().getProducts().getFirst()
        ).isEqualTo("id");
    }

    @Test
    @DisplayName("Override SearchParameters pinned products from rule test")
    void updateSearchParametersByRulePinnedProductsFromRuleTest() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto.builder()
                .pinnedProducts(List.of(
                    new PinnedProductDto(1, "4", true),
                    new PinnedProductDto(12, "2", true)
                ))
                .build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var rule = rulePinnedProducts(List.of(
            new PinnedProduct(1, "1", true),
            new PinnedProduct(5, "2", true),
            new PinnedProduct(8, "3", true)
        ));
        var triggeredRule = new TriggeredRule(
            rule,
            rule.getTriggerSets().stream().findFirst().orElse(null),
            Optional.empty()
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.of(triggeredRule));

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(searchParameters.getPinnedProducts()).containsExactlyInAnyOrder(
            new PinnedProduct(1, "4", true),
            new PinnedProduct(8, "3", true),
            new PinnedProduct(12, "2", true)
        );

        assertThat(searchParameters.getLabeledProductIds()).containsExactlyInAnyOrderEntriesOf(
            Map.of("4", RecordLabel.PINNED,
                "3", RecordLabel.PINNED,
                "2", RecordLabel.PINNED)
        );
    }

    @Test
    @DisplayName("Override SearchParameters included navigation from request test")
    void updateSearchParametersByRuleIncludedNavigationFromRequestTest() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto
                .builder()
                .includedNavigations(List.of("price"))
                .build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var rule = rule(ACTIVE_RULE_ID, "INACTIVE_RULE", ACTIVE_AREA_ID, ACTIVE_RULE_TEMPLATE);
        var triggeredRule = new TriggeredRule(
            rule,
            rule.getTriggerSets().stream().findFirst().orElse(null),
            Optional.empty()
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.of(triggeredRule));

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(searchParameters.getIncludedNavigations().getFirst()).isEqualTo("price");
    }

    @Test
    @DisplayName("Override SearchParameters included navigation from rule test")
    void updateSearchParametersByRuleIncludedNavigationFromRuleTest() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto
                .builder()
                .build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var rule = ruleIncludedNavigations(List.of("price"));
        var triggeredRule = new TriggeredRule(
            rule,
            rule.getTriggerSets().stream().findFirst().orElse(null),
            Optional.empty()
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.of(triggeredRule));

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(searchParameters.getIncludedNavigations().getFirst()).isEqualTo("price");
    }

    @Test
    @DisplayName("Override SearchParameters included navigation from rule and request test")
    void updateSearchParametersByRuleIncludedNavigationFromRuleAndRequestTest() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto
                .builder()
                .includedNavigations(List.of("price"))
                .build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var rule = ruleIncludedNavigations(List.of("color"));
        var triggeredRule = new TriggeredRule(
            rule,
            rule.getTriggerSets().stream().findFirst().orElse(null),
            Optional.empty()
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.of(triggeredRule));

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(searchParameters.getIncludedNavigations().getFirst()).isEqualTo("price");
        assertThat(searchParameters.getIncludedNavigations().get(1)).isEqualTo("color");
    }

    @Test
    @DisplayName("Override SearchParameters query without search filters test")
    void overrideSearchParametersQueryWithoutSearchFiltersTestByRule() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto
                .builder()
                .query("test")
                .build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var rule = rule(ACTIVE_RULE_ID, "INACTIVE_RULE", ACTIVE_AREA_ID, ACTIVE_RULE_TEMPLATE);
        var triggeredRule = new TriggeredRule(
            rule,
            rule.getTriggerSets().stream().findFirst().orElse(null),
            Optional.empty()
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.of(triggeredRule));

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(searchParameters.getQuery()).isEqualTo("test");
    }

    @Test
    @DisplayName("Override SearchParameters attribute filters test")
    void overrideSearchParametersSearchAttributeFiltersTestByRule() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto.builder().build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var rule = ruleAttributeFilters(List.of(
            new AttributeFilter(
                List.of(
                    ValueFilter
                        .builder()
                        .type(ValueFilter.ValueFilterType.TEXTUAL)
                        .field("field")
                        .value("value")
                        .build()
                ),
                List.of(new RangeFilter("price", new Range(0d, 100d, "", false)))
            )
        ));
        var triggeredRule = new TriggeredRule(
            rule,
            rule.getTriggerSets().stream().findFirst().orElse(null),
            Optional.empty()
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.of(triggeredRule));

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(
            searchParameters.getAttributeFilters().getFirst().valueFilters().getFirst().getField()
        ).isEqualTo("field");
        assertThat(
            searchParameters.getAttributeFilters().getFirst().valueFilters().getFirst().getValue()
        ).isEqualTo("value");
        assertThat(
            searchParameters.getAttributeFilters().getFirst().rangeFilters().getFirst().getField()
        ).isEqualTo("price");
        assertThat(
            searchParameters.getAttributeFilters().getFirst().rangeFilters().getFirst().getRange().low()
        ).isEqualTo(0);
        assertThat(
            searchParameters.getAttributeFilters().getFirst().rangeFilters().getFirst().getRange().high()
        ).isEqualTo(100);
    }

    @Test
    @DisplayName("Override SearchParameters product id filter test")
    void overrideSearchParametersSearchProductIdFilterTestByRule() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto.builder().build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var rule = ruleProductIdFilter(new ProductIdFilter(List.of("included"), List.of("excluded")));
        var triggeredRule = new TriggeredRule(
            rule,
            rule.getTriggerSets().stream().findFirst().orElse(null),
            Optional.empty()
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.of(triggeredRule));

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(searchParameters.getProductIdFilter().includedProductIds().getFirst()).isEqualTo("included");
        assertThat(searchParameters.getProductIdFilter().excludedProductIds().getFirst()).isEqualTo("excluded");
    }

    @Test
    @DisplayName("Override SearchParameters pinned refinements test")
    void overrideSearchParametersSearchPinnedRefinementsTestByRule() {
        var searchParameters = searchParametersBuilder.buildSearchParameters(
            SearchRequestDto.builder().build(),
            SearchMode.PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var rule = rulePinnedRefinements(List.of(new PinnedRefinement("color", List.of(new Refinement("red", 1)))));
        var triggeredRule = new TriggeredRule(
            rule,
            rule.getTriggerSets().stream().findFirst().orElse(null),
            Optional.empty()
        );

        var ruleProcessorResult = new RuleProcessorResult(1, 1, Duration.ZERO, Optional.of(triggeredRule));

        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleProcessorResult);

        assertThat(searchParameters.getPinnedRefinements().getFirst().navigation()).isEqualTo("color");
        assertThat(
            searchParameters.getPinnedRefinements().getFirst().refinements().getFirst().value()
        ).isEqualTo("red");
        assertThat(
            searchParameters.getPinnedRefinements().getFirst().refinements().getFirst().priority()
        ).isEqualTo(1);
    }

}
