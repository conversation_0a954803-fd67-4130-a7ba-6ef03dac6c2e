package com.groupbyinc.search.ssa.application.builders;

import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.rule.Overwrites;

import io.micronaut.core.propagation.PropagatedContext;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.Set;

import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_AREA_ID;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_REDIRECT;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_RULE;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_SITE_FILTER;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_ZONE;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.BRANDS;
import static com.groupbyinc.search.ssa.stub.TestData.BRAND_PINNED_REFINEMENT;
import static com.groupbyinc.search.ssa.stub.TestData.COLOR_FAMILIES;
import static com.groupbyinc.search.ssa.stub.TestData.PRICE;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTION;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;
import static com.groupbyinc.search.ssa.stub.TestData.ROCKET_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;
import static com.groupbyinc.search.ssa.stub.TestData.rangeNavigation;
import static com.groupbyinc.search.ssa.stub.TestData.rule;
import static com.groupbyinc.search.ssa.stub.TestData.valueNavigation;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

@MicronautTest
@DisplayName("SearchParametersPreparer Tests")
class SearchParametersPreparerTest {

    @Inject SearchParametersPreparer searchParametersPreparer;

    private PropagatedContext.Scope scope;

    @BeforeEach
    void setUp() {
        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    @Test
    @DisplayName("Prepares searches parameters success")
    void prepareSearchParameters() {
        var request = SearchRequestDto
            .builder()
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();
        var builder = SearchParameters.builder();

        searchParametersPreparer.prepare(request, builder);
        var result = builder.build();

        // Verify
        assertNotNull(result);

        var merchandisingConfig = result.getMerchandisingConfiguration();
        assertEquals(ACTIVE_AREA_ID, merchandisingConfig.areaConfiguration().id());
        assertEquals(Set.of(ACTIVE_REDIRECT), merchandisingConfig.redirectConfigurations());

        var navigations = merchandisingConfig.navigationConfigurations();
        assertTrue(navigations.contains(rangeNavigation(3, PRICE, ACTIVE_AREA_ID, 3)));
        assertTrue(navigations.contains(valueNavigation(2, COLOR_FAMILIES, ACTIVE_AREA_ID, 2)));
        assertTrue(
            navigations.contains(
                valueNavigation(1, BRANDS, ACTIVE_AREA_ID, 1, List.of(BRAND_PINNED_REFINEMENT))
            )
        );

        assertEquals(Set.of(ACTIVE_RULE), merchandisingConfig.ruleConfigurations());

        assertTrue(merchandisingConfig.getBiasingProfileByName("White").isPresent());
        assertTrue(merchandisingConfig.getBiasingProfileByName("Black").isPresent());
        assertTrue(merchandisingConfig.getAreaDefaultBiasingProfile().isPresent());
        assertEquals("White", merchandisingConfig.getAreaDefaultBiasingProfile().get().getName().orElse(null));


        var attributes = merchandisingConfig.attributeConfigurations();

        assertTrue(attributes.containsKey(COLOR_FAMILIES));

        assertFalse(merchandisingConfig.features().enableTrafficSplit());

        assertNotNull(merchandisingConfig.instantSupplier());

        assertEquals(ACTIVE_SITE_FILTER, merchandisingConfig.siteFilter());

        var zones = merchandisingConfig.zones();
        assertTrue(zones.contains(ACTIVE_ZONE));

        assertNotNull(merchandisingConfig.features());

        var pinnedRefinements = result.getPinnedRefinements();
        assertEquals(1, pinnedRefinements.size());
        assertEquals(BRANDS, pinnedRefinements.getFirst().navigation());
        assertEquals(1, pinnedRefinements.getFirst().refinements().size());
        assertEquals("Nike", pinnedRefinements.getFirst().refinements().getFirst().value());
        assertEquals(1, pinnedRefinements.getFirst().refinements().getFirst().priority());

        assertEquals(false, result.getIncludeExpandedResults());
        assertEquals(300, result.getFacetLimit());
    }

    @Test
    @DisplayName("Prepares searches parameters but area is missing")
    void prepareSearchParametersButAreaIsMissing() {
        scope = PropagatedContext.getOrEmpty().plus(createContext(ROCKET_MERCHANDISER)).propagate();

        var request = SearchRequestDto
            .builder()
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();
        var builder = SearchParameters.builder();

        // Act and verify
        assertThrows(
            NoSuchElementException.class,
            () -> searchParametersPreparer.prepare(request, builder)
        );
    }

    @Test
    @DisplayName("Returns rule for context with overwrites")
    void getRuleForContextWithOverwrites() {
        var request = SearchRequestDto
            .builder()
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .overwrites(new Overwrites(Set.of(rule(1, "NewName", ACTIVE_AREA_ID, null))))
            .build();
        var builder = SearchParameters.builder();

        searchParametersPreparer.prepare(request, builder);
        var result = builder.build();

        // Verify
        assertNotNull(result);

        var merchandisingConfig = result.getMerchandisingConfiguration();
        assertEquals(1, merchandisingConfig.ruleConfigurations().size());

        var rule = merchandisingConfig.ruleConfigurations().stream().findFirst().orElse(null);
        assertNotNull(rule);

        assertNull(rule.getTemplate());
        assertEquals("NewName", rule.getName());
    }

}
