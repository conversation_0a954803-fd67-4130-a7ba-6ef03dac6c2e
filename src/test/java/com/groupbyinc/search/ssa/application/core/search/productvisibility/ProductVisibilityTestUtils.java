package com.groupbyinc.search.ssa.application.core.search.productvisibility;

import com.groupbyinc.proto.commandcenter.config.AttributeMessage;
import com.groupbyinc.proto.commandcenter.config.AttributeMessage.AttributeType;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.RecordLabel;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.rule.ProductVisibilityBias;

import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

public class ProductVisibilityTestUtils {
    static final Predicate<Integer> ALL_TRUE = i -> true;
    static final Predicate<Integer> ALL_FALSE = i -> false;
    static final Predicate<Integer> EVERY_SECOND = i -> i % 2 == 0; // [0, 2, 4, ...]
    static final Predicate<Integer> EVERY_SECOND_OFFSET = i -> (i+1) % 2 == 0; // [1, 3, 5, ...]
    static final Predicate<Integer> EVERY_FIFTH = i -> i % 4 == 0; // [0, 4, 8, ...]
    static final Predicate<Integer> EVERY_TENTH = i -> i % 9 == 0; // [0, 9, 18, ...]
    static final Predicate<Integer> ON_1_8 = i -> i <= 7;
    static final Predicate<Integer> ON_1_5 = i -> i <= 4;
    static final Predicate<Integer> ON_19_20 = i -> i >= 19 && i <= 20;
    static final Predicate<Integer> ON_32_40 = i -> i >= 32 && i <= 39;

    /**
     * simplification, attribute extraction logic tested in AttributeUtilsTest
     */
    static final String ATTRIBUTE = "attributes.boosting";
    static final String ATTRIBUTE_NESTED = "boosting";
    static final String STR_VAL_MATCH = "yes";
    static final String STR_VAL_NOT_MATCH = "no";
    static final Double NUM_VAL_MATCH = 1.;
    static final Double NUM_VAL_NOT_MATCH = 0.;

    static List<ProductVisibility> visibilities(int size,
                                                Predicate<Integer> matchingPredicate,
                                                Predicate<Integer> lockedPredicate) {
        return IntStream.range(0, size)
            .mapToObj(i -> new ProductVisibility(matchingPredicate.test(i), lockedPredicate.test(i), String.valueOf(i)))
            .toList();
    }

    static List<ProductVisibility> visibilities(int size, Predicate<Integer> matchingPredicate) {
        return visibilities(size, matchingPredicate, ALL_FALSE);
    }

    static List<Record> records(int size,
                                AttributeType type,
                                Predicate<Integer> matchingPredicate,
                                Predicate<Integer> lockedPredicate) {
        var matching = type == AttributeType.NUMERICAL ? NUM_VAL_MATCH : STR_VAL_MATCH;
        var notMatching = type == AttributeType.NUMERICAL ? NUM_VAL_NOT_MATCH : STR_VAL_NOT_MATCH;
        return IntStream.range(0, size)
            .mapToObj(i ->
                record(
                    String.valueOf(i),
                    matchingPredicate.test(i) ? matching : notMatching,
                    lockedPredicate.test(i)
                )
            ).toList();
    }

    static List<Record> records(int size, AttributeType type, Predicate<Integer> matchingPredicate) {
        return records(size, type, matchingPredicate, ALL_FALSE);
    }

    static Record record(String id, Object boostingAttrValue) {
        return record(id, boostingAttrValue, false);
    }

    static Record record(String id, Object boostingAttrValue, boolean pinned) {
        var meta = Map.of("attributes", (Object) Map.of(ATTRIBUTE_NESTED, boostingAttrValue));
        return new Record(
            id,
            id,
            id,
            "http://example.com",
            "title" + id,
            "productsClothing",
            meta,
            pinned ? RecordLabel.PINNED : null,
            null,
            null,
            null
        );
    }

    static ProductVisibilityBias biasStrAttr(Double multi,
                                             Double offset,
                                             Double cap) {
        return new ProductVisibilityBias(ATTRIBUTE, List.of(STR_VAL_MATCH), List.of(), multi, offset, cap);
    }

    static ProductVisibilityBias biasNumAttr(Double multi,
                                             Double offset,
                                             Double cap) {
        return new ProductVisibilityBias(ATTRIBUTE, List.of(), List.of(NUM_VAL_MATCH), multi, offset, cap);
    }

    static AttributeConfiguration attrConfig(AttributeMessage.AttributeType type) {
        return AttributeConfiguration.builder().key(ATTRIBUTE).path(ATTRIBUTE).type(type).build();
    }

    static Score score(Double matching,
                       Double notMatching,
                       Double multi,
                       Double cap,
                       Double maxPossible,
                       Double maxAllowed,
                       Double target) {
        return new Score(matching + notMatching, matching, notMatching, multi, cap, maxPossible, maxAllowed, target);
    }

    static Score score(Double matching,
                       Double notMatching,
                       Double multi,
                       Double cap,
                       Double maxPossible,
                       Double maxAllowed,
                       Double target,
                       Double newScore) {
        var s = new Score(matching + notMatching, matching, notMatching, multi, cap, maxPossible, maxAllowed, target);
        s.newScore = newScore;
        s.newNotMatchingScore = s.totalScore - newScore;
        return s;
    }

    static void assertScoresEqual(Score expect, Score actual) {
        if (expect == null) {
            assertNull(actual);
            return;
        }
        assertEquals(expect.getTotalScore(), actual.getTotalScore(), "getTotalScore");
        assertEquals(expect.getScore(), actual.getScore(), "getScore");
        assertEquals(expect.getNotMatchingScore(), actual.getNotMatchingScore(), "getNotMatchingScore");
        assertEquals(expect.getAppliedMultiplier(), actual.getAppliedMultiplier(), "getAppliedMultiplier");
        assertEquals(expect.getVisibilityCap(), actual.getVisibilityCap(), "getVisibilityCap");
        assertEquals(expect.getMaxPossibleScore(), actual.getMaxPossibleScore(), "getMaxPossibleScore");
        assertEquals(expect.getMaxAllowedScore(), actual.getMaxAllowedScore(), "getMaxAllowedScore");
        assertEquals(expect.getTargetScore(), actual.getTargetScore(), "getTargetScore");
        assertEquals(expect.getNewScore(), actual.getNewScore(), "getNewScore");
        assertEquals(expect.getNewNotMatchingScore(), actual.getNewNotMatchingScore(), "getNewNotMatchingScore");
    }
}
