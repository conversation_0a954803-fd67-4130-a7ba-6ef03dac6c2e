package com.groupbyinc.search.ssa.application.builders;

import com.groupbyinc.search.ssa.api.dto.BiasDto;
import com.groupbyinc.search.ssa.api.dto.BiasingProfileDto;
import com.groupbyinc.search.ssa.api.dto.CustomParameterDto;
import com.groupbyinc.search.ssa.api.dto.NavigationTypeDto;
import com.groupbyinc.search.ssa.api.dto.PinnedProductDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SelectedRefinementDto;
import com.groupbyinc.search.ssa.api.dto.SortDto;
import com.groupbyinc.search.ssa.core.Order;
import com.groupbyinc.search.ssa.core.Pagination;
import com.groupbyinc.search.ssa.core.SearchMode;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SpellCorrectionMode;
import com.groupbyinc.search.ssa.core.SponsoredRecordsRequest;
import com.groupbyinc.search.ssa.core.biasing.Bias;
import com.groupbyinc.search.ssa.core.navigation.Facet;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.rule.Overwrites;
import com.groupbyinc.utils.validation.ValidationException;

import io.micronaut.core.propagation.PropagatedContext;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Set;

import static com.groupbyinc.search.ssa.api.dto.BiasDto.StrengthDto.ABSOLUTE_INCREASE;
import static com.groupbyinc.search.ssa.core.SearchMode.FACETED_SEARCH;
import static com.groupbyinc.search.ssa.core.SearchMode.PRODUCT_SEARCH;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_RULE;
import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_SITE_FILTER_NAME;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

@MicronautTest
@DisplayName("SearchParametersBuilder Tests")
public class SearchParametersBuilderTest {

    @Inject
    SearchParametersBuilder searchParametersBuilder;

    private PropagatedContext.Scope scope;

    @BeforeEach
    void setUp() {
        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    @Test
    @DisplayName("SearchParameters has sensible defaults")
    void hasSensibleDefaults() {
        var searchParameters = SearchParameters.builder().build();

        assertThat(searchParameters.getQuery()).isEqualTo("");
        assertThat(searchParameters.getPreFilter()).isEqualTo("");
        assertThat(searchParameters.getInventoryStoreId()).isEqualTo("");
        assertThat(searchParameters.getSpellCorrectionMode()).isEqualTo(SpellCorrectionMode.AUTO);
        assertThat(searchParameters.getCustomParameters()).isEmpty();
        assertThat(searchParameters.getPagination()).isEqualTo(Pagination.defaultPagination());
        assertThat(searchParameters.getOriginalPagination()).isEqualTo(Pagination.defaultPagination());
        assertThat(searchParameters.getRefinements()).isNotNull();
        assertThat(searchParameters.getRefinements()).isNotNull();
        assertThat(searchParameters.getSorts()).isNotNull();
        assertThat(searchParameters.getIncludedNavigations()).isNotNull();
        assertThat(searchParameters.getExcludedNavigations()).isNotNull();
        assertThat(searchParameters.getValueFilters()).isNotNull();
        assertThat(searchParameters.getRangeFilters()).isNotNull();
        assertThat(searchParameters.getAttributeFilters()).isNotNull();
        assertThat(searchParameters.getVariantRollupKeys()).isNotNull();
        assertThat(searchParameters.getBoostedProductBuckets()).isNotNull();
        assertThat(searchParameters.getBuriedProductBuckets()).isNotNull();
        assertThat(searchParameters.getPageCategories()).isNotNull();
        assertThat(searchParameters.getPinnedRefinements()).isNotNull();
        assertThat(searchParameters.getPinnedProducts()).isNotNull();
    }

    @Test
    @DisplayName("Build SearchParameters query test")
    void buildSearchParametersQueryTest() {
        var searchRequest = SearchRequestDto.builder()
            .query("test")
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertThat(searchParameters.getQuery()).isEqualTo("test");

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertThat(searchParameters.getQuery()).isEqualTo("test");
    }

    @Test
    @DisplayName("Build SearchParameters pagination test")
    void buildSearchParametersPaginationTest() {
        var searchRequest = SearchRequestDto.builder()
            .skip(5L)
            .pageSize(10)
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertThat(searchParameters.getPagination().getSize()).isEqualTo(10);
        assertThat(searchParameters.getPagination().getOffset()).isEqualTo(5);
        assertThat(searchParameters.getOriginalPagination().getSize()).isEqualTo(10);
        assertThat(searchParameters.getOriginalPagination().getOffset()).isEqualTo(5);

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertThat(searchParameters.getPagination().getSize()).isEqualTo(10);
        assertThat(searchParameters.getPagination().getOffset()).isEqualTo(5);
        assertThat(searchParameters.getOriginalPagination().getSize()).isEqualTo(10);
        assertThat(searchParameters.getOriginalPagination().getOffset()).isEqualTo(5);
    }

    @Test
    @DisplayName("Build SearchParameters spell correction test")
    void buildSearchParametersSpellCorrectionTest() {
        var searchRequest = SearchRequestDto.builder()
            .spellCorrectionMode(SpellCorrectionMode.AUTO)
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertThat(searchParameters.getSpellCorrectionMode()).isEqualTo(SpellCorrectionMode.AUTO);

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertThat(searchParameters.getSpellCorrectionMode()).isEqualTo(SpellCorrectionMode.AUTO);
    }

    @Test
    @DisplayName("Build SearchParameters biasing profile test")
    void buildSearchParametersBiasingProfileTest() {
        var searchRequest = SearchRequestDto.builder()
            .biasing(new BiasingProfileDto(List.of(
                new BiasDto("color", "red", ABSOLUTE_INCREASE, null, BiasDto.TypeDto.TEXTUAL, null)
            )))
            .biasingProfile("test")
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertThat(searchParameters.getBiasingProfileName()).isEqualTo("test");
        assertThat(searchParameters.getBiasingProfile().getBiases().getFirst().getField()).isEqualTo("color");
        assertThat(searchParameters.getBiasingProfile().getBiases().getFirst().getContent()).isEqualTo("red");
        assertThat(searchParameters.getBiasingProfile().getBiases().getFirst().getType()).isEqualTo(Bias.Type.TEXTUAL);
        assertThat(
            searchParameters.getBiasingProfile().getBiases().getFirst().getStrength()
        ).isEqualTo(Bias.Strength.ABSOLUTE_INCREASE);

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertThat(searchParameters.getBiasingProfileName()).isEqualTo("test");
        assertThat(searchParameters.getBiasingProfile().getBiases().getFirst().getField()).isEqualTo("color");
        assertThat(searchParameters.getBiasingProfile().getBiases().getFirst().getContent()).isEqualTo("red");
        assertThat(searchParameters.getBiasingProfile().getBiases().getFirst().getType()).isEqualTo(Bias.Type.TEXTUAL);
        assertThat(
            searchParameters.getBiasingProfile().getBiases().getFirst().getStrength()
        ).isEqualTo(Bias.Strength.ABSOLUTE_INCREASE);
    }

    @Test
    @DisplayName("Build SearchParameters search mode test")
    void buildSearchParametersSearchModeTest() {
        var searchRequest = SearchRequestDto.builder().build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertThat(searchParameters.getSearchMode()).isEqualTo(PRODUCT_SEARCH);

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertThat(searchParameters.getSearchMode()).isEqualTo(FACETED_SEARCH);
    }

    @Test
    @DisplayName("Build SearchParameters facet limit test")
    void buildSearchFacetLimitTest() {
        var searchRequest = SearchRequestDto
            .builder()
            .facetLimit(150)
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertThat(searchParameters.getFacetLimit()).isEqualTo(150);

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertThat(searchParameters.getFacetLimit()).isEqualTo(150);
    }

    @Test
    @DisplayName("Build SearchParameters site and pre filter test")
    void buildSearchSiteAndPreFilterTest() {
        var preFilter = "preFilter";
        var searchRequest = SearchRequestDto
            .builder()
            .site(ACTIVE_SITE_FILTER_NAME)
            .preFilter(preFilter)
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertThat(searchParameters.getPreFilter()).isEqualTo(preFilter);
        assertThat(searchParameters.getSiteFilterName()).isEqualTo(ACTIVE_SITE_FILTER_NAME);

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertThat(searchParameters.getPreFilter()).isEqualTo(preFilter);
        assertThat(searchParameters.getSiteFilterName()).isEqualTo(ACTIVE_SITE_FILTER_NAME);
    }

    @Test
    @DisplayName("Build SearchParameters page categories test")
    void buildSearchPageCategoriesTest() {
        var searchRequest = SearchRequestDto
            .builder()
            .pageCategories(List.of("Man > Shoes"))
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertThat(searchParameters.getPageCategories().getFirst()).isEqualTo("Man > Shoes");

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertThat(searchParameters.getPageCategories().getFirst()).isEqualTo("Man > Shoes");
    }

    @Test
    @DisplayName("Build SearchParameters custom parameters test")
    void buildSearchCustomParametersTest() {
        var searchRequest = SearchRequestDto
            .builder()
            .customUrlParams(List.of(new CustomParameterDto("key", "value")))
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertThat(searchParameters.getCustomParameters().getFirst().key()).isEqualTo("key");
        assertThat(searchParameters.getCustomParameters().getFirst().value()).isEqualTo("value");

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertThat(searchParameters.getCustomParameters().getFirst().key()).isEqualTo("key");
        assertThat(searchParameters.getCustomParameters().getFirst().value()).isEqualTo("value");
    }

    @Test
    @DisplayName("Build SearchParameters sorts test")
    void buildSearchSortsTest() {
        var searchRequest = SearchRequestDto
            .builder()
            .sorts(List.of(new SortDto("key", SortDto.OrderDto.Ascending)))
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertThat(searchParameters.getSorts().getFirst().getField()).isEqualTo("key");
        assertThat(searchParameters.getSorts().getFirst().getOrder()).isEqualTo(Order.ASCENDING);

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertThat(searchParameters.getSorts().getFirst().getField()).isEqualTo("key");
        assertThat(searchParameters.getSorts().getFirst().getOrder()).isEqualTo(Order.ASCENDING);
    }

    @Test
    @DisplayName("Build SearchParameters inventory store ID test")
    void buildSearchInventoryStoreIdTest() {
        var searchRequest = SearchRequestDto
            .builder()
            .inventoryStoreId("testStoreId")
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertThat(searchParameters.getInventoryStoreId()).isEqualTo("testStoreId");

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertThat(searchParameters.getInventoryStoreId()).isEqualTo("testStoreId");
    }

    @Test
    @DisplayName("Build SearchParameters excluded navigations test")
    void buildSearchExcludedNavigationsTest() {
        var searchRequest = SearchRequestDto
            .builder()
            .excludedNavigations(Set.of("color"))
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertTrue(searchParameters.getExcludedNavigations().contains("color"));

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertTrue(searchParameters.getExcludedNavigations().contains("color"));
    }

    @Test
    @DisplayName("Build SearchParameters included navigations test")
    void buildSearchIncludedNavigationsTest() {
        var searchRequest = SearchRequestDto
            .builder()
            .includedNavigations(List.of("color"))
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertTrue(searchParameters.getIncludedNavigations().contains("color"));

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertTrue(searchParameters.getIncludedNavigations().contains("color"));
    }

    @Test
    @DisplayName("Build SearchParameters included and excluded navigations test")
    void buildSearchIncludedAndExcludedNavigationsTest() {
        var searchRequest = SearchRequestDto
            .builder()
            .includedNavigations(List.of("color"))
            .excludedNavigations(Set.of("color"))
            .build();

        assertThrows(
            ValidationException.class,
            () -> searchParametersBuilder
                .buildSearchParameters(searchRequest, PRODUCT_SEARCH, Facet.EMPTY)
        );

        assertThrows(
            ValidationException.class,
            () -> searchParametersBuilder
                .buildSearchParameters(searchRequest, FACETED_SEARCH, Facet.EMPTY));
    }

    @Test
    @DisplayName("Build SearchParameters variant rollup keys test")
    void buildSearchVariantRollupKeysTest() {
        var searchRequest = SearchRequestDto
            .builder()
            .variantRollupKeys(List.of("color"))
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertTrue(searchParameters.getVariantRollupKeys().contains("color"));

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertTrue(searchParameters.getVariantRollupKeys().contains("color"));
    }

    @Test
    @DisplayName("Build SearchParameters include expanded results test")
    void buildSearchIncludeExpandedResultsTest() {
        var searchRequest = SearchRequestDto
            .builder()
            .includeExpandedResults(true)
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertTrue(searchParameters.getIncludeExpandedResults());

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertTrue(searchParameters.getIncludeExpandedResults());
    }

    @Test
    @DisplayName("Build SearchParameters pin unexpanded results test")
    void buildSearchPinUnexpandedResultsTest() {
        var searchRequest = SearchRequestDto
            .builder()
            .pinUnexpandedResults(true)
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertTrue(searchParameters.getPinUnexpandedResults());

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertTrue(searchParameters.getPinUnexpandedResults());
    }

    @Test
    @DisplayName("Build SearchParameters response mask test")
    void buildSearchResponseMaskTest() {
        var searchRequest = SearchRequestDto
            .builder()
            .responseMask(List.of("priceInfo.price"))
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertTrue(searchParameters.getResponseMask().contains("priceInfo.price"));

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertTrue(searchParameters.getResponseMask().contains("priceInfo.price"));
    }

    @Test
    @DisplayName("Build SearchParameters top sort enabled test")
    void buildSearchTopSortEnabledTest() {
        var searchRequest = SearchRequestDto
            .builder()
            .enableTopsort(true)
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertTrue(searchParameters.getTopSortEnabled());

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertTrue(searchParameters.getTopSortEnabled());
    }

    @Test
    @DisplayName("Build SearchParameters facet test")
    void buildSearchFacetTest() {
        var searchRequest = SearchRequestDto.builder().build();

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequest,
            PRODUCT_SEARCH,
            new Facet("prefix", "contains", "name", NavigationType.VALUE, "brand"));

        assertThat(searchParameters.getFacet().prefix()).isEqualTo("prefix");
        assertThat(searchParameters.getFacet().type()).isEqualTo(NavigationType.VALUE);
        assertThat(searchParameters.getFacet().displayName()).isEqualTo("name");
        assertThat(searchParameters.getFacet().contains()).isEqualTo("contains");
        assertThat(searchParameters.getFacet().navigationName()).isEqualTo("brand");

        searchParameters = searchParametersBuilder.buildSearchParameters(
            searchRequest,
            FACETED_SEARCH,
            new Facet("prefix", "contains", "name", NavigationType.VALUE, "brand"));

        assertThat(searchParameters.getFacet().prefix()).isEqualTo("prefix");
        assertThat(searchParameters.getFacet().type()).isEqualTo(NavigationType.VALUE);
        assertThat(searchParameters.getFacet().displayName()).isEqualTo("name");
        assertThat(searchParameters.getFacet().contains()).isEqualTo("contains");
        assertThat(searchParameters.getFacet().navigationName()).isEqualTo("brand");
    }

    @Test
    @DisplayName("Build SearchParameters pinned products test")
    void buildSearchPinnedProductsTest() {
        var searchRequest = SearchRequestDto.builder()
            .pinnedProducts(List.of(new PinnedProductDto(1, "id", false)))
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertThat(searchParameters.getPinnedProducts().getFirst().position()).isEqualTo(1);
        assertThat(searchParameters.getPinnedProducts().getFirst().productId()).isEqualTo("id");

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertThat(searchParameters.getPinnedProducts().getFirst().position()).isEqualTo(1);
        assertThat(searchParameters.getPinnedProducts().getFirst().productId()).isEqualTo("id");
    }

    @Test
    @DisplayName("Build SearchParameters sponsored records test")
    void buildSearchSponsoredRecordsTest() {
        var searchRequest = SearchRequestDto.builder()
            .sponsoredRecords(new SponsoredRecordsRequest(1, List.of(1)))
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertThat(searchParameters.getSponsoredRecordsRequest().count()).isEqualTo(1);
        assertThat(searchParameters.getSponsoredRecordsRequest().positions().getFirst()).isEqualTo(1);

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertThat(searchParameters.getSponsoredRecordsRequest().count()).isEqualTo(1);
        assertThat(searchParameters.getSponsoredRecordsRequest().positions().getFirst()).isEqualTo(1);
    }

    @Test
    @DisplayName("Build SearchParameters refinements test")
    void buildSearchRefinementsTest() {
        var searchRequest = SearchRequestDto.builder()
            .refinements(
                List.of(
                    new SelectedRefinementDto("color", NavigationTypeDto.Value, "red", null, null, null, true),
                    new SelectedRefinementDto("price", NavigationTypeDto.Range, null, 0d, 100d, null, true)
                )
            )
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertThat(searchParameters.getRefinements().getFirst().getField()).isEqualTo("color");
        assertThat(searchParameters.getRefinements().getFirst().getType()).isEqualTo(NavigationType.VALUE);
        assertThat(searchParameters.getRefinements().getFirst().getValue()).isEqualTo("red");
        assertThat(searchParameters.getRefinements().get(1).getField()).isEqualTo("price");
        assertThat(searchParameters.getRefinements().get(1).getType()).isEqualTo(NavigationType.RANGE);
        assertThat(searchParameters.getRefinements().get(1).getRange().low()).isEqualTo(0);
        assertThat(searchParameters.getRefinements().get(1).getRange().high()).isEqualTo(100);

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertThat(searchParameters.getRefinements().getFirst().getField()).isEqualTo("color");
        assertThat(searchParameters.getRefinements().getFirst().getType()).isEqualTo(NavigationType.VALUE);
        assertThat(searchParameters.getRefinements().getFirst().getValue()).isEqualTo("red");
        assertThat(searchParameters.getRefinements().get(1).getField()).isEqualTo("price");
        assertThat(searchParameters.getRefinements().get(1).getType()).isEqualTo(NavigationType.RANGE);
        assertThat(searchParameters.getRefinements().get(1).getRange().low()).isEqualTo(0);
        assertThat(searchParameters.getRefinements().get(1).getRange().high()).isEqualTo(100);
    }

    @Test
    @DisplayName("Build SearchParameters overwrites test")
    void buildSearchOverwritesTest() {
        var searchRequest = SearchRequestDto.builder()
            .overwrites(new Overwrites(Set.of(ACTIVE_RULE)))
            .build();

        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);

        assertTrue(searchParameters.getOverwrites().rules().contains(ACTIVE_RULE));

        searchParameters = buildSearchParameters(searchRequest, FACETED_SEARCH);

        assertTrue(searchParameters.getOverwrites().rules().contains(ACTIVE_RULE));
    }

    @Test
    @DisplayName("Build SearchParameters part number fallback test")
    void buildSearchPartNumberFallbackTest() {
        var searchRequest = SearchRequestDto.builder()
            .enablePartNumberFallback(false)
            .build();
        var searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);
        assertThat(searchParameters.isPartNumberFallbackEnabled()).isFalse();

        searchRequest = SearchRequestDto.builder()
            .enablePartNumberFallback(true)
            .build();
        searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);
        assertThat(searchParameters.isPartNumberFallbackEnabled()).isTrue();

        searchRequest = SearchRequestDto.builder().build();
        searchParameters = buildSearchParameters(searchRequest, PRODUCT_SEARCH);
        assertThat(searchParameters.isPartNumberFallbackEnabled()).isTrue();
    }

    private SearchParameters buildSearchParameters(SearchRequestDto searchRequest, SearchMode searchMode) {
        return searchParametersBuilder.buildSearchParameters(
            searchRequest,
            searchMode,
            Facet.EMPTY);
    }
}
