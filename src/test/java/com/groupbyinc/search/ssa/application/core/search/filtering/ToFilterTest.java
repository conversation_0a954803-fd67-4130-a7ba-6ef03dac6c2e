package com.groupbyinc.search.ssa.application.core.search.filtering;

import com.groupbyinc.search.ssa.application.core.search.filtering.filter.ToFilter;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public abstract class ToFilterTest<T> {

    @ParameterizedTest(name = "{0} is equivalent to {1}")
    @MethodSource("canBuildAValidFilterArguments")
    @DisplayName("Can build a valid filter string")
    void canBuildAValidFilter(ToFilter<T> toFilter, T expected) {
        assertThat(toFilter.toFilter()).isEqualTo(expected);
    }

    public abstract Stream<Arguments> canBuildAValidFilterArguments();

}
