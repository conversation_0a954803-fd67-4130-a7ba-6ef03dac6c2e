package com.groupbyinc.search.ssa.application.core.search.strategy.partnumber;

import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.partnumber.PartNumberSearchService;
import com.groupbyinc.search.ssa.productcatalog.ProductCatalogService;

import io.micronaut.core.propagation.PropagatedContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.application.core.search.strategy.partnumber.PartNumberSearchStrategy.NO_PN_ATTRIBUTES_WARNING;
import static com.groupbyinc.search.ssa.application.core.search.strategy.partnumber.PartNumberSearchStrategy.PN_DISABLED_BY_FF_WARNING;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.PART_NUMBER_INDEX_DELAY_SECONDS;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_PART_NUMBER_SEARCH;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.DEFAULT_CONTEXT;
import static com.groupbyinc.search.ssa.stub.TestData.METADATA;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;
import static com.groupbyinc.search.ssa.util.Constants.PART_NUMBER_SEARCHABLE;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mock.Strictness.LENIENT;

@ExtendWith(MockitoExtension.class)
class PartNumberSearchStrategyTest {

    private static final String QUERY = "query";
    private static final Integer COLLECTION_ID = 1;
    private static final String ATTRIBUTE = "attribute";
    private static final String EMPTY_QUERY = "";
    private final static int DEFAULT_INDEX_DELAY = 0;

    @Mock
    PartNumberSearchService partNumberSearchService;

    @Mock
    ProductCatalogService productCatalogService;

    @Mock(strictness = LENIENT)
    FeaturesManager featuresManager;

    private PropagatedContext.Scope scope;

    private PartNumberSearchStrategy partNumberSearchStrategy;

    @BeforeEach
    void setUp() {
        partNumberSearchStrategy = new PartNumberSearchStrategy(
            partNumberSearchService,
            productCatalogService,
            featuresManager,
            DEFAULT_INDEX_DELAY
        );

        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
        DEFAULT_CONTEXT.getWarnings().clear();
    }

    @ParameterizedTest(name = "{0}")
    @MethodSource("getTestParameters")
    void isApplicable(String testDisplayName,
                      SearchParameters searchParameters,
                      boolean featureFlagValue,
                      boolean expectedIsApplicable,
                      List<String> expectedWarningMessages) {
        given(
            featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_PART_NUMBER_SEARCH))
        ).willReturn(featureFlagValue);

        boolean actualIsApplicable = partNumberSearchStrategy.isApplicable(searchParameters);

        assertThat(actualIsApplicable).isEqualTo(expectedIsApplicable);

        if (expectedWarningMessages.isEmpty()) {
            assertThat(getRequestContext().getWarnings()).isEmpty();
        } else {
            assertThat(getRequestContext().getWarnings().size()).isEqualTo(expectedWarningMessages.size());
            assertThat(getRequestContext().getWarnings()).containsExactlyInAnyOrderElementsOf(expectedWarningMessages);
        }
    }

    private static Stream<Arguments> getTestParameters() {
        return Stream.of(
            arguments(
                "FF:On, ReqPN:null, PNAttrs:Yes, Query:Yes -> isApplicable:true, Warns:0",
                SearchParameters
                    .builder()
                    .query(QUERY)
                    .merchandisingConfiguration(
                        MerchandisingConfiguration.builder()
                            .attributeConfigurations(
                                Map.of(ATTRIBUTE, getPartNumberAttribute(true)))
                            .build())
                    .build(),
                true, // FF value
                true, // isApplicable
                Collections.emptyList()
            ),
            arguments(
                "FF:Off, ReqPN:null, PNAttrs:Yes, Query:Yes -> isApplicable:false, Warns:0",
                SearchParameters
                    .builder()
                    .query(QUERY)
                    .merchandisingConfiguration(
                        MerchandisingConfiguration.builder()
                            .attributeConfigurations(
                                Map.of(ATTRIBUTE, getPartNumberAttribute(true)))
                            .build())
                    .build(),
                false, // FF value
                false, // isApplicable
                Collections.emptyList()
            ),
            arguments(
                "FF:On, ReqPN:null, PNAttrs:NoEffective, Query:Yes -> isApplicable:false, Warns:1 (NoPNAttrs)",
                SearchParameters
                    .builder()
                    .query(QUERY)
                    .merchandisingConfiguration(
                        MerchandisingConfiguration.builder()
                            .attributeConfigurations(
                                Map.of(ATTRIBUTE, getPartNumberAttribute(false)))
                            .build())
                    .build(),
                true,  // FF value
                false, // isApplicable
                List.of(NO_PN_ATTRIBUTES_WARNING)
            ),
            arguments(
                "FF:On, ReqPN:null, PNAttrs:Yes, Query:No -> isApplicable:false, Warns:0",
                SearchParameters
                    .builder()
                    .query(EMPTY_QUERY)
                    .merchandisingConfiguration(
                        MerchandisingConfiguration.builder()
                            .attributeConfigurations(
                                Map.of(ATTRIBUTE, getPartNumberAttribute(true)))
                            .build())
                    .build(),
                true,  // FF value
                false, // isApplicable
                Collections.emptyList()
            ),
            arguments(
                "FF:Off, ReqPN:true, PNAttrs:Yes, Query:Yes -> isApplicable:false, Warns:1 (PNDisabledByFF)",
                SearchParameters
                    .builder()
                    .query(QUERY)
                    .partNumberSearchEnabled(true)
                    .merchandisingConfiguration(
                        MerchandisingConfiguration.builder()
                            .attributeConfigurations(
                                Map.of(ATTRIBUTE, getPartNumberAttribute(true)))
                            .build())
                    .build(),
                false, // FF value
                false, // isApplicable
                List.of(PN_DISABLED_BY_FF_WARNING)),
            arguments(
                "FF:On, ReqPN:true, PNAttrs:NoEffective, Query:Yes -> isApplicable:false, Warns:1 (NoPNAttrs)",
                SearchParameters
                    .builder()
                    .query(QUERY)
                    .partNumberSearchEnabled(true)
                    .merchandisingConfiguration(
                        MerchandisingConfiguration.builder()
                            .attributeConfigurations(
                                Map.of(ATTRIBUTE, getPartNumberAttribute(false)))
                            .build())
                    .build(),
                true,  // FF value
                false, // isApplicable
                List.of(NO_PN_ATTRIBUTES_WARNING)
            ),
            arguments(
                "FF:On, ReqPN:false, PNAttrs:Yes, Query:Yes -> isApplicable:false, Warns:0",
                SearchParameters
                    .builder()
                    .query(QUERY)
                    .partNumberSearchEnabled(false)
                    .merchandisingConfiguration(
                        MerchandisingConfiguration.builder()
                            .attributeConfigurations(
                                Map.of(ATTRIBUTE, getPartNumberAttribute(true)))
                            .build())
                    .build(),
                true,  // FF value
                false, // isApplicable
                Collections.emptyList()
            ),
            arguments(
                "FF:On, ReqPN:true, PNAttrs:Yes, Query:No -> isApplicable:false, Warns:0",
                SearchParameters
                    .builder()
                    .partNumberSearchEnabled(true) // no query
                    .merchandisingConfiguration(
                        MerchandisingConfiguration.builder()
                            .attributeConfigurations(
                                Map.of(ATTRIBUTE, getPartNumberAttribute(true)))
                            .build())
                    .build(),
                true,  // FF value
                false, // isApplicable
                Collections.emptyList()
            ),
            arguments(
                "FF:On, ReqPN:null, PNAttrs:NoActual, Query:Yes -> isApplicable:false, Warns:1 (NoPNAttrs)",
                SearchParameters
                    .builder()
                    .query(QUERY)
                    .merchandisingConfiguration(
                        MerchandisingConfiguration.builder().attributeConfigurations(Map.of()).build())
                    .build(),
                true,  // FF value
                false, // isApplicable
                List.of(NO_PN_ATTRIBUTES_WARNING)
            ),
            arguments(
                "FF:Off, ReqPN:false, PNAttrs:Yes, Query:Yes -> isApplicable:false, Warns:0",
                SearchParameters
                    .builder()
                    .query(QUERY)
                    .partNumberSearchEnabled(false)
                    .merchandisingConfiguration(
                        MerchandisingConfiguration.builder()
                            .attributeConfigurations(
                                Map.of(ATTRIBUTE, getPartNumberAttribute(true)))
                            .build())
                    .build(),
                false, // FF value
                false, // isApplicable
                Collections.emptyList()
            )
        );
    }

    private static AttributeConfiguration getPartNumberAttribute(boolean partNumberSearchable) {
        return AttributeConfiguration.builder()
            .key(ATTRIBUTE)
            .path(ATTRIBUTE)
            .displayName(ATTRIBUTE)
            .metadata(List.of(METADATA))
            .collectionId(COLLECTION_ID)
            .partNumberSearchable(partNumberSearchable)
            .build();
    }

    @ParameterizedTest(name = "fieldName: {0}, delay: {1}, modifiedSecondsAgo: {2}, expected: {3}")
    @MethodSource("getTestParametersForIndexingDelay")
    void isApplicableWithIndexingDelay(String lastModifiedField,
                                       int partNumberIndexingDelay,
                                       int secondsAgo,
                                       boolean expected) {

        given(featuresManager.getBooleanFlagConfiguration(DEFAULT_CONTEXT.getLdContext(), ENABLE_PART_NUMBER_SEARCH))
            .willReturn(true);
        given(featuresManager.getNumberFeatureFlagConfiguration(
                DEFAULT_CONTEXT.getLdContext(), PART_NUMBER_INDEX_DELAY_SECONDS, DEFAULT_INDEX_DELAY
            )
        ).willReturn(partNumberIndexingDelay);

        var attribute = AttributeConfiguration.builder()
            .key(ATTRIBUTE)
            .path(ATTRIBUTE)
            .displayName(ATTRIBUTE)
            .metadata(List.of(METADATA))
            .collectionId(COLLECTION_ID)
            .partNumberSearchable(true)
            .lastModifiedField(lastModifiedField)
            .lastModifiedDate(
                LocalDateTime.now(ZoneOffset.UTC).minusSeconds(secondsAgo)
            ).build();

        var searchParameters = SearchParameters.builder()
            .query(QUERY)
            .merchandisingConfiguration(
                MerchandisingConfiguration.builder()
                    .attributeConfigurations(Map.of(ATTRIBUTE, attribute))
                    .build()
            )
            .build();

        assertThat(partNumberSearchStrategy.isApplicable(searchParameters))
            .isEqualTo(expected);
    }

    private static Stream<Arguments> getTestParametersForIndexingDelay() {
        return Stream.of(
            arguments(null, 10, 20, true),
            arguments("retrievable", 10, 20, true),
            arguments(PART_NUMBER_SEARCHABLE, 0, 1, true),
            arguments(PART_NUMBER_SEARCHABLE, 10, 5, false),
            arguments(PART_NUMBER_SEARCHABLE, 10, 20, true)
        );
    }

}
