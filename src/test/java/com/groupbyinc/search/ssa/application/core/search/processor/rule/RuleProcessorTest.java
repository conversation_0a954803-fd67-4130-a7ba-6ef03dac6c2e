//package com.groupbyinc.search.ssa.application.core.search.processor.rule;
//
//import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
//import com.groupbyinc.search.ssa.core.SearchParameters;
//import com.groupbyinc.search.ssa.core.Sort;
//import com.groupbyinc.search.ssa.core.biasing.Bias;
//import com.groupbyinc.search.ssa.core.biasing.BiasingProfile;
//import com.groupbyinc.search.ssa.core.filter.AttributeFilter;
//import com.groupbyinc.search.ssa.core.filter.RangeFilter;
//import com.groupbyinc.search.ssa.core.filter.SearchFilter;
//import com.groupbyinc.search.ssa.core.filter.ValueFilter;
//import com.groupbyinc.search.ssa.core.navigation.Range;
//import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
//import com.groupbyinc.search.ssa.core.trigger.TriggerSet;
//import com.groupbyinc.search.ssa.features.FeaturesManager;
//import org.assertj.core.internal.bytebuddy.utility.RandomString;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.params.ParameterizedTest;
//import org.junit.jupiter.params.provider.CsvSource;
//
//import java.time.Duration;
//import java.time.Instant;
//import java.util.List;
//import java.util.Optional;
//import java.util.Random;
//import java.util.Set;
//import java.util.TreeSet;
//import java.util.UUID;
//import java.util.function.Supplier;
//
//import static com.groupbyinc.search.ssa.stub.TestData.DEFAULT_CONTEXT;
//import static com.groupbyinc.search.ssa.core.Order.DESCENDING;
//import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlag.ENABLE_CURATE_RESULTS;
//import static org.assertj.core.api.Assertions.assertThat;
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertTrue;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.eq;
//import static org.mockito.BDDMockito.given;
//import static org.mockito.Mockito.mock;
//
//@DisplayName("RuleEngine Tests")
//class RuleProcessorTest {
//
//    private static final Instant startTime = Instant.parse("2021-03-15T15:34:05.183275Z");
//    private static final Instant endTime = startTime.plusMillis(53);
//
//    private Supplier<Instant> instantSupplier;
//    private MerchandisingConfiguration merchandisingConfiguration;
//    private SearchParameters searchParameters;
//    private TriggerSet alwaysTriggers;
//    private TriggerSet neverTriggers;
//    private FeaturesManager featuresManager;
//
//    @BeforeEach
//    @SuppressWarnings("unchecked")
//    void setUp() {
//        alwaysTriggers = mock(TriggerSet.class);
//        given(alwaysTriggers.trigger(any())).willReturn(true);
//
//        neverTriggers = mock(TriggerSet.class);
//        given(neverTriggers.trigger(any())).willReturn(false);
//
//        // Given: An instant supplier that will return the start time once and then the end time
//        instantSupplier = (Supplier<Instant>) mock(Supplier.class);
//        given(instantSupplier.get()).willReturn(startTime, endTime);
//
//        featuresManager = mock(FeaturesManager.class);
//
//        merchandisingConfiguration = mock(MerchandisingConfiguration.class);
//
//        // And: A basic instance of search parameters
//        searchParameters = SearchParameters.builder()
//            .context(DEFAULT_CONTEXT)
//            .query("Catnip fish")
//            .visitorId(UUID.randomUUID().toString())
//            .merchandisingConfiguration(merchandisingConfiguration)
//            .build();
//    }
//
//    @Test
//    @DisplayName("Empty rules can't trigger and the search parameters are left unchanged")
//    void emptyRulesLeavesTheSearchParametersUnchanged() {
//        // Given: We create a new instance of the rule engine with empty rules and the mocked instant supplier
//        var ruleEngine = new RuleProcessor(new TreeSet<>(), instantSupplier, featuresManager);
//
//        // When: We call preProcess on the rule engine
//        var preProcessingResult = ruleEngine.preProcess(searchParameters);
//
//        // Then: The result is empty with the expected duration and the same instance of the unmodified search parameters
//        assertThat(preProcessingResult).hasValue(new RuleProcessorResult(
//            null,
//            null,
//            Duration.between(startTime, endTime),
//            0,
//            0,
//            null
//        ));
//    }
//
//    @Test
//    @DisplayName("If no rule triggers the search parameters are left unchanged")
//    void noTriggeredRuleLeavesTheSearchParametersUnchanged() {
//
//        // Given: A rule that won't trigger
//        var rule = createRule(true, neverTriggers);
//
//        // And: We create a new instance of the rule engine with a rule that won't trigger and the mocked instant supplier
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(rule);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//
//        // When: We call preProcess on the rule engine
//        var preProcessingResult = ruleEngine.preProcess(searchParameters);
//
//        // Then: The result is empty with the expected duration and the same instance of the unmodified search parameters
//        assertThat(preProcessingResult).hasValue(new RuleProcessorResult(
//            null,
//            null,
//            Duration.between(startTime, endTime),
//            1,
//            1,
//            null
//        ));
//    }
//
//    @Test
//    @DisplayName("If rule triggers are empty rule will be triggered")
//    void ruleTriggeredWithEmptyTriggerInTriggerSet() {
//        var rule = createRule(true, new TriggerSet());
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(rule);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//
//        // When: We call preProcess on the rule engine
//        var preProcessingResult = ruleEngine.preProcess(searchParameters);
//
//        assertTrue(preProcessingResult.isPresent());
//        assertTrue(preProcessingResult.get().getTriggeredRule().isPresent());
//        assertEquals(preProcessingResult.get().getTriggeredRule().get().id(), rule.id());
//    }
//
//
//    @Test
//    @DisplayName("If rule has no trigger sets rule will be triggered")
//    void ruleTriggeredWithEmptyTriggerSet() {
//        var rule = createRule(true, null);
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(rule);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//
//        // When: We call preProcess on the rule engine
//        var preProcessingResult = ruleEngine.preProcess(searchParameters);
//
//        assertTrue(preProcessingResult.isPresent());
//        assertTrue(preProcessingResult.get().getTriggeredRule().isPresent());
//        assertEquals(preProcessingResult.get().getTriggeredRule().get().id(), rule.id());
//    }
//
//    @Test
//    @DisplayName("If the triggered rule has no configuration the search parameters are left unchanged")
//    void triggeredRuleWithNoConfigurationLeavesTheSearchParametersUnchanged() {
//
//        // Given: A rule that will trigger
//        var rule = createRule(true, alwaysTriggers);
//
//        // And: We create a new instance of the rule engine with a rule that won't trigger and the mocked instant supplier
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(rule);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//
//        // When: We call preProcess on the rule engine
//        var preProcessingResult = ruleEngine.preProcess(searchParameters);
//
//        // Then: The result contains the triggered rule with the expected duration and the same instance of the unmodified search parameters
//        assertThat(preProcessingResult.isPresent()).isTrue();
//        assertThat(
//            preProcessingResult.get().getSearchParameters().getContext()
//        ).isEqualTo(searchParameters.getContext());
//    }
//
//    @Test
//    @DisplayName("The first rule to trigger is chosen and further processing is ceased")
//    void firstRuleToTriggerIsChosen() {
//
//        // Given: A disabled rule that would otherwise always trigger
//        var ruleA = createRule(false, alwaysTriggers);
//
//        // And: A disabled rule that would never trigger
//        var ruleB = createRule(false, neverTriggers);
//
//        // And: An enabled rule that would always trigger
//        var ruleC = createRule(true, alwaysTriggers);
//
//        // And: An enabled rule that would always trigger
//        var ruleD = createRule(true, alwaysTriggers);
//
//        // And: We create a new instance of the rule engine with the rules
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(ruleB);
//        set.add(ruleA);
//        set.add(ruleC);
//        set.add(ruleD);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//
//        // When: We call preProcess on the rule engine
//        var preProcessingResult = ruleEngine.preProcess(searchParameters);
//
//        // Then: The result contains the triggered rule with the expected duration and the same instance of the unmodified search parameters
//        assertThat(preProcessingResult.isPresent()).isTrue();
//        assertThat(
//            preProcessingResult.get().getSearchParameters().getContext()
//        ).isEqualTo(searchParameters.getContext());
//    }
//
//    @Test
//    @DisplayName("Rule outside of the active time range will not be triggered")
//    void ruleOutsideOfActiveTimeRangeWillNotBeTriggered() {
//
//        // Given: A rule that becomes active in the future
//        var rule = createTimeBasedRule(alwaysTriggers, new TemporalRange<>(startTime.plusSeconds(100), startTime.plusSeconds(1000)));
//
//        // And: We create a new instance of the rule engine with the rules
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(rule);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//
//        // When: We call preProcess on the rule engine
//        var preProcessingResult = ruleEngine.preProcess(searchParameters);
//
//        // Then: The result is empty with the expected duration and the same instance of the unmodified search parameters
//        assertThat(preProcessingResult.isPresent()).isTrue();
//        assertThat(
//            preProcessingResult.get().getSearchParameters()
//        ).isNull();
//    }
//
//    @Test
//    @DisplayName("Rule engine does no pre-process when there already is a resolved biasing profile, sort, and navigation overrides")
//    void ruleEngineDoesNotRunWhenThereAlreadyIsABiasingProfileAndSort() {
//
//        // Given: Search parameters that already have a resolved biasing profile, sort, and navigation override
//        searchParameters = searchParameters.toBuilder()
//            .biasingProfile(
//                BiasingProfile.builder()
//                    .biases(
//                        List.of(
//                            new Bias(
//                                "colorFamilies",
//                                "Red",
//                                Bias.Strength.STRONG_DECREASE,
//                                Bias.Type.TEXTUAL,
//                                null
//                            )
//                        )
//                    )
//                    .build()
//            )
//            .sorts(List.of(
//                new Sort("rating", DESCENDING)
//            ))
//            .includedNavigations(
//                List.of("brands", "color")
//            )
//            .build();
//
//        // And: A rule engine with one rule
//        var rule = createRule(true, alwaysTriggers);
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(rule);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//
//        // When: The rule engine tried to pre-process
//        var ruleEngineResult = ruleEngine.preProcess(searchParameters);
//
//        // Then: It returns an empty result, meaning that it did not run
//        assertThat(ruleEngineResult).isEmpty();
//    }
//
//    @Test
//    @DisplayName("Rule engine resolves the biasing profile when one is configured")
//    void ruleEngineResolvesTheBiasingProfileWhenOneIsConfigured() {
//
//        // Given: A rule which references a biasing profile by name
//        var biasingOverrideRule = baseRule(true, alwaysTriggers)
//            .activeHoursEnabled(true)
//            .activeTo(Long.MAX_VALUE)
//            .activeFrom(0L)
//            .biasingProfileName("Please Work")
//            .build();
//
//        // When: The rule engine is created and pre-processed
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(biasingOverrideRule);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//        var ruleEngineResult = ruleEngine.preProcess(searchParameters);
//
//        // Then: The result contains the triggered rule and the search parameters have the biasing profile
//        assertThat(ruleEngineResult.isPresent()).isTrue();
//        var name = ruleEngineResult.get()
//            .getSearchParameters().getBiasingProfileName().orElse(null);
//        assertThat(name).isEqualTo(
//            biasingOverrideRule.getBiasingProfileName()
//        );
//    }
//
//    @Test
//    @DisplayName("Rule engine leaves the search parameters unchanged if the biasing profile is not found")
//    void ruleEngineLeavesTheSearchParametersUnchangedIfTheBiasingProfileIsNotFound() {
//
//        // Given: A rule which references a biasing profile by name
//        var biasingOverrideRule = baseRule(true, alwaysTriggers)
//            .activeHoursEnabled(true)
//            .activeTo(Long.MAX_VALUE)
//            .activeFrom(0L)
//            .biasingProfileName("What is this?")
//            .build();
//
//        // And: That biasing profile cannot be found by name
//        given(merchandisingConfiguration
//            .getBiasingProfileByName("What is this?")
//        ).willReturn(Optional.empty());
//
//        // When: The rule engine is created and pre-processed
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(biasingOverrideRule);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//        var ruleEngineResult = ruleEngine.preProcess(searchParameters);
//
//        // Then: The result contains the triggered rule and the search parameters are the same
//        assertThat(ruleEngineResult.isPresent()).isTrue();
//        assertThat(
//            ruleEngineResult.get()
//                .getSearchParameters().getBiasingProfile().isPresent()
//        ).isFalse();
//    }
//
//    @Test
//    @DisplayName("Rule engine resolves the sort when one is configured")
//    void ruleEngineResolvesTheSortWhenOneIsConfigured() {
//        var sort = new Sort("rating", DESCENDING);
//
//        // Given: A rule which has a sort
//        var sortOverrideRule = baseRule(true, alwaysTriggers)
//            .activeHoursEnabled(true)
//            .activeTo(Long.MAX_VALUE)
//            .activeFrom(0L)
//            .sort(sort)
//            .build();
//
//        // When: The rule engine is created and pre-processed
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(sortOverrideRule);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//        var ruleEngineResult = ruleEngine.preProcess(searchParameters);
//
//        // Then: The result contains the triggered rule and the search parameters have the sort
//        assertThat(ruleEngineResult.isPresent()).isTrue();
//        assertThat(
//            ruleEngineResult.get().getSearchParameters().getSorts()
//        ).isEqualTo(List.of(sort));
//    }
//
//    @Test
//    @DisplayName("Rule engine resolves the navigation override when one is configured")
//    void ruleEngineResolvesTheNavigationOverrideWhenOneIsConfigured() {
//
//        // Given: A rule which has a navigation override
//        var navigationOverrideRule = baseRule(true, alwaysTriggers)
//            .includedNavigations(List.of("brand"))
//            .activeHoursEnabled(true)
//            .activeTo(Long.MAX_VALUE)
//            .activeFrom(0L)
//            .build();
//
//        // When: The rule engine is created and pre-processed
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(navigationOverrideRule);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//        var ruleEngineResult = ruleEngine.preProcess(searchParameters);
//
//        // Then: The result contains the triggered rule and the search parameters have the navigation override
//        assertThat(ruleEngineResult.isPresent()).isTrue();
//        assertThat(
//            ruleEngineResult.get().getSearchParameters().getIncludedNavigations()
//        ).isEqualTo(List.of("brand"));
//    }
//
//    @Test
//    @DisplayName("Rule engine resolves search filters configured")
//    void ruleEngineResolvesSearchFiltersWhenConfigured() {
//
//        // Given: A rule which has search filters
//        var navigationOverrideRule = baseRule(true, alwaysTriggers)
//            .activeHoursEnabled(true)
//            .activeTo(Long.MAX_VALUE)
//            .activeFrom(0L)
//            .searchFilters(List.of(
//                new SearchFilter("Alcohol."),
//                new SearchFilter("Got any chippies mate?")
//            ))
//            .build();
//
//        var query = "Where you going with them chippies, mate?";
//        searchParameters = searchParameters.toBuilder().query(query).build();
//
//        // When: The rule engine is created and pre-processed
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(navigationOverrideRule);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//        var ruleEngineResult = ruleEngine.preProcess(searchParameters);
//
//        // Then: The result contains the triggered rule and the search parameters have the filter
//        assertThat(ruleEngineResult.isPresent()).isTrue();
//        assertThat(
//            ruleEngineResult.get().getSearchParameters().getQuery()
//        ).isEqualTo("Alcohol. Got any chippies mate?");
//    }
//
//    @ParameterizedTest
//    @CsvSource({"TRUE", "FALSE"})
//    @DisplayName("Rule engine resolves value filters when configured")
//    void ruleEngineResolvesValueFiltersWhenConfigured(boolean featureFlag) {
//        given(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_CURATE_RESULTS))).willReturn(featureFlag);
//
//        // Given: A rule which has value filters
//        var valueFilters = List.of(
//            new ValueFilter("brands", "Jahyms", null, true, ValueFilter.ValueFilterType.TEXTUAL),
//            new ValueFilter("hellscapes", "Rohebyn", null, false, ValueFilter.ValueFilterType.TEXTUAL)
//        );
//
//        var attributeFilter = new AttributeFilter(valueFilters, null);
//
//        var attributeFilters = List.of(attributeFilter);
//
//        var navigationOverrideRule = baseRule(true, alwaysTriggers)
//            .activeHoursEnabled(true)
//            .activeTo(Long.MAX_VALUE)
//            .activeFrom(0L)
//            .valueFilters(valueFilters)
//            .attributeFilters(attributeFilters)
//            .build();
//
//        // When: The rule engine is created and pre-processed
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(navigationOverrideRule);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//        var ruleEngineResult = ruleEngine.preProcess(searchParameters);
//
//        // Then: The result contains the triggered rule and the search parameters have the filter
//        assertThat(ruleEngineResult.isPresent()).isTrue();
//
//        var filtersNew = ruleEngineResult.get().getSearchParameters().getAttributeFilters();
//        var filtersOld = ruleEngineResult.get().getSearchParameters().getValueFilters();
//
//        if (featureFlag) {
//            assertThat(filtersNew.get(0).valueFilters()).isEqualTo(valueFilters);
//            assertThat(filtersNew.get(0).valueFilters().size()).isEqualTo(2);
//            assertThat(filtersOld.size()).isEqualTo(0);
//        } else {
//            assertThat(filtersOld).isEqualTo(valueFilters);
//            assertThat(filtersOld.size()).isEqualTo(2);
//            assertThat(filtersNew.size()).isEqualTo(0);
//        }
//    }
//
//    @ParameterizedTest
//    @CsvSource({"TRUE", "FALSE"})
//    @DisplayName("Rule engine resolves range filters when configured")
//    void ruleEngineResolvesRangeFiltersWhenConfigured(boolean featureFlag) {
//        given(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_CURATE_RESULTS))).willReturn(featureFlag);
//
//        // Given: A rule which has range filters
//        var rangeFilters = List.of(
//            new RangeFilter("ratings", new Range(1d, 5d, null)),
//            new RangeFilter("price", new Range(0d, 25d, null))
//        );
//
//        var attributeFilter = new AttributeFilter(null, rangeFilters);
//
//        var attributeFilters = List.of(attributeFilter);
//
//        var navigationOverrideRule = baseRule(true, alwaysTriggers)
//            .activeHoursEnabled(true)
//            .activeTo(Long.MAX_VALUE)
//            .activeFrom(0L)
//            .rangeFilters(
//                rangeFilters
//            )
//            .attributeFilters(attributeFilters)
//            .build();
//
//        // When: The rule engine is created and pre-processed
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(navigationOverrideRule);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//        var ruleEngineResult = ruleEngine.preProcess(searchParameters);
//
//        // Then: The result contains the triggered rule and the search parameters have the filter
//        assertThat(ruleEngineResult.isPresent()).isTrue();
//
//        var filtersNew = ruleEngineResult.get().getSearchParameters().getAttributeFilters();
//        var filtersOld = ruleEngineResult.get().getSearchParameters().getRangeFilters();
//
//        if (featureFlag) {
//            assertThat(filtersNew.get(0).rangeFilters()).isEqualTo(rangeFilters);
//            assertThat(filtersNew.get(0).rangeFilters().size()).isEqualTo(2);
//            assertThat(filtersOld.size()).isEqualTo(0);
//        } else {
//            assertThat(filtersOld).isEqualTo(rangeFilters);
//            assertThat(filtersOld.size()).isEqualTo(2);
//            assertThat(filtersNew.size()).isEqualTo(0);
//        }
//    }
//
//    // region Helpers
//    private static RuleConfiguration createTimeBasedRule(TriggerSet triggerSet,
//                                                         TemporalRange<Instant> activeTimeRange) {
//        return baseRule(true, triggerSet)
//            .activeFrom(activeTimeRange.getMin().toEpochMilli())
//            .activeTo(activeTimeRange.getMax().toEpochMilli())
//            .build();
//    }
//
//    private static RuleConfiguration createRule(boolean enabled,
//                                                TriggerSet triggerSet) {
//        return baseRule(enabled, triggerSet).build();
//    }
//
//    private static RuleConfiguration.RuleConfigurationBuilder baseRule(boolean enabled,
//                                                                       TriggerSet triggerSet) {
//        var builder =  RuleConfiguration.builder()
//            .id(new Random().nextInt())
//            .name(RandomString.make(10))
//            .activeHoursEnabled(true)
//            .activeFrom(0L)
//            .activeTo(Long.MAX_VALUE)
//            .enabled(enabled)
//            .areaId(1);
//
//        if (triggerSet != null) {
//            builder
//                .triggerSets(Set.of(triggerSet));
//        }
//        return builder;
//    }
//    // endregion Helpers
//
//}
