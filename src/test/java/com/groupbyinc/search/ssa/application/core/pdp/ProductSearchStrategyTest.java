package com.groupbyinc.search.ssa.application.core.pdp;

import com.groupbyinc.search.ssa.core.product.Product;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.productcatalog.ProductCatalogService;
import com.groupbyinc.search.ssa.retail.ProductGoogleSearcher;

import io.micronaut.core.propagation.PropagatedContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.DEFAULT_CONTEXT;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

@ExtendWith(MockitoExtension.class)
class ProductSearchStrategyTest {

    private ProductSearchStrategy productSearchStrategy;

    @Mock private FeaturesManager featuresManager;
    @Mock private ProductGoogleSearcher productGoogleSearcher;
    @Mock private ProductCatalogService productCatalogService;

    private PropagatedContext.Scope scope;

    @BeforeEach
    void setUp() {
        productSearchStrategy = new ProductSearchStrategy(
            featuresManager,
            productCatalogService,
            productGoogleSearcher
        );

        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    @Test
    void testGetStrategyForContextReturnsGoogleStrategy() {
        given(featuresManager.getBooleanFlagConfiguration(
            DEFAULT_CONTEXT.getLdContext(), FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_PDP_FETCH)
        ).willReturn(false);

        assertThat(productSearchStrategy.getStrategyFor(DEFAULT_CONTEXT)).isSameAs(productGoogleSearcher);
    }

    @Test
    void testGetStrategyForContextReturnsProductCatalogStrategyWithGoogleFallback() {
        given(featuresManager.getBooleanFlagConfiguration(
            DEFAULT_CONTEXT.getLdContext(), FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_PDP_FETCH)
        ).willReturn(true);

        var strategy = productSearchStrategy.getStrategyFor(DEFAULT_CONTEXT);
        assertThat(strategy.getClass()).isSameAs(ProductSearchStrategy.StrategyWithFallback.class);
        var withFallback = (ProductSearchStrategy.StrategyWithFallback) strategy;
        assertThat(withFallback.strategy).isSameAs(productCatalogService);
        assertThat(withFallback.fallbackStrategy).isSameAs(productGoogleSearcher);
    }

    @Test
    void testStrategyWithFallbackWillNotFallbackOnReturnedProduct() {
        var pId = "1";
        var variants = List.of("1_1", "1_2");
        var product = Optional.of(Product.builder().build());

        given(featuresManager.getBooleanFlagConfiguration(
            DEFAULT_CONTEXT.getLdContext(), FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_PDP_FETCH)
        ).willReturn(true);
        given(productCatalogService.getProductDetails(pId, variants)).willReturn(product);

        var result = productSearchStrategy.getStrategyFor(DEFAULT_CONTEXT)
            .getProductDetails(pId, variants);

        assertThat(result).isSameAs(product);

        verifyNoInteractions(productGoogleSearcher);
    }

    @Test
    void testStrategyWithFallbackWillFallbackOnEmptyProduct() {
        var pId = "1";
        var variants = List.of("1_1", "1_2");
        var product = Optional.of(Product.builder().build());

        given(featuresManager.getBooleanFlagConfiguration(
            DEFAULT_CONTEXT.getLdContext(), FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_PDP_FETCH)
        ).willReturn(true);
        given(productCatalogService.getProductDetails(pId, variants)).willReturn(Optional.empty());
        given(productGoogleSearcher.getProductDetails(pId, variants)).willReturn(product);

        var result = productSearchStrategy.getStrategyFor(DEFAULT_CONTEXT)
            .getProductDetails(pId, variants);

        assertThat(result).isSameAs(product);
        verify(productCatalogService, times(1)).getProductDetails(any(), any());
        verify(productGoogleSearcher, times(1)).getProductDetails(any(), any());
    }

}
