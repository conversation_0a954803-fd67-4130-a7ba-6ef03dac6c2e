package com.groupbyinc.search.ssa.application.core.search.variantrollupkeys;

import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.stub.ResourceUtils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKey.RollupKeyType.FULFILLMENT_INFO;
import static com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKey.RollupKeyType.INVENTORY_ATTRIBUTE;
import static com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKey.RollupKeyType.VARIANT_ATTRIBUTE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.ID;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;
import static com.groupbyinc.search.ssa.util.Constants.VARIANT_ROLLUP_VALUES;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

@ExtendWith(MockitoExtension.class)
class VariantRollupKeysProcessorTest {

    private static final String PRODUCT_ID = "productId";
    private static final String PRIMARY_PRODUCT_ID = "primaryProductId";
    private static final String TITLE = "title";
    private static final String PLACE_ID = "MTL";
    private static final String NO_EXIST_PLACE_ID = "SOME_PLACE_ID";

    private static final List<VariantRollupKey> VARIANT_ROLLUP_KEYS = List.of(
        new VariantRollupKey("price", VARIANT_ATTRIBUTE, "price", "priceInfo.price"),
        new VariantRollupKey("variantId", VARIANT_ATTRIBUTE, "id", "id"),
        new VariantRollupKey("attributes.sku", VARIANT_ATTRIBUTE, "attributes.sku", "attributes.sku"),
        new VariantRollupKey("inventory(MTL,price)", INVENTORY_ATTRIBUTE, "price", "priceInfo.price", PLACE_ID),
        new VariantRollupKey(
            "inventory(MTL,attributes.listprice)",
            INVENTORY_ATTRIBUTE,
            "attributes.listprice",
            "attributes.listprice",
            PLACE_ID
        ),
        new VariantRollupKey(
            "customFulfillment1.MTL",
            FULFILLMENT_INFO,
            "customFulfillment1",
            "custom-type-1",
            PLACE_ID
        )
    );

    @Mock
    VariantRollupValuesConverter variantRollupValuesConverter;

    @InjectMocks
    VariantRollupKeysProcessor variantRollupKeysProcessor;

    @Captor
    ArgumentCaptor<Map<String, Object>> variantRollupValuesCaptor;

    @Test
    void setFulfillmentVariantRollupValues() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("fetch/response-metadata-with-variants.json");
        var records = List.of(
            Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCT_ID, PRIMARY_PRODUCT_ID, TITLE, productMetadata)
        );

        var variantRollupKeys = VARIANT_ROLLUP_KEYS.stream()
            .filter(key -> key.type() == FULFILLMENT_INFO)
            .toList();

        var expectedVariantRollupValues = Map.of("customFulfillment1.MTL", 2.0);

        // when
        variantRollupKeysProcessor.setFulfillmentVariantRollupValues(records, variantRollupKeys);

        // then
        assertThat(productMetadata.get(VARIANT_ROLLUP_VALUES)).isEqualTo(expectedVariantRollupValues);
    }

    @Test
    void setVariantRollupValues_whenVariantsNested() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("fetch/response-metadata-with-variants.json");
        productMetadata.put(VARIANT_ROLLUP_VALUES, new HashMap<>(Map.of("customFulfillment1.MTL", 2.0)));

        var records = List.of(
            Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCT_ID, PRIMARY_PRODUCT_ID, TITLE, productMetadata)
        );

        var expectedVariantRollupValues = Map.of(
            "customFulfillment1.MTL", 2.0,
            "price", Set.of(31.3, 250.0),
            "variantId", Set.of("TDK878", "TDK793"),
            "attributes.sku", Set.of("TDK878", "TDK793"),
            "inventory(MTL,price)", Set.of(236.0, 28.2),
            "inventory(MTL,attributes.listprice)", Set.of("236.00", "28.20")
        );

        // when
        variantRollupKeysProcessor.setVariantRollupValues(records, VARIANT_ROLLUP_KEYS);

        // then
        verify(variantRollupValuesConverter).convertVariantRollupObjects(variantRollupValuesCaptor.capture());
        var capturedValue = variantRollupValuesCaptor.getValue();
        assertThat(capturedValue).containsExactlyInAnyOrderEntriesOf(expectedVariantRollupValues);
    }

    @Test
    @SuppressWarnings("unchecked")
    void setVariantRollupValues_whenVariantsSeparate() {
        // given
        Map<String, Map<String, Object>> productsById = new HashMap<>();
        Map<String, Object> productsMetadata =
            ResourceUtils.getResourceAsMap("fetch/response-metadata-variants-separate.json");

        ((List<Map<String, Object>>) productsMetadata.get("records")).forEach(record ->
            productsById.put((String) record.get(ID), record)
        );

        var expectedVariantRollupValues = Map.of(
            "price", Set.of(31.3, 250.0),
            "variantId", Set.of("TDK878", "TDK793"),
            "attributes.sku", Set.of("TDK878", "TDK793"),
            "inventory(MTL,price)", Set.of(236.0, 28.2),
            "inventory(MTL,attributes.listprice)", Set.of("236.00", "28.20")
        );

        // when
        variantRollupKeysProcessor.setVariantRollupValues(productsById, VARIANT_ROLLUP_KEYS);

        // then
        verify(variantRollupValuesConverter).convertVariantRollupObjects(variantRollupValuesCaptor.capture());
        var capturedValue = variantRollupValuesCaptor.getValue();
        assertThat(capturedValue).containsExactlyInAnyOrderEntriesOf(expectedVariantRollupValues);
    }

    @Test
    void setVariantRollupValues_shouldGetVariantRollupValuesFromPrimary_whenNoSeparateVariants() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("fetch/response-metadata-no-variants.json");
        Map<String, Map<String, Object>> productsById = Map.of((String) productMetadata.get(ID), productMetadata);

        var expectedVariantRollupValues = Map.of(
            "price", Set.of(9.11),
            "variantId", Set.of("818-3060"),
            "attributes.sku", Set.of("818-3060"),
            "inventory(MTL,price)", Set.of(8.59),
            "inventory(MTL,attributes.listprice)", Set.of("8.59")
        );

        // when
        variantRollupKeysProcessor.setVariantRollupValues(productsById, VARIANT_ROLLUP_KEYS);

        // then
        verify(variantRollupValuesConverter).convertVariantRollupObjects(variantRollupValuesCaptor.capture());
        var capturedValue = variantRollupValuesCaptor.getValue();
        assertThat(capturedValue).containsExactlyInAnyOrderEntriesOf(expectedVariantRollupValues);
    }

    @Test
    void setVariantRollupValues_shouldGetVariantRollupValuesFromPrimary_whenNoVariants() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("fetch/response-metadata-no-variants.json");
        productMetadata.put(VARIANT_ROLLUP_VALUES, new HashMap<>(Map.of("customFulfillment1.MTL", 1.0)));
        var records = List.of(
            Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCT_ID, PRIMARY_PRODUCT_ID, TITLE, productMetadata)
        );

        var expectedVariantRollupValues = Map.of(
            "customFulfillment1.MTL", 1.0,
            "price", Set.of(9.11),
            "variantId", Set.of("818-3060"),
            "attributes.sku", Set.of("818-3060"),
            "inventory(MTL,price)", Set.of(8.59),
            "inventory(MTL,attributes.listprice)", Set.of("8.59")
        );

        // when
        variantRollupKeysProcessor.setVariantRollupValues(records, VARIANT_ROLLUP_KEYS);

        // then
        verify(variantRollupValuesConverter).convertVariantRollupObjects(variantRollupValuesCaptor.capture());
        var capturedValue = variantRollupValuesCaptor.getValue();
        assertThat(capturedValue).containsExactlyInAnyOrderEntriesOf(expectedVariantRollupValues);
    }

    @Test
    void setVariantRollupValues_shouldNotSetVariantRollupValues_IfValuesEmpty() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("fetch/response-metadata-with-variants.json");
        var records = List.of(
            Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCT_ID, PRIMARY_PRODUCT_ID, TITLE, productMetadata)
        );
        var variantRollupKeys = List.of(
            new VariantRollupKey(
                "inventory(" + NO_EXIST_PLACE_ID + ",price)",
            INVENTORY_ATTRIBUTE,
            "price",
            "priceInfo.price",
            NO_EXIST_PLACE_ID
            )
        );

        // when
        variantRollupKeysProcessor.setVariantRollupValues(records, variantRollupKeys);

        // then
        verifyNoInteractions(variantRollupValuesConverter);
        assertThat(productMetadata).doesNotContainKey(VARIANT_ROLLUP_VALUES);
    }

    @Test
    void setVariantRollupValues_shouldHandleDuplicateVariantRollupKeys() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("fetch/response-metadata-with-variants.json");
        var records = List.of(
            Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, PRODUCT_ID, PRIMARY_PRODUCT_ID, TITLE, productMetadata)
        );
        var variantRollupKeys = List.of(
            new VariantRollupKey("variantId", VARIANT_ATTRIBUTE, "id", "id"),
            new VariantRollupKey("variantId", VARIANT_ATTRIBUTE, "id", "id"),
            new VariantRollupKey("price", VARIANT_ATTRIBUTE, "price", "priceInfo.price"),
            new VariantRollupKey("price", VARIANT_ATTRIBUTE, "price", "priceInfo.price"),
            new VariantRollupKey("attributes.sku", VARIANT_ATTRIBUTE, "attributes.sku", "attributes.sku"),
            new VariantRollupKey("attributes.sku", VARIANT_ATTRIBUTE, "attributes.sku", "attributes.sku"),
            new VariantRollupKey("inventory(MTL,price)", INVENTORY_ATTRIBUTE, "price", "priceInfo.price", PLACE_ID),
            new VariantRollupKey("inventory(MTL,price)", INVENTORY_ATTRIBUTE, "price", "priceInfo.price", PLACE_ID)
        );
        var expectedVariantRollupValues = Map.of(
            "variantId", Set.of("TDK878", "TDK793"),
            "price", Set.of(31.3, 250.0),
            "attributes.sku", Set.of("TDK878", "TDK793"),
            "inventory(MTL,price)", Set.of(236.0, 28.2)
        );

        // when
        variantRollupKeysProcessor.setVariantRollupValues(records, variantRollupKeys);

        // then
        verify(variantRollupValuesConverter).convertVariantRollupObjects(variantRollupValuesCaptor.capture());
        var capturedValue = variantRollupValuesCaptor.getValue();
        assertThat(capturedValue).containsExactlyInAnyOrderEntriesOf(expectedVariantRollupValues);
    }
}
