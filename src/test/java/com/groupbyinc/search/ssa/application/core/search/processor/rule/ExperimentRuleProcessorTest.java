//package com.groupbyinc.search.ssa.core.engine;
//
//import com.groupbyinc.search.ssa.application.core.search.processor.rule.RuleProcessor;
//import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
//import com.groupbyinc.search.ssa.core.SearchParameters;
//import com.groupbyinc.search.ssa.core.features.Features;
//import com.groupbyinc.search.ssa.core.filter.AttributeFilter;
//import com.groupbyinc.search.ssa.core.filter.RangeFilter;
//import com.groupbyinc.search.ssa.core.filter.ValueFilter;
//import com.groupbyinc.search.ssa.core.navigation.Range;
//import com.groupbyinc.search.ssa.core.rule.ExperimentVariant;
//import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
//import com.groupbyinc.search.ssa.core.rule.RuleType;
//import com.groupbyinc.search.ssa.core.rule.RuleVariant;
//import com.groupbyinc.search.ssa.core.trigger.TriggerSet;
//import com.groupbyinc.search.ssa.features.FeaturesManager;
//import org.assertj.core.internal.bytebuddy.utility.RandomString;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.DisplayName;
//import org.junit.jupiter.api.Test;
//
//import java.time.Instant;
//import java.util.List;
//import java.util.Random;
//import java.util.Set;
//import java.util.TreeSet;
//import java.util.function.Supplier;
//
//import static com.groupbyinc.search.ssa.stub.TestData.DEFAULT_CONTEXT;
//
//import static org.assertj.core.api.Assertions.assertThat;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.BDDMockito.given;
//import static org.mockito.Mockito.mock;
//
//@DisplayName("RuleEngine Tests for processing Experiment Rules")
//class ExperimentRuleProcessorTest {
//
//    private static final Instant startTime = Instant.parse("2021-03-15T15:34:05.183275Z");
//    private static final Instant endTime = startTime.plusMillis(53);
//
//    private Supplier<Instant> instantSupplier;
//
//    private TriggerSet alwaysTriggers;
//    private SearchParameters searchParametersVariantA;
//    private FeaturesManager featuresManager;
//
//    @BeforeEach
//    @SuppressWarnings("unchecked")
//    void setUp() {
//        alwaysTriggers = mock(TriggerSet.class);
//        given(alwaysTriggers.trigger(any())).willReturn(true);
//
//        // Given: An instant supplier that will return the start time once and then the end time
//        instantSupplier = (Supplier<Instant>) mock(Supplier.class);
//        given(instantSupplier.get()).willReturn(startTime, endTime);
//
//        var merchandisingConfiguration = mock(MerchandisingConfiguration.class);
//        given(merchandisingConfiguration.features()).willReturn(new Features());
//
//        // A basic instance of search parameters which corresponds to the first rule variant
//        searchParametersVariantA = SearchParameters.builder()
//            .context(DEFAULT_CONTEXT)
//            .query("Catnip fish")
//            .visitorId("2")
//            .merchandisingConfiguration(merchandisingConfiguration)
//            .build();
//
//        featuresManager = mock(FeaturesManager.class);
//    }
//
//    @Test
//    @DisplayName("Rule engine resolves the biasing profile in Experiment Rule Variant")
//    void ruleEngineResolvesTheCorrectBiasingProfileDependingOnContext() {
//        List<ExperimentVariant> variants = List.of(
//            ExperimentVariant
//                .builder()
//                .name("Rule A")
//                .variantTriggerPercentage(100)
//                .ruleVariant(
//                    RuleVariant
//                        .builder()
//                        .biasingProfileName("Biasing Profile name A")
//                        .build()
//                ).build()
//        );
//
//        // Given: A rule which contains a variant
//        var ruleWithVariants = baseRule(alwaysTriggers)
//            .variants(variants)
//            .type(RuleType.EXPERIMENT)
//            .build();
//
//        // When: The rule engine is with Context.visitorId that corresponds to the first rule variant
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(ruleWithVariants);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//
//        var ruleEngineResult = ruleEngine.preProcess(searchParametersVariantA);
//
//        // Then: The result contains the biasing profile from the first rule variant
//        String name = null;
//        if(ruleEngineResult.isPresent()) {
//            var triggeredRule = ruleEngineResult.get().getTriggeredRule();
//            if (triggeredRule.isPresent()) {
//                name = triggeredRule.get().getBiasingProfileName();
//            }
//        }
//
//        assertThat(name).isEqualTo("Biasing Profile name A");
//    }
//
//    @Test
//    @DisplayName("Rule engine resolves the attribute filters in Experiment Rule Variant")
//    void ruleEngineResolvesTheCorrectAttributeFilersDependingOnContext() {
//        var attributeFilters =  List.of(
//            createAttributeFilter(
//                List.of(createValueFilter("Superman")),
//                List.of(createRangeFilter(8.0))
//            ),
//            createAttributeFilter(
//                List.of(createValueFilter("Puma")),
//                List.of(createRangeFilter(10.0))
//            )
//        );
//
//        List<ExperimentVariant> variants = List.of(
//            ExperimentVariant
//                .builder()
//                .name("Rule A")
//                .variantTriggerPercentage(100)
//                .ruleVariant(
//                    RuleVariant
//                        .builder()
//                        .attributeFilters(attributeFilters)
//                        .build()
//                ).build()
//        );
//
//        // Given: A rule which contains a variant
//        var ruleWithVariants = baseRule(alwaysTriggers)
//            .variants(variants)
//            .type(RuleType.EXPERIMENT)
//            .build();
//
//        // When: The rule engine is with Context.visitorId that corresponds to the first rule variant
//        TreeSet<RuleConfiguration> set = new TreeSet<>();
//        set.add(ruleWithVariants);
//        var ruleEngine = new RuleProcessor(set, instantSupplier, featuresManager);
//
//        var ruleEngineResult = ruleEngine.preProcess(searchParametersVariantA);
//
//        // Then: The result contains the biasing profile from the first rule variant
//        String attributeFiltersInTriggeredRuleVariant = null;
//        if(ruleEngineResult.isPresent()) {
//            var triggeredRule = ruleEngineResult.get().getTriggeredRule();
//            if (triggeredRule.isPresent()) {
//                attributeFiltersInTriggeredRuleVariant = triggeredRule.get().getAttributeFilters().toString();
//            }
//        }
//
//        assertThat(attributeFiltersInTriggeredRuleVariant).isEqualTo(
//            "[" +
//                        "AttributeFilter[" +
//                            "valueFilters=[" +
//                                "ValueFilter(" +
//                                    "field=brands, " +
//                                    "value=Superman, " +
//                                    "numberValue=null, " +
//                                    "exclude=false, " +
//                                    "type=TEXTUAL)" +
//                            "], " +
//                            "rangeFilters=[" +
//                                "RangeFilter(" +
//                                    "field=price, " +
//                                    "range=Range[low=1.0, high=8.0, description=null, exclude=false])" +
//                            "]" +
//                        "], " +
//                        "AttributeFilter[" +
//                            "valueFilters=[" +
//                                "ValueFilter(" +
//                                    "field=brands, " +
//                                    "value=Puma, " +
//                                    "numberValue=null, " +
//                                    "exclude=false, " +
//                                    "type=TEXTUAL)" +
//                            "], " +
//                            "rangeFilters=[" +
//                                "RangeFilter(" +
//                                    "field=price, range=Range[low=1.0, high=10.0, description=null, exclude=false])" +
//                            "]" +
//                        "]" +
//                "]"
//        );
//    }
//
//    private static AttributeFilter createAttributeFilter(
//        List<ValueFilter> valueFilters, List<RangeFilter> rangeFilters) {
//        return new AttributeFilter(
//            valueFilters,
//            rangeFilters);
//    }
//
//    private static ValueFilter createValueFilter(String value) {
//        return ValueFilter.builder()
//            .type(ValueFilter.ValueFilterType.TEXTUAL)
//            .field("brands")
//            .value(value)
//            .exclude(false)
//            .build();
//    }
//
//    private static RangeFilter createRangeFilter(double max) {
//        return RangeFilter.builder()
//            .field("price")
//            .range(new Range(1.0, max, null))
//            .build();
//    }
//
//    private static RuleConfiguration.RuleConfigurationBuilder baseRule(TriggerSet triggerSet) {
//        return RuleConfiguration.builder()
//            .id(new Random().nextInt())
//            .name(RandomString.make(10))
//            .activeHoursEnabled(true)
//            .activeFrom(0L)
//            .activeTo(Long.MAX_VALUE)
//            .enabled(true)
//            .triggerSets(Set.of(triggerSet))
//            .areaId(1);
//    }
//
//}
