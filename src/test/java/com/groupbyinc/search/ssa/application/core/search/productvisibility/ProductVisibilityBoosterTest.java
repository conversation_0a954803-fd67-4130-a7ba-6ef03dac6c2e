package com.groupbyinc.search.ssa.application.core.search.productvisibility;

import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.application.core.search.processor.rule.RuleProcessorResult;
import com.groupbyinc.search.ssa.application.core.search.processor.rule.TriggeredRule;
import com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityBooster.BoostingResult;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.rule.ProductVisibilityBias;
import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
import com.groupbyinc.search.ssa.features.FeaturesManager;

import io.micronaut.core.propagation.PropagatedContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static com.groupbyinc.proto.commandcenter.config.AttributeMessage.AttributeType.NUMERICAL;
import static com.groupbyinc.proto.commandcenter.config.AttributeMessage.AttributeType.TEXTUAL;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.ALL_TRUE;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.ATTRIBUTE;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.EVERY_FIFTH;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.EVERY_SECOND;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.EVERY_TENTH;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.ON_19_20;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.ON_1_5;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.ON_1_8;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.ON_32_40;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.assertScoresEqual;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.attrConfig;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.biasNumAttr;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.biasStrAttr;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.record;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.records;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityTestUtils.score;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_PRODUCT_VISIBILITY_BIAS;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ProductVisibilityBoosterTest {

    ProductVisibilityBooster booster;

    @Mock
    FeaturesManager featuresManager;
    @Mock
    ConfigurationManager configurationManager;

    private PropagatedContext.Scope scope;

    @BeforeEach
    public void setup() {
        booster = new ProductVisibilityBooster(featuresManager, configurationManager);

        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    @MethodSource("testScenarios")
    @ParameterizedTest
    public void boostProducts(boolean featureEnabled,
                              RuleProcessorResult rule,
                              List<Record> records,
                              AttributeConfiguration attrConfig,
                              BoostingResult expect) {
        // given
        when(
            featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_PRODUCT_VISIBILITY_BIAS))
        ).thenReturn(featureEnabled);

        var attributeConfigs = new HashMap<String, AttributeConfiguration>();
        attributeConfigs.put(ATTRIBUTE, attrConfig);
        when(
            configurationManager.getAttributeConfiguration(any(), any())
        ).thenReturn(attributeConfigs);

        //when
        var result = booster.boostProducts(rule, records);

        assertEquals(expect.reranked(), result.reranked());
        assertScoresEqual(expect.score(), result.score());

        if (!expect.reranked()) {
            assertEquals(0, result.rerankedRecords().size());
        } else {
            assertEquals(expect.rerankedRecords().size(), result.rerankedRecords().size());
            for (int i = 0; i < expect.rerankedRecords().size(); i++) {
                var expectRecord = expect.rerankedRecords().get(i);
                var resultRecord = result.rerankedRecords().get(i);
                assertEquals(expectRecord.getProductId(), resultRecord.getProductId());
            }
        }
    }

    private static Stream<Arguments> testScenarios() {
        return Stream.of(
            Arguments.argumentSet(
                "No boosting - no triggered rule",
                true,
                null,
                records(1, TEXTUAL, ALL_TRUE),
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "No boosting - no bias in rule",
                true,
                ruleForBias(null),
                records(1, TEXTUAL, ALL_TRUE),
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "No boosting - bias invalid - no attribute values",
                true,
                ruleForBias(new ProductVisibilityBias(ATTRIBUTE, List.of(), List.of(), 2., 0., 1.)),
                records(1, TEXTUAL, ALL_TRUE),
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "No boosting - bias invalid - both attribute values",
                true,
                ruleForBias(new ProductVisibilityBias(ATTRIBUTE, List.of(), List.of(), 2., 0., 1.)),
                records(1, TEXTUAL, ALL_TRUE),
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "No boosting - attribute configuration missing",
                true,
                ruleForBias(biasStrAttr(2., 0., 1.)),
                records(1, TEXTUAL, ALL_TRUE),
                null,
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "No boosting - attribute configuration NUMERIC, but bias textual",
                true,
                ruleForBias(biasStrAttr(2., 0., 1.)),
                records(1, TEXTUAL, ALL_TRUE),
                attrConfig(NUMERICAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "No boosting - attribute configuration TEXTUAL, but bias numeric",
                true,
                ruleForBias(biasNumAttr(2., 0., 1.)),
                records(1, TEXTUAL, ALL_TRUE),
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "No boosting - feature disabled",
                false,
                ruleForBias(biasStrAttr(2., 0., 1.)),
                records(1, TEXTUAL, ALL_TRUE),
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "Page 40, on 1-8, 3.0x multiplier",
                true,
                ruleForBias(biasStrAttr(2., .5, 1.)),
                records(40, TEXTUAL, ON_1_8), // 0, 1, 2 ... 7
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "Page 40, on 1-8, 2.0x multiplier",
                true,
                ruleForBias(biasStrAttr(2., 0., 1.)),
                records(40, TEXTUAL, ON_1_8), // 0, 1, 2 ... 7
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "Page 40, on 1-8, 1.5x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.25, 1.)),
                records(40, TEXTUAL, ON_1_8), // 0, 1, 2 ... 7
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "Page 40, on 1-8, 1.2x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.4, 1.)),
                records(40, TEXTUAL, ON_1_8), // 0, 1, 2 ... 7
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "Page 40, on 1-8, 1.1x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.45, 1.)),
                records(40, TEXTUAL, ON_1_8), // 0, 1, 2 ... 7
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "Page 40, on 1-8, 1x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.5, 1.)),
                records(40, TEXTUAL, ON_1_8), // 0, 1, 2 ... 7
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "Page 40, equal distribution, 3x multiplier",
                true,
                ruleForBias(biasStrAttr(2., 0.5, 1.)),
                records(40, TEXTUAL, EVERY_SECOND), // 0, 2, 4, 6 ...
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true), // 1
                        record("2", true), // 2
                        record("4", true), // 3
                        record("6", true), // 4
                        record("8", true), // 5
                        record("10", true), // 6
                        record("12", true), // 7
                        record("14", true), // 8  -> 8 * 100 = 800 | target 1496.0
                        record("16", true), // 9
                        record("18", true), // 10
                        record("20", true), // 11
                        record("22", true), // 12
                        record("24", true), // 13
                        record("26", true), // 14
                        record("28", true), // 15
                        record("30", true), // 16 -> 800 + 8 * 67 = 1336 | target 1496.0
                        record("1", false), // 17
                        record("32", true), // 18
                        record("34", true), // 19
                        record("36", true), // 20
                        record("38", true), // 21
                        record("3", false), // 22
                        record("5", false), // 23
                        record("7", false), // 24 -> 1336 + 4 * 40 = 1496 | target 1496.0
                        record("9", false), // 25
                        record("11", false), // 26
                        record("13", false), // 27
                        record("15", false), // 28
                        record("17", false), // 29
                        record("19", false), // 30
                        record("21", false), // 31
                        record("23", false), // 32
                        record("25", false), // 33
                        record("27", false), // 34
                        record("29", false), // 35
                        record("31", false), // 36
                        record("33", false), // 37
                        record("35", false), // 38
                        record("37", false), // 39
                        record("39", false)  // 40
                    ),
                    true,
                    score(996.0, 996.0, 3.0, 1.0, 1496.0, 1992.0, 1496.0, 1496.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, equal distribution, 2x multiplier",
                true,
                ruleForBias(biasStrAttr(2., 0., 1.)),
                records(40, TEXTUAL, EVERY_SECOND), // 0, 2, 4, 6 ...
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true), // 1
                        record("2", true), // 2
                        record("4", true), // 3
                        record("6", true), // 4
                        record("8", true), // 5
                        record("10", true), // 6
                        record("12", true), // 7
                        record("14", true), // 8  -> 8 * 100 = 800 | target 1496.0
                        record("16", true), // 9
                        record("18", true), // 10
                        record("20", true), // 11
                        record("22", true), // 12
                        record("24", true), // 13
                        record("26", true), // 14
                        record("28", true), // 15
                        record("30", true), // 16 -> 800 + 8 * 67 = 1336 | target 1496.0
                        record("1", false), // 17
                        record("32", true), // 18
                        record("34", true), // 19
                        record("36", true), // 20
                        record("38", true), // 21
                        record("3", false), // 22
                        record("5", false), // 23
                        record("7", false), // 24 -> 1336 + 4 * 40 = 1496 | target 1496.0
                        record("9", false), // 25
                        record("11", false), // 26
                        record("13", false), // 27
                        record("15", false), // 28
                        record("17", false), // 29
                        record("19", false), // 30
                        record("21", false), // 31
                        record("23", false), // 32
                        record("25", false), // 33
                        record("27", false), // 34
                        record("29", false), // 35
                        record("31", false), // 36
                        record("33", false), // 37
                        record("35", false), // 38
                        record("37", false), // 39
                        record("39", false)  // 40
                    ),
                    true,
                    score(996.0, 996.0, 2.0, 1.0, 1496.0, 1992.0, 1496.0, 1496.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, equal distribution, 1.5x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.25, 1.)),
                records(40, TEXTUAL, EVERY_SECOND), // 0, 2, 4, 6 ...
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true), // 1
                        record("2", true), // 2
                        record("4", true), // 3
                        record("6", true), // 4
                        record("8", true), // 5
                        record("10", true), // 6
                        record("12", true), // 7
                        record("14", true), // 8 -> 8 * 100 = 800 | target 1469.0
                        record("1", false), // 9
                        record("16", true), // 10
                        record("18", true), // 11
                        record("20", true), // 12
                        record("22", true), // 13
                        record("24", true), // 14
                        record("26", true), // 15
                        record("28", true), // 16 -> 800 + 7 * 67 = 1269 | target 1469.0
                        record("30", true), // 17
                        record("32", true), // 18
                        record("34", true), // 19
                        record("36", true), // 20
                        record("38", true), // 21
                        record("3", false), // 22
                        record("5", false), // 23
                        record("7", false), // 24 -> 1269 + 5 * 40 = 1469 | target 1469.0
                        record("9", false), // 25
                        record("11", false), // 26
                        record("13", false), // 27
                        record("15", false), // 28
                        record("17", false), // 29
                        record("19", false), // 30
                        record("21", false), // 31
                        record("23", false), // 32
                        record("25", false), // 33
                        record("27", false), // 34
                        record("29", false), // 35
                        record("31", false), // 36
                        record("33", false), // 37
                        record("35", false), // 38
                        record("37", false), // 39
                        record("39", false)  // 40
                    ),
                    true,
                    score(996.0, 996.0, 1.5, 1.0, 1496.0, 1992.0, 1494.0, 1469.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, equal distribution, 1.2x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.4, 1.)),
                records(40, TEXTUAL, EVERY_SECOND), // 0, 2, 4, 6 ...
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true), // 1
                        record("1", false), // 2
                        record("2", true), // 3
                        record("3", false), // 4
                        record("4", true), // 5
                        record("6", true), // 6
                        record("8", true), // 7
                        record("10", true), // 8 -> 6 * 100 = 600 | target 1195.2
                        record("12", true), // 9
                        record("14", true), // 10
                        record("16", true), // 11
                        record("18", true), // 12
                        record("20", true), // 13
                        record("22", true), // 14
                        record("5", false), // 15
                        record("7", false), // 16 -> 500 + 6 * 67 = 1002 | target 1195.2
                        record("24", true), // 17
                        record("9", false), // 18
                        record("11", false), // 19
                        record("13", false), // 20
                        record("15", false), // 21
                        record("17", false), // 22
                        record("19", false), // 23
                        record("21", false), // 24 -> 902 + 1 * 40 = 1042 | target 1195.2
                        record("26", true), // 25
                        record("28", true), // 26
                        record("30", true), // 27
                        record("32", true), // 28
                        record("23", false), // 29
                        record("25", false), // 30
                        record("27", false), // 31
                        record("29", false), // 32 -> 1042 + 4 * 25 = 1142 | target 1195.2
                        record("34", true), // 33
                        record("31", false), // 34
                        record("33", false), // 35
                        record("35", false), // 36
                        record("36", true), // 37
                        record("37", false), // 38
                        record("38", true), // 39
                        record("39", false) // 40 -> 1142 + 3 * 17 = 1193 | target 1195.2
                    ),
                    true,
                    score(996.0, 996.0, 1.2, 1.0, 1496.0, 1992.0, 1195.2, 1193.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, equal distribution, 1.1x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.45, 1.)),
                records(40, TEXTUAL, EVERY_SECOND), // 0, 2, 4, 6 ...
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true), // 1
                        record("1", false), // 2
                        record("2", true), // 3
                        record("3", false), // 4
                        record("4", true), // 5
                        record("6", true), // 6
                        record("8", true), // 7
                        record("10", true), // 8 -> 6 * 100 = 600 | target 1095.6
                        record("12", true), // 9
                        record("14", true), // 10
                        record("16", true), // 11
                        record("5", false), // 12
                        record("7", false), // 13
                        record("9", false), // 14
                        record("11", false), // 15
                        record("13", false), // 16 -> 600 + 3 * 67 = 801 | target 1095.6
                        record("18", true), // 17
                        record("15", false), // 18
                        record("17", false), // 19
                        record("19", false), // 20
                        record("20", true), // 21
                        record("21", false), // 22
                        record("22", true), // 23
                        record("23", false), // 24 -> 801 + 3 * 40 = 921 | target 1095.6
                        record("24", true), // 25
                        record("25", false), // 26
                        record("26", true), // 27
                        record("27", false), // 28
                        record("28", true), // 29
                        record("29", false), // 30
                        record("30", true), // 31
                        record("31", false), // 32 -> 921 + 4 * 25 = 1021 | target 1095.6
                        record("32", true), // 33
                        record("33", false), // 34
                        record("34", true), // 35
                        record("35", false), // 36
                        record("36", true), // 37
                        record("37", false), // 38
                        record("38", true), // 39
                        record("39", false) // 40 -> 1021 + 4 * 17 = 1089 | target 1095.6
                    ),
                    true,
                    score(996.0, 996.0, 1.1, 1.0, 1496.0, 1992.0, 1095.6, 1089.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, equal distribution, 1.0x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.5, 1.)),
                records(40, TEXTUAL, EVERY_SECOND), // 0, 2, 4, 6 ...
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "Page 40, every 5th, 3x multiplier",
                true,
                ruleForBias(biasStrAttr(2., .5, 1.)),
                records(40, TEXTUAL, EVERY_FIFTH), // 5, 10, 15...
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true), // 1
                        record("4", true), // 2
                        record("8", true), // 3
                        record("12", true), // 4
                        record("16", true), // 5
                        record("20", true), // 6
                        record("24", true), // 7
                        record("28", true), // 8 -> 8 * 100 = 800 | target 996.0
                        record("1", false), // 9
                        record("32", true), // 10
                        record("36", true), // 11
                        record("2", false), // 12
                        record("3", false), // 13
                        record("5", false), // 14
                        record("6", false), // 15
                        record("7", false), // 16 -> 800 + 2 * 67 = 934 | target 996.0
                        record("9", false), // 17
                        record("10", false), // 18
                        record("11", false), // 19
                        record("13", false), // 20
                        record("14", false), // 21
                        record("15", false), // 22
                        record("17", false), // 23
                        record("18", false), // 24
                        record("19", false), // 25
                        record("21", false), // 26
                        record("22", false), // 27
                        record("23", false), // 28
                        record("25", false), // 29
                        record("26", false), // 30
                        record("27", false), // 31
                        record("29", false), // 32
                        record("30", false), // 33
                        record("31", false), // 34
                        record("33", false), // 35
                        record("34", false), // 36
                        record("35", false), // 37
                        record("37", false), // 38
                        record("38", false), // 39
                        record("39", false)  // 40
                    ),
                    true,
                    score(498.0, 1494.0, 3.0, 1.0, 934.0, 1992.0, 934.0, 934.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, every 5th, 2x multiplier",
                true,
                ruleForBias(biasStrAttr(2., 0., 1.)),
                records(40, TEXTUAL, EVERY_FIFTH), // 5, 10, 15...
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true), // 1
                        record("4", true), // 2
                        record("8", true), // 3
                        record("12", true), // 4
                        record("16", true), // 5
                        record("20", true), // 6
                        record("24", true), // 7
                        record("28", true), // 8 -> 8 * 100 = 800 | target 996.0
                        record("1", false), // 9
                        record("32", true), // 10
                        record("36", true), // 11
                        record("2", false), // 12
                        record("3", false), // 13
                        record("5", false), // 14
                        record("6", false), // 15
                        record("7", false), // 16 -> 800 + 2 * 67 = 934 | target 996.0
                        record("9", false), // 17
                        record("10", false), // 18
                        record("11", false), // 19
                        record("13", false), // 20
                        record("14", false), // 21
                        record("15", false), // 22
                        record("17", false), // 23
                        record("18", false), // 24
                        record("19", false), // 25
                        record("21", false), // 26
                        record("22", false), // 27
                        record("23", false), // 28
                        record("25", false), // 29
                        record("26", false), // 30
                        record("27", false), // 31
                        record("29", false), // 32
                        record("30", false), // 33
                        record("31", false), // 34
                        record("33", false), // 35
                        record("34", false), // 36
                        record("35", false), // 37
                        record("37", false), // 38
                        record("38", false), // 39
                        record("39", false)  // 40
                    ),
                    true,
                    score(498.0, 1494.0, 2.0, 1.0, 934.0, 1992.0, 934.0, 934.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, every 5th, 1.5x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.25, 1.)),
                records(40, TEXTUAL, EVERY_FIFTH), // 5, 10, 15...
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true), // 1
                        record("1", false), // 2
                        record("2", false), // 3
                        record("3", false), // 4
                        record("4", true), // 5
                        record("8", true), // 6
                        record("12", true), // 7
                        record("16", true), // 8 -> 5 *100 = 500 | target 743.0
                        record("20", true), // 9
                        record("24", true), // 10
                        record("28", true), // 11
                        record("5", false), // 12
                        record("6", false), // 13
                        record("7", false), // 14
                        record("9", false), // 15
                        record("10", false), // 16 -> 500 + 3 * 67 = 701 | target 743.0
                        record("11", false), // 17
                        record("13", false), // 18
                        record("14", false), // 19
                        record("15", false), // 20
                        record("17", false), // 21
                        record("18", false), // 22
                        record("19", false), // 23
                        record("21", false), // 24 -> 701 + 0 * 40 = 701 | target 743.0
                        record("32", true), // 25
                        record("22", false), // 26
                        record("23", false), // 27
                        record("25", false), // 28
                        record("26", false), // 29
                        record("27", false), // 30
                        record("29", false), // 31
                        record("30", false), // 32 -> 701 + 1 * 25 = 726 | target 743.0
                        record("31", false), // 33
                        record("33", false), // 34
                        record("34", false), // 35
                        record("35", false), // 36
                        record("36", true), // 37
                        record("37", false), // 38
                        record("38", false), // 39
                        record("39", false)  // 40 -> 726 + 1 * 17 = 743 | target 743.0
                    ),
                    true,
                    score(498.0, 1494.0, 1.5, 1.0, 934.0, 1992.0, 747.0, 743.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, every 5th, 1.2x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.4, 1.)),
                records(40, TEXTUAL, EVERY_FIFTH), // 5, 10, 15...
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true), // 1
                        record("1", false), // 2
                        record("2", false), // 3
                        record("3", false), // 4
                        record("4", true), // 5
                        record("8", true), // 6
                        record("12", true), // 7
                        record("5", false), // 8 -> 4 * 100 = 400 | target 597.6
                        record("16", true), // 9
                        record("6", false), // 10
                        record("7", false), // 11
                        record("9", false), // 12
                        record("10", false), // 13
                        record("11", false), // 14
                        record("13", false), // 15
                        record("14", false), // 16 -> 400 + 1 * 67 = 467 | target 597.6
                        record("15", false), // 17
                        record("17", false), // 18
                        record("18", false), // 19
                        record("19", false), // 20
                        record("20", true), // 21
                        record("21", false), // 22
                        record("22", false), // 23
                        record("23", false), // 24 -> 467 + 1 * 40 = 507 | target 597.6
                        record("24", true), // 25
                        record("25", false), // 26
                        record("26", false), // 27
                        record("27", false), // 28
                        record("28", true), // 29
                        record("29", false), // 30
                        record("30", false), // 31
                        record("31", false), // 32 -> 507 + 2 * 25 = 557 | target 597.6
                        record("32", true), // 33
                        record("33", false), // 34
                        record("34", false), // 35
                        record("35", false), // 36
                        record("36", true), // 37
                        record("37", false), // 38
                        record("38", false), // 39
                        record("39", false)  // 40 -> 557 + 2 * 17 = 591 | target 597.6
                    ),
                    true,
                    score(498.0, 1494.0, 1.2, 1.0, 934.0, 1992.0, 597.6, 591.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, every 5th, 1.1x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.45, 1.)),
                records(40, TEXTUAL, EVERY_FIFTH), // 5, 10, 15...
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true), // 1
                        record("1", false), // 2
                        record("2", false), // 3
                        record("3", false), // 4
                        record("4", true), // 5
                        record("8", true), // 6
                        record("5", false), // 7
                        record("6", false), // 8 -> 3 * 100 = 300 | target 547.8
                        record("12", true), // 9
                        record("7", false), // 10
                        record("9", false), // 11
                        record("10", false), // 12
                        record("11", false), // 13
                        record("13", false), // 14
                        record("14", false), // 15
                        record("15", false), // 16 -> 300 + 1 * 67 = 367 | target 547.8
                        record("16", true), // 17
                        record("20", true), // 18
                        record("24", true), // 19
                        record("17", false), // 20
                        record("18", false), // 21
                        record("19", false), // 22
                        record("21", false), // 23
                        record("22", false), // 24 -> 367 + 3 * 40 = 487 | target 547.8
                        record("28", true), // 25
                        record("23", false), // 26
                        record("25", false), // 27
                        record("26", false), // 28
                        record("27", false), // 29
                        record("29", false), // 30
                        record("30", false), // 31
                        record("31", false), // 32 -> 487 + 1 * 25 = 512 | target 547.8
                        record("32", true), // 33
                        record("33", false), // 34
                        record("34", false), // 35
                        record("35", false), // 36
                        record("36", true), // 37
                        record("37", false), // 38
                        record("38", false), // 39
                        record("39", false)  // 40 -> 512 + 2 * 17 = 546 | target 547.8
                    ),
                    true,
                    score(498.0, 1494.0, 1.1, 1.0, 934.0, 1992.0, 547.8, 546.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, every 5th, 1x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.5, 1.)),
                records(40, TEXTUAL, EVERY_FIFTH), // 5, 10, 15...
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "Page 40, on 32-40, 3x multiplier",
                true,
                ruleForBias(biasStrAttr(2., .5, 1.)),
                records(40, TEXTUAL, ON_32_40), // 32, 33, 34 ... 40
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", false), // 1
                        record("1", false), // 2
                        record("2", false), // 3
                        record("3", false), // 4
                        record("4", false), // 5
                        record("32", true), // 6
                        record("33", true), // 7
                        record("34", true), // 8 -> 3 * 100 = 300 | target 408.0
                        record("5", false), // 9
                        record("6", false), // 10
                        record("7", false), // 11
                        record("8", false), // 12
                        record("9", false), // 13
                        record("10", false), // 14
                        record("11", false), // 15
                        record("12", false), // 16 -> 300 + 0 * 67 = 300 | target 408.0
                        record("35", true), // 17
                        record("13", false), // 18
                        record("14", false), // 19
                        record("15", false), // 20
                        record("16", false), // 21
                        record("17", false), // 22
                        record("18", false), // 23
                        record("19", false), // 24 -> 300 + 1 * 40 = 340 | target 408.0
                        record("20", false), // 25
                        record("21", false), // 26
                        record("22", false), // 27
                        record("23", false), // 28
                        record("24", false), // 29
                        record("25", false), // 30
                        record("26", false), // 31
                        record("27", false), // 32 -> 340 + 0 * 25 = 340 | target 408.0
                        record("28", false), // 33
                        record("29", false), // 34
                        record("30", false), // 35
                        record("31", false), // 36
                        record("36", true), // 37
                        record("37", true), // 38
                        record("38", true), // 39
                        record("39", true)  // 40 -> 340 + 4 * 17 = 408 | target 408.0
                    ),
                    true,
                    score(136.0, 1856.0, 3.0, 1.0, 800.0, 1992.0, 408.0, 408.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, on 32-40, 2x multiplier",
                true,
                ruleForBias(biasStrAttr(2., 0., 1.)),
                records(40, TEXTUAL, ON_32_40), // 32, 33, 34 ... 40
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", false), // 1
                        record("1", false), // 2
                        record("2", false), // 3
                        record("3", false), // 4
                        record("4", false), // 5
                        record("32", true), // 6
                        record("5", false), // 7
                        record("6", false), // 8 -> 1 * 100 = 100 | target 272.0
                        record("33", true), // 9
                        record("7", false), // 10
                        record("8", false), // 11
                        record("9", false), // 12
                        record("10", false), // 13
                        record("11", false), // 14
                        record("12", false), // 15
                        record("13", false), // 16 -> 100 + 1 * 67 = 167 | target 272.0
                        record("14", false), // 17
                        record("15", false), // 18
                        record("16", false), // 19
                        record("17", false), // 20
                        record("18", false), // 21
                        record("19", false), // 22
                        record("20", false), // 23
                        record("21", false), // 24 -> 167 + 0 * 40 = 167 | target 272.0
                        record("22", false), // 25
                        record("23", false), // 26
                        record("24", false), // 27
                        record("25", false), // 28
                        record("26", false), // 29
                        record("27", false), // 30
                        record("28", false), // 31
                        record("29", false), // 32 -> 167 + 0 * 25 = 167 | target 272.0
                        record("30", false), // 33
                        record("31", false), // 34
                        record("34", true), // 35
                        record("35", true), // 36
                        record("36", true), // 37
                        record("37", true), // 38
                        record("38", true), // 39
                        record("39", true)  // 40 -> 167 + 6 * 17 = 269 | target 272.0
                    ),
                    true,
                    score(136.0, 1856.0, 2.0, 1.0, 800.0, 1992.0, 272.0, 269.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, on 32-40, 1.5x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.25, 1.)),
                records(40, TEXTUAL, ON_32_40), // 32, 33, 34 ... 40
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", false), // 1
                        record("1", false), // 2
                        record("2", false), // 3
                        record("3", false), // 4
                        record("4", false), // 5
                        record("5", false), // 6
                        record("6", false), // 7
                        record("7", false), // 8 -> 0 * 100 = 0 | target 204.0
                        record("32", true), // 9
                        record("8", false), // 10
                        record("9", false), // 11
                        record("10", false), // 12
                        record("11", false), // 13
                        record("12", false), // 14
                        record("13", false), // 15
                        record("14", false), // 16 -> 0 + 1 * 67 = 67 | target 204.0
                        record("15", false), // 17
                        record("16", false), // 18
                        record("17", false), // 19
                        record("18", false), // 20
                        record("19", false), // 21
                        record("20", false), // 22
                        record("21", false), // 23
                        record("22", false), // 24 -> 67 + 0 * 40 = 67 | target 204.0
                        record("33", true), // 25
                        record("34", true), // 26
                        record("23", false), // 27
                        record("24", false), // 28
                        record("25", false), // 29
                        record("26", false), // 30
                        record("27", false), // 31
                        record("28", false), // 32 -> 67 + 2 * 25 = 117 | target 204.0
                        record("35", true), // 33
                        record("29", false), // 34
                        record("30", false), // 35
                        record("31", false), // 36
                        record("36", true), // 37
                        record("37", true), // 38
                        record("38", true), // 39
                        record("39", true)  // 40 -> 117 + 5 * 17 = 202 | target 204.0
                    ),
                    true,
                    score(136.0, 1856.0, 1.5, 1.0, 800.0, 1992.0, 204.0, 202.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, on 32-40, 1.2x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.4, 1.)),
                records(40, TEXTUAL, ON_32_40), // 32, 33, 34 ... 40
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", false), // 1
                        record("1", false), // 2
                        record("2", false), // 3
                        record("3", false), // 4
                        record("4", false), // 5
                        record("5", false), // 6
                        record("6", false), // 7
                        record("7", false), // 8 -> 0 * 100 = 0 | target 163.2
                        record("8", false), // 9
                        record("9", false), // 10
                        record("10", false), // 11
                        record("11", false), // 12
                        record("12", false), // 13
                        record("13", false), // 14
                        record("14", false), // 15
                        record("15", false), // 16 -> 0 + 0 * 67 = 0 | target 163.2
                        record("32", true), // 17
                        record("16", false), // 18
                        record("17", false), // 19
                        record("18", false), // 20
                        record("19", false), // 21
                        record("20", false), // 22
                        record("21", false), // 23
                        record("22", false), // 24 -> 0 + 1 * 40 = 40 | target 163.2
                        record("23", false), // 25
                        record("24", false), // 26
                        record("25", false), // 27
                        record("26", false), // 28
                        record("27", false), // 29
                        record("28", false), // 30
                        record("29", false), // 31
                        record("30", false), // 32 -> 40 + 0 * 25 = 40 | target 163.2
                        record("31", false), // 33
                        record("33", true), // 34
                        record("34", true), // 35
                        record("35", true), // 36
                        record("36", true), // 37
                        record("37", true), // 38
                        record("38", true), // 39
                        record("39", true)  // 40 -> 40 + 7 * 17 = 159 | target 163.2
                    ),
                    true,
                    score(136.0, 1856.0, 1.2, 1.0, 800.0, 1992.0, 163.2, 159.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, on 32-40, 1.1x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.45, 1.)),
                records(40, TEXTUAL, ON_32_40), // 32, 33, 34 ... 40
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", false), // 1
                        record("1", false), // 2
                        record("2", false), // 3
                        record("3", false), // 4
                        record("4", false), // 5
                        record("5", false), // 6
                        record("6", false), // 7
                        record("7", false), // 8 -> 0 * 100 = 0 | target 149.6
                        record("8", false), // 9
                        record("9", false), // 10
                        record("10", false), // 11
                        record("11", false), // 12
                        record("12", false), // 13
                        record("13", false), // 14
                        record("14", false), // 15
                        record("15", false), // 16 -> 0 + 0 * 67 = 0 | target 149.6
                        record("16", false), // 17
                        record("17", false), // 18
                        record("18", false), // 19
                        record("19", false), // 20
                        record("20", false), // 21
                        record("21", false), // 22
                        record("22", false), // 23
                        record("23", false), // 24 -> 0 + 0 * 40 = 40 | target 149.6
                        record("32", true), // 25
                        record("24", false), // 26
                        record("25", false), // 27
                        record("26", false), // 28
                        record("27", false), // 29
                        record("28", false), // 30
                        record("29", false), // 31
                        record("30", false), // 32 -> 0 + 1 * 25 = 25 | target 149.6
                        record("31", false), // 33
                        record("33", true), // 34
                        record("34", true), // 35
                        record("35", true), // 36
                        record("36", true), // 37
                        record("37", true), // 38
                        record("38", true), // 39
                        record("39", true)  // 40 -> 25 + 7 * 17 = 144 | target 149.6
                    ),
                    true,
                    score(136.0, 1856.0, 1.1, 1.0, 800.0, 1992.0, 149.6, 144.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, on 32-40, 1x multiplier",
                true,
                ruleForBias(biasStrAttr(2., -.5, 1.)),
                records(40, TEXTUAL, ON_32_40), // 32, 33, 34 ... 40
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "Page 40, every 5th, 2x multiplier, locked: 1-5",
                true,
                ruleForBias(biasStrAttr(2., 0., 1.)),
                records(40, TEXTUAL, EVERY_FIFTH, ON_1_5),
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true, true), // 1
                        record("1", false, true), // 2
                        record("2", false, true), // 3
                        record("3", false, true), // 4
                        record("4", true, true), // 5
                        record("8", true), // 6
                        record("12", true), // 7
                        record("16", true), // 8 -> 5 * 100 = 500 | target 835.0
                        record("20", true), // 9
                        record("24", true), // 10
                        record("28", true), // 11
                        record("32", true), // 12
                        record("36", true), // 13
                        record("5", false), // 14
                        record("6", false), // 15
                        record("7", false), // 16 -> 500 + 5 * 67 = 835 | target 835.0
                        record("9", false), // 17
                        record("10", false), // 18
                        record("11", false), // 19
                        record("13", false), // 20
                        record("14", false), // 21
                        record("15", false), // 22
                        record("17", false), // 23
                        record("18", false), // 24
                        record("19", false), // 25
                        record("21", false), // 26
                        record("22", false), // 27
                        record("23", false), // 28
                        record("25", false), // 29
                        record("26", false), // 30
                        record("27", false), // 31
                        record("29", false), // 32
                        record("30", false), // 33
                        record("31", false), // 34
                        record("33", false), // 35
                        record("34", false), // 36
                        record("35", false), // 37
                        record("37", false), // 38
                        record("38", false), // 39
                        record("39", false)  // 40
                    ),
                    true,
                    score(498.0, 1494.0, 2.0, 1.0, 835.0, 1992.0, 835.0, 835.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, every 5th, 1.5x multiplier, locked: 1-5",
                true,
                ruleForBias(biasStrAttr(2., -.25, 1.)),
                records(40, TEXTUAL, EVERY_FIFTH, ON_1_5),
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true, true), // 1
                        record("1", false, true), // 2
                        record("2", false, true), // 3
                        record("3", false, true), // 4
                        record("4", true, true), // 5
                        record("8", true), // 6
                        record("12", true), // 7
                        record("16", true), // 8 -> 5 * 100 = 500 | target 747.0
                        record("20", true), // 9
                        record("24", true), // 10
                        record("28", true), // 11
                        record("5", false), // 12
                        record("6", false), // 13
                        record("7", false), // 14
                        record("9", false), // 15
                        record("10", false), // 16 -> 500 + 3 * 67 = 701 | target 747.0
                        record("11", false), // 17
                        record("13", false), // 18
                        record("14", false), // 19
                        record("15", false), // 20
                        record("17", false), // 21
                        record("18", false), // 22
                        record("19", false), // 23
                        record("21", false), // 24 -> 701 + 0 * 40 = 701 | target 747.0
                        record("32", true), // 25
                        record("22", false), // 26
                        record("23", false), // 27
                        record("25", false), // 28
                        record("26", false), // 29
                        record("27", false), // 30
                        record("29", false), // 31
                        record("30", false), // 32 -> 701 + 1 * 25 = 726 | target 747.0
                        record("31", false), // 33
                        record("33", false), // 34
                        record("34", false), // 35
                        record("35", false), // 36
                        record("36", true), // 37
                        record("37", false), // 38
                        record("38", false), // 39
                        record("39", false)  // 40 -> 726 + 1 * 17 = 743 | target 747.0
                    ),
                    true,
                    score(498.0, 1494.0, 1.5, 1.0, 835.0, 1992.0, 747.0, 743.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, every 5th, 2x multiplier, locked: every 2nd",
                true,
                ruleForBias(biasStrAttr(2., 0., 1.)),
                records(40, TEXTUAL, EVERY_FIFTH, EVERY_SECOND),
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "Page 40, every 5th, 1.5x multiplier, locked: every 5th",
                true,
                ruleForBias(biasStrAttr(2., -.25, 1.)),
                records(40, TEXTUAL, EVERY_FIFTH, EVERY_FIFTH),
                attrConfig(TEXTUAL),
                BoostingResult.notApplicable()
            ),
            Arguments.argumentSet(
                "Page 40, every 5th, 2x multiplier, locked: every 10th",
                true,
                ruleForBias(biasStrAttr(2., 0., 1.)),
                records(40, TEXTUAL, EVERY_SECOND, EVERY_TENTH),
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true, true), // 1
                        record("2", true), // 2
                        record("4", true), // 3
                        record("6", true), // 4
                        record("8", true), // 5
                        record("1", false), // 6
                        record("3", false), // 7
                        record("5", false), // 8 -> 5 * 100 = 500 | target 1469.0
                        record("7", false), // 9
                        record("9", false, true), // 10
                        record("10", true), // 11
                        record("12", true), // 12
                        record("14", true), // 13
                        record("16", true), // 14
                        record("11", false), // 15
                        record("13", false), // 16 -> 500 + 4 * 67 = 768 | target 1469.0
                        record("15", false), // 17
                        record("17", false), // 18
                        record("18", true, true), // 19
                        record("20", true), // 20
                        record("22", true), // 21
                        record("24", true), // 22
                        record("26", true), // 23
                        record("19", false), // 24 -> 768 + 5 * 40 = 968 | target 1469.0
                        record("21", false), // 25
                        record("23", false), // 26
                        record("25", false), // 27
                        record("27", false, true), // 28
                        record("28", true), // 29
                        record("30", true), // 30
                        record("32", true), // 31
                        record("34", true), // 32 -> 968 + 4 * 25 = 1068 | target 1469.0
                        record("29", false), // 33
                        record("31", false), // 34
                        record("33", false), // 35
                        record("35", false), // 36
                        record("36", true, true), // 37
                        record("38", true), // 38
                        record("37", false), // 39
                        record("39", false)  // 40 -> 1068 + 2 * 17 = 1102 | target 1469.0
                    ),
                    true,
                    score(996.0, 996.0, 2.0, 1.0, 1469.0, 1992.0, 1469.0, 1102.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, every 5th, 1.2x multiplier, locked: every 10th",
                true,
                ruleForBias(biasStrAttr(2., -.4, 1.)),
                records(40, TEXTUAL, EVERY_SECOND, EVERY_TENTH),
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true, true), // 1
                        record("2", true), // 2
                        record("4", true), // 3
                        record("6", true), // 4
                        record("8", true), // 5
                        record("1", false), // 6
                        record("3", false), // 7
                        record("5", false), // 8 -> 5 * 100 = 500 | target 1195.2
                        record("7", false), // 9
                        record("9", false, true), // 10
                        record("10", true), // 11
                        record("12", true), // 12
                        record("14", true), // 13
                        record("16", true), // 14
                        record("11", false), // 15
                        record("13", false), // 16 -> 500 + 4 * 67 = 768 | target 1195.2
                        record("15", false), // 17
                        record("17", false), // 18
                        record("18", true, true), // 19
                        record("20", true), // 20
                        record("22", true), // 21
                        record("24", true), // 22
                        record("26", true), // 23
                        record("19", false), // 24 -> 768 + 5 * 40 = 968 | target 1195.2
                        record("21", false), // 25
                        record("23", false), // 26
                        record("25", false), // 27
                        record("27", false, true), // 28
                        record("28", true), // 29
                        record("30", true), // 30
                        record("32", true), // 31
                        record("34", true), // 32 -> 968 + 4 * 25 = 1068 | target 1195.2
                        record("29", false), // 33
                        record("31", false), // 34
                        record("33", false), // 35
                        record("35", false), // 36
                        record("36", true, true), // 37
                        record("38", true), // 38
                        record("37", false), // 39
                        record("39", false)  // 40 -> 1068 + 2 * 17 = 1102 | target 1195.2
                    ),
                    true,
                    score(996.0, 996.0, 1.2, 1.0, 1469.0, 1992.0, 1195.2, 1102.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, every 5th, 2x multiplier, locked: [19, 20]",
                true,
                ruleForBias(biasStrAttr(2., 0., 1.)),
                records(40, TEXTUAL, EVERY_SECOND, ON_19_20),
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true), // 1
                        record("2", true), // 2
                        record("4", true), // 3
                        record("6", true), // 4
                        record("8", true), // 5
                        record("10", true), // 6
                        record("12", true), // 7
                        record("14", true), // 8 -> 8 * 100 = 800 | target 1496.0
                        record("16", true), // 9
                        record("18", true), // 10
                        record("1", false), // 11
                        record("3", false), // 12
                        record("5", false), // 13
                        record("7", false), // 14
                        record("9", false), // 15
                        record("11", false), // 16 -> 800 + 2 * 67 = 934 | target 1496.0
                        record("13", false), // 17
                        record("15", false), // 18
                        record("17", false), // 19
                        record("19", false, true), // 20
                        record("20", true, true), // 21
                        record("22", true), // 22
                        record("24", true), // 23
                        record("26", true), // 24 -> 934 + 4 * 40 = 1094 | target 1496.0
                        record("28", true), // 25
                        record("30", true), // 26
                        record("32", true), // 27
                        record("34", true), // 28
                        record("36", true), // 29
                        record("38", true), // 30
                        record("21", false), // 31
                        record("23", false), // 32 -> 1094 + 6 * 25 = 1244 | target 1496.0
                        record("25", false), // 33
                        record("27", false), // 34
                        record("29", false), // 35
                        record("31", false), // 36
                        record("33", false), // 37
                        record("35", false), // 38
                        record("37", false), // 39
                        record("39", false)  // 40
                    ),
                    true,
                    score(996.0, 996.0, 2.0, 1.0, 1496.0, 1992.0, 1496.0, 1244.0)
                )
            ),
            Arguments.argumentSet(
                "Page 40, every 5th, 1.2x multiplier, locked: [19, 20]",
                true,
                ruleForBias(biasStrAttr(2., -.4, 1.)),
                records(40, TEXTUAL, EVERY_SECOND, ON_19_20),
                attrConfig(TEXTUAL),
                new BoostingResult(
                    List.of(
                        record("0", true), // 1
                        record("1", false), // 2
                        record("2", true), // 3
                        record("3", false), // 4
                        record("4", true), // 5
                        record("6", true), // 6
                        record("8", true), // 7
                        record("10", true), // 8 -> 6 * 100 = 600 | target 1195.2
                        record("12", true), // 9
                        record("14", true), // 10
                        record("16", true), // 11
                        record("18", true), // 12
                        record("5", false), // 13
                        record("7", false), // 14
                        record("9", false), // 15
                        record("11", false), // 16 -> 600 + 4 * 67 = 868 | target 1195.2
                        record("13", false), // 17
                        record("15", false), // 18
                        record("17", false), // 19
                        record("19", false, true), // 20
                        record("20", true, true), // 21
                        record("22", true), // 22
                        record("24", true), // 23
                        record("26", true), // 24 -> 868 + 4 * 40 = 1028 | target 1195.2
                        record("28", true), // 25
                        record("30", true), // 26
                        record("32", true), // 27
                        record("34", true), // 28
                        record("36", true), // 29
                        record("38", true), // 30
                        record("21", false), // 31
                        record("23", false), // 32 -> 1028 + 6 * 25 = 1178 | target 1195.2
                        record("25", false), // 33
                        record("27", false), // 34
                        record("29", false), // 35
                        record("31", false), // 36
                        record("33", false), // 37
                        record("35", false), // 38
                        record("37", false), // 39
                        record("39", false)  // 40
                    ),
                    true,
                    score(996.0, 996.0, 1.2, 1.0, 1496.0, 1992.0, 1195.2, 1178.0)
                )
            )
        );
    }

    private static RuleProcessorResult ruleForBias(ProductVisibilityBias bias) {
        return RuleProcessorResult.builder()
            .triggeredRule(Optional.of(
                new TriggeredRule(
                    RuleConfiguration.builder()
                        .productVisibilityBias(bias)
                        .build(),
                    null,
                    null
                )
            )).build();
    }

}
