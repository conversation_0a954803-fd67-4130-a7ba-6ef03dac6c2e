package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.BiasingProfileMessage;
import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.utils.embedded.pubsub.PubsubExtension;
import com.groupbyinc.utils.pubsub.client.PubsubClient;

import com.google.api.gax.core.NoCredentialsProvider;
import com.google.protobuf.ByteString;
import com.google.pubsub.v1.PubsubMessage;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.Collections;
import java.util.concurrent.TimeoutException;

import static java.util.concurrent.TimeUnit.SECONDS;
import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class SubscriberFactoryIntegrationTest {

    public static final String CONFIG_TOPIC = "config";
    public static final String INGESTION_TOPIC = "ingestion-topic";
    public static final String CONFIG_SUBSCRIPTION = "subscription";
    public static final String INGESTION_SUBSCRIPTION = "ingestion-subscription";

    public static final String HOST = "host";
    private static PubsubExtension pubSubExtension;
    private static PubsubClient client;
    private ConfigUpdater updaterMock;
    private SubscriberFactory factory;

    @BeforeAll
    public static void setUpSpec() {
        pubSubExtension = new PubsubExtension();
        pubSubExtension.beforeAll(null);
        var projectId = pubSubExtension.getGcpProjectId();
        client = new PubsubClient(projectId,
            NoCredentialsProvider.create(),
            pubSubExtension.getTransportChannelProvider()
        );
    }

    @BeforeEach
    public void setUp() {
        factory = createPubSubFactory();
    }

    @AfterEach
    public void cleanUp() throws TimeoutException {
        factory.shutdown();
        pubSubExtension.afterEach(null);
        updaterMock = null;
    }

    @AfterAll
    public static void cleanUpSpec() throws Exception {
        pubSubExtension.afterAll(null);
        pubSubExtension = null;
        client = null;
    }

    @Test
    @DisplayName("Verify update message received")
    void updateMessageReceived() {
        String localSubscription = CONFIG_SUBSCRIPTION + 1;
        pubSubExtension.listenOn(CONFIG_TOPIC, localSubscription);
        when(updaterMock.isApplicable(any())).thenReturn(true);

        pubSubExtension.publish(CONFIG_TOPIC, createPubSubMessage());

        pubSubExtension.awaitMessage(localSubscription, Duration.ofSeconds(2));

        await().atMost(2, SECONDS).untilAsserted(() ->{
            verify(updaterMock).isApplicable(any());
            verify(updaterMock).update(any());
            }
        );
    }

    @Test
    @DisplayName("Verify update message received but no updaters available")
    void updateMessageReceivedNotUpdated() {
        String localSubscription = CONFIG_SUBSCRIPTION + 1;
        pubSubExtension.listenOn(CONFIG_TOPIC, localSubscription);

        pubSubExtension.publish(CONFIG_TOPIC, createPubSubMessage());

        pubSubExtension.awaitMessage(localSubscription, Duration.ofSeconds(2));

        verify(updaterMock).isApplicable(any());
        verify(updaterMock, never()).update(any());
    }

    @Test
    @DisplayName("Verify update message received but no has wrong format")
    void updateMessageReceivedFormatException() {
        String localSubscription = CONFIG_SUBSCRIPTION + 1;
        pubSubExtension.listenOn(CONFIG_TOPIC, localSubscription);

        var message = PubsubMessage.newBuilder()
            .setData(ByteString.copyFromUtf8("{}"))
            .build();
        pubSubExtension.publish(CONFIG_TOPIC, message);

        pubSubExtension.awaitMessage(localSubscription, Duration.ofSeconds(2));

        verify(updaterMock, never()).isApplicable(any());
        verify(updaterMock, never()).update(any());

    }

    private PubsubMessage createPubSubMessage() {
        var message = SiteSearchConfigurationUpdateMessage.newBuilder()
            .addBiasingProfile(BiasingProfileMessage.newBuilder()
                .setName("New Profile")
                .build())
            .build();
        return PubsubMessage.newBuilder()
            .setData(message.toByteString())
            .build();
    }

    SubscriberFactory createPubSubFactory() {
        updaterMock = mock(ConfigUpdater.class);
        var factory = new SubscriberFactory(
            client,
            Collections.singleton(updaterMock),
            600,
            86400,
            1,
            CONFIG_TOPIC,
            CONFIG_SUBSCRIPTION,
            HOST,
            INGESTION_TOPIC,
            INGESTION_SUBSCRIPTION,
            null
        );
        factory.init();
        return factory;
    }

}
