package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.MessageType;
import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.proto.commandcenter.config.TenantMessage;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.tenant.TenantConfiguration;

import com.groupbyinc.search.ssa.core.tenant.TenantSettings;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
@DisplayName("TenantUpdater Tests")
class TenantUpdaterTest {

    @Mock
    private ConfigurationManager configurationManager;
    private TenantUpdater updater;

    @BeforeEach
    void setUp() {
        updater = new TenantUpdater(configurationManager);
    }

    @Test
    @SuppressWarnings("unchecked")
    @DisplayName("Map TenantMessage to TenantConfiguration and update")
    void mapAndUpdateTenants() {
        TenantMessage tenantMessage = TenantMessage
            .newBuilder()
            .setId(1)
            .setName("name")
            .setMessageType(MessageType.DELETE)
            .setEnabled(true)
            .setTenantSettings(TenantMessage.TenantSettings
                .newBuilder()
                .setIncludeExpandedResults(true)
                .setFacetLimit(300)
                .build()
            )
            .build();

        SiteSearchConfigurationUpdateMessage updateMessage = SiteSearchConfigurationUpdateMessage
            .newBuilder()
            .addTenant(tenantMessage)
            .build();

        updater.update(updateMessage);


        List<TenantConfiguration> expected = List.of(TenantConfiguration.builder()
                .id(1)
                .enabled(true)
                .name("name")
                .messageType(MessageType.DELETE)
                .settings(new TenantSettings(true, 300))
                .build()
        );
        ArgumentCaptor<List<TenantConfiguration>> argument = ArgumentCaptor.forClass(List.class);
        verify(configurationManager).updateTenantConfig(argument.capture());
        assertEquals(expected, argument.getValue());
        verify(configurationManager, times(1)).updateTenantConfig(expected);
    }
}
