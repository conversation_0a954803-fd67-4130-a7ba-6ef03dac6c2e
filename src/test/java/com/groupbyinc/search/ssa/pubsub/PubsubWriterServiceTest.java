package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.search.ssa.api.dto.Experiment;
import com.groupbyinc.search.ssa.api.dto.RecordDto;
import com.groupbyinc.search.ssa.api.dto.SearchMetadataDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SearchResponseDto;
import com.groupbyinc.search.ssa.beacon.client.DirectSearchBeaconRequest;
import com.groupbyinc.search.ssa.core.SearchMetadata;
import com.groupbyinc.search.ssa.util.Constants;
import com.groupbyinc.utils.embedded.pubsub.PubsubExtension;
import com.groupbyinc.utils.pubsub.client.PubsubClient;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.gax.core.NoCredentialsProvider;
import lombok.SneakyThrows;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.groupbyinc.search.ssa.beacon.config.DirectSearchBeaconConfiguration.EVENT_TYPE;
import static com.groupbyinc.search.ssa.stub.TestData.COLLECTION;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
@DisplayName("PubsubWriterServiceTest Tests")
class PubsubWriterServiceTest {

    private static final String DIRECT_SEARCH_TOPIC_NAME = "wis-direct-search-topic";
    private static final String DIRECT_SEARCH_SUBSCRIPTION_NAME = "wis-direct-search-topic-subscription";

    @RegisterExtension
    private static final PubsubExtension pubSubExtension = new PubsubExtension();
    private static PubsubClient client;

    @BeforeAll
    public static void setUpSpec() {
        client = new PubsubClient(pubSubExtension.getGcpProjectId(),
            NoCredentialsProvider.create(),
            pubSubExtension.getTransportChannelProvider()
        );
    }

    @AfterAll
    public static void cleanUpSpec() {
        client.close();
    }

    @SneakyThrows
    @Test
    @DisplayName("Send direst search message successfully")
    void sendDirectSearch() {
        var mapper = new ObjectMapper();
        var topic = client.ensureTopicExists(DIRECT_SEARCH_TOPIC_NAME);
        var pubsubService = new PubsubWriterService(mapper, client.newPublisher(topic, 1, null));
        pubSubExtension.listenOn(DIRECT_SEARCH_TOPIC_NAME, DIRECT_SEARCH_SUBSCRIPTION_NAME);
        var input = getSearchRequest();

        var result = pubsubService.sendDirectBeacon(input);
        assertTrue(result);

        var receivedMessage = pubSubExtension.awaitMessage(
            DIRECT_SEARCH_SUBSCRIPTION_NAME,
            Duration.ofSeconds(3));
        var receivedPayload = mapper.readValue(
            receivedMessage.getData().toString("UTF-8"),
            new TypeReference<DirectSearchBeaconRequest<SearchResponseDto>>() {}
        );

        // verify a few fields to make sure valid event data is sent
        assertEquals(input.customerId(), receivedMessage.getAttributesOrThrow(Constants.GROUPBY_CUSTOMER_ID_HEADER));
        assertEquals(input.customerId(), receivedPayload.customerId());
        assertEquals(input.eventType(), receivedPayload.eventType());
        assertEquals(input.responseId(), receivedPayload.responseId());
        assertEquals(input.event().getArea(), receivedPayload.event().getArea());
        assertEquals(input.event().getQuery(), receivedPayload.event().getQuery());
        assertEquals(input.event().getRecords().size(), receivedPayload.event().getRecords().size());
        assertEquals(
            input.event().getRecords().getFirst().getId(),
            receivedPayload.event().getRecords().getFirst().getId()
        );
        assertEquals(
            input.event().getRecords().getFirst().getTitle(),
            receivedPayload.event().getRecords().getFirst().getTitle()
        );
    }

    @Test
    @DisplayName("Send direst search with empty message")
    void sendDirectSearchWithEmptyRequest() {
        var topic = client.ensureTopicExists("wis-direct-search-topic");
        var directSearchPublisher = client.newPublisher(topic, 1, null);
        var pubsubService = new PubsubWriterService(new ObjectMapper(), directSearchPublisher);

        var result = pubsubService.sendDirectBeacon(null);
        assertFalse(result);
    }

    @Test
    @DisplayName("Send direst search message with publisher not configured")
    void sendDirectSearchWithPublisherNotConfigured() {
        var pubsubService = new PubsubWriterService(new ObjectMapper(), null);

        var result = pubsubService.sendDirectBeacon(getSearchRequest());
        assertFalse(result);
    }

    private DirectSearchBeaconRequest<SearchResponseDto> getSearchRequest() {
        return new DirectSearchBeaconRequest<>(
            "gbiqa",
            "111",
            SearchResponseDto.builder()
                .area("area")
                .query("search query")
                .correctedQuery("search_query")
                .originalRequest(SearchRequestDto.builder()
                    .area("area")
                    .collection(COLLECTION)
                    .query("search query")
                    .build())
                .records(List.of(new RecordDto(
                    "111",
                    "boats",
                    "boat_1",
                    COLLECTION,
                    Map.of("Price", 100),
                    null,
                    null,
                    null,
                    "111",
                    null)
                ))
                .availableNavigation(Collections.emptyList())
                .metadata(SearchMetadataDto.fromDomain(
                    SearchMetadata.builder().attributionToken("attributionToken")
                        .build())
                )
                .experiments(List.of(new Experiment("exp_name", "exp_val")))
                .build(),
            EVENT_TYPE,
            Collections.emptyList()
        );
    }

}
