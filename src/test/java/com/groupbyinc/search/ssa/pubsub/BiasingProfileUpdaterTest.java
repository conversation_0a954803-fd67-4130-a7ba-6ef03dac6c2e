package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.BiasingProfileMessage;
import com.groupbyinc.proto.commandcenter.config.MessageType;
import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.biasing.Bias;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfileConfiguration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
@DisplayName("BiasingProfileUpdater Tests")
class BiasingProfileUpdaterTest {

    @Mock
    private ConfigurationManager configurationManager;
    private BiasingProfileUpdater updater;

    @BeforeEach
    void setUp() {
        updater = new BiasingProfileUpdater(configurationManager);
    }

    @Test
    @DisplayName("Map BiasingProfileMessage to BiasingProfileConfiguration and update")
    void mapAndUpdateBiasingProfiles() {
        var biasingProfileMessage = BiasingProfileMessage
            .newBuilder()
            .setId(3)
            .setName("Red Perry Ellis")
            .setAreaId(1)
            .addAllBiases(List.of(
                BiasingProfileMessage.BiasMessage.newBuilder()
                    .setContent("Perry Ellis")
                    .setField("brands")
                    .setStrength(BiasingProfileMessage.BiasMessage.Strength.STRONG_INCREASE)
                    .build(),

                BiasingProfileMessage.BiasMessage.newBuilder()
                    .setContent("White")
                    .setField("colorFamilies")
                    .setStrength(BiasingProfileMessage.BiasMessage.Strength.WEAK_INCREASE)
                    .build()
            ))
            .setAreaDefault(true)
            .setMessageType(MessageType.DELETE)
            .build();
        var updateMessage = SiteSearchConfigurationUpdateMessage
            .newBuilder()
            .addBiasingProfile(biasingProfileMessage)
            .build();

        updater.update(updateMessage);


        var expected = List.of(BiasingProfileConfiguration.builder()
            .id(3)
            .name("Red Perry Ellis")
            .areaId(1)
            .biases(List.of(
                Bias.builder()
                    .content("Perry Ellis")
                    .field("brands")
                    .strength(Bias.Strength.STRONG_INCREASE)
                    .build(),

                Bias.builder()
                    .content("White")
                    .field("colorFamilies")
                    .strength(Bias.Strength.WEAK_INCREASE)
                    .build()
            ))
            .areaDefault(true)
            .messageType(MessageType.DELETE)
            .build());
        verify(configurationManager).updateBiasingProfileConfig(expected);
    }
}
