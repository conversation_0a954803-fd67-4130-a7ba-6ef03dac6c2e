package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.MessageType;
import com.groupbyinc.proto.commandcenter.config.NavigationMessage;
import com.groupbyinc.proto.commandcenter.config.Order;
import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.navigation.NavigationSort;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static com.groupbyinc.search.ssa.core.navigation.NavigationType.VALUE;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
@DisplayName("NavigationUpdater Tests")
class NavigationUpdaterTest {

    @Mock
    private ConfigurationManager configurationManager;
    private NavigationUpdater updater;

    @BeforeEach
    void setUp() {
        updater = new NavigationUpdater(configurationManager);
    }

    @Test
    @SuppressWarnings("unchecked")
    @DisplayName("Map NavigationMessage to NavigationConfiguration and update")
    void mapAndUpdateNavigations() {
        var navigationMessage = NavigationMessage
            .newBuilder()
            .setId(4)
            .setType(NavigationMessage.NavigationType.VALUE)
            .setAreaId(1)
            .setField("colorFamilies")
            .setPriority(10)
            .setMultiSelect(true)
            .setSort(NavigationMessage.NavigationSortMessage.newBuilder()
                .setOrderType(Order.ASCENDING)
                .setFieldValue(1)
                .build())
            .setMessageType(MessageType.DELETE)
            .build();
        SiteSearchConfigurationUpdateMessage updateMessage = SiteSearchConfigurationUpdateMessage
            .newBuilder()
            .addNavigation(navigationMessage)
            .build();

        updater.update(updateMessage);

        var expected = List.of(NavigationConfiguration.builder()
            .id(4)
            .type(VALUE)
            .areaId(1)
            .field("colorFamilies")
            .priority(10)
            .multiSelect(true)
            .sort(
                new NavigationSort(
                    NavigationSort.SortField.VALUE,
                    com.groupbyinc.search.ssa.core.Order.DESCENDING
                )
            )
            .messageType(MessageType.DELETE)
            .build());
        ArgumentCaptor<List<NavigationConfiguration>> argument = ArgumentCaptor.forClass(List.class);
        verify(configurationManager).updateNavigationConfig(argument.capture());
        assertEquals(expected, argument.getValue());
        verify(configurationManager, times(1)).updateNavigationConfig(expected);
    }
}
