package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.utils.pubsub.client.PubsubClient;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.gax.batching.BatchingSettings;
import com.google.pubsub.v1.Topic;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.threeten.bp.Duration;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("WriterFactoryTest Tests")
class WriterFactoryTest {

    private static final String WISDOM_DIRECT_TOPIC = "wis-direct-search-topic";
    private static final String WISDOM_DIRECT_GCP_PROJECT = "seek-wisdom-over-there";
    private static final Integer PARALLEL_THREADS = 2;
    private static final Long REQUEST_BYTE_THRESHOLD = 6969L;
    private static final Long ELEMENT_COUNT_THRESHOLD = 42L;
    private static final Long DELAY_THRESHOLD = 31L;

    @Mock
    private static PubsubClient client;
    @Mock
    private static Topic topic;
    @Captor
    private ArgumentCaptor<BatchingSettings> settingsCaptor;

    @Test
    @DisplayName("Factory will not create publisher when direct search is disabled")
    void writerFactoryInitWithWisdomDirectSearchDisabled() {
        var writerFactory = new WriterFactory(
            client,
            PARALLEL_THREADS,
            WISDOM_DIRECT_TOPIC,
            WISDOM_DIRECT_GCP_PROJECT,
            ELEMENT_COUNT_THRESHOLD,
            REQUEST_BYTE_THRESHOLD,
            DELAY_THRESHOLD,
            false
        );
        var pubsubWriterService = writerFactory.pubsubWriterService(new ObjectMapper());

        assertNotNull(pubsubWriterService);
        verifyNoInteractions(client);
    }

    @Test
    @DisplayName("Factory will create publisher when direct search is enabled and custom GCP project is configured")
    void writerFactoryInitWithWisdomDirectSearchEnabledAndCustomGCPProject() {
        when(client.findTopic(WISDOM_DIRECT_GCP_PROJECT, WISDOM_DIRECT_TOPIC)).thenReturn(Optional.of(topic));

        var writerFactory = new WriterFactory(
            client,
            PARALLEL_THREADS,
            WISDOM_DIRECT_TOPIC,
            WISDOM_DIRECT_GCP_PROJECT,
            ELEMENT_COUNT_THRESHOLD,
            REQUEST_BYTE_THRESHOLD,
            DELAY_THRESHOLD,
            true
        );
        var pubsubWriterService = writerFactory.pubsubWriterService(new ObjectMapper());

        assertNotNull(pubsubWriterService);
        verify(client).findTopic(WISDOM_DIRECT_GCP_PROJECT, WISDOM_DIRECT_TOPIC);
        verify(client).newPublisher(eq(topic), eq(PARALLEL_THREADS), settingsCaptor.capture());

        assertNotNull(settingsCaptor.getValue());
        assertEquals(Duration.ofMillis(DELAY_THRESHOLD), settingsCaptor.getValue().getDelayThreshold());
        assertEquals(ELEMENT_COUNT_THRESHOLD, settingsCaptor.getValue().getElementCountThreshold());
        assertEquals(REQUEST_BYTE_THRESHOLD, settingsCaptor.getValue().getRequestByteThreshold());
    }

    @Test
    @DisplayName("Factory will create publisher when direct search is enabled and custom GCP project is not set")
    void writerFactoryInitWithWisdomDirectSearchEnabled() {
        when(client.findTopic(WISDOM_DIRECT_TOPIC)).thenReturn(Optional.of(topic));

        var writerFactory = new WriterFactory(
            client,
            PARALLEL_THREADS,
            WISDOM_DIRECT_TOPIC,
            null,
            ELEMENT_COUNT_THRESHOLD,
            REQUEST_BYTE_THRESHOLD,
            DELAY_THRESHOLD,
            true
        );
        var pubsubWriterService = writerFactory.pubsubWriterService(new ObjectMapper());

        assertNotNull(pubsubWriterService);
        verify(client).findTopic(WISDOM_DIRECT_TOPIC);
        verify(client).newPublisher(eq(topic), eq(PARALLEL_THREADS), settingsCaptor.capture());

        assertNotNull(settingsCaptor.getValue());
        assertEquals(Duration.ofMillis(DELAY_THRESHOLD), settingsCaptor.getValue().getDelayThreshold());
        assertEquals(ELEMENT_COUNT_THRESHOLD, settingsCaptor.getValue().getElementCountThreshold());
        assertEquals(REQUEST_BYTE_THRESHOLD, settingsCaptor.getValue().getRequestByteThreshold());
    }

    @Test
    @DisplayName("Factory will not create publisher when direct search is enabled but topic is not configured")
    void writerFactoryInitWithWisdomDirectSearchEnabledTopicMissing() {
        when(client.findTopic(WISDOM_DIRECT_GCP_PROJECT, WISDOM_DIRECT_TOPIC)).thenReturn(Optional.empty());

        var writerFactory = new WriterFactory(
            client,
            PARALLEL_THREADS,
            WISDOM_DIRECT_TOPIC,
            WISDOM_DIRECT_GCP_PROJECT,
            ELEMENT_COUNT_THRESHOLD,
            REQUEST_BYTE_THRESHOLD,
            DELAY_THRESHOLD,
            true
        );
        var pubsubWriterService = writerFactory.pubsubWriterService(new ObjectMapper());

        assertNotNull(pubsubWriterService);
        verify(client).findTopic(WISDOM_DIRECT_GCP_PROJECT, WISDOM_DIRECT_TOPIC);
        verifyNoMoreInteractions(client);
    }

    @Test
    @DisplayName("Factory will not create publisher when direct search is enabled but topic is not provided")
    void writerFactoryInitWithWisdomDirectSearchEnabledTopicNotProvided() {
        var writerFactory = new WriterFactory(
            client,
            PARALLEL_THREADS,
            null,
            WISDOM_DIRECT_GCP_PROJECT,
            ELEMENT_COUNT_THRESHOLD,
            REQUEST_BYTE_THRESHOLD,
            DELAY_THRESHOLD,
            true
        );
        var pubsubWriterService = writerFactory.pubsubWriterService(new ObjectMapper());

        assertNotNull(pubsubWriterService);
        verifyNoInteractions(client);
    }
}
