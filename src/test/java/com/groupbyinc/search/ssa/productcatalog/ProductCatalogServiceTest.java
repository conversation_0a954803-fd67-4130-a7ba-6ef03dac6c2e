package com.groupbyinc.search.ssa.productcatalog;

import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKeysParser;
import com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKeysProcessor;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.RecordLabel;
import com.groupbyinc.search.ssa.core.RecordSponsoredInfo;
import com.groupbyinc.search.ssa.core.RequestOptions;
import com.groupbyinc.search.ssa.core.Rewrites;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.productcatalog.client.ProductCatalogClient;
import com.groupbyinc.search.ssa.productcatalog.exception.SiteSearchProductCatalogException;
import com.groupbyinc.search.ssa.productcatalog.model.ProductCatalogPdpRequest;
import com.groupbyinc.search.ssa.productcatalog.model.ProductCatalogRequest;
import com.groupbyinc.search.ssa.productcatalog.model.ProductCatalogResponse;
import com.groupbyinc.search.ssa.productcatalog.model.ProductRequest;
import com.groupbyinc.search.ssa.stub.ResourceUtils;
import com.groupbyinc.search.ssa.topsort.model.AuctionResponse;
import com.groupbyinc.search.ssa.util.PrefixTreeMap;

import io.micronaut.core.propagation.PropagatedContext;
import io.micronaut.http.client.exceptions.HttpClientException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.core.SearchMode.PRODUCT_SEARCH;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_ID;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_PRIMARY_ID;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_TITLE;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_URI;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_VARIANTS;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_FETCH;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_MISSING_PRODUCTS_REMOVAL;
import static com.groupbyinc.search.ssa.retail.filtering.RetailFilterService.PRODUCT_ID_FIELD;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.DEFAULT_CONTEXT;
import static com.groupbyinc.search.ssa.stub.TestData.LOGIN_ID;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTION;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;
import static com.groupbyinc.search.ssa.stub.TestData.REQUEST_ID;
import static com.groupbyinc.search.ssa.stub.TestData.TEST_ENVIRONMENT;
import static com.groupbyinc.search.ssa.stub.TestData.VISITOR_ID;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;

@ExtendWith(MockitoExtension.class)
class ProductCatalogServiceTest {

    private static final List<String> MASK = List.of();

    private ProductCatalogService service;

    @Mock
    private ProductCatalogClient client;
    @Mock
    private FeaturesManager featuresManager;
    @Mock
    private VariantRollupKeysParser variantRollupKeysParser;
    @Mock
    private VariantRollupKeysProcessor  variantRollupKeysProcessor;
    @Captor
    private ArgumentCaptor<ProductCatalogRequest> requestCaptor;
    @Captor
    private ArgumentCaptor<ProductCatalogPdpRequest> requestPdpCaptor;

    private PropagatedContext.Scope scope;

    @BeforeEach
    void setUp() {
        service = new ProductCatalogService(
            featuresManager,
            client,
            new ProductCatalogFilterService(),
            variantRollupKeysParser,
            variantRollupKeysProcessor
        );

        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    @Test
    public void testGetProductionDetailsReturnsProduct() {
        given(client.fetchPdpProduct(requestPdpCaptor.capture(), any())).willReturn(Flux.just(
            new ProductCatalogResponse(null, List.of(ResourceUtils.getResourceAsMap("retail/pdp_product_valid.json")))
        ));

        var result = service.getProductDetails("111", List.of());

        assertThat(result.isPresent()).isTrue();
        assertThat(result.get().getId()).isEqualTo("111");
        assertThat(result.get().getPrimaryProductId()).isEqualTo("111");
        assertThat(result.get().getTitle()).isEqualTo("Title example");
        assertThat(result.get().getVariants()).hasSize(3);
        assertThat(result.get().getVariants().getFirst().getId()).isEqualTo("111_1");
        assertThat(result.get().getVariants().getFirst().getPrimaryProductId()).isEqualTo("111");
        assertThat(result.get().getVariants().get(1).getId()).isEqualTo("111_2");
        assertThat(result.get().getVariants().get(1).getPrimaryProductId()).isEqualTo("111");
        assertThat(result.get().getVariants().get(2).getId()).isEqualTo("111_3");
        assertThat(result.get().getVariants().get(2).getPrimaryProductId()).isEqualTo("111");

        assertThat(requestPdpCaptor.getValue().product().id()).isEqualTo("111");
        assertThat(requestPdpCaptor.getValue().product().primaryProductId()).isEqualTo("111");
        assertThat(requestPdpCaptor.getValue().tenant()).isEqualTo(DEFAULT_CONTEXT.getMerchandiser().merchandiserId());
        assertThat(requestPdpCaptor.getValue().collection()).isEqualTo(DEFAULT_CONTEXT.getCollection());
    }

    @Test
    public void testGetProductDetailsReturnsProductWithPrioritizedVariants() {
        given(client.fetchPdpProduct(requestPdpCaptor.capture(), any())).willReturn(Flux.just(
            new ProductCatalogResponse(null, List.of(ResourceUtils.getResourceAsMap("retail/pdp_product_valid.json")))
        ));

        var result = service.getProductDetails("111", List.of("111_3", "111_2"));

        assertThat(result.isPresent()).isTrue();
        assertThat(result.get().getId()).isEqualTo("111");
        assertThat(result.get().getPrimaryProductId()).isEqualTo("111");
        assertThat(result.get().getTitle()).isEqualTo("Title example");
        assertThat(result.get().getVariants()).hasSize(3);
        assertThat(result.get().getVariants().getFirst().getId()).isEqualTo("111_3");
        assertThat(result.get().getVariants().getFirst().getPrimaryProductId()).isEqualTo("111");
        assertThat(result.get().getVariants().get(1).getId()).isEqualTo("111_2");
        assertThat(result.get().getVariants().get(1).getPrimaryProductId()).isEqualTo("111");
        assertThat(result.get().getVariants().get(2).getId()).isEqualTo("111_1");
        assertThat(result.get().getVariants().get(2).getPrimaryProductId()).isEqualTo("111");

        assertThat(requestPdpCaptor.getValue().product().id()).isEqualTo("111");
        assertThat(requestPdpCaptor.getValue().product().primaryProductId()).isEqualTo("111");
        assertThat(requestPdpCaptor.getValue().tenant()).isEqualTo(DEFAULT_CONTEXT.getMerchandiser().merchandiserId());
        assertThat(requestPdpCaptor.getValue().collection()).isEqualTo(DEFAULT_CONTEXT.getCollection());
    }

    @Test
    public void testGetProductsDetailsReturnsProducts() {
        given(client.fetchProductsMetadata(requestCaptor.capture(), any())).willReturn(Flux.just(
            getProductCatalogResponse()));

        var sp = getSearchParameters();

        var result = service.getProductsDetails(sp, Set.of("111"), true);
        var records = result.records();

        assertThat(records).hasSize(1);
        assertThat(records.getFirst().getPrimaryProductId()).isEqualTo("111");
        assertThat(records.getFirst().getTitle()).isEqualTo("Title example");
        assertThat((List<?>) records.getFirst().getMetadata().get(PRODUCT_FIELD_VARIANTS)).hasSize(3);

        assertThat(requestCaptor.getValue().products()).hasSize(1);
        assertThat(requestCaptor.getValue().products().getFirst().id()).isEqualTo("111");
        assertThat(requestCaptor.getValue().products().getFirst().primaryProductId()).isEqualTo("111");
        assertThat(requestCaptor.getValue().tenant()).isEqualTo(DEFAULT_CONTEXT.getMerchandiser().merchandiserId());
        assertThat(requestCaptor.getValue().collection()).isEqualTo(DEFAULT_CONTEXT.getCollection());
        assertThat(requestCaptor.getValue().includeVariants()).isTrue();
        assertThat(requestCaptor.getValue().includeVariants()).isTrue();
    }

    @Test
    public void testGetProductsDetailsReturnsSomeProducts() {
        given(client.fetchProductsMetadata(requestCaptor.capture(), any())).willReturn(
            Flux.just(new ProductCatalogResponse(
                new ProductCatalogResponse.QueryDetails(1L, List.of("222 - not found")),
                List.of(ResourceUtils.getResourceAsMap("retail/pdp_product_valid.json"))
            ))
        );

        var sp = getSearchParameters();

        var result = service.getProductsDetails(sp, Set.of("111", "222"), true);
        var records = result.records();

        assertThat(records).hasSize(1);
        assertThat(records.getFirst().getPrimaryProductId()).isEqualTo("111");
        assertThat(records.getFirst().getTitle()).isEqualTo("Title example");
        assertThat((List<?>) records.getFirst().getMetadata().get(PRODUCT_FIELD_VARIANTS)).hasSize(3);
        assertTrue(getRequestContext().getWarnings().stream().anyMatch(s -> s.equals("222 - not found")));

        assertThat(requestCaptor.getValue().products()).hasSize(2);
        assertThat(requestCaptor.getValue().tenant()).isEqualTo(DEFAULT_CONTEXT.getMerchandiser().merchandiserId());
        assertThat(requestCaptor.getValue().collection()).isEqualTo(DEFAULT_CONTEXT.getCollection());
        assertThat(requestCaptor.getValue().includeVariants()).isTrue();
        assertThat(requestCaptor.getValue().includeVariants()).isTrue();
    }

    @Test
    public void testGetProductsDetailsThrowsException() {
        given(client.fetchProductsMetadata(requestCaptor.capture(), any())).willReturn(
            Flux.error(new HttpClientException("Pigeon the Messenger was competitors undercover agent"))
        );

        var sp = getSearchParameters();

        var e = assertThrows(
            SiteSearchProductCatalogException.class,
            () -> service.getProductsDetails(sp, Set.of("111"), true)
        );

        assertThat(e).hasCauseInstanceOf(HttpClientException.class);
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testApplyModifyingResponse() {
        //prepare
        var records = Stream.of("1", "2")
            .map(id -> {
                var meta = new HashMap<String, Object>(Map.of(
                    PRODUCT_ID_FIELD, id,
                    "attributes", new HashMap<String, Object>(Map.of("k%s".formatted(id), "v%s".formatted(id))),
                    PRODUCT_FIELD_VARIANTS, new ArrayList<>(List.of(
                        flatRecord("%s_1".formatted(id), id, "v1", Map.of("k1", "v1")),
                        flatRecord("%s_2".formatted(id), id, "v2", Map.of("k2", "v2"))
                    ))
                ));
                return Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, id, id, "t%s".formatted(id), meta);
            }).collect(Collectors.toList());
        var sponsoredRecords = Stream.of("s1")
            .map(id -> {
                var meta = new HashMap<String, Object>(Map.of(
                    PRODUCT_ID_FIELD, id,
                    "attributes", new HashMap<String, Object>(Map.of("k%s".formatted(id), "v%s".formatted(id))),
                    PRODUCT_FIELD_VARIANTS, new ArrayList<>(List.of(
                        flatRecord("%s_1".formatted(id), id, "v1", Map.of("k1", "v1")),
                        flatRecord("%s_2".formatted(id), id, "v2", Map.of("k2", "v2"))
                    ))
                ));
                return Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, id, id, "t%s".formatted(id), meta);
            }).collect(Collectors.toList());

        var sp = SearchParameters.builder()
            .searchMode(PRODUCT_SEARCH)
            .responseMask(MASK)
            .build();
        var sr = SearchResults.builder()
            .records(records)
            .sponsoredRecords(sponsoredRecords)
            .build();

        given(client.fetchProductsMetadata(requestCaptor.capture(), eq(false))).willReturn(
            Flux.just(new ProductCatalogResponse(
                new ProductCatalogResponse.QueryDetails(1L, List.of("Don't feed the cat")),
                List.of(
                    flatRecord("1", "1", "t1", Map.of("foo1", "bar1")),
                    flatRecord("1_1", "1", "t1_1", Map.of("foo1_1", "bar1_1")),
                    flatRecord("1_2", "1", "t1_2", Map.of("foo1_2", "bar1_2")),
                    flatRecord("2", "2", "t2", Map.of("foo2", "bar2")),
                    flatRecord("2_1", "2", "t2_1", Map.of("foo2_1", "bar2_1")),
                    flatRecord("2_2", "2", "t2_2", Map.of("foo2_2", "bar2_2")),
                    flatRecord("s1", "s1", "ts1", Map.of("foo_s_1", "bar_s_1")),
                    flatRecord("s1_1", "s1", "ts1_1", Map.of("foo_s_1_1", "bar_s_1_1")),
                    flatRecord("s1_2", "s1", "ts1_2", Map.of("foo_s_1_2", "bar_s_1_2"))
                )
            ))
        );
        given(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_DATA_CATALOG_FETCH))).willReturn(true);

        //act
        var result = service.fillProductsWithMetadata(sp, sr);
        var resRecords = result.getRecords();
        var resSponsored = result.getSponsoredRecords();

        //verify
        assertTrue(getRequestContext().getWarnings().stream().anyMatch(s -> s.equals(("Don't feed the cat"))));
        assertThat(result.getRewrites()).containsExactly(Rewrites.PRODUCT_CATALOG.name());

        assertThat(resRecords).hasSize(2);
        assertThat(resRecords.getFirst().getProductId()).isEqualTo(records.getFirst().getProductId());
        assertThat(resRecords.getFirst().getPrimaryProductId()).isEqualTo(records.getFirst().getPrimaryProductId());
        assertThat(resRecords.getFirst().getTitle()).isEqualTo(records.getFirst().getTitle());
        assertThat(resRecords.getFirst().getUrl()).isEqualTo(records.getFirst().getUrl());
        assertThat(resRecords.getFirst().getMetadata()).hasSize(7);
        assertThat(
            resRecords.getFirst().getMetadata().get(PRODUCT_ID_FIELD)
        ).isEqualTo(records.getFirst().getProductId());
        assertThat(
            resRecords.getFirst().getMetadata().get(PRODUCT_FIELD_TITLE)).isEqualTo(records.getFirst().getTitle()
        );
        assertThat(resRecords.getFirst().getMetadata().get("attributes")).isEqualTo(Map.of("k1", "v1"));
        assertThat(resRecords.getFirst().getMetadata().get("foo1")).isEqualTo("bar1");
        assertThat(resRecords.getFirst().getMetadata().get(PRODUCT_FIELD_VARIANTS)).isInstanceOf(List.class);

        var variants1 = (List<Map<String, Object>>) resRecords.getFirst().getMetadata().get(PRODUCT_FIELD_VARIANTS);
        assertThat(variants1.getFirst()).hasSize(6);
        assertThat(variants1.getFirst().get(PRODUCT_FIELD_PRIMARY_ID)).isEqualTo("1");
        assertThat(variants1.getFirst().get(PRODUCT_ID_FIELD)).isEqualTo("1_1");
        assertThat(variants1.getFirst().get(PRODUCT_FIELD_TITLE)).isEqualTo("t1_1");
        assertThat(variants1.getFirst().get(PRODUCT_FIELD_URI)).isEqualTo("uri/1_1");
        assertThat(variants1.getFirst().get("foo1_1")).isEqualTo("bar1_1");
        assertThat(variants1.getFirst().get("k1")).isEqualTo("v1");
        assertThat(variants1.get(1)).hasSize(6);
        assertThat(variants1.get(1).get(PRODUCT_FIELD_PRIMARY_ID)).isEqualTo("1");
        assertThat(variants1.get(1).get(PRODUCT_ID_FIELD)).isEqualTo("1_2");
        assertThat(variants1.get(1).get(PRODUCT_FIELD_TITLE)).isEqualTo("t1_2");
        assertThat(variants1.get(1).get(PRODUCT_FIELD_URI)).isEqualTo("uri/1_2");
        assertThat(variants1.get(1).get("foo1_2")).isEqualTo("bar1_2");
        assertThat(variants1.get(1).get("k2")).isEqualTo("v2");

        assertThat(resRecords.get(1).getProductId()).isEqualTo(records.get(1).getProductId());
        assertThat(resRecords.get(1).getPrimaryProductId()).isEqualTo(records.get(1).getPrimaryProductId());
        assertThat(resRecords.get(1).getTitle()).isEqualTo(records.get(1).getTitle());
        assertThat(resRecords.get(1).getUrl()).isEqualTo(records.get(1).getUrl());
        assertThat(resRecords.get(1).getMetadata()).hasSize(7);
        assertThat(resRecords.get(1).getMetadata().get(PRODUCT_ID_FIELD)).isEqualTo(records.get(1).getProductId());
        assertThat(resRecords.get(1).getMetadata().get(PRODUCT_FIELD_TITLE)).isEqualTo(records.get(1).getTitle());
        assertThat(resRecords.get(1).getMetadata().get("attributes")).isEqualTo(Map.of("k2", "v2"));
        assertThat(resRecords.get(1).getMetadata().get("foo2")).isEqualTo("bar2");
        assertThat(resRecords.get(1).getMetadata().get(PRODUCT_FIELD_VARIANTS)).isInstanceOf(List.class);

        var variants2 = (List<Map<String, Object>>) resRecords.get(1).getMetadata().get(PRODUCT_FIELD_VARIANTS);
        assertThat(variants2.getFirst()).hasSize(6);
        assertThat(variants2.getFirst().get(PRODUCT_FIELD_PRIMARY_ID)).isEqualTo("2");
        assertThat(variants2.getFirst().get(PRODUCT_ID_FIELD)).isEqualTo("2_1");
        assertThat(variants2.getFirst().get(PRODUCT_FIELD_TITLE)).isEqualTo("t2_1");
        assertThat(variants2.getFirst().get(PRODUCT_FIELD_URI)).isEqualTo("uri/2_1");
        assertThat(variants2.getFirst().get("foo2_1")).isEqualTo("bar2_1");
        assertThat(variants2.getFirst().get("k1")).isEqualTo("v1");
        assertThat(variants2.get(1)).hasSize(6);
        assertThat(variants2.get(1).get(PRODUCT_FIELD_PRIMARY_ID)).isEqualTo("2");
        assertThat(variants2.get(1).get(PRODUCT_ID_FIELD)).isEqualTo("2_2");
        assertThat(variants2.get(1).get(PRODUCT_FIELD_TITLE)).isEqualTo("t2_2");
        assertThat(variants2.get(1).get(PRODUCT_FIELD_URI)).isEqualTo("uri/2_2");
        assertThat(variants2.get(1).get("foo2_2")).isEqualTo("bar2_2");
        assertThat(variants2.get(1).get("k2")).isEqualTo("v2");


        assertThat(resSponsored).hasSize(1);
        assertThat(resSponsored.getFirst().getProductId()).isEqualTo(sponsoredRecords.getFirst().getProductId());
        assertThat(resSponsored.getFirst().getPrimaryProductId())
            .isEqualTo(sponsoredRecords.getFirst().getPrimaryProductId());
        assertThat(resSponsored.getFirst().getTitle()).isEqualTo(sponsoredRecords.getFirst().getTitle());
        assertThat(resSponsored.getFirst().getUrl()).isEqualTo(sponsoredRecords.getFirst().getUrl());
        assertThat(resSponsored.getFirst().getMetadata()).hasSize(7);
        assertThat(resSponsored.getFirst().getMetadata().get(PRODUCT_ID_FIELD))
            .isEqualTo(sponsoredRecords.getFirst().getProductId());
        assertThat(resSponsored.getFirst().getMetadata().get(PRODUCT_FIELD_TITLE))
            .isEqualTo(sponsoredRecords.getFirst().getTitle());
        assertThat(resSponsored.getFirst().getMetadata().get("attributes")).isEqualTo(Map.of("ks1", "vs1"));
        assertThat(resSponsored.getFirst().getMetadata().get("foo_s_1")).isEqualTo("bar_s_1");
        assertThat(resSponsored.getFirst().getMetadata().get(PRODUCT_FIELD_VARIANTS)).isInstanceOf(List.class);

        var variantsS1 = (List<Map<String, Object>>) resSponsored.getFirst().getMetadata().get(PRODUCT_FIELD_VARIANTS);
        assertThat(variantsS1.getFirst()).hasSize(6);
        assertThat(variantsS1.getFirst().get(PRODUCT_FIELD_PRIMARY_ID)).isEqualTo("s1");
        assertThat(variantsS1.getFirst().get(PRODUCT_ID_FIELD)).isEqualTo("s1_1");
        assertThat(variantsS1.getFirst().get(PRODUCT_FIELD_TITLE)).isEqualTo("ts1_1");
        assertThat(variantsS1.getFirst().get(PRODUCT_FIELD_URI)).isEqualTo("uri/s1_1");
        assertThat(variantsS1.getFirst().get("foo_s_1_1")).isEqualTo("bar_s_1_1");
        assertThat(variantsS1.getFirst().get("k1")).isEqualTo("v1");
        assertThat(variantsS1.get(1)).hasSize(6);
        assertThat(variantsS1.get(1).get(PRODUCT_FIELD_PRIMARY_ID)).isEqualTo("s1");
        assertThat(variantsS1.get(1).get(PRODUCT_ID_FIELD)).isEqualTo("s1_2");
        assertThat(variantsS1.get(1).get(PRODUCT_FIELD_TITLE)).isEqualTo("ts1_2");
        assertThat(variantsS1.get(1).get(PRODUCT_FIELD_URI)).isEqualTo("uri/s1_2");
        assertThat(variantsS1.get(1).get("foo_s_1_2")).isEqualTo("bar_s_1_2");
        assertThat(variantsS1.get(1).get("k2")).isEqualTo("v2");

        var captured = requestCaptor.getValue();
        assertThat(captured.tenant()).isEqualTo(DEFAULT_CONTEXT.getMerchandiser().merchandiserId());
        assertThat(captured.collection()).isEqualTo(DEFAULT_CONTEXT.getCollection());
        assertThat(captured.includedFields()).isEmpty();
        assertThat(captured.products()).containsExactly(
            new ProductRequest("1", "1"),
            new ProductRequest("1_1", "1"),
            new ProductRequest("1_2", "1"),
            new ProductRequest("2", "2"),
            new ProductRequest("2_1", "2"),
            new ProductRequest("2_2", "2"),
            new ProductRequest("s1", "s1"),
            new ProductRequest("s1_1", "s1"),
            new ProductRequest("s1_2", "s1")
        );
    }

    @Test
    public void testApplyReturnsOriginalRecordsInCaseOfNoProductsMetadataFound() {
        //prepare
        var records = Stream.of("1", "2")
            .map(id -> {
                var meta = new HashMap<String, Object>(Map.of(
                    PRODUCT_ID_FIELD, id,
                    "attributes", new HashMap<String, Object>(Map.of("k%s".formatted(id), "v%s".formatted(id))),
                    PRODUCT_FIELD_VARIANTS, new ArrayList<>(List.of(
                        flatRecord("%s_1".formatted(id), id, "v1", Map.of("k1", "v1")),
                        flatRecord("%s_2".formatted(id), id, "v2", Map.of("k2", "v2"))
                    ))
                ));
                return Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, id, id, "t%s".formatted(id), meta);
            }).collect(Collectors.toList());

        var sp = SearchParameters.builder()
            .searchMode(PRODUCT_SEARCH)
            .responseMask(MASK)
            .build();
        var sr = SearchResults.builder().records(records).build();

        given(
            client.fetchProductsMetadata(any(), eq(false))
        ).willReturn(
            Flux.just(
                new ProductCatalogResponse(
                    new ProductCatalogResponse.QueryDetails(1L, List.of("Not Found", "Not Found")),
                    List.of()
                )
            )
        );
        given(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_DATA_CATALOG_FETCH))).willReturn(true);

        //act
        var result = service.fillProductsWithMetadata(sp, sr);
        var recordsResult = result.getRecords();

        //verify
        assertThat(result.getRewrites()).isEmpty();

        assertThat(getRequestContext().getWarnings()).hasSize(2);
        assertTrue(getRequestContext().getWarnings().stream().anyMatch(s -> s.contains(("Not Found"))));

        assertThat(recordsResult).hasSize(2);
        assertThat(recordsResult).isEqualTo(records);
    }

    @Test
    public void testSkipOriginalRecordsInCaseOfNoProductsMetadataFound() {
        //prepare
        var records = Stream.of("1", "2")
            .map(id -> {
                var meta = new HashMap<String, Object>(Map.of(
                    PRODUCT_ID_FIELD, id,
                    "attributes", new HashMap<String, Object>(Map.of("k%s".formatted(id), "v%s".formatted(id))),
                    PRODUCT_FIELD_VARIANTS, new ArrayList<>(List.of(
                        flatRecord("%s_1".formatted(id), id, "v1", Map.of("k1", "v1")),
                        flatRecord("%s_2".formatted(id), id, "v2", Map.of("k2", "v2"))
                    ))
                ));
                return Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, id, id, "t%s".formatted(id), meta);
            }).collect(Collectors.toList());

        var sp = SearchParameters.builder()
            .searchMode(PRODUCT_SEARCH)
            .responseMask(MASK)
            .build();
        var sr = SearchResults.builder().records(records).build();

        given(
            client.fetchProductsMetadata(any(), eq(false))
        ).willReturn(
            Flux.just(
                new ProductCatalogResponse(
                    new ProductCatalogResponse.QueryDetails(1L, List.of()),
                    List.of()
                )
            )
        );
        given(
            featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_DATA_CATALOG_FETCH))
        ).willReturn(true);
        given(
            featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_MISSING_PRODUCTS_REMOVAL))
        ).willReturn(true);

        //act
        var result = service.fillProductsWithMetadata(sp, sr);

        //verify
        assertThat(getRequestContext().getWarnings()).hasSize(2);
        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.contains(("Data catalog product not found for id 1")))
        );
        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.contains(("Data catalog product not found for id 2")))
        );

        assertThat(result.getRecords()).isEmpty();
    }

    @Test
    public void testApplyWillPassFieldProjectionToClientCall() {
        //prepare
        var records = Stream
            .of("1", "2")
            .map(id -> Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, id, id, "t%s".formatted(id), null))
            .toList();

        var sp = SearchParameters.builder()
            .searchMode(PRODUCT_SEARCH)
            .responseMask(List.of("attributes.foo"))
            .build();
        var sr = SearchResults.builder().records(records).build();

        given(
            client.fetchProductsMetadata(requestCaptor.capture(), eq(false))
        ).willReturn(Flux.just(getProductCatalogResponse()));
        given(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_DATA_CATALOG_FETCH))).willReturn(true);

        //act
        var result = service.fillProductsWithMetadata(sp, sr);

        //verify
        assertThat(result.getRecords()).hasSize(2);

        var captured = requestCaptor.getValue();
        assertThat(captured.tenant()).isEqualTo(DEFAULT_CONTEXT.getMerchandiser().merchandiserId());
        assertThat(captured.collection()).isEqualTo(DEFAULT_CONTEXT.getCollection());
        assertThat(captured.includedFields()).containsExactly("attributes.foo");
    }

    @Test
    public void testApplyWillPassWildcardFieldProjectionToClientCall() {
        //prepare
        var records = Stream
            .of("1", "2")
            .map(id -> Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, id, id, "t%s".formatted(id), null))
            .toList();

        var sp = SearchParameters.builder()
            .searchMode(PRODUCT_SEARCH)
            .prefixTreeMap(PrefixTreeMap.fromList(List.of("*", "attributes.foo")))
            .responseMask(MASK)
            .build();
        var sr = SearchResults.builder().records(records).build();

        given(
            client.fetchProductsMetadata(requestCaptor.capture(), eq(false))
        ).willReturn(Flux.just(getProductCatalogResponse()));
        given(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_DATA_CATALOG_FETCH))).willReturn(true);

        //act
        var result = service.fillProductsWithMetadata(sp, sr);

        //verify

        assertThat(result.getRecords()).hasSize(2);
        assertThat(result.getRecords()).isEqualTo(records);

        var captured = requestCaptor.getValue();
        assertThat(captured.tenant()).isEqualTo(DEFAULT_CONTEXT.getMerchandiser().merchandiserId());
        assertThat(captured.collection()).isEqualTo(DEFAULT_CONTEXT.getCollection());
        assertThat(captured.includedFields()).isEmpty();
    }

    @Test
    public void testApplyAddsDebugInfo() {
        scope = PropagatedContext.getOrEmpty().plus(
            new RequestContext(
                APPAREL_MERCHANDISER,
                REQUEST_ID,
                PRODUCTION,
                PRODUCTS_CLOTHING,
                VISITOR_ID,
                LOGIN_ID,
                List.of(),
                TEST_ENVIRONMENT,
                new RequestOptions(false, true)
            )
        ).propagate();

        //prepare
        var records = Stream
            .of("1", "2")
            .map(id -> Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, id, id, "t%s".formatted(id), null))
            .toList();

        var sp = SearchParameters.builder()
            .searchMode(PRODUCT_SEARCH)
            .responseMask(MASK)
            .build();
        var sr = SearchResults.builder().records(records).build();

        given(
            client.fetchProductsMetadata(
                requestCaptor.capture(),
                eq(RequestOptions.EMPTY.skipCache())
            )
        ).willReturn(Flux.just(getProductCatalogResponse()));
        given(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_DATA_CATALOG_FETCH))).willReturn(true);

        //act
        var result = service.fillProductsWithMetadata(sp, sr);

        //verify
        assertThat(result.getRecords()).hasSize(2);
        assertThat(result.getRecords()).isEqualTo(records);

        assertThat(getRequestContext().getDebugDetails().getDebugInfos()).isNotEmpty();
    }

    private static Map<String, Object> flatRecord(String id,
                                                  String primaryId,
                                                  String title,
                                                  Map<String, Object> metadata) {
        var record = new HashMap<String, Object>(Map.of(
            PRODUCT_FIELD_ID, id,
            PRODUCT_FIELD_PRIMARY_ID, primaryId,
            PRODUCT_FIELD_TITLE, title,
            PRODUCT_FIELD_URI, "uri/%s".formatted(id)
        ));
        record.putAll(metadata);
        return record;
    }

    @Test
    public void testFetchProductsMetadataThrowsException() {
        //prepare
        var records = Stream
            .of("1", "2")
            .map(id -> Record.of(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, id, id, "t%s".formatted(id), null))
            .toList();
        var sp = SearchParameters.builder()
            .responseMask(MASK)
            .build();
        var sr = SearchResults.builder().records(records).build();

        given(client.fetchProductsMetadata(requestCaptor.capture(), any())).willReturn(
            Flux.error(new HttpClientException("Exception: Method failed... as planned."))
        );

        //act
        var e = assertThrows(
            SiteSearchProductCatalogException.class,
            () -> service.fetchProducts(sp, sr, false, "")
        );

        //verify
        assertThat(e).hasCauseInstanceOf(HttpClientException.class);
    }

    @Test
    void testGetProductId() {
        var id = "123";
        assertThat(
            service.getProductId(Map.of(
                PRODUCT_FIELD_ID, id,
                PRODUCT_FIELD_PRIMARY_ID, "321"
            ))
        ).isEqualTo(id);
    }

    @Test
    void testRecordFromMap() {
        //prepare
        var id = "i1";
        var title = "t1";
        var uri = "https://www.example.com/t1";
        var recordMap = recordMapFor(id, null, title, uri);
        var meta = Map.of(
            "attributes", List.of(Map.of("k1", "v1", "k2", "v2"), Map.of("k3", "v3", "k4", "v4")),
            "variants", List.of(
                recordMapFor("i1_1", id, "vt1", "https://www.example.com/vt1"),
                recordMapFor("i1_2", id, "vt2", "https://www.example.com/t2")),
            "extras", "meh");

        // act
        var result = service
            .recordFromMap(APPAREL_MERCHANDISER, PRODUCTS_CLOTHING, recordMap, meta, RecordLabel.PINNED, Set.of(RecordLabel.PINNED), null, null);

        //verify
        assertThat(result.getProductId()).isEqualTo(id);
        assertThat(result.getPrimaryProductId()).isEqualTo(id);
        assertThat(result.getTitle()).isEqualTo(title);
        assertThat(result.getLabel()).isEqualTo(RecordLabel.PINNED);
        assertThat(result.getSponsored()).isNull();
        assertThat(result.getSponsoredInfo()).isNull();
        assertThat(result.getUrl()).isEqualTo(
            "http://%s1%s.com/%s".formatted(
                DEFAULT_CONTEXT.getMerchandiser().merchandiserId(),
                DEFAULT_CONTEXT.getCollection(),
                id
            )
        );
        assertThat(result.getMetadata()).isEqualTo(meta);
    }

    @Test
    void testSponsoredRecordFromMap() {
        //prepare
        var id = "i1";
        var title = "t1";
        var uri = "https://www.example.com/t1";
        var recordMap = recordMapFor(id, null, title, uri);
        var meta = Map.of(
            "attributes", List.of(Map.of("k1", "v1", "k2", "v2"), Map.of("k3", "v3", "k4", "v4")),
            "variants", List.of(
                recordMapFor("i1_1", id, "vt1", "https://www.example.com/vt1"),
                recordMapFor("i1_2", id, "vt2", "https://www.example.com/t2")),
            "extras", "meh");
        var sponsoredInfo = new RecordSponsoredInfo(
            new AuctionResponse.Winner(1, "product", id, UUID.randomUUID().toString(), UUID.randomUUID().toString())
        );

        // act
        var result = service.recordFromMap(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            recordMap,
            meta,
            RecordLabel.PINNED,
            Set.of(RecordLabel.PINNED, RecordLabel.SPONSORED),
            true,
            sponsoredInfo
        );

        //verify
        assertThat(result.getProductId()).isEqualTo(id);
        assertThat(result.getPrimaryProductId()).isEqualTo(id);
        assertThat(result.getTitle()).isEqualTo(title);
        assertThat(result.getLabel()).isEqualTo(RecordLabel.PINNED);
        assertThat(result.getLabels()).containsExactlyInAnyOrder(RecordLabel.PINNED, RecordLabel.SPONSORED);
        assertThat(result.getSponsored()).isTrue();
        assertThat(result.getSponsoredInfo()).isEqualTo(sponsoredInfo);
        assertThat(result.getUrl()).isEqualTo(
            "http://%s1%s.com/%s".formatted(
                DEFAULT_CONTEXT.getMerchandiser().merchandiserId(),
                DEFAULT_CONTEXT.getCollection(),
                id
            )
        );
        assertThat(result.getMetadata()).isEqualTo(meta);
    }

    @Test
    void testExtractMetadata() {
        //prepare
        var attrs = new ArrayList<>(List.of(Map.of("k1", "v1", "k2", "v2"), Map.of("k3", "v3", "k4", "v4")));
        var variants = new ArrayList<>(List.of(
            recordMapFor("i1_1", "i1", "vt1", "https://www.example.com/vt1"),
            recordMapFor("i1_2", "i2", "vt2", "https://www.example.com/t2")));
        var baseRecordMap = new HashMap<>(recordMapFor("i1", "p1", "t1", "u1"));
        var meta = new HashMap<>(Map.of(
            PRODUCT_FIELD_VARIANTS, variants,
            "attributes", attrs,
            "extras", "meh"
        ));
        baseRecordMap.putAll(meta);

        //act
        var result = service.extractMetadata(baseRecordMap);

        //verify
        assertThat(result).hasSize(7);
        assertThat(result.get(PRODUCT_FIELD_ID)).isEqualTo("i1");
        assertThat(result.get(PRODUCT_FIELD_TITLE)).isEqualTo("t1");
        assertThat(result.get(PRODUCT_FIELD_VARIANTS)).isEqualTo(variants);
        assertThat(result.get("attributes")).isEqualTo(attrs);
        assertThat(result.get("extras")).isEqualTo("meh");
    }

    @Test
    void testMerge() {
        var result = service.merge(
            new HashMap<>(Map.of("k1", "v1", "k2", "v2")),
            new HashMap<>(Map.of("k2", "better v2", "k3", 7, "k4", List.of("cheese")))
        );

        assertThat(result).hasSize(4);
        assertThat(result.get("k1")).isEqualTo("v1");
        assertThat(result.get("k2")).isEqualTo("better v2");
        assertThat(result.get("k3")).isEqualTo(7);
        assertThat(result.get("k4")).isEqualTo(List.of("cheese"));
    }


    private static SearchParameters getSearchParameters() {
        return SearchParameters.builder()
            .searchMode(PRODUCT_SEARCH)
            .responseMask(MASK)
            .build();
    }

    private static Map<String, Object> recordMapFor(String id, String primaryId, String title, String uri) {
        return Map.of(
            PRODUCT_FIELD_ID, id,
            PRODUCT_FIELD_PRIMARY_ID, primaryId != null ? primaryId : id,
            PRODUCT_FIELD_TITLE, title,
            PRODUCT_FIELD_URI, uri
        );
    }

    private static ProductCatalogResponse getProductCatalogResponse() {
        return new ProductCatalogResponse(
            new ProductCatalogResponse.QueryDetails(1L, List.of()),
            List.of(ResourceUtils.getResourceAsMap("retail/pdp_product_valid.json"))
        );
    }

}
