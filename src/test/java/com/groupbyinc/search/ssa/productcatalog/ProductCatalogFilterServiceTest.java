package com.groupbyinc.search.ssa.productcatalog;

import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.stub.ResourceUtils;

import io.micronaut.core.propagation.PropagatedContext;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;
import static com.groupbyinc.search.ssa.stub.TestData.createContext;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

class ProductCatalogFilterServiceTest {

    private static final String PRODUCT_ID = "TDK642-P";
    private static final String PRIMARY_PRODUCT_ID = "TDK642-P";
    private static final String TITLE = "Thinned Point Taper Shank Drill Bit, High Speed Steel";

    private final ProductCatalogFilterService productCatalogFilterService = new ProductCatalogFilterService();

    private PropagatedContext.Scope scope;

    @BeforeEach
    void setUp() {
        scope = PropagatedContext.getOrEmpty().plus(createContext(APPAREL_MERCHANDISER)).propagate();
    }

    @AfterEach
    void tearDown() {
        scope.close();
    }

    @Test
    void filterRecordByRefinements_shouldFilterCorrectly() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata.json");
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );

        var refinements = Map.of(
            "attributes.sku", List.of(createValueRefinement("attributes.sku", "TDK793", true)),
            "priceInfo.price", List.of(createRangeRefinement("priceInfo.price", 250.0, 310.0, true))
        );

        // when
        boolean result = productCatalogFilterService.filterRecordByRefinements(record, refinements);

        // then
        assertThat(result).isTrue();
        assertResultContainsVariantsIds(record, "TDK793");
    }

    @Test
    void filterRecordByRefinements_shouldFilterOutRecord() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata.json");
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );

        var refinements = Map.of(
            "attributes.sku", List.of(createValueRefinement("attributes.sku", "NON_EXISTENT_SKU", true)),
            "priceInfo.price", List.of(createRangeRefinement("priceInfo.price", 500.0, 600.0, false))
        );

        // when
        boolean result = productCatalogFilterService.filterRecordByRefinements(record, refinements);

        // then
        assertThat(result).isFalse();
    }

    @Test
    void filterRecordByRefinements_shouldKeepCorrectVariants() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata.json");
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );

        var refinements = Map.of(
            "attributes.sku", List.of(createValueRefinement("attributes.sku", "TDK878", true)),
            "priceInfo.price", List.of(createRangeRefinement("priceInfo.price", 30.0, 32.0, true))
        );

        // when
        boolean result = productCatalogFilterService.filterRecordByRefinements(record, refinements);

        // then
        assertThat(result).isTrue();
        assertResultContainsVariantsIds(record, "TDK878");
    }

    @Test
    void filterRecordByRefinements_shouldRespectMultipleRefinementValues() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata.json");
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );

        var refinements = Map.of(
            "attributes.sku", List.of(
                createValueRefinement("attributes.sku", "TDK878",true),
                createValueRefinement("attributes.sku", "TDK793", true)
            )
        );

        // when
        boolean result = productCatalogFilterService.filterRecordByRefinements(record, refinements);

        // then
        assertThat(result).isTrue();
        assertResultContainsVariantsIds(record, "TDK878", "TDK793");
    }

    @Test
    void filterRecordByRefinements_shouldFilterByStringAttribute() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata.json");
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );

        var refinements = Map.of(
            "availability", List.of(createValueRefinement("availability", "IN_STOCK", true))
        );

        // when
        boolean result = productCatalogFilterService.filterRecordByRefinements(record, refinements);

        // then
        assertThat(result).isTrue();
        assertResultContainsVariantsIds(record, "TDK878");
    }

    @Test
    void filterRecordByRefinements_shouldFilterByNumberAttribute() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata.json");
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );

        var refinements = Map.of(
            "attributes.weight", List.of(createRangeRefinement("attributes.weight", 0.5, 1.0, true))
        );

        // when
        boolean result = productCatalogFilterService.filterRecordByRefinements(record, refinements);

        // then
        assertThat(result).isTrue();
        assertResultContainsVariantsIds(record, "TDK793");
    }

    @Test
    void filterRecordByRefinements_shouldFilterByStringArrayAttribute() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata.json");
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );

        var refinements = Map.of("sizes", List.of(createValueRefinement("sizes", "L", true))
        );

        // when
        boolean result = productCatalogFilterService.filterRecordByRefinements(record, refinements);

        // then
        assertThat(result).isTrue();
        assertResultContainsVariantsIds(record, "TDK878");
    }

    @Test
    void filterRecordByRefinements_shouldRespectMultipleRefinementValuesAndOr() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata_7_variants.json");
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );

        var refinements = Map.of(
            "sizes", List.of(
                createValueRefinement("sizes", "L", false),
                createValueRefinement("sizes", "M", true),
                createValueRefinement("sizes", "S", true),
                createValueRefinement("sizes", "XL", true)
            )
        );

        // when
        boolean result = productCatalogFilterService.filterRecordByRefinements(record, refinements);

        // then
        assertThat(result).isTrue();
        assertResultContainsVariantsIds(record, "TDK878", "TDK794");
    }

    @Test
    void filterRecordByRefinements_shouldRespectMultipleRefinementValuesAndOr_ForRangeRefinement() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata_7_variants.json");
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );
        var attribute = "attributes.inventoryQuantity";

        var refinements = Map.of(
            attribute, List.of(
                createRangeRefinement(attribute, 4.5, 6.0, false),
                createRangeRefinement(attribute, 5.0, 10.0, false),
                createRangeRefinement(attribute, 11, 20.0, true),
                createRangeRefinement(attribute, 0.5, 1.0, true)
            )
        );

        // when
        boolean result = productCatalogFilterService.filterRecordByRefinements(record, refinements);

        // then
        assertThat(result).isTrue();
        assertResultContainsVariantsIds(record, "TDK878", "TDK793");
    }

    @Test
    void filterRecordByRefinements_shouldFilterOutRecord_whenMultipleRefinementValuesAndOr() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata_7_variants.json");
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );
        var attribute = "attributes.inventoryQuantity";

        var refinements = Map.of(
            attribute, List.of(
                createRangeRefinement(attribute, 30.0, 60.0, false),
                createRangeRefinement(attribute, 5.0, 10.0, true),
                createRangeRefinement(attribute, 11, 20.0, true),
                createRangeRefinement(attribute, 0.5, 1.0, true)
            )
        );

        // when
        boolean result = productCatalogFilterService.filterRecordByRefinements(record, refinements);

        // then
        assertThat(result).isFalse();
    }

    @Test
    void filterRecordByRefinements_shouldKeepPrimary_whenNoVariantsMatch() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata.json");
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );

        var refinements = Map.of(
            "priceInfo.price", List.of(createRangeRefinement("priceInfo.price", 300.0, 320.0, true))
        );

        // when
        boolean result = productCatalogFilterService.filterRecordByRefinements(record, refinements);

        // then
        assertThat(result).isTrue();
        assertThat((List<?>) record.getMetadata().get("variants")).isEmpty();
    }

    @Test
    void filterRecordByRefinements_shouldNotFilter_whenAttributeNotPresent() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata.json");
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );

        var refinements = Map.of(
            "rating", List.of(createRangeRefinement("rating", 4.0, 5.0, true))
        );

        // when
        boolean result = productCatalogFilterService.filterRecordByRefinements(record, refinements);

        // then
        assertThat(result).isTrue();
        assertResultContainsVariantsIds(record, "TDK878", "TDK793");
    }

    @Test
    void filterInvalidRecords_shouldFilterOutInvalidRecords() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata.json");
        var validRecord = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );
        var invalidRecord = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID + "_inv",
            PRIMARY_PRODUCT_ID,
            "",
            productMetadata
        );

        var records = List.of(validRecord, invalidRecord);
        // when
        var filteredRecords = productCatalogFilterService.filterInvalidRecords(records);

        // then
        assertThat(filteredRecords).containsExactly(validRecord);

        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.equals("Product#TDK642-P_inv had no title and was filtered out."))
        );
    }

    @Test
    void filterInvalidSponsoredRecords_shouldFilterOutInvalidSponsoredRecords() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata.json");
        var validRecord = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );
        var invalidRecord = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            "",
            productMetadata
        );
        var records = List.of(validRecord, invalidRecord);

        // when
        var filteredRecords = productCatalogFilterService.filterInvalidSponsoredRecords(records);

        // then
        assertThat(filteredRecords).containsExactly(validRecord);

        assertTrue(
            getRequestContext()
                .getWarnings()
                .stream()
                .anyMatch(s -> s.equals("Sponsored Product#TDK642-P had no title and was filtered out."))
        );
    }

    @Test
    void limitVariants_shouldLimitNumberOfVariants() {
        // given
        var productMetadata = ResourceUtils.getResourceAsMap("filtering/product_metadata_7_variants.json");
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            PRODUCT_ID,
            PRIMARY_PRODUCT_ID,
            TITLE,
            productMetadata
        );


        // when
        productCatalogFilterService.limitVariants(record);

        // then
        var variants = (List<?>) record.getMetadata().get("variants");
        assertThat(variants.size()).isLessThanOrEqualTo(ProductCatalogFilterService.VARIANTS_LIMIT);
    }

    private static void assertResultContainsVariantsIds(Record record, String... ids) {
        assertThat(record.getMetadata().get("variants")).isNotNull();
        List<?> variants = (List<?>) record.getMetadata().get("variants");
        assertThat(variants.size()).isEqualTo(ids.length);
        for (int i = 0; i < ids.length; i++) {
            assertThat(((Map<?, ?>) variants.get(i)).get("id")).isEqualTo(ids[i]);
        }
    }

    private SelectedRefinement createValueRefinement(String field, String value, boolean or) {
        return SelectedRefinement.builder()
            .field(field)
            .type(NavigationType.VALUE)
            .value(value)
            .dynamic(false)
            .or(or)
            .build();
    }

    private SelectedRefinement createRangeRefinement(String field, double low, double high, boolean or) {
        return SelectedRefinement.builder()
            .field(field)
            .type(NavigationType.RANGE)
            .range(new Range(low, high, null))
            .dynamic(false)
            .or(or)
            .build();
    }

}
