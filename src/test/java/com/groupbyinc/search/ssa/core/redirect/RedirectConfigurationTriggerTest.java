package com.groupbyinc.search.ssa.core.redirect;

import com.groupbyinc.search.ssa.core.SearchParameters;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static com.groupbyinc.search.ssa.stub.TestData.ACTIVE_REDIRECT;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("RedirectConfiguration Trigger Tests")
class RedirectConfigurationTriggerTest {

    @Test
    @DisplayName("Comma-separated trigger type CONTAINS matches query")
    void triggerMatchesQuery() {
        var searchParameters = getSearchParameters("bacon double cheeseburger");

        var matches = ACTIVE_REDIRECT.trigger(searchParameters);

        assertThat(matches).isEqualTo(true);
    }

    @Test
    @DisplayName("Comma-separated trigger type CONTAINS not matches query")
    void triggerNotMatchesQuery() {
        var searchParameters = getSearchParameters("Spicy pizza");

        var matches = ACTIVE_REDIRECT.trigger(searchParameters);

        assertThat(matches).isEqualTo(false);
    }

    private static SearchParameters getSearchParameters(String query) {
        return SearchParameters.builder().query(query).build();
    }

}
