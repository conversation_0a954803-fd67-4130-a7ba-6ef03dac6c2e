package com.groupbyinc.search.ssa.core.trigger;

import com.groupbyinc.search.ssa.core.CustomParameter;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("CustomParameterTrigger Tests")
public class CustomParameterTriggerTest {

    @ParameterizedTest(name = "Trigger {0} matches ''{1}'' is {2}")
    @MethodSource("customParameterArguments")
    @DisplayName("Custom parameter trigger matching")
    void customParameter(CustomParameterTrigger trigger, CustomParameter customParameter, boolean expectedMatches) {

        var matches = trigger.matches(customParameter);

        assertThat(matches).isEqualTo(expectedMatches);
    }

    static Stream<Arguments> customParameterArguments() {

        return Stream.of(
            Arguments.of(
                new CustomParameterTrigger("key", "value"),
                new CustomParameter("key", "value"),
                true
            ),
            Arguments.of(
                new CustomParameterTrigger("key", "value"),
                new CustomParameter("foo", "bar"),
                false
            )
        );
    }
}
