package com.groupbyinc.search.ssa.core.trigger;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.function.Function;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("QueryPatternTrigger Tests")
public class QueryPatternTriggerTest {

    @ParameterizedTest(name = "Trigger {0} matches ''{1}'' is {2}")
    @MethodSource("queryPatternArguments")
    @DisplayName("Query pattern trigger matching")
    void queryPattern(QueryPatternTrigger trigger, String query, boolean expectedMatches) {

        var matches = trigger.matches(query);

        assertThat(matches).isEqualTo(expectedMatches);
    }

    static Stream<Arguments> queryPatternArguments() {

        return Stream.of(
            queryPatternContainsArguments(),
            queryPatternMatchesArguments(),
            queryPatternStartsWithArguments(),
            queryPatternEndsWithArguments(),
            queryPatternRegexArguments()
        )
            .flatMap(Function.identity());
    }

    static Stream<Arguments> queryPatternContainsArguments() {

        return Stream.of(
            Arguments.of(
                new QueryPatternTrigger(
                    QueryPatternTrigger.Type.CONTAINS,
                    "CHeeSE"
                ),
                "bacon double cheeseburger",
                true
            ),
            Arguments.of(
                new QueryPatternTrigger(
                    QueryPatternTrigger.Type.CONTAINS,
                    "wurst"
                ),
                "bacon double cheeseburger",
                false
            )
        );
    }

    static Stream<Arguments> queryPatternMatchesArguments() {

        return Stream.of(
            Arguments.of(
                new QueryPatternTrigger(
                    QueryPatternTrigger.Type.MATCHES,
                    "bacon DOUBLE cheeseburger"
                ),
                "bacon double cheeseburger",
                true
            ),
            Arguments.of(
                new QueryPatternTrigger(
                    QueryPatternTrigger.Type.MATCHES,
                    "bacon single cheeseburger"
                ),
                "bacon double cheeseburger",
                false
            )
        );
    }

    static Stream<Arguments> queryPatternStartsWithArguments() {

        return Stream.of(
            Arguments.of(
                new QueryPatternTrigger(
                    QueryPatternTrigger.Type.STARTS_WITH,
                    "bacon"
                ),
                "bacon double cheeseburger",
                true
            ),
            Arguments.of(
                new QueryPatternTrigger(
                    QueryPatternTrigger.Type.STARTS_WITH,
                    "burger"
                ),
                "bacon double cheeseburger",
                false
            )
        );
    }

    static Stream<Arguments> queryPatternEndsWithArguments() {

        return Stream.of(
            Arguments.of(
                new QueryPatternTrigger(
                    QueryPatternTrigger.Type.ENDS_WITH,
                    "burger"
                ),
                "bacon double cheeseburger",
                true
            ),
            Arguments.of(
                new QueryPatternTrigger(
                    QueryPatternTrigger.Type.ENDS_WITH,
                    "cheese"
                ),
                "bacon double cheeseburger",
                false
            )
        );
    }

    static Stream<Arguments> queryPatternRegexArguments() {

        return Stream.of(
            Arguments.of(
                new QueryPatternTrigger(
                    QueryPatternTrigger.Type.REGEX,
                    "^bacon.*burger$"
                ),
                "bacon double cheeseburger",
                true
            ),
            Arguments.of(
                new QueryPatternTrigger(
                    QueryPatternTrigger.Type.REGEX,
                    "eee{3}"
                ),
                "bacon double cheeseburger",
                false
            )
        );
    }
}
