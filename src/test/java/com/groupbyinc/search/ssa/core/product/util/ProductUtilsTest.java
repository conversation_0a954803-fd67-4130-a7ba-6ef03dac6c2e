package com.groupbyinc.search.ssa.core.product.util;

import com.groupbyinc.search.ssa.core.product.Product;
import com.groupbyinc.search.ssa.stub.ResourceUtils;

import com.fasterxml.jackson.databind.JsonMappingException;
import org.junit.jupiter.api.Test;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

class ProductUtilsTest {

    @Test
    public void testPrioritizeVariants() {
        var primary = ProductUtils.fromMap(ResourceUtils.getResourceAsMap("retail/pdp_product_valid.json"));

        var result = ProductUtils.prioritizeVariants(List.of("111_3", "111_2"), primary.getVariants());

        assertThat(result).hasSize(3);
        assertThat(result.getFirst().getId()).isEqualTo("111_3");
        assertThat(result.get(1).getId()).isEqualTo("111_2");
        assertThat(result.get(2).getId()).isEqualTo("111_1");
    }

    @Test
    public void testPrioritizeVariantsThatDontExist() {
        var primary = ProductUtils.fromMap(ResourceUtils.getResourceAsMap("retail/pdp_product_valid.json"));

        var result = ProductUtils.prioritizeVariants(List.of("111_4"), primary.getVariants());

        assertThat(result).hasSize(3);
        //order of untouched variants collection is not guaranteed
        assertThat(result.stream().map(Product::getId).sorted().toList()).contains("111_1", "111_2", "111_3");
    }

    @Test
    public void testProductFromMap() {
        var result = ProductUtils.fromMap(ResourceUtils.getResourceAsMap("retail/pdp_product_valid.json"));

        assertThat(result.getId()).isEqualTo("111");
        assertThat(result.getPrimaryProductId()).isEqualTo("111");
        assertThat(result.getName()).isEmpty();
        assertThat(result.getTitle()).isEqualTo("Title example");
        assertThat(result.getDescription()).isEqualTo("Largo de manga Corta Material Algodn Cuello Redondo Fit Slim");
        assertThat(result.getType()).isEqualTo("PRIMARY");
        assertThat(result.getUri()).isEqualTo("https://example.mx/tienda/pdp/playera/1101861569");
        assertThat(result.getGtin()).isEqualTo("123123");
        assertThat(result.getCollectionMemberIds()).containsExactly("1", "2", "3");
        assertThat(result.getLanguageCode()).isEqualTo("es-mx");
        assertThat(result.getTags()).containsExactly("tag1", "tag2");
        assertThat(result.getCategories()).containsExactly(
            "Banana Republic > Mujer > Blusas, Camisas y Playeras",
            "MUJER > ROPA DE MUJER > Blusas, Camisas y Playeras",
            "Mujer > Ropa > Playeras"
        );
        assertThat(result.getBrands()).containsExactly(
            "POTTERY BARN",
            "POTTERY WAND"
        );
        assertThat(result.getRetrievableFields()).isNotNull();
        assertThat(result.getRetrievableFields().getPaths()).containsExactly(
            "color_info",
            "price_info",
            "audience",
            "images",
            "sizes",
            "materials",
            "name",
            "availability",
            "title",
            "uri"
        );

        assertThat(result.getAttributes()).hasSize(2);
        assertThat(result.getAttributes().get("textAttribute").getText()).containsExactly("prAttr1", "prAttr2");
        assertThat(result.getAttributes().get("textAttribute").getNumbers()).isEmpty();
        assertThat(result.getAttributes().get("numberAttribute").getText()).isEmpty();
        assertThat(result.getAttributes().get("numberAttribute").getNumbers()).containsExactly(1.5, 2D);

        assertThat(result.getPriceInfo()).isNotNull();
        assertThat(result.getPriceInfo().getCurrencyCode()).isEqualTo("MXN");
        assertThat(result.getPriceInfo().getPrice()).isEqualTo(99.99F);
        assertThat(result.getPriceInfo().getOriginalPrice()).isEqualTo(299.99F);
        assertThat(result.getPriceInfo().getPriceEffectiveTime()).isNotNull();

        assertThat(result.getPriceInfo().getPriceEffectiveTime().getSeconds())
            .isEqualTo(OffsetDateTime.of(2024, 1, 1, 0, 0, 0, 0, ZoneOffset.UTC).toEpochSecond());
        assertThat(result.getPriceInfo().getPriceExpireTime()).isNotNull();
        assertThat(result.getPriceInfo().getPriceExpireTime().getSeconds())
            .isEqualTo(OffsetDateTime.of(2024, 2, 1, 0, 0, 0, 0, ZoneOffset.UTC).toEpochSecond());

        assertThat(result.getPriceInfo().getPriceRange()).isNotNull();
        assertThat(result.getPriceInfo().getPriceRange().getPrice()).isNotNull();
        assertThat(result.getPriceInfo().getPriceRange().getPrice().getMinimum()).isEqualTo(49);
        assertThat(result.getPriceInfo().getPriceRange().getPrice().getMaximum()).isEqualTo(149);
        assertThat(result.getPriceInfo().getPriceRange().getPrice().getExclusiveMinimum()).isEqualTo(66);
        assertThat(result.getPriceInfo().getPriceRange().getPrice().getExclusiveMaximum()).isEqualTo(99);
        assertThat(result.getPriceInfo().getPriceRange().getOriginalPrice()).isNotNull();
        assertThat(result.getPriceInfo().getPriceRange().getOriginalPrice().getMinimum()).isEqualTo(99);
        assertThat(result.getPriceInfo().getPriceRange().getOriginalPrice().getMaximum()).isEqualTo(199);
        assertThat(result.getPriceInfo().getPriceRange().getOriginalPrice().getExclusiveMinimum()).isEqualTo(88);
        assertThat(result.getPriceInfo().getPriceRange().getOriginalPrice().getExclusiveMaximum()).isEqualTo(111);

        assertThat(result.getRating()).isNotNull();
        assertThat(result.getRating().getRatingCount()).isEqualTo(1);
        assertThat(result.getRating().getAverageRating()).isEqualTo(5);
        assertThat(result.getRating().getRatingHistogram()).containsExactly(0, 1, 2, 3, 4);

        assertThat(result.getAvailability()).isEqualTo("AVAILABLE");
        assertThat(result.getAvailableQuantity()).isEqualTo(3);

        assertThat(result.getAvailableTime()).isNotNull();
        assertThat(result.getAvailableTime().getSeconds())
            .isEqualTo(OffsetDateTime.of(2024, 1, 5, 0, 0, 0, 0, ZoneOffset.UTC).toEpochSecond());
        assertThat(result.getPublishTime()).isNotNull();
        assertThat(result.getPublishTime().getSeconds())
            .isEqualTo(OffsetDateTime.of(2024, 2, 2, 0, 0, 0, 0, ZoneOffset.UTC).toEpochSecond());

        assertThat(result.getFulfillmentInfos()).hasSize(2);
        assertThat(result.getFulfillmentInfos().getFirst().getType()).isEqualTo("a-next-day-delivery");
        assertThat(result.getFulfillmentInfos().getFirst().getPlaceIds()).containsExactly("your place");
        assertThat(result.getFulfillmentInfos().get(1).getType()).isEqualTo("b-take-away");
        assertThat(result.getFulfillmentInfos().get(1).getPlaceIds()).containsExactly("store", "kiosk");

        assertThat(result.getImages()).hasSize(3);
        assertThat(result.getImages().getFirst().getHeight()).isEqualTo(250);
        assertThat(result.getImages().getFirst().getWidth()).isEqualTo(250);
        assertThat(result.getImages().getFirst().getUri()).isEqualTo("largeImage##https://example.mx/lg/1.jpg");
        assertThat(result.getImages().get(1).getHeight()).isEqualTo(250);
        assertThat(result.getImages().get(1).getWidth()).isEqualTo(250);
        assertThat(result.getImages().get(1).getUri()).isEqualTo("thumbnailImage##https://example.mx/xl/2.jpg");
        assertThat(result.getImages().get(2).getHeight()).isEqualTo(250);
        assertThat(result.getImages().get(2).getWidth()).isEqualTo(250);
        assertThat(result.getImages().get(2).getUri()).isEqualTo("smallImage##https://example.mx/sm/3.jpg");

        assertThat(result.getAudience()).isNotNull();
        assertThat(result.getAudience().getGenders()).containsExactly("male", "female", "apache helicopter");
        assertThat(result.getAudience().getAgeGroups()).containsExactly("infant", "child", "teen", "adult", "undead");

        assertThat(result.getColorInfo()).isNotNull();
        assertThat(result.getColorInfo().getColorFamilies()).containsExactly("Blanco", "Azul");
        assertThat(result.getColorInfo().getColors()).containsExactly("Blanco", "Blue");

        assertThat(result.getSizes()).containsExactly("S", "M", "L", "XL");
        assertThat(result.getMaterials()).containsExactly("Algodn", "Cuero", "Tela");
        assertThat(result.getPatterns()).containsExactly("circles", "squares");
        assertThat(result.getConditions()).containsExactly("new", "tattered");
        assertThat(result.getPromotions()).hasSize(2);
        assertThat(result.getPromotions().getFirst().getPromotionId()).isEqualTo("p1");
        assertThat(result.getPromotions().get(1).getPromotionId()).isEqualTo("p2");

        assertThat(result.getVariants()).hasSize(3);

        var variant1 = result.getVariants().getFirst();
        assertThat(variant1.getId()).isEqualTo("111_1");
        assertThat(variant1.getPrimaryProductId()).isEqualTo("111");
        assertThat(variant1.getName()).isEmpty();
        assertThat(variant1.getTitle()).isEqualTo("PLAYERA BANANA 679325, XCH, BLANCO");
        assertThat(variant1.getType()).isEqualTo("VARIANT");
        assertThat(variant1.getLanguageCode()).isEqualTo("es-mx");

        assertThat(variant1.getAttributes()).hasSize(2);
        assertThat(variant1.getAttributes().get("textAttribute").getText()).containsExactly("v1Attr1", "v1Attr2");
        assertThat(variant1.getAttributes().get("textAttribute").getNumbers()).isEmpty();
        assertThat(variant1.getAttributes().get("numberAttribute").getText()).isEmpty();
        assertThat(variant1.getAttributes().get("numberAttribute").getNumbers()).containsExactly(3D, 4D);
        assertThat(variant1.getRetrievableFields()).isNotNull();
        assertThat(variant1.getRetrievableFields().getPaths()).containsExactly("color_info", "price_info", "audience");

        assertThat(variant1.getPriceInfo()).isNotNull();
        assertThat(variant1.getPriceInfo().getCurrencyCode()).isEqualTo("MXN");
        assertThat(variant1.getPriceInfo().getPrice()).isEqualTo(149.5F);
        assertThat(variant1.getPriceInfo().getOriginalPrice()).isEqualTo(299F);
        assertThat(variant1.getPriceInfo().getPriceRange()).isNotNull();
        assertThat(variant1.getPriceInfo().getPriceRange().getPrice()).isNotNull();
        assertThat(variant1.getPriceInfo().getPriceRange().getPrice().getMinimum()).isEqualTo(99);
        assertThat(variant1.getPriceInfo().getPriceRange().getPrice().getMaximum()).isEqualTo(111);
        assertThat(variant1.getPriceInfo().getPriceRange().getPrice().getExclusiveMinimum()).isEqualTo(101);
        assertThat(variant1.getPriceInfo().getPriceRange().getPrice().getExclusiveMaximum()).isEqualTo(102);
        assertThat(variant1.getPriceInfo().getPriceRange().getOriginalPrice()).isNotNull();
        assertThat(variant1.getPriceInfo().getPriceRange().getOriginalPrice().getMinimum()).isEqualTo(11);
        assertThat(variant1.getPriceInfo().getPriceRange().getOriginalPrice().getMaximum()).isEqualTo(22);
        assertThat(variant1.getPriceInfo().getPriceRange().getOriginalPrice().getExclusiveMinimum()).isEqualTo(12);
        assertThat(variant1.getPriceInfo().getPriceRange().getOriginalPrice().getExclusiveMaximum()).isEqualTo(13);

        assertThat(variant1.getAvailability()).isEqualTo("AVAILABLE");
        assertThat(variant1.getAvailableQuantity()).isEqualTo(7);
        assertThat(variant1.getColorInfo()).isNotNull();
        assertThat(variant1.getColorInfo().getColorFamilies()).containsExactly("Blanco");
        assertThat(variant1.getColorInfo().getColors()).containsExactly("Blanco");
        assertThat(variant1.getMaterials()).containsExactly("Algodn");

        var variant2 = result.getVariants().get(1);
        assertThat(variant2.getId()).isEqualTo("111_2");
        assertThat(variant2.getPrimaryProductId()).isEqualTo("111");
        assertThat(variant2.getName()).isEmpty();
        assertThat(variant2.getTitle()).isEqualTo("PLAYERA BANANA 679325, CH, BLANCO");
        assertThat(variant2.getType()).isEqualTo("VARIANT");
        assertThat(variant2.getLanguageCode()).isEqualTo("es-mx");
        assertThat(variant2.getRetrievableFields()).isNotNull();
        assertThat(variant2.getRetrievableFields().getPaths()).containsExactly("color_info", "price_info", "audience");

        assertThat(variant2.getAttributes()).hasSize(2);
        assertThat(variant2.getAttributes().get("textAttribute").getText()).containsExactly("v2Attr1", "v2Attr2");
        assertThat(variant2.getAttributes().get("textAttribute").getNumbers()).isEmpty();
        assertThat(variant2.getAttributes().get("numberAttribute").getText()).isEmpty();
        assertThat(variant2.getAttributes().get("numberAttribute").getNumbers()).containsExactly(5D, 6D);

        assertThat(variant2.getPriceInfo()).isNotNull();
        assertThat(variant2.getPriceInfo().getCurrencyCode()).isEqualTo("MXN");
        assertThat(variant2.getPriceInfo().getPrice()).isEqualTo(129.5F);
        assertThat(variant2.getPriceInfo().getOriginalPrice()).isEqualTo(279F);

        assertThat(variant2.getAvailability()).isEqualTo("OUT_OF_STOCK");
        assertThat(variant2.getAvailableQuantity()).isEqualTo(0);
        assertThat(variant2.getColorInfo()).isNotNull();
        assertThat(variant2.getColorInfo().getColorFamilies()).containsExactly("Rojo");
        assertThat(variant2.getColorInfo().getColors()).containsExactly("Rojo");
        assertThat(variant2.getMaterials()).containsExactly("Wood");

        var variant3 = result.getVariants().get(2);
        assertThat(variant3.getId()).isEqualTo("111_3");
        assertThat(variant3.getPrimaryProductId()).isEqualTo("111");
        assertThat(variant3.getName()).isEmpty();
        assertThat(variant3.getTitle()).isEqualTo("PLAYERA GRAPE 679325, CH, BLANCO");
        assertThat(variant3.getType()).isEqualTo("VARIANT");
        assertThat(variant3.getLanguageCode()).isEqualTo("es-mx");
        assertThat(variant3.getRetrievableFields()).isNotNull();
        assertThat(variant3.getRetrievableFields().getPaths()).isEmpty();

        assertThat(variant3.getAttributes()).hasSize(2);
        assertThat(variant3.getAttributes().get("textAttribute").getText()).containsExactly("v3Attr1", "v3Attr2");
        assertThat(variant3.getAttributes().get("textAttribute").getNumbers()).isEmpty();
        assertThat(variant3.getAttributes().get("numberAttribute").getText()).isEmpty();
        assertThat(variant3.getAttributes().get("numberAttribute").getNumbers()).containsExactly(7D, 8D);

        assertThat(variant3.getPriceInfo()).isNotNull();
        assertThat(variant3.getPriceInfo().getCurrencyCode()).isEqualTo("MXN");
        assertThat(variant3.getPriceInfo().getPrice()).isEqualTo(129.5F);
        assertThat(variant3.getPriceInfo().getOriginalPrice()).isEqualTo(279F);

        assertThat(variant3.getAvailability()).isEqualTo("OUT_OF_STOCK");
        assertThat(variant3.getAvailableQuantity()).isEqualTo(0);
        assertThat(variant3.getColorInfo()).isNotNull();
        assertThat(variant3.getColorInfo().getColorFamilies()).containsExactly("Rojo");
        assertThat(variant3.getColorInfo().getColors()).containsExactly("Rojo");
        assertThat(variant3.getMaterials()).containsExactly("Wood");

        assertThat(result.getLocalInventories()).hasSize(3);

        var inv1 = result.getLocalInventories().getFirst();
        assertThat(inv1.placeId()).isEqualTo("place1");
        assertThat(inv1.attributes()).hasSize(2);
        assertThat(inv1.attributes().get("textAttribute").getText()).containsExactly("inv1Attr1", "inv1Attr2");
        assertThat(inv1.attributes().get("textAttribute").getNumbers()).isEmpty();
        assertThat(inv1.attributes().get("numberAttribute").getText()).isEmpty();
        assertThat(inv1.attributes().get("numberAttribute").getNumbers()).containsExactly(11D, 12D);
        assertThat(inv1.priceInfo()).isNotNull();
        assertThat(inv1.priceInfo().getCurrencyCode()).isEqualTo("MXN");
        assertThat(inv1.priceInfo().getPrice()).isEqualTo(44F);
        assertThat(inv1.priceInfo().getOriginalPrice()).isEqualTo(55F);
        assertThat(inv1.priceInfo().getPriceRange()).isNotNull();
        assertThat(inv1.priceInfo().getPriceRange().getPrice()).isNotNull();
        assertThat(inv1.priceInfo().getPriceRange().getPrice().getMinimum()).isEqualTo(66);
        assertThat(inv1.priceInfo().getPriceRange().getPrice().getMaximum()).isEqualTo(77);
        assertThat(inv1.priceInfo().getPriceRange().getPrice().getExclusiveMinimum()).isEqualTo(67);
        assertThat(inv1.priceInfo().getPriceRange().getPrice().getExclusiveMaximum()).isEqualTo(76);
        assertThat(inv1.priceInfo().getPriceRange().getOriginalPrice()).isNotNull();
        assertThat(inv1.priceInfo().getPriceRange().getOriginalPrice().getMinimum()).isEqualTo(88);
        assertThat(inv1.priceInfo().getPriceRange().getOriginalPrice().getMaximum()).isEqualTo(99);
        assertThat(inv1.priceInfo().getPriceRange().getOriginalPrice().getExclusiveMinimum()).isEqualTo(89);
        assertThat(inv1.priceInfo().getPriceRange().getOriginalPrice().getExclusiveMaximum()).isEqualTo(98);

        var inv2 = result.getLocalInventories().get(1);
        assertThat(inv2.placeId()).isEqualTo("place2");
        assertThat(inv2.attributes()).hasSize(2);
        assertThat(inv2.attributes().get("textAttribute").getText()).containsExactly("inv2Attr1", "inv2Attr2");
        assertThat(inv2.attributes().get("textAttribute").getNumbers()).isEmpty();
        assertThat(inv2.attributes().get("numberAttribute").getText()).isEmpty();
        assertThat(inv2.attributes().get("numberAttribute").getNumbers()).containsExactly(21D, 22D);
        assertThat(inv2.priceInfo()).isNotNull();
        assertThat(inv2.priceInfo().getCurrencyCode()).isEqualTo("MXN");
        assertThat(inv2.priceInfo().getPrice()).isEqualTo(33F);
        assertThat(inv2.priceInfo().getOriginalPrice()).isEqualTo(44F);

        var inv3 = result.getLocalInventories().get(2);
        assertThat(inv3.placeId()).isEqualTo("place3");
        assertThat(inv3.attributes()).hasSize(2);
        assertThat(inv3.attributes().get("textAttribute").getText()).containsExactly("inv3Attr1", "inv3Attr2");
        assertThat(inv3.attributes().get("textAttribute").getNumbers()).isEmpty();
        assertThat(inv3.attributes().get("numberAttribute").getText()).isEmpty();
        assertThat(inv3.attributes().get("numberAttribute").getNumbers()).containsExactly(31D, 32D);
        assertThat(inv3.priceInfo()).isNotNull();
        assertThat(inv3.priceInfo().getCurrencyCode()).isEqualTo("MXN");
        assertThat(inv3.priceInfo().getPrice()).isEqualTo(22F);
        assertThat(inv3.priceInfo().getOriginalPrice()).isEqualTo(33F);
    }

    @Test
    public void testProductFromMapInvalid() {
        // verify no exception
        var result = ProductUtils.fromMap(ResourceUtils.getResourceAsMap("retail/pdp_product_invalid.json"));

        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo("what");
        assertThat(result.getAvailableTime()).isNull();
        assertThat(result.getPublishTime()).isNull();
        assertThat(result.getVariants()).isEmpty();
        assertThat(result.getLocalInventories()).isEmpty();
    }

    @Test
    public void testProductFromMapInvalidTypes() {
        var e = assertThrows(
            IllegalArgumentException.class,
            () -> ProductUtils.fromMap(ResourceUtils.getResourceAsMap("retail/pdp_product_invalid_type.json")));

        assertThat(e).hasCauseInstanceOf(JsonMappingException.class);
        assertThat(e)
            .hasMessageContaining("Cannot deserialize value")
            .hasMessageContaining("Product[\"id\"]");

    }
}
