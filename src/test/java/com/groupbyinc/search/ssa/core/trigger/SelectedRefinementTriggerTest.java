package com.groupbyinc.search.ssa.core.trigger;

import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.core.navigation.NavigationType.RANGE;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.VALUE;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("SelectedRefinementTrigger Tests")
public class SelectedRefinementTriggerTest {

    @ParameterizedTest(name = "Trigger {0} matches ''{1}'' is {2}")
    @MethodSource("selectedRefinementSelectedArguments")
    @DisplayName("Selected refinement selected trigger matching")
    void selectedRefinementSelected(SelectedRefinementTrigger trigger,
                                    SelectedRefinement selectedRefinement,
                                    boolean expectedMatches) {
        var matches = trigger.matches(selectedRefinement);

        assertThat(matches).isEqualTo(expectedMatches);
    }

    static Stream<Arguments> selectedRefinementSelectedArguments() {
        return Stream.of(
            Arguments.of(
                new SelectedRefinementTrigger("brands"),
                brandSelectedRefinement(),
                true
            ),
            Arguments.of(
                new SelectedRefinementTrigger("price"),
                priceSelectedRefinement(),
                true
            ),
            Arguments.of(
                new SelectedRefinementTrigger("brands"),
                colorFamiliesSelectedRefinement(),
                false
            )
        );
    }

    public static SelectedRefinement brandSelectedRefinement() {
        return new SelectedRefinement(
            "brands",
            VALUE,
            null,
            "Nike",
            false,
            false
        );
    }

    public static SelectedRefinement priceSelectedRefinement() {
        return new SelectedRefinement(
            "price",
            RANGE,
            new Range(0d, 50d, null),
            null,
            false,
            false
        );
    }

    public static SelectedRefinement colorFamiliesSelectedRefinement() {
        return new SelectedRefinement(
            "colorFamilies",
            NavigationType.VALUE,
            null,
            "Red",
            false,
            false
        );
    }

}
