package com.groupbyinc.search.ssa.core;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class SponsoredRecordsRequestTest {
    private static final int COUNT = 0;

    @Test
    void testNormalizePositions() {
        var pageSize = 10;

        // left - given, right - expected
        var testCases = List.of(
            Pair.of(
                List.of(0, 1, 2, 3, 4, 5),
                List.of(0, 1, 2, 3, 4, 5)),
            Pair.of(
                List.of(0, 1, 2, 3, 4, 5, 6, 7, 8, 9),
                List.of(0, 1, 2, 3, 4, 5, 6, 7, 8, 9)),
            Pair.of(
                List.of(0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11),
                List.of(0, 1, 2, 3, 4, 5, 6, 7, 8, 9)),
            Pair.of(
                List.of(0, 3, 5, 7, 9),
                List.of(0, 3, 5, 7, 9)),
            Pair.of(
                List.of(0, 0, 0, 1, 3, 5, 7),
                List.of(0, 1, 3, 5, 7)),
            Pair.of(
                List.of(0, 0, 1, 3, 5, 7, 9, 9, 9),
                List.of(0, 1, 3, 5, 7, 9)),
            Pair.of(
                List.of(-1, -2, -3, 5, 0, 3),
                List.of(0, 3, 5)),
            Pair.of(
                List.of(-1, -2, -3, 5, 0, 3, 9, 11),
                List.of(0, 3, 5, 9)),
            Pair.of(
                List.of(-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1),
                List.of()),
            Pair.of(
                List.of(99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99, 99),
                List.of()),
            Pair.of(
                List.of(-333, 2, -22, 5, 3, 4201, 6, 13, 9999, 3333, 15),
                List.of(2, 3, 5, 6)),
            Pair.of(
                List.of(Integer.MIN_VALUE, -333, -22, 5, 3, Integer.MAX_VALUE, 6, Integer.MAX_VALUE, 9999, 3333, 15),
                List.of(3, 5, 6))
        );

        testCases.forEach(t -> assertThat(new SponsoredRecordsRequest(COUNT, t.getLeft()).positionsNormalized(pageSize))
            .as("Input: %s".formatted(t.getLeft()))
            .isEqualTo(t.getRight()));
    }

}
