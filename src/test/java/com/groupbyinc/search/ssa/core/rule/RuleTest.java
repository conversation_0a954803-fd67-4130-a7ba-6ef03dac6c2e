package com.groupbyinc.search.ssa.core.rule;

import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.trigger.TriggerSet;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;

@DisplayName("Rule Tests")
class RuleTest {

    private SearchParameters searchParameters;
    private TriggerSet alwaysTriggers;
    private TriggerSet neverTriggers;

    @BeforeEach
    void setUp() {
        searchParameters = mock(SearchParameters.class);
        alwaysTriggers = mock(TriggerSet.class);
        given(alwaysTriggers.trigger(any())).willReturn(true);
        neverTriggers = mock(TriggerSet.class);
        given(neverTriggers.trigger(any())).willReturn(false);
    }

    @Test
    @DisplayName("Rule triggers when at least one trigger set is triggered")
    void ruleTriggersWhenOneTriggerSetIsTriggered() {

        // Given: A rule that is enabled and has a triggered trigger set
        var rule = RuleConfiguration.builder()
            .id(1)
            .name("Rule A")
            .areaId(1)
            .priority(50)
            .triggerSets(Set.of(neverTriggers, alwaysTriggers))
            .build();

        // When: We attempt to trigger the rule
        // Then: The rule is triggered
        assertTrue(rule.triggersOn(searchParameters).triggered());
    }

    @Test
    @DisplayName("Rule does not trigger when no trigger sets are triggered")
    void ruleDoesNotTriggerWhenNoTriggerSetsTrigger() {

        // Given: A rule that has no trigger sets that would otherwise trigger
        var rule = RuleConfiguration.builder()
            .id(1)
            .name("Rule A")
            .areaId(1)
            .priority(50)
            .triggerSets(Set.of(neverTriggers))
            .build();

        // When: We attempt to trigger the rule
        // Then: The rule is not triggered
        assertFalse(rule.triggersOn(searchParameters).triggered());
    }
}
