package com.groupbyinc.search.ssa.core;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("Record Tests")
class RecordTest {

    @Test
    void canCreateARecordForAGivenContext() {
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            "1070930",
            "p1070930",
            "Zyrtec Allergy Tablets 10mg",
            Map.of("foo", "bar"),
            RecordLabel.BOOSTED
        );
        assertThat(record.getId()).isEqualTo("d2e49ac58483e5d2461431f5b592425e");
        assertThat(record.getProductId()).isEqualTo("1070930");
        assertThat(record.getPrimaryProductId()).isEqualTo("p1070930");
        assertThat(record.getUrl()).isEqualTo("http://apparel1productsClothing.com/1070930");
        assertThat(record.getTitle()).isEqualTo("Zyrtec Allergy Tablets 10mg");
        assertThat(record.getCollection()).isEqualTo(PRODUCTS_CLOTHING);
        assertThat(record.getMetadata()).isEqualTo(Map.of("foo", "bar"));
        assertThat(record.getLabel()).isEqualTo(RecordLabel.BOOSTED);
    }

    @Test
    void canCreateARecordForAGivenContextWithPrimaryProductItEmpty() {
        var record = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            "1070930",
            null,
            "Zyrtec Allergy Tablets 10mg",
            Map.of("foo", "bar"),
            RecordLabel.BOOSTED
        );
        assertThat(record.getId()).isEqualTo("d2e49ac58483e5d2461431f5b592425e");
        assertThat(record.getProductId()).isEqualTo("1070930");
        assertThat(record.getPrimaryProductId()).isEqualTo("1070930");
        assertThat(record.getUrl()).isEqualTo("http://apparel1productsClothing.com/1070930");
        assertThat(record.getTitle()).isEqualTo("Zyrtec Allergy Tablets 10mg");
        assertThat(record.getCollection()).isEqualTo(PRODUCTS_CLOTHING);
        assertThat(record.getMetadata()).isEqualTo(Map.of("foo", "bar"));
        assertThat(record.getLabel()).isEqualTo(RecordLabel.BOOSTED);
    }

    @Test
    void createsDifferentHashesForDifferentRecords() {
        var recordA = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            "1070930",
            "p1070930",
            "Zyrtec Allergy Tablets 10mg",
            Map.of("foo", "bar"),
            RecordLabel.PINNED
        );
        var recordB = Record.of(
            APPAREL_MERCHANDISER,
            PRODUCTS_CLOTHING,
            "1070931",
            "p1070931",
            "Zyrtec Allergy Tablets 25mg",
            Map.of("foo", "bar"),
            RecordLabel.BURIED
        );

        assertThat(recordA.getId()).isNotEqualTo(recordB.getId());
    }
}
