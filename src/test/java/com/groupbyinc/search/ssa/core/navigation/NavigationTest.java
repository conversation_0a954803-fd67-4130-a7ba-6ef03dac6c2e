package com.groupbyinc.search.ssa.core.navigation;

import com.groupbyinc.search.ssa.core.Order;
import com.groupbyinc.search.ssa.core.rule.PinnedRefinement;
import com.groupbyinc.search.ssa.core.rule.Refinement;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class NavigationTest {

    @Test
    @DisplayName("Test merged refinements' sorting with different sort configurations")
    void testMergeNavigationRefinements_withDifferentSortConfigurations() {
        // given
        var commonNavName = "brands";
        var commonNavField = "field_" + commonNavName;

        var googleRefinements = List.of(
            NavigationRefinement.valueRefinement("zebra", 5, false),
            NavigationRefinement.valueRefinement("apple", 3, false)
        );
        var googleNav = Navigation.builder()
            .name(commonNavName)
            .field(commonNavField)
            .type(NavigationType.VALUE)
            .sort(new NavigationSort(NavigationSort.SortField.VALUE, Order.DESCENDING))
            .refinements(googleRefinements)
            .build();

        // Create secondary navigation with common refinements
        var mongoRefinements = List.of(
            NavigationRefinement.valueRefinement("zebra", 8, false),
            NavigationRefinement.valueRefinement("banana", 4, false)
        );
        var mongoNav = Navigation.builder()
            .name(commonNavName)
            .field(commonNavField)
            .type(NavigationType.VALUE)
            .refinements(mongoRefinements)
            .build();

        var pinnedRefinements = List.of(new PinnedRefinement(commonNavField, List.of(new Refinement("banana", 1))));

        // when
        var mergedNav = Navigation.mergeNavigationRefinements(googleNav, mongoNav, pinnedRefinements);

        // then
        var mergedRefinements = mergedNav.getRefinements();
        assertThat(mergedRefinements).hasSize(3); // zebra, banana, apple

        // Verify descending alphabetical order: zebra, banana, apple
        assertThat(mergedRefinements.get(0).getValue()).isEqualTo("banana"); // pinned
        assertThat(mergedRefinements.get(1).getValue()).isEqualTo("zebra");
        assertThat(mergedRefinements.get(2).getValue()).isEqualTo("apple");

        // Verify the common refinement count is summed
        assertThat(mergedRefinements.get(1).getCount()).isEqualTo(13); // zebra: 5 + 8
    }

    @Test
    @DisplayName("Test range navigation refinement sorting")
    void testMergeNavigationRefinements_withRangeNavigationSorting() {
        // given
        var navName = "price";

        var mongoRange1 = new Range(100.0, 200.0);
        var mongoRange2 = new Range(0.0, 50.0);
        var mongoRefinements = List.of(
            NavigationRefinement.rangeRefinement(mongoRange1, 5),
            NavigationRefinement.rangeRefinement(mongoRange2, 3)
        );
        var mongoNav = Navigation.builder()
            .name(navName)
            .field("field_" + navName)
            .type(NavigationType.RANGE)
            .refinements(mongoRefinements)
            .build();

        var googleRange1 = new Range(100.0, 200.0);
        var googleRange3 = new Range(50.0, 100.0);
        var googleRefinements = List.of(
            NavigationRefinement.rangeRefinement(googleRange1, 8),
            NavigationRefinement.rangeRefinement(googleRange3, 4)
        );
        var googleNav = Navigation.builder()
            .name(navName)
            .field("field_" + navName)
            .type(NavigationType.RANGE)
            .refinements(googleRefinements)
            .build();

        // when
        var mergedNav = Navigation.mergeNavigationRefinements(googleNav, mongoNav, List.of());

        // then
        var mergedRefinements = mergedNav.getRefinements();
        assertThat(mergedRefinements).hasSize(3);

        // Verify descending order by range low value: 100.0-200.0, 50.0-100.0, 0.0-50.0
        assertThat(mergedRefinements.getFirst().getRange().low()).isEqualTo(100.0);
        assertThat(mergedRefinements.getFirst().getRange().high()).isEqualTo(200.0);
        assertThat(mergedRefinements.getFirst().getCount()).isEqualTo(13); // 5 + 8

        assertThat(mergedRefinements.get(1).getRange().low()).isEqualTo(50.0);
        assertThat(mergedRefinements.get(1).getRange().high()).isEqualTo(100.0);

        assertThat(mergedRefinements.get(2).getRange().low()).isEqualTo(0.0);
        assertThat(mergedRefinements.get(2).getRange().high()).isEqualTo(50.0);
    }
}
