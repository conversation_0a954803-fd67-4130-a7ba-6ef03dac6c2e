package com.groupbyinc.search.ssa.core.trigger;

import com.groupbyinc.search.ssa.core.CustomParameter;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.core.navigation.NavigationType.VALUE;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("TriggerSet Tests")
class TriggerSetTest {

    private TriggerSet triggerSet;

    @BeforeEach
    void setUp() {
        triggerSet = new TriggerSet(new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
    }

    @ParameterizedTest(name = "When triggered is {0}")
    @CsvSource({ "true" })
    @DisplayName("Defaults to empty lists of triggers when not provided")
    void defaultsToEmptyListsWhenTriggersAreNotProvided(boolean expectedTriggered) {
        var searchParameters = SearchParameters
            .builder()
            .query("bacon double cheeseburger")
            .refinements(List.of(
                burgerKingSelectedRefinement()
            ))
            .customParameters(List.of(
                new CustomParameter("landing-page", "spring-burger-deals")
            ))
            .build();

        var triggers = triggerSet.trigger(searchParameters);

        assertThat(triggers).isEqualTo(expectedTriggered);
    }

    @ParameterizedTest(name = "When triggered is {0}")
    @CsvSource({ "true" })
    @DisplayName("Scenario with triggers of all types")
    void complexScenario(boolean expectedTriggered) {

        triggerSet.getQueryPatternTriggers().add(
            QueryPatternTrigger
                .builder()
                .type(QueryPatternTrigger.Type.CONTAINS)
                .value("cat,bacon")
                .build()
        );

        triggerSet.getQueryPatternTriggers().add(
            QueryPatternTrigger
                .builder()
                .type(QueryPatternTrigger.Type.CONTAINS)
                .value("dog,cheese")
                .build()
        );

        triggerSet.getSelectedRefinementTriggers().add(
            new SelectedRefinementTrigger("brands", "Burger King")
        );

        triggerSet.getSelectedRefinementTriggers().add(
            new SelectedRefinementTrigger("price", new Range(3d, 10d, null))
        );

        triggerSet.getCustomParameterTriggers().add(
            new CustomParameterTrigger("landing-page", "spring-burger-deals")
        );

        var searchParameters = SearchParameters
            .builder()
            .query("bacon double cheeseburger")
            .refinements(List.of(
                priceWithRange3_10_SelectedRefinement(),
                burgerKingSelectedRefinement(),
                dietaryRequirementsSelectedRefinement()
            ))
            .customParameters(List.of(
                new CustomParameter("landing-page", "spring-burger-deals"),
                new CustomParameter("shopper-is-cheapskate", "true")
            ))
            .build();

        var triggersOn = triggerSet.trigger(searchParameters);

        assertThat(triggersOn).isEqualTo(expectedTriggered);
    }

    @Nested
    @DisplayName("QueryPattern Trigger")
    class QueryPatternTriggerSetTest {

        @Test
        @DisplayName("Set does not trigger when query patterns do not match")
        void setDoesNotTriggerWhenQueryPatternsDoNotMatch() {
            triggerSet.getQueryPatternTriggers().add(
                QueryPatternTrigger.builder()
                    .type(QueryPatternTrigger.Type.CONTAINS)
                    .value("one,bacon,two")
                    .build()
            );

            var searchParameters = SearchParameters
                .builder()
                .query("foot long hot dog")
                .build();

            var triggersOn = triggerSet.trigger(searchParameters);

            assertThat(triggersOn).isFalse();
        }

        @Test
        @DisplayName("Set does not trigger when query is not set")
        void setDoesNotTriggerWhenQueryIsNotSet() {
            triggerSet.getQueryPatternTriggers().add(
                QueryPatternTrigger
                    .builder()
                    .type(QueryPatternTrigger.Type.CONTAINS)
                    .value("one,two,bacon")
                    .build()
            );

            var searchParameters = SearchParameters.builder()
                .build();

            var triggersOn = triggerSet.trigger(searchParameters);

            assertThat(triggersOn).isFalse();
        }

        @Test
        @DisplayName("Set triggers when the query patterns match exactly")
        void setTriggersWhenQueryPatternsMatchExactly() {
            triggerSet.getQueryPatternTriggers().add(
                QueryPatternTrigger.builder()
                    .type(QueryPatternTrigger.Type.CONTAINS)
                    .value("bacon")
                    .build()
            );
            triggerSet.getQueryPatternTriggers().add(
                QueryPatternTrigger.builder()
                    .type(QueryPatternTrigger.Type.CONTAINS)
                    .value("one,two,cheeseburger")
                    .build()
            );

            var searchParameters = SearchParameters
                .builder()
                .query("bacon double cheeseburger")
                .build();

            var triggersOn = triggerSet.trigger(searchParameters);

            assertThat(triggersOn).isTrue();
        }

        @ParameterizedTest(name = "When triggered is {0}")
        @CsvSource({ "true" })
        @DisplayName("Set triggering when query patterns match and other search parameters are present")
        void setTriggeringWhenQueryPatternsMatchAndOtherSearchParametersArePresent(boolean expectedTriggered) {

            triggerSet.getQueryPatternTriggers().add(
                QueryPatternTrigger.builder()
                    .type(QueryPatternTrigger.Type.CONTAINS)
                    .value("bacon")
                    .build()
            );

            var searchParameters = SearchParameters
                .builder()
                .query("bacon double cheeseburger")
                .refinements(List.of(
                    burgerKingSelectedRefinement()
                ))
                .customParameters(List.of(new CustomParameter("landing-page", "easter-2021")))
                .build();

            var triggersOn = triggerSet.trigger(searchParameters);

            assertThat(triggersOn).isEqualTo(expectedTriggered);
        }

    }

    @Nested
    @DisplayName("CustomParameter Trigger")
    class CustomParameterTriggerSetTest {

        @Test
        @DisplayName("Set does not trigger when custom parameters do not match")
        void setDoesNotTriggerWhenCustomParametersDoNotMatch() {

            triggerSet.getCustomParameterTriggers().add(
                new CustomParameterTrigger("gluten-free", "true")
            );

            var searchParameters = SearchParameters
                .builder()
                .customParameters(List.of(
                    new CustomParameter("vegan", "false")
                ))
                .build();

            var triggersOn = triggerSet.trigger(searchParameters);

            assertThat(triggersOn).isFalse();
        }

        @Test
        @DisplayName("Set does not trigger when custom parameters is empty")
        void setDoesNotTriggerWhenCustomParametersIsEmpty() {

            triggerSet.getCustomParameterTriggers().add(
                new CustomParameterTrigger("gluten-free", "true")
            );

            var searchParameters = SearchParameters.builder().build();

            var triggersOn = triggerSet.trigger(searchParameters);

            assertThat(triggersOn).isFalse();
        }

        @Test
        @DisplayName("Set triggers when custom parameters match exactly")
        void setTriggersWhenCustomParametersMatchExactly() {

            triggerSet.getCustomParameterTriggers().add(
                new CustomParameterTrigger("gluten-free", "true")
            );
            triggerSet.getCustomParameterTriggers().add(
                new CustomParameterTrigger("vegan", "false")
            );

            var searchParameters = SearchParameters
                .builder()
                .customParameters(List.of(
                    new CustomParameter("gluten-free", "true"),
                    new CustomParameter("vegan", "false")
                ))
                .build();

            var triggersOn = triggerSet.trigger(searchParameters);

            assertThat(triggersOn).isTrue();
        }

        @ParameterizedTest(name = "When triggered is {0}")
        @CsvSource({ "true" })
        @DisplayName("Set triggering when custom parameters partially match and other search parameters are present")
        void setTriggeringWhenCustomParametersPartiallyMatch(boolean expectedTriggered) {

            triggerSet.getCustomParameterTriggers().add(
                new CustomParameterTrigger("gluten-free", "true")
            );

            var searchParameters = SearchParameters
                .builder()
                .query("pizza")
                .customParameters(List.of(
                    new CustomParameter("gluten-free", "true"),
                    new CustomParameter("vegan", "false")
                ))
                .refinements(List.of(
                    brandSelectedRefinement()
                ))
                .build();

            var triggersOn = triggerSet.trigger(searchParameters);

            assertThat(triggersOn).isEqualTo(expectedTriggered);
        }

    }

    @Nested
    @DisplayName("SelectedRefinement Trigger")
    class SelectedRefinementTriggerSetTest {

        @Test
        @DisplayName("Set does not trigger when selected refinements do not match")
        void setDoesNotTriggerWhenSelectedRefinementsDoNotMatch() {

            triggerSet.getSelectedRefinementTriggers().add(
                new SelectedRefinementTrigger("brands", "Nike")
            );

            var searchParameters = SearchParameters
                .builder()
                .refinements(List.of(
                    colorFamiliesSelectedRefinement()
                ))
                .build();

            var triggersOn = triggerSet.trigger(searchParameters);

            assertThat(triggersOn).isFalse();
        }

        @Test
        @DisplayName("Set does not trigger when selected refinements is empty")
        void setDoesNotTriggerWhenRefinementsAreEmpty() {

            triggerSet.getSelectedRefinementTriggers().add(
                new SelectedRefinementTrigger("brands", "Nike")
            );

            var searchParameters = SearchParameters.builder().build();

            var triggersOn = triggerSet.trigger(searchParameters);

            assertThat(triggersOn).isFalse();
        }

        @Test
        @DisplayName("Set triggers when selected refinements match exactly")
        void setTriggersWhenSelectedRefinementsMatchExactly() {

            triggerSet.getSelectedRefinementTriggers().add(
                new SelectedRefinementTrigger("brands", "Nike")
            );
            triggerSet.getSelectedRefinementTriggers().add(
                new SelectedRefinementTrigger("colorFamilies", "Red")
            );

            var searchParameters = SearchParameters
                .builder()
                .refinements(List.of(
                    brandSelectedRefinement(),
                    colorFamiliesSelectedRefinement()
                ))
                .build();

            var triggersOn = triggerSet.trigger(searchParameters);

            assertThat(triggersOn).isTrue();
        }

        @ParameterizedTest(name = "When triggered is {0}")
        @CsvSource({ "true" })
        @DisplayName("Set triggering when selected refinements partially match and other search parameters are present")
        void setTriggeringWhenSelectedRefinementsPartiallyMatch(boolean expectedTriggered) {

            triggerSet.getSelectedRefinementTriggers().add(
                new SelectedRefinementTrigger("brands", "Nike")
            );

            var searchParameters = SearchParameters
                .builder()
                .query("on shoes")
                .refinements(List.of(
                    brandSelectedRefinement(),
                    colorFamiliesSelectedRefinement()
                ))
                .customParameters(List.of(
                    new CustomParameter("landing-page", "easter-2021")
                ))
                .build();

            var triggersOn = triggerSet.trigger(searchParameters);

            assertThat(triggersOn).isEqualTo(expectedTriggered);
        }

        @ParameterizedTest
        @MethodSource("exactMatchArgs")
        @DisplayName("Collection matches returns true when exact match is used and there's an exact match")
        void collectionsMatchCanPerformExactMatchingOnSearchRefinements(
            List<SelectedRefinementTrigger> selectedTriggers,
            List<SelectedRefinement> selectedRefinements
        ) {
            triggerSet.getSelectedRefinementTriggers().addAll(selectedTriggers);

            var isExactMatch = TriggerSet.collectionMatches(
                selectedRefinements,
                triggerSet.getSelectedRefinementTriggers(),
                true
            );

            assertThat(isExactMatch).isTrue();
        }

        @ParameterizedTest
        @MethodSource("nonExactMatchArgs")
        @DisplayName("Collection matches returns false when exact match is used and there's no exact match")
        void collectionsMatchExactMatchingDoesNotMatchOnNonExactSearchRefinementsMatch(
            List<SelectedRefinementTrigger> selectedTriggers,
            List<SelectedRefinement> selectedRefinements
        ) {
            triggerSet.getSelectedRefinementTriggers().addAll(selectedTriggers);

            var isExactMatch = TriggerSet.collectionMatches(
                selectedRefinements,
                triggerSet.getSelectedRefinementTriggers(),
                true
            );

            assertThat(isExactMatch).isFalse();
        }

        @ParameterizedTest
        @MethodSource("exactMatchOffArgs")
        @DisplayName("Collection matches returns true when exact match is not used and there's at least one match")
        void collectionsMatchWhenMatchExactMatchingIsDisabled(
            List<SelectedRefinementTrigger> selectedTriggers,
            List<SelectedRefinement> selectedRefinements
        ) {
            triggerSet.getSelectedRefinementTriggers().addAll(selectedTriggers);

            var isExactMatch = TriggerSet.collectionMatches(
                selectedRefinements,
                triggerSet.getSelectedRefinementTriggers(),
                false
            );

            assertThat(isExactMatch).isTrue();
        }

        // region method sources
        private static Stream<Arguments> exactMatchArgs() {
            var triggers1 = List.of(
                new SelectedRefinementTrigger("brands", "Nike"),
                new SelectedRefinementTrigger("released", new Range(10d, 40d, null))
            );
            var refinements1 = List.of(
                buildSelectedRefinement(),
                buildSelectedRefinement(new Range(10d, 40d, null))
            );
            var triggers2 = List.of(
                new SelectedRefinementTrigger("brands", "Nike")
            );
            var refinements2 = List.of(
                buildSelectedRefinement()
            );
            var triggers3 = List.of(
                new SelectedRefinementTrigger("released", new Range(10d, 40d, null))
            );
            var refinements3 = List.of(
                buildSelectedRefinement(new Range(10d, 40d, null))
            );
            var triggers4 = List.of(
                new SelectedRefinementTrigger("brands", "Nike"),
                new SelectedRefinementTrigger("released", new Range(10d, 40d, null))
            );
            var refinements4 = List.of(
                buildSelectedRefinement(new Range(10d, 40d, null)),
                buildSelectedRefinement()
            );

            return Stream.of(
                Arguments.of(triggers1, refinements1),
                Arguments.of(triggers2, refinements2),
                Arguments.of(triggers3, refinements3),
                Arguments.of(triggers4, refinements4)
            );
        }

        private static Stream<Arguments> nonExactMatchArgs() {
            var triggers1 = List.of(
                new SelectedRefinementTrigger("released", new Range(10d, 40d, null))
            );
            var refinements1 = List.of(
                buildSelectedRefinement()
            );

            var triggers2 = List.of(
                new SelectedRefinementTrigger("brands", "Nike"),
                new SelectedRefinementTrigger("released", new Range(10d, 40d, null))
            );
            var refinements2 = List.of(
                buildSelectedRefinement()
            );

            var triggers3 = List.of(
                new SelectedRefinementTrigger("released", new Range(10d, 40d, null))
            );
            var refinements3 = List.of(
                buildSelectedRefinement(new Range(10d, 39d, null))
            );

            var triggers4 = List.of(
                new SelectedRefinementTrigger("released", new Range(10d, 40d, null))
            );
            var refinements4 = List.of(
                buildSelectedRefinement(),
                buildSelectedRefinement(new Range(10d, 40d, null))
            );

            return Stream.of(
                Arguments.of(triggers1, refinements1),
                Arguments.of(triggers2, refinements2),
                Arguments.of(triggers3, refinements3),
                Arguments.of(triggers4, refinements4)
            );
        }

        private static Stream<Arguments> exactMatchOffArgs() {
            var triggers1 = List.of(
                new SelectedRefinementTrigger("brands", "Nike")
            );
            var refinements1 = List.of(
                buildSelectedRefinement()
            );

            var triggers2 = List.of(
                new SelectedRefinementTrigger("released", new Range(10d, 40d, null))
            );
            var refinements2 = List.of(
                buildSelectedRefinement(new Range(10d, 40d, null))
            );

            var triggers3 = List.of(
                new SelectedRefinementTrigger("released", new Range(10d, 40d, null))
            );
            var refinements3 = List.of(
                buildSelectedRefinement(),
                buildSelectedRefinement(new Range(10d, 40d, null))
            );

            return Stream.of(
                Arguments.of(triggers1, refinements1),
                Arguments.of(triggers2, refinements2),
                Arguments.of(triggers3, refinements3)
            );
        }
        // endregion method sources

        private static SelectedRefinement buildSelectedRefinement() {
            var sr = new SelectedRefinement();
            sr.setField("brands");
            sr.setValue("Nike");
            sr.setType(NavigationType.VALUE);
            return sr;
        }

        private static SelectedRefinement buildSelectedRefinement(Range range) {
            var sr = new SelectedRefinement();
            sr.setField("released");
            sr.setRange(range);
            sr.setType(NavigationType.RANGE);
            return sr;
        }
    }

    public static SelectedRefinement brandSelectedRefinement() {
        return new SelectedRefinement(
            "brands",
            VALUE,
            null,
            "Nike",
            false,
            false
        );
    }

    public static SelectedRefinement colorFamiliesSelectedRefinement() {
        return new SelectedRefinement(
            "colorFamilies",
            NavigationType.VALUE,
            null,
            "Red",
            false,
            false
        );
    }

    public static SelectedRefinement burgerKingSelectedRefinement() {
        return new SelectedRefinement(
            "brands",
            NavigationType.VALUE,
            null,
            "Burger King",
            false,
            false);
    }

    public static SelectedRefinement dietaryRequirementsSelectedRefinement() {
        return new SelectedRefinement(
            "dietaryRequirements",
            NavigationType.VALUE,
            null,
            "Carnivorous",
            false,
            false
        );
    }

    public static SelectedRefinement priceWithRange3_10_SelectedRefinement() {
        return new SelectedRefinement(
            "price",
            NavigationType.RANGE,
            new Range(3d, 10d, null),
            null,
            false,
            false
        );
    }

}
