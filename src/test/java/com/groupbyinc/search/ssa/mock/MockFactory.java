package com.groupbyinc.search.ssa.mock;

import com.google.api.core.ApiService;
import com.google.cloud.pubsub.v1.Subscriber;

import com.groupbyinc.search.ssa.application.cache.CacheOperations;
import com.groupbyinc.search.ssa.commandcenter.CommandCenterConfigurationClient;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.metrics.opentelemetry.MeterRegistry;
import com.groupbyinc.search.ssa.redis.RedisCache;
import com.groupbyinc.search.ssa.stub.StubCommandCenterConfigurationClient;
import com.groupbyinc.search.ssa.stub.StubMeterRegistry;
import com.groupbyinc.utils.pubsub.client.PubsubClient;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Primary;
import io.micronaut.context.annotation.Replaces;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.env.Environment;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.metrics.Meter;
import jakarta.inject.Named;
import jakarta.inject.Singleton;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;

@Factory
@Requires(env = Environment.TEST)
public class MockFactory {

    @Primary
    @Context
    @Named("search")
    public RedisCache search() {
        return mock(RedisCache.class);
    }

    @Primary
    @Context
    @Named("browse")
    public RedisCache browse() {
        return mock(RedisCache.class);
    }

    @Primary
    @Context
    @Named("mongo")
    public RedisCache mongo() {
        return mock(RedisCache.class);
    }

    @Primary
    @Context
    public FeaturesManager featuresManager() {
        return mock(FeaturesManager.class);
    }

    @Primary
    @Context
    public CacheOperations baseCacheOperations() {
        return mock(CacheOperations.class);
    }

    @Primary
    @Singleton
    public CommandCenterConfigurationClient allConfigurations() {
        return new StubCommandCenterConfigurationClient();
    }

    @Primary
    @Singleton
    PubsubClient pubsubClient() {
        var pubsubClient = mock(PubsubClient.class);
        var subscriber = mock(Subscriber.class);
        given(pubsubClient.newSubscriber(any(), any(), anyInt())).willReturn(subscriber);
        given(subscriber.stopAsync()).willReturn(mock(ApiService.class));
        return pubsubClient;
    }

    @Context
    @Primary
    @Replaces(Meter.class)
    public Meter meter() {
        return mock(Meter.class);
    }

    @Context
    @Primary
    @Replaces(OpenTelemetry.class)
    public OpenTelemetry openTelemetry() {
        return mock(OpenTelemetry.class);
    }

    @Context
    @Primary
    @Replaces(MeterRegistry.class)
    public MeterRegistry meterRegistry() {
        return new StubMeterRegistry();
    }

}
