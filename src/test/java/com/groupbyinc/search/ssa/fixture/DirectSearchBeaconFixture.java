package com.groupbyinc.search.ssa.fixture;

import com.github.tomakehurst.wiremock.WireMockServer;

import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.ok;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.options;
import static io.micronaut.http.HttpHeaders.AUTHORIZATION;

public class DirectSearchBeaconFixture {

    public static final String AUTH_TOKEN = "ded78c0c-867c-4972-8cd3-c804d18640a3";
    public static final String BEACON_PATH = "/wisdom/v2/internal/directSearch";

    /** WireMock server to mock the beacon API. */
    private final WireMockServer beaconApi;

    /**
     * Creates a new instance, backed by a WireMock server that is already started.
     */
    public DirectSearchBeaconFixture() {
        this.beaconApi = new WireMockServer(options().dynamicPort());
        this.beaconApi.start();
    }

    public void stubSuccess() {
        beaconApi.stubFor(
            post(urlPathEqualTo(BEACON_PATH))
                .withHeader(AUTHORIZATION, equalTo(AUTH_TOKEN))
                .willReturn(ok())
        );
    }

    /**
     * Stop the WireMock backing server.
     */
    public void stop() {
        beaconApi.stop();
    }

    /**
     * Get the base url of the WireMock backing server.
     *
     * @return Base url.
     */
    public String getBaseUrl() {
        return beaconApi.baseUrl();
    }

    public void assertDirectSearchBeaconRequestWasSent(String expectedJson) {
        beaconApi.verify(
            postRequestedFor(urlPathEqualTo(BEACON_PATH))
                .withRequestBody(equalToJson(expectedJson))
        );
    }
}
