package com.groupbyinc.search.ssa.fixture;

import com.groupbyinc.search.ssa.core.authz.Identity;
import com.groupbyinc.search.ssa.core.authz.Role;
import com.groupbyinc.search.ssa.stub.ResourceUtils;

import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import io.micronaut.security.authentication.Authentication;
import io.micronaut.security.token.jwt.validator.AuthenticationJWTClaimsSetAdapter;
import lombok.Getter;

import java.sql.Date;
import java.time.Instant;
import java.util.Set;

@Getter
public class AuthFixture {

    public static final String JWKS_PATH = "/.well-known/jwks.json";
    public static final String JWKS_RESPONSE = ResourceUtils.getResourceContents("jwks/jwks.json");

    private final Identity identity;
    private final JWTClaimsSet claimsSet;
    private final String token;
    private final Authentication internalHttpRequestPrincipalAttribute;

    public AuthFixture(String subject, String company, Set<Role> roles) {

        var issueTime = Instant.now();
        var expirationTime = issueTime.plusSeconds(30);

        this.identity = new Identity(subject, company, roles);

        var rolesClaim = roles.stream().map(Role::getName).toList();
        this.claimsSet = new JWTClaimsSet.Builder()
            .issueTime(Date.from(issueTime))
            .expirationTime(Date.from(expirationTime))
            .subject(subject)
            .claim("company", company)
            .claim("roles", rolesClaim)
            .build();

        this.token = buildAuthenticationToken(claimsSet);

        this.internalHttpRequestPrincipalAttribute = new AuthenticationJWTClaimsSetAdapter(claimsSet);
    }

    public static AuthFixture merchandiserDefaultRole(String merchandiserId) {
        return new AuthFixture(merchandiserId, merchandiserId, Set.of(Role.CUSTOMER_DEFAULT));
    }

    /**
     * Builds and signs a JWT based on a claims set.
     *
     * @param claimsSet Claims set on the signed JWT.
     *
     * @return Serialized form of the JWS.
     */
    private static String buildAuthenticationToken(JWTClaimsSet claimsSet) {

        try {

            var signingKey = RSAKey.parse(ResourceUtils.getResourceContents("jwks/rsa-key.json"));
            var jwsHeader = new JWSHeader.Builder(JWSAlgorithm.RS256)
                .keyID(signingKey.getKeyID())
                .build();

            var jwt = new SignedJWT(jwsHeader, claimsSet);
            jwt.sign(new RSASSASigner(signingKey));

            return jwt.serialize();

        } catch (Exception e) {
            throw new RuntimeException("Failed to parse rsa-key.json", e);
        }
    }
}
