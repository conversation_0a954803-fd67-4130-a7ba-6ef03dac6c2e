package com.groupbyinc.search.ssa.util;

import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static com.groupbyinc.proto.commandcenter.config.AttributeMessage.AttributeType.NUMERICAL;
import static com.groupbyinc.proto.commandcenter.config.AttributeMessage.AttributeType.TEXTUAL;
import static com.groupbyinc.search.ssa.mongo.filtering.DataForFilterTests.defaultAttributeConfigs;
import static com.groupbyinc.search.ssa.util.AttributeUtils.parseInventoryNavigations;
import static com.groupbyinc.search.ssa.util.AttributeUtils.transformField;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class AttributeUtilsTest {

    @Test
    void testTransformField() {
        assertTransform("price", "priceInfo.price");
        assertTransform("originalPrice", "priceInfo.originalPrice");
        assertTransform("currencyCode", "priceInfo.currencyCode");
        assertTransform("cost", "priceInfo.cost");
        assertTransform("colorFamilies", "colorInfo.colorFamilies");
        assertTransform("colors", "colorInfo.colors");
        assertTransform("rating", "rating.averageRating");
        assertTransform("averageRating", "rating.averageRating");
        assertTransform("ratingCount", "rating.ratingCount");
        assertTransform("ratingHistogram", "rating.ratingHistogram");
    }

    private void assertTransform(String given, String expected) {
        assertEquals(expected, transformField(given, defaultAttributeConfigs(), false));
    }

    @Test
    void testParseInventoryNavigations() {
        assertFalse(parseInventoryNavigations("").isPresent());
        assertFalse(parseInventoryNavigations(" ").isPresent());
        assertFalse(parseInventoryNavigations(null).isPresent());

        assertFalse(parseInventoryNavigations("price").isPresent());
        assertFalse(parseInventoryNavigations("attributes.price").isPresent());

        assertTrue(parseInventoryNavigations("inventory(placeId, price)").isPresent());
        assertTrue(parseInventoryNavigations("inventory(placeId,price)").isPresent());
        assertTrue(parseInventoryNavigations("inventory(placeId, attributes.price)").isPresent());
        assertTrue(parseInventoryNavigations("inventory(placeId,attributes.price)").isPresent());

        assertTrue(parseInventoryNavigations("placeId.inventories.price").isPresent());
        assertTrue(parseInventoryNavigations("placeId.inventories.attributes.price").isPresent());
    }

    @ParameterizedTest
    @MethodSource("getAttributeValueFromRecordScenarios")
    void testGetAttributeValueFromRecord(Record record,
                                         AttributeConfiguration attribute,
                                         List<Object> expect) {
        Comparator<Object> comparator = (o1, o2) -> {
            if (o1 instanceof Number n1 && o2 instanceof Number n2) {
                return Double.compare(n1.doubleValue(), n2.doubleValue());
            }
            return o1.toString().compareTo(o2.toString());
        };

        // when
        var result = AttributeUtils.getAttributeValueFromRecord(record, attribute);

        // then
        assertNotNull(result);
        assertEquals(expect.size(), result.size());

        expect = new ArrayList<>(expect);
        result = new ArrayList<>(result);
        expect.sort(comparator);
        result.sort(comparator);

        for (int i = 0; i < expect.size(); i++) {
            assertEquals(expect.get(i), result.get(i));
        }
    }

    private static Stream<Arguments> getAttributeValueFromRecordScenarios() {
        return Stream.of(
            Arguments.argumentSet(
                "Root - string - simple, with variants",
                record(Map.of(
                        "title", "t1",
                        "variants", List.of(Map.of("title", "t1v1"), Map.of("title", "t1v2"))
                    )
                ),
                AttributeConfiguration.builder()
                    .key("title")
                    .path("title")
                    .type(TEXTUAL)
                    .build(),
                List.of("t1", "t1v1", "t1v2")
            ),
            Arguments.argumentSet(
                "Root - string - wrapped, with variants",
                record(Map.of(
                        "title", wrap("t1"),
                        "variants", List.of(Map.of("title", wrap("t1v1")), Map.of("title", wrap("t1v2")))
                    )
                ),
                AttributeConfiguration.builder()
                    .key("title")
                    .path("title")
                    .type(TEXTUAL)
                    .build(),
                List.of("t1", "t1v1", "t1v2")
            ),
            Arguments.argumentSet(
                "Root - number - simple, with variants",
                record(Map.of(
                        "priceInfo", Map.of("price", 1.99),
                        "variants", List.of(
                            Map.of("priceInfo", Map.of("price", 2.99)),
                            Map.of("priceInfo", Map.of("price", 3.99))
                        )
                    )
                ),
                AttributeConfiguration.builder()
                    .key("price")
                    .path("priceInfo.price")
                    .type(NUMERICAL)
                    .build(),
                List.of(1.99, 2.99, 3.99)
            ),
            Arguments.argumentSet(
                "Root - number - wrapped, with variants",
                record(Map.of(
                        "priceInfo", Map.of("price", wrap(1.99)),
                        "variants", List.of(
                            Map.of("priceInfo", Map.of("price", wrap(2.99))),
                            Map.of("priceInfo", Map.of("price", wrap(3.99)))
                        )
                    )
                ),
                AttributeConfiguration.builder()
                    .key("price")
                    .path("priceInfo.price")
                    .type(NUMERICAL)
                    .build(),
                List.of(1.99, 2.99, 3.99)
            ),
            Arguments.argumentSet(
                "Attributes - string - simple, with variants",
                record(Map.of(
                        "attributes", Map.of("customStr", "t1"),
                        "variants", List.of(
                            Map.of("attributes", Map.of("customStr", "t1v1")),
                            Map.of("attributes", Map.of("customStr", "t1v2"))
                        )
                    )
                ),
                AttributeConfiguration.builder()
                    .key("attributes.customStr")
                    .path("attributes.customStr")
                    .type(TEXTUAL)
                    .build(),
                List.of("t1", "t1v1", "t1v2")
            ),
            Arguments.argumentSet(
                "Attributes - string - wrapped, with variants",
                record(Map.of(
                        "attributes", Map.of("customStr", wrap("t1")),
                        "variants", List.of(
                            Map.of("attributes", Map.of("customStr", wrap("t1v1"))),
                            Map.of("attributes", Map.of("customStr", wrap("t1v2")))
                        )
                    )
                ),
                AttributeConfiguration.builder()
                    .key("attributes.customStr")
                    .path("attributes.customStr")
                    .type(TEXTUAL)
                    .build(),
                List.of("t1", "t1v1", "t1v2")
            ),
            Arguments.argumentSet(
                "Attributes - number - simple, with variants",
                record(Map.of(
                        "attributes", Map.of("customNum", 5.5),
                        "variants", List.of(
                            Map.of("attributes", Map.of("customNum", 6.6)),
                            Map.of("attributes", Map.of("customNum", 7.7))
                        )
                    )
                ),
                AttributeConfiguration.builder()
                    .key("attributes.customNum")
                    .path("attributes.customNum")
                    .type(NUMERICAL)
                    .build(),
                List.of(5.5, 6.6, 7.7)
            ),
            Arguments.argumentSet(
                "Attributes - number - wrapped, with variants",
                record(Map.of(
                        "attributes", Map.of("customNum", wrap(5.5)),
                        "variants", List.of(
                            Map.of("attributes", Map.of("customNum", wrap(6.6))),
                            Map.of("attributes", Map.of("customNum", wrap(7.7)))
                        )
                    )
                ),
                AttributeConfiguration.builder()
                    .key("attributes.customNum")
                    .path("attributes.customNum")
                    .type(NUMERICAL)
                    .build(),
                List.of(5.5, 6.6, 7.7)
            ),
            Arguments.argumentSet(
                "Root - type mismatch - expected number, contains string",
                record(Map.of("attr", "t1")),
                AttributeConfiguration.builder()
                    .key("attr")
                    .path("attr")
                    .type(NUMERICAL)
                    .build(),
                List.of()
            ),
            Arguments.argumentSet(
                "Root - type mismatch - expected string, contains number",
                record(Map.of("attr", 1)),
                AttributeConfiguration.builder()
                    .key("attr")
                    .path("attr")
                    .type(TEXTUAL)
                    .build(),
                List.of()
            ),
            Arguments.argumentSet(
                "Attributes - type mismatch - expected number, contains string",
                record(Map.of("attributes", Map.of("attr", 5.5))),
                AttributeConfiguration.builder()
                    .key("attr")
                    .path("attr")
                    .type(NUMERICAL)
                    .build(),
                List.of()
            ),
            Arguments.argumentSet(
                "Attributes - type mismatch - expected string, contains number",
                record(Map.of("attributes", Map.of("attr", 5.5))),
                AttributeConfiguration.builder()
                    .key("attr")
                    .path("attr")
                    .type(TEXTUAL)
                    .build(),
                List.of()
            )
        );
    }

    private static Map<String, Object> wrap(Object v) {
        if (v instanceof String) {
            return Map.of(Constants.TEXT, List.of(v));
        } else {
            return Map.of(Constants.NUMBERS, List.of(v));
        }
    }

    private static Record record(Map<String, Object> meta) {
        return new Record(
            "001",
            "001",
            "001",
            "http://example.com",
            "title",
            "zapparel",
            meta,
            null,
            null,
            null,
            null
        );
    }

}
