package com.groupbyinc.search.ssa.util;

import org.junit.jupiter.api.Test;

import java.security.NoSuchAlgorithmException;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class StringUtilsTest {

    @Test
    void regex() {
        assertTrue(StringUtils.regex("covida", Pattern.compile("covid")));
        assertTrue(StringUtils.regex("AcovidA", Pattern.compile("covid")));
        assertFalse(StringUtils.regex("covi", Pattern.compile("covid")));
    }

    @Test
    void hash() throws NoSuchAlgorithmException {
        var salt = "f9c977ea-b423-4b3e-9952-d38cec720b70";

        assertEquals(
            "BXN/9VgANHIAQDHwcGprjr1ZLiA0fCg4O4ErMNswF0M=",
            StringUtils.getHash(salt, "123123")
        );

        assertEquals(
            "+8kb3B7NiVy6uwYS829gEObWgmDaoLjraxhOVyRc7l4=",
            StringUtils.getHash(salt, "abc")
        );

        assertEquals(
            "+N3wBLMCGL97Q8Hx00qXdRTXvXTf51vh3+c2rK7vnH4=",
            StringUtils.getHash(salt, "abc123")
        );

        assertEquals(
            "absk3BQ/1OTY3pZkTp+GDGcBKhgAf2NprUBqX3qG05o=",
            StringUtils.getHash(salt, "abc123$%^")
        );
    }

}
