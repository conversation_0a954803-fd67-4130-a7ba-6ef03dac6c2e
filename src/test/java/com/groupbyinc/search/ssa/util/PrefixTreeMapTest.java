package com.groupbyinc.search.ssa.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONObject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@DisplayName("Prefix tree Tests")
@SuppressWarnings("unchecked")
public class PrefixTreeMapTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    private static final String RESPONSE_STRING = """
        {
            "id": "123",
            "name": "Test name",
            "categories": [
                [
                    {
                        "first": "A",
                        "second": "B"
                    },
                    {
                        "first": "C",
                        "second": "E"
                    }
                ],[
                    {
                        "first": "F"
                    }
                ]
            ],
            "attributes": {
                "DISCOUNT_d": {
                    "numbers": [0],
                    "searchable": false,
                    "indexable": false
                },
                "PRODUCT_ID": {
                    "text": ["5572639"],
                    "searchable": true,
                    "indexable": true
                }
            },
            "priceInfo": {
                "price": 99.99
            },
            "variants": [
                {
                    "name": "Name",
                    "attributes": {
                        "SHIPPING": {
                            "numbers": [0],
                            "indexable": false
                        }
                    },
                    "colorInfo": {
                      "colorFamilies": ["Blue"],
                      "colors": ["BLUE"]
                    }
                },{
                    "name": "Name_2",
                    "attributes": {
                        "SHIPPING": {
                            "numbers": [1],
                            "indexable": true
                        }
                    },
                    "colorInfo": {
                        "colorFamilies": ["Red"],
                        "colors": ["RED"]
                    }
                }
            ]
        }
        """;

    @Test
    void createTreeTest() {
        List<String> list = List.of(
            "key.subKey.subSubKey",
            "key.subKey.subSubKey_2",
            "key_1.subKey",
            "key_2"
        );

        var map = PrefixTreeMap.fromList(list);

        assertEquals(5, map.size());

        assertTrue(map.containsKey("key"));
        assertTrue(map.containsKey("key_1"));
        assertTrue(map.containsKey("key_2"));
        assertTrue(map.containsKey("id"));
        assertTrue(map.containsKey("variants"));

        assertTrue(map.get("key").getChildren().containsKey("subKey"));
        assertTrue(map.get("key").getChildren().get("subKey").getChildren().containsKey("subSubKey"));
        assertTrue(map.get("key").getChildren().get("subKey").getChildren().containsKey("subSubKey_2"));

        assertTrue(map.get("key_2").getChildren().isEmpty());
        assertTrue(map.get("key_1").getChildren().containsKey("subKey"));

        assertTrue(map.get("variants").getChildren().containsKey("id"));
    }

    @Test
    void createTreeAllAllowedTest() {
        List<String> list = List.of("*");

        var map = PrefixTreeMap.fromList(list);

        assertEquals(0, map.size());
    }

    @Test
    void createTreeAllAllowedWithExtraTest() {
        List<String> list = List.of("*", "key", "key.value");

        var map = PrefixTreeMap.fromList(list);

        assertEquals(0, map.size());
    }

    @Test
    void cleanUpAllAllowed() throws JsonProcessingException {
        List<String> list = List.of("*");
        var response = objectMapper.readValue(
            "{\"priceInfo\":{\"price\":1.99}}",
            new TypeReference<HashMap<String, Object>>() {}
        );

        var map = PrefixTreeMap.fromList(list);

        var clean = map.cleanUpMapBaseOnPrefixTree(response);

        assertEquals(1, response.size());
        assertEquals(1, clean.size());
        assertEquals(1.99, ((Map<String, Double>) clean.get("priceInfo")).get("price"));
    }

    @Test
    void cleanUpNotExistingKey() throws JsonProcessingException {
        List<String> list = List.of("key");
        var response = objectMapper.readValue(
            "{\"priceInfo\":{\"price\":1.99}}",
            new TypeReference<HashMap<String, Object>>() {}
        );

        var map = PrefixTreeMap.fromList(list);

        var clean = map.cleanUpMapBaseOnPrefixTree(response);

        assertTrue(clean.isEmpty());
    }

    @Test
    void cleanUpMultiplyTopLevelKeys() throws JsonProcessingException {
        List<String> list = List.of("id", "name");
        var response = objectMapper.readValue(
            RESPONSE_STRING,
            new TypeReference<HashMap<String, Object>>() {}
        );

        var map = PrefixTreeMap.fromList(list);

        var clean = map.cleanUpMapBaseOnPrefixTree(response);

        assertEquals(3, clean.size());
        assertTrue(map.containsKey("id"));
        assertTrue(map.containsKey("name"));
        assertTrue(map.containsKey("variants"));
        assertTrue(map.get("variants").getChildren().containsKey("id"));

        assertEquals("123", clean.get("id"));
        assertEquals("Test name", clean.get("name"));
    }

    @Test
    void cleanUpObjectReturnedByItsKey() throws JsonProcessingException {
        List<String> list = List.of("priceInfo");
        var response = objectMapper.readValue(
            RESPONSE_STRING,
            new TypeReference<HashMap<String, Object>>() {}
        );

        var map = PrefixTreeMap.fromList(list);

        var clean = map.cleanUpMapBaseOnPrefixTree(response);

        assertEquals(3, clean.size());
        assertEquals(99.99, ((Map<String, Double>) clean.get("priceInfo")).get("price"));
    }

    @Test
    void cleanUpInnerKeys() throws JsonProcessingException {
        List<String> list = List.of("attributes.DISCOUNT_d.searchable", "attributes.PRODUCT_ID.text");
        var response = objectMapper.readValue(
            RESPONSE_STRING,
            new TypeReference<HashMap<String, Object>>() {}
        );

        var map = PrefixTreeMap.fromList(list);

        var clean = map.cleanUpMapBaseOnPrefixTree(response);

        assertEquals(3, clean.size());
        assertTrue(map.containsKey("attributes"));
        assertTrue(map.containsKey("id"));
        assertTrue(map.containsKey("variants"));
        assertTrue(map.get("variants").getChildren().containsKey("id"));
        var attributes = (Map<String, Object>) clean.get("attributes");

        assertEquals(2, attributes.size());
        assertTrue(attributes.containsKey("DISCOUNT_d"));
        assertTrue(attributes.containsKey("PRODUCT_ID"));

        var discount = (Map<String, Object>) attributes.get("DISCOUNT_d");
        assertEquals(1, discount.size());
        assertFalse((Boolean) discount.get("searchable"));

        var product = (Map<String, Object>) attributes.get("PRODUCT_ID");
        assertEquals(1, product.size());
        assertEquals(1, ((List<String>) product.get("text")).size());
        assertEquals("5572639", ((List<String>) product.get("text")).getFirst());
    }

    @Test
    void cleanUpArrayObjects() throws JsonProcessingException {
        List<String> list = List.of("categories.first");
        var response = objectMapper.readValue(
            RESPONSE_STRING,
            new TypeReference<HashMap<String, Object>>() {}
        );

        var map = PrefixTreeMap.fromList(list);

        var clean = map.cleanUpMapBaseOnPrefixTree(response);

        assertEquals(3, clean.size());
        assertTrue(clean.containsKey("categories"));
        assertTrue(clean.containsKey("id"));
        assertTrue(clean.containsKey("variants"));
        assertTrue(map.get("variants").getChildren().containsKey("id"));
        var categories = (List<Object>) clean.get("categories");
        assertEquals(2, categories.size());

        var first = (List<Object>) categories.getFirst();
        assertEquals(2, first.size());
        var second = (List<Object>) categories.get(1);
        assertEquals(1, second.size());

        first.forEach(o -> {
            var m =  (Map<String, Object>) o;
            assertEquals(1, m.size());
            assertTrue(m.containsKey("first"));
        });

        second.forEach(o -> {
            var m =  (Map<String, Object>) o;
            assertEquals(1, m.size());
            assertTrue(m.containsKey("first"));
        });
    }

    @Test
    void cleanUpArrayObjectsPartial() throws JsonProcessingException {
        List<String> list = List.of("categories.second");
        var response = objectMapper.readValue(
            RESPONSE_STRING,
            new TypeReference<HashMap<String, Object>>() {}
        );

        var map = PrefixTreeMap.fromList(list);

        var clean = map.cleanUpMapBaseOnPrefixTree(response);

        assertEquals(3, clean.size());
        assertTrue(clean.containsKey("categories"));
        assertTrue(clean.containsKey("id"));
        assertTrue(clean.containsKey("variants"));
        assertTrue(map.get("variants").getChildren().containsKey("id"));
        var categories = (List<Object>) clean.get("categories");
        assertEquals(1, categories.size());

        var first = (List<Object>) categories.getFirst();
        assertEquals(2, first.size());

        first.forEach(o -> {
            var m =  (Map<String, Object>) o;
            assertEquals(1, m.size());
            assertTrue(m.containsKey("second"));
        });
    }

    @Test
    void cleanUpMixed() throws JsonProcessingException {
        List<String> list = List.of(
            "id",
            "priceInfo",
            "variants.name",
            "categories.second",
            "attributes.PRODUCT_ID.text",
            "variants.colorInfo.colorFamilies",
            "variants.attributes.SHIPPING.numbers"
        );

        var response = objectMapper.readValue(
            RESPONSE_STRING,
            new TypeReference<HashMap<String, Object>>() {}
        );

        var map = PrefixTreeMap.fromList(list);

        var clean = map.cleanUpMapBaseOnPrefixTree(response);

        var expected = """
        {
            "id": "123",
            "categories": [
                [
                    {
                        "second": "B"
                    },
                    {
                        "second": "E"
                    }
                ]
            ],
            "attributes": {
                "PRODUCT_ID": {
                    "text": ["5572639"]
                }
            },
            "priceInfo": {
                "price": 99.99
            },
            "variants": [
                {
                    "name": "Name",
                    "attributes": {
                        "SHIPPING": {
                            "numbers": [0]
                        }
                    },
                    "colorInfo": {
                      "colorFamilies": ["Blue"]
                    }
                },{
                    "name": "Name_2",
                    "attributes": {
                        "SHIPPING": {
                            "numbers": [1]
                        }
                    },
                    "colorInfo": {
                        "colorFamilies": ["Red"]
                    }
                }
            ]
        }
        """;

        var expectedMap = objectMapper.readValue(
            expected,
            new TypeReference<HashMap<String, Object>>() {}
        );

        var expectedJson = new JSONObject(expectedMap).toMap();
        var json = new JSONObject(clean).toMap();

        assertEquals(expectedJson, json);
    }

}
