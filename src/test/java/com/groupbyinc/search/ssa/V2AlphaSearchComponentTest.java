package com.groupbyinc.search.ssa;

import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SearchResponseDto;
import com.groupbyinc.search.ssa.api.dto.crm.UserAttributeDto;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.fixture.AuthFixture;
import com.groupbyinc.search.ssa.stub.ResourceUtils;

import com.google.cloud.retail.v2alpha.SearchResponse;
import com.google.cloud.retail.v2alpha.SearchServiceClient;
import com.google.protobuf.util.JsonFormat;
import io.micronaut.context.ApplicationContext;
import io.micronaut.core.type.Argument;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.HttpStatus;
import io.micronaut.http.client.exceptions.HttpClientResponseException;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static com.groupbyinc.search.ssa.SearchComponentTest.IGNORING_FIELDS;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_CRM_PERSONALIZATION;
import static com.groupbyinc.search.ssa.stub.TestData.APPAREL;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTION;
import static com.groupbyinc.search.ssa.stub.TestData.PRODUCTS_CLOTHING;
import static com.groupbyinc.search.ssa.util.Constants.GROUPBY_CUSTOMER_ID_HEADER;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.awaitility.Durations.FIVE_HUNDRED_MILLISECONDS;
import static org.awaitility.Durations.FIVE_SECONDS;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;

@MicronautTest
@DisplayName("V2Alpha SearchComponent Tests")
class V2AlphaSearchComponentTest extends ComponentTest {

    private SearchServiceClient v2AlphaSearchServiceClient;
    private SearchServiceClient.SearchPage searchPage;
    private FeaturesManager featuresManager;

    @BeforeAll
    void waitForServerToBecomeReady() {
        await()
            .atMost(FIVE_SECONDS)
            .pollInterval(FIVE_HUNDRED_MILLISECONDS)
            .ignoreException(HttpClientResponseException.class)
            .untilAsserted(() -> {
                var httpResponse = httpClient.toBlocking().exchange("/health/readiness");
                assertThat(httpResponse)
                    .extracting(HttpResponse::getStatus)
                    .isEqualTo(HttpStatus.OK);
            });
    }

    @BeforeEach
    void setUp() {
        searchPage = mock(SearchServiceClient.SearchPage.class);
        var searchPagedResponse = mock(SearchServiceClient.SearchPagedResponse.class);
        given(searchPagedResponse.getPage()).willReturn(searchPage);
        given(v2AlphaSearchServiceClient.search(any())).willReturn(searchPagedResponse);
        given(featuresManager.getBooleanFlagConfiguration(any(), eq(ENABLE_CRM_PERSONALIZATION))).willReturn(true);
    }

    @Override
    protected void registerSingletons(ApplicationContext appContext) {
        v2AlphaSearchServiceClient = mock(SearchServiceClient.class);
        featuresManager = mock(FeaturesManager.class);
        appContext.registerSingleton(v2AlphaSearchServiceClient);
        appContext.registerSingleton(featuresManager);
    }

    @Test
    @DisplayName("userAttributes validations")
    void userAttributesValidations() throws Exception {
        var authFixture = AuthFixture.merchandiserDefaultRole(APPAREL);
        var searchResponseBuilder = SearchResponse.newBuilder();
        JsonFormat.parser().merge(
            ResourceUtils.getResourceContents("retail/retail-response-no-products.json"),
            searchResponseBuilder
        );
        given(searchPage.getResponse()).willReturn(searchResponseBuilder.build());

        SearchRequestDto request = SearchRequestDto.builder()
            .query("dress")
            .area(PRODUCTION)
            .collection(PRODUCTS_CLOTHING)
            .build();
        List<UserAttributeDto> userAttributesRequest = List.of(
            new UserAttributeDto("Interests", List.of("electronics", "books")),
            new UserAttributeDto("Interests", List.of("electronics", "books1", "")),
            new UserAttributeDto("User_segments", List.of("premium_user", "logged_in"))
        );
        request.setUserAttributes(userAttributesRequest);

        var httpResponse = httpClient.toBlocking().exchange(
            HttpRequest.POST(
                    "/api/search", request)
                .bearerAuth(authFixture.getToken())
                .header(GROUPBY_CUSTOMER_ID_HEADER, APPAREL),
            Argument.of(SearchResponseDto.class));

        assertThat((Object) httpResponse.getStatus()).isEqualTo(HttpStatus.OK);

        var expectedSearchResponseDto = objectMapper.readValue(
            ResourceUtils.getResourceContents("retail/api-userAttributes-response.json"),
            SearchResponseDto.class
        );
        assertThat(httpResponse.body())
            .isNotNull()
            .usingRecursiveComparison()
            .ignoringAllOverriddenEquals()
            .ignoringFields(IGNORING_FIELDS)
            .isEqualTo(expectedSearchResponseDto);

        List<UserAttributeDto> actual = httpResponse.body().getOriginalRequest().getUserAttributes();
        List<UserAttributeDto> expected = expectedSearchResponseDto.getOriginalRequest().getUserAttributes();
        assertThat(actual.size()).isEqualTo(expected.size());
    }
}
