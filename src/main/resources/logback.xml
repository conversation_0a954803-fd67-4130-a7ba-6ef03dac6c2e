<configuration>
    <variable name="APP_LOG_LEVEL" value="${APP_LOG_LEVEL:-INFO}" />

    <!-- Silence non-ERROR logs from all packages by default -->
    <logger name="io" level="error"/>
    <logger name="com" level="error"/>
    <logger name="org" level="error"/>
    <logger name="reactor" level="error"/>
    <logger name="ch" level="error"/>

    <!-- Expose application startup time log message from Micronaut -->
    <logger name="io.micronaut.runtime.Micronaut" level="info"/>

    <!-- Expose migration logs from Flyway -->
    <logger name="org.flywaydb.core" level="info"/>

    <!-- Our own classes -->
    <logger name="com.groupbyinc" level="${APP_LOG_LEVEL}"/>

    <appender name="CONSOLE_JSON" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="com.groupbyinc.search.ssa.application.logging.LogSamplingFilter"/>

        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="io.micronaut.gcp.logging.StackdriverJsonLayout">
                <projectId>${projectId}</projectId>
                <includeTraceId>true</includeTraceId>
                <includeSpanId>true</includeSpanId>
                <includeLevel>true</includeLevel>
                <includeThreadName>false</includeThreadName>
                <includeMDC>true</includeMDC>
                <includeLoggerName>true</includeLoggerName>
                <includeFormattedMessage>true</includeFormattedMessage>
                <includeExceptionInMessage>true</includeExceptionInMessage>
                <includeContextName>true</includeContextName>
                <includeMessage>false</includeMessage>
                <includeException>false</includeException>
            </layout>
        </encoder>
    </appender>

    <root level="debug">
        <appender-ref ref="CONSOLE_JSON"/>
    </root>
</configuration>
