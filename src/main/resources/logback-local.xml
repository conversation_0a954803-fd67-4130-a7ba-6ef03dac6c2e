<configuration>

    <!-- Silence non-ERROR logs from all packages by default -->
    <logger name="io" level="error"/>
    <logger name="com" level="error"/>
    <logger name="org" level="error"/>
    <logger name="reactor" level="error"/>
    <logger name="ch" level="error"/>

    <!-- Expose application startup time log message from Micronaut -->
    <logger name="io.micronaut.runtime.Micronaut" level="info"/>

    <!-- Expose migration logs from Flyway -->
    <logger name="org.flywaydb" level="info"/>

    <!-- Our own classes -->
    <logger name="com.groupbyinc" level="debug"/>
    <logger name="com.groupbyinc.search.sscs.common" level="info"/>

    <!-- Configure console logging (standard output) -->
    <appender name="STANDARD_CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="com.groupbyinc.search.ssa.application.logging.LogSamplingFilter"/>
        <encoder>
            <pattern>
                %cyan(%d{HH:mm:ss.SSS}) %green([%thread]) %highlight(%-5level) %magenta(%logger{36}) %cyan([%.12X{requestId}]) %cyan([%X{customer}]) %cyan([%X{area}]) %cyan([%X{collection}]) %msg%n
            </pattern>
        </encoder>
    </appender>

    <root level="debug">
        <appender-ref ref="STANDARD_CONSOLE"/>
    </root>

</configuration>
