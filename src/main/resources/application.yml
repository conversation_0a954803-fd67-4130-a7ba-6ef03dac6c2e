micronaut:
  application:
    name: site-search-api
  netty:
    event-loops:
      httpclient:
        num-threads: 10
        prefer-native-transport: true
      httphandler:
        num-threads: 10
        prefer-native-transport: true
  executors:
    io:
      core-pool-size: 20
  http:
    client:
      read-timeout: 30s
      max-content-length: 200MB
      event-loop-group: httpclient
    services:
      beacon:
        # In mult-tenant enviornment we don't depend on __merchandiser-idk
        # and would hardcode it in consul
        url: https://us.dev.groupbycloud.com
        pool:
          enabled: true
          max-connections: 20
      topsort:
        url: https://api.topsort.com
        pool:
          enabled: true
          max-connections: 20
        read-timeout: 250ms
      productcatalog:
        url: http://data-catalog-fetch-api-fetch-app-svc.data-catalog-fetch-api
        pool:
          enabled: true
          max-connections: 20
        read-timeout: 2s
  router:
    static-resources:
      redoc:
        paths: classpath:META-INF/swagger/views/redoc
        mapping: /api/redoc/**
      swagger:
        paths: classpath:META-INF/swagger
        mapping: /api/swagger/**
      swagger-ui:
        paths: classpath:META-INF/swagger/views/swagger-ui
        mapping: /api/swagger-ui/**
  security:
    enabled: true
    intercept-url-map:
      - pattern: /api/swagger-ui/**
        access:
          - isAnonymous()
      - pattern: /api/swagger/**
        access:
          - isAnonymous()
      - pattern: /api/redoc/**
        access:
          - isAnonymous()
      - pattern: /openapi*/**
        access:
          - isAnonymous()
      - pattern: /internal/**                                                     # For request within k8s cluster.
        access:
          - isAnonymous()
    token:
      jwt:
        enabled: true
        signatures:
          jwks:
            groupby:
              url: http://authentication.authentication/.well-known/jwks.json
  caches:
    jwks:
      expire-after-write: 1h
  server:
    cors:
      enabled: true
    max-request-size: 100MB
    netty:
      worker:
        event-loop-group: httphandler
      server-type: full_content
endpoints:
  health:
    details-visible: anonymous

resilience4j:
  thread-pool-bulkhead:
    enabled: true #Micronaut BulkheadInterceptor bean requires both bulkhead and thread-pool-bulkhead properties to be set
  bulkhead:
    enabled: true
    instances:
      search:
        maxConcurrentCalls: ${bulkhead.search.parallelism:8}
      pbp:
        maxConcurrentCalls: ${bulkhead.pdp.parallelism:20}

jackson:
  serialization:
    WRITE_DATES_AS_TIMESTAMPS: false
  serialization-inclusion: non_null
  mapper:
    ACCEPT_CASE_INSENSITIVE_ENUMS: true

commandcenter:
  client:
    url: 'https://atlas-command-center.dev.groupbycloud.com/config'

security:
  crypto:
    secret-aes: ''
  hash:
    salt: f9c977ea-b423-4b3e-9952-d38cec720b70

tracing:
  zipkin:
    enabled: false

gcp:
  projectId: groupby-development
  pubsub:
    ackDelay: 600 #10 min
    subscription.expirationTime: 86400 #1 day
    parallelThreads: 1
    direct-search:
      topic:
      project-id:
      count-threshold: 100
      request-byte-threshold: 5000
      delay-threshold: 50
    config:
      topic: site-search-configuration-update
      subscription: site-search-configuration-update-ordered-subscription
    ingestion:
      # This have to be reworked in feature, as minimum a new topic should be created on "ingestion" side.
      topic: command-center-ingestion-success
      subscription: site-search-ingestion-success-ordered-subscription
  use-sdk: true
direct-search-beacon:
  enabled: true

singletenant:
  enabled: false
  name: test

otel:
  metrics:
    enabled: false
    counters: '*'
    histograms: '*'

features:
  launch-darkly:
    sdk-key: ''
    pooling-interval-minutes: 5
    events-flush-minutes: 10
    flags: '{}'
    environment: ''

retail:
  warmup:
    enabled: false
    collection:
    area:
    merchandiser-id:

topsort:
  min-request-page-size: 120

# redis cache configuration
redis:
  servers:
    browse:
      uri: ''
    search:
      uri: ''
    mongo:
      uri: ''
  health.enabled: false

# Mongo DB custom connection configs.
# It's outside mongodb config node so Micronaut won't try to use it.
mongo:
  account: ''
  key: ''
  host: ''
  search-index-suffix-override: ''
  analytics:
    default-value: 0.1
    multiplier: 1024
  part-number:
    match-score-multiplier: 2
    boost-score-multiplier: 4
    exact-match-multiplier: 2048
    index-delay-seconds: 14400  # 4 hours
  facets:
    dynamic-facets-count: 5
    category-bucket-cache-ttl: 60m
  timeout:
    meta: 1
    search: 20

# Mongo DB Micronaut specific connection configs.
mongodb:
  application-name: ${micronaut.application.name}-${gcp.project-id}
  uri: mongodb+srv://${mongo.account}:${mongo.key}@${mongo.host}
  #PRIMARY, PRIMARY_PREFERRED, SECONDARY, SECONDARY_PREFERRED, NEAREST
  read-preference: ${MONGO_READ_PREFERENCE:SECONDARY_PREFERRED}
  ssl:
    enabled: true
  cluster:
    serverSelectionTimeout: 60000ms
  compressor-list:
    - snappy
    - zlib
    - zstd
  connection-pool:
    min-size: 5
    max-size: 20
    max-connecting: 2
    max-connection-idle-time: 300s

# Logs' sampling rate applied by default if no value set in the external configuration
default-log-sampling-rate: 0.1 # 10% of sampled logs are published
