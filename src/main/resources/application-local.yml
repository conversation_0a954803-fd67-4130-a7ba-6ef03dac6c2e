micronaut:
  application:
    name: site-search-api
  security:
    enabled: false
    token:
      jwt:
        enabled: true
        signatures:
          jwks:
            groupby:
              url: 'http://localhost/.well-known/jwks.json'
  http:
    services:
      productcatalog:
        url: 'http://localhost:8085'
  server:
    cors:
      enabled: true
    port: 8081

endpoints:
  health:
    details-visible: anonymous
    enabled: true
    sensitive: false
commandcenter:
  client:
    url: 'http://localhost:8080/config'

mongo:
  part-number:
    index-delay-seconds: 60
  facets:
    dynamic-facets-count: 10
  timeout:
    meta: 2
    search: 20


mongodb:
  application-name: ${micronaut.application.name}-local
  # for uri - don't forget to add /?proxyHost=localhost&proxyPort=1080 in your injection

gcp:
  useSdk: false
  credentialsPath: .run/groupby-development-51aac1fa93fd.json
  pubsub:
    config:
      topic: site-search-configuration-update-local
      subscription: site-search-configuration-update-local
    ingestion:
      # This have to be reworked in feature, as minimum a new topic should be created on "ingestion" side.
      topic: command-center-ingestion-success-local
      subscription: site-search-ingestion-success-ordered-subscription-local
direct-search-beacon:
  enabled: false

tracing:
  zipkin:
    enabled: false

otel:
  metrics:
    enabled: true

features:
  launch-darkly:
    sdk-key: '****************************************'
    environment: 'local'
    flags: '{"enableMongoBrowseEngine": {"enabled": true}, "enablePartNumberSearch": true, "partNumberIndexDelaySeconds": 60 }'

retail:
  warmup:
    enabled:
    collection:
    area:
    merchandiser-id:

topsort:
  client:
    # Use GBI Sandbox auctions token
    token:

security:
  crypto:
    secret-aes: devKeyShouldBeSameInCCApiAndSearch

redis:
  servers:
    browse:
      uri: 'redis://127.0.0.1:6379'
    search:
      uri: 'redis://127.0.0.1:6379'
    mongo:
      uri: 'redis://127.0.0.1:6379'
  health.enabled: false

default-log-sampling-rate: 1.0
