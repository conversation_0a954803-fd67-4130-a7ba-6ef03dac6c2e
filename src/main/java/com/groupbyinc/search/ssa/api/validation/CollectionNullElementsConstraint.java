package com.groupbyinc.search.ssa.api.validation;

import jakarta.validation.Constraint;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Constraint(validatedBy = {})
@Retention(RetentionPolicy.RUNTIME)
public @interface CollectionNullElementsConstraint {

    String message() default "Array objects should not contain 'null' values.";

}
