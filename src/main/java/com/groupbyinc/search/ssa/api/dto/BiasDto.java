package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.api.validation.biasing.constraints.BiasConstraint;
import com.groupbyinc.search.ssa.core.biasing.Bias;
import com.groupbyinc.search.ssa.core.biasing.NumericContent;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.core.annotation.Nullable;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Value;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.BIAS_CONTENT_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.BIAS_CONTENT_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.BIAS_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.BIAS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.BIAS_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.BIAS_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NUMERIC_CONTENT_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NUMERIC_CONTENT_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.STRENGTH_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.STRENGTH_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TYPE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TYPE_TITLE;

@Value
@Schema(
    title = BIAS_TITLE,
    description = BIAS_DESCRIPTION
)
@BiasConstraint
@Introspected
public class BiasDto {

    @Schema(
        description = BIAS_FIELD_DESCRIPTION,
        example = BIAS_FIELD_EXAMPLE
    )
    @NotBlank
    @Size(min = 1, max = 80)
    String field;

    @Schema(
        description = BIAS_CONTENT_FIELD_DESCRIPTION,
        nullable = true,
        example = BIAS_CONTENT_EXAMPLE
    )
    @Size(max = 80)
    String content;

    @Nullable
    StrengthDto strength;

    @Nullable
    Float strengthValue;

    TypeDto type;

    @Valid
    @Schema(
        title = NUMERIC_CONTENT_TITLE,
        description = NUMERIC_CONTENT_DESCRIPTION,
        nullable = true
    )
    NumericContentDto numericContent;

    @Schema(
        title = STRENGTH_TITLE,
        description = STRENGTH_DESCRIPTION
    )
    public enum StrengthDto {
        @JsonProperty("Absolute_Increase")
        ABSOLUTE_INCREASE,
        @JsonProperty("Strong_Increase")
        STRONG_INCREASE,
        @JsonProperty("Medium_Increase")
        MEDIUM_INCREASE,
        @JsonProperty("Weak_Increase")
        WEAK_INCREASE,
        @JsonProperty("Leave_Unchanged")
        LEAVE_UNCHANGED,
        @JsonProperty("Weak_Decrease")
        WEAK_DECREASE,
        @JsonProperty("Medium_Decrease")
        MEDIUM_DECREASE,
        @JsonProperty("Strong_Decrease")
        STRONG_DECREASE,
        @JsonProperty("Absolute_Decrease")
        ABSOLUTE_DECREASE
    }

    @Schema(title = TYPE_TITLE, description = TYPE_DESCRIPTION)
    public enum TypeDto {
        TEXTUAL,
        NUMERIC
    }

    public Bias toDomain() {

        Bias.Strength strength = null;
        if (this.strength != null) {
            strength = switch (this.strength) {
                case ABSOLUTE_INCREASE -> Bias.Strength.ABSOLUTE_INCREASE;
                case STRONG_INCREASE -> Bias.Strength.STRONG_INCREASE;
                case MEDIUM_INCREASE -> Bias.Strength.MEDIUM_INCREASE;
                case WEAK_INCREASE -> Bias.Strength.WEAK_INCREASE;
                case LEAVE_UNCHANGED -> Bias.Strength.LEAVE_UNCHANGED;
                case WEAK_DECREASE -> Bias.Strength.WEAK_DECREASE;
                case MEDIUM_DECREASE -> Bias.Strength.MEDIUM_DECREASE;
                case STRONG_DECREASE -> Bias.Strength.STRONG_DECREASE;
                case ABSOLUTE_DECREASE -> Bias.Strength.ABSOLUTE_DECREASE;
            };
        }

        Bias.Type type = null;
        if (this.type != null) {
            type = switch (this.type) {
                case TEXTUAL -> Bias.Type.TEXTUAL;
                case NUMERIC -> Bias.Type.NUMERIC;
            };
        }

        NumericContent numContent = null;
        if (this.numericContent != null) {
            numContent = this.numericContent.toDomain();
        }

        return new Bias(
            field,
            content,
            strength,
            strengthValue,
            type,
            numContent
        );
    }

}
