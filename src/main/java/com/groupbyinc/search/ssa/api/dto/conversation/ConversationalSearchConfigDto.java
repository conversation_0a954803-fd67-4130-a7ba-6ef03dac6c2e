package com.groupbyinc.search.ssa.api.dto.conversation;

import com.groupbyinc.search.ssa.core.conversation.ConversationalSearchConfig;
import com.groupbyinc.search.ssa.core.conversation.ProductAttributeValue;
import com.groupbyinc.search.ssa.core.conversation.SelectedAnswer;
import com.groupbyinc.search.ssa.core.conversation.UserAnswer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NonNull;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_CONVERSATION_ID_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_FOLLOWUP_CONVERSATION_REQUESTED_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_USER_ANSWER_FIELD_DESCRIPTION;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

public record ConversationalSearchConfigDto(
    @Schema(
        description = SEARCH_REQUEST_FOLLOWUP_CONVERSATION_REQUESTED_FIELD_DESCRIPTION,
        requiredMode = REQUIRED)
    @Getter
    @NonNull
    Boolean followupConversationRequested,

    @Schema(
        description = SEARCH_REQUEST_CONVERSATION_ID_FIELD_DESCRIPTION,
        nullable = true)
    String conversationId,

    @Schema(
        description = SEARCH_REQUEST_USER_ANSWER_FIELD_DESCRIPTION,
        nullable = true)
    UserAnswerDto userAnswer
) {

    public ConversationalSearchConfigDto(boolean followupConversationRequested) {
        this(followupConversationRequested, null, null);
    }

    public ConversationalSearchConfig toDomain() {
        UserAnswer userAnswerConverted;
        if (userAnswer == null) {
            return new ConversationalSearchConfig(followupConversationRequested, conversationId);
        }
        if (userAnswer.selectedAnswer() != null) {
            var pavDto = userAnswer.selectedAnswer().productAttributeValue();
            userAnswerConverted = new UserAnswer(new SelectedAnswer(new ProductAttributeValue(pavDto.name(), pavDto.value())));
        } else {
            userAnswerConverted = new UserAnswer(userAnswer.textAnswer());
        }
        return new ConversationalSearchConfig(followupConversationRequested, conversationId, userAnswerConverted);
    }
}
