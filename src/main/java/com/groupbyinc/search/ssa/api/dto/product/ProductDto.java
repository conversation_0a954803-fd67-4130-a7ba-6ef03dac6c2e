package com.groupbyinc.search.ssa.api.dto.product;

import com.groupbyinc.search.ssa.core.product.Audience;
import com.groupbyinc.search.ssa.core.product.ColorInfo;
import com.groupbyinc.search.ssa.core.product.FieldMask;
import com.groupbyinc.search.ssa.core.product.FulfillmentInfo;
import com.groupbyinc.search.ssa.core.product.Image;
import com.groupbyinc.search.ssa.core.product.PriceInfo;
import com.groupbyinc.search.ssa.core.product.Product;
import com.groupbyinc.search.ssa.core.product.ProductCustomAttribute;
import com.groupbyinc.search.ssa.core.product.ProductLocalInventory;
import com.groupbyinc.search.ssa.core.product.Promotion;
import com.groupbyinc.search.ssa.core.product.Rating;
import com.groupbyinc.search.ssa.core.product.Timestamp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.util.List;
import java.util.Map;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.*;

import static io.micronaut.core.util.CollectionUtils.isEmpty;

@Schema(
    title = PRODUCT_TITLE,
    description = PRODUCT_DESCRIPTION
)
@Getter
public class ProductDto {

    @Schema(
        description = PRODUCT_NAME_FIELD_DESCRIPTION,
        example = PRODUCT_NAME_FIELD_EXAMPLE
    )
    private final String name;

    @Schema(
        description = PRODUCT_ID_FIELD_DESCRIPTION,
        example = PRODUCT_ID_FIELD_EXAMPLE
    )
    private final String id;

    @Schema(
        description = PRODUCT_TYPE_FIELD_DESCRIPTION,
        example = PRODUCT_TYPE_FIELD_EXAMPLE
    )
    private final String type;

    @Schema(
        description = PRIMARY_PRODUCT_ID_FIELD_DESCRIPTION,
        example = PRIMARY_PRODUCT_ID_FIELD_EXAMPLE
    )
    private final String primaryProductId;

    @Schema(
        description = PRODUCT_COLLECTION_MEMBER_IDS_FIELD_DESCRIPTION,
        example = PRODUCT_COLLECTION_MEMBER_IDS_FIELD_EXAMPLE
    )
    private final List<String> collectionMemberIds;

    @Schema(
        description = PRODUCT_GTIN_FIELD_DESCRIPTION,
        example = PRODUCT_GTIN_FIELD_EXAMPLE
    )
    private final String gtin;

    @Schema(
        description = PRODUCT_CATEGORIES_FIELD_DESCRIPTION,
        example = PRODUCT_CATEGORIES_FIELD_EXAMPLE
    )
    private final List<String> categories;

    @Schema(
        description = PRODUCT_TITLE_FIELD_DESCRIPTION,
        example = PRODUCT_TITLE_FIELD_EXAMPLE
    )
    private final String title;

    @Schema(
        description = PRODUCT_BRANDS_FIELD_DESCRIPTION,
        example = PRODUCT_BRANDS_FIELD_EXAMPLE
    )
    private final List<String> brands;

    @Schema(
        description = PRODUCT_DESCRIPTION_FIELD,
        example = PRODUCT_DESCRIPTION_FIELD_EXAMPLE
    )
    private final String description;

    @Schema(
        description = PRODUCT_LANGUAGE_CODE_FIELD_DESCRIPTION,
        example = PRODUCT_LANGUAGE_CODE_FIELD_EXAMPLE
    )
    private final String languageCode;

    @Schema(
        description = PRODUCT_ATTRIBUTES_FIELD_DESCRIPTION,
        example = PRODUCT_ATTRIBUTES_FIELD_EXAMPLE
    )
    private final Map<String, ProductCustomAttribute> attributes;

    @Schema(
        description = PRODUCT_TAGS_FIELD_DESCRIPTION,
        example = PRODUCT_TAGS_FIELD_EXAMPLE
    )
    private final List<String> tags;

    @Schema(
        description = PRODUCT_PRICE_INFO_FIELD_DESCRIPTION
    )
    private final PriceInfo priceInfo;

    @Schema(
        description = RATING_DESCRIPTION
    )
    private final Rating rating;

    @Schema(
        description = PRODUCT_AVAILABLE_TIME_FIELD_DESCRIPTION
    )
    private final Timestamp availableTime;

    @Schema(
        description = PRODUCT_AVAILABILITY_FIELD_DESCRIPTION,
        example = PRODUCT_AVAILABILITY_FIELD_EXAMPLE
    )
    private final String availability;

    @Schema(
        description = PRODUCT_QUANTITY_FIELD_DESCRIPTION,
        example = PRODUCT_QUANTITY_FIELD_EXAMPLE
    )
    private final Integer availableQuantity;

    @Schema(
        description = FULFILMENT_INFO_DESCRIPTION
    )
    private final List<FulfillmentInfo> fulfillmentInfos;

    @Schema(
        description = PRODUCT_URI_FIELD_DESCRIPTION,
        example = PRODUCT_URI_FIELD_EXAMPLE
    )
    private final String uri;

    @Schema(
        description = PRODUCT_IMAGES_FIELD_DESCRIPTION
    )
    private final List<Image> images;

    @Schema(
        description = AUDIENCE_DESCRIPTION
    )
    private final Audience audience;

    @Schema(
        description = PRODUCT_COLOR_INFO_FIELD_DESCRIPTION
    )
    private final ColorInfo colorInfo;

    @Schema(
        description = PRODUCT_SIZES_FIELD_DESCRIPTION,
        example = PRODUCT_SIZES_FIELD_EXAMPLE
    )
    private final List<String> sizes;

    @Schema(
        description = PRODUCT_MATERIALS_FIELD_DESCRIPTION,
        example = PRODUCT_MATERIALS_FIELD_EXAMPLE
    )
    private final List<String> materials;

    @Schema(
        description = PRODUCT_PATTERNS_FIELD_DESCRIPTION,
        example = PRODUCT_PATTERNS_FIELD_EXAMPLE
    )
    private final List<String> patterns;

    @Schema(
        description = PRODUCT_CONDITION_FIELD_DESCRIPTION,
        example = PRODUCT_CONDITION_FIELD_EXAMPLE
    )
    private final List<String> conditions;

    @Schema(
        description = PRODUCT_PUBLISH_TIME_FIELD_DESCRIPTION
    )
    private final Timestamp publishTime;

    @Schema(
        description = PRODUCT_RETRIEVABLE_FIELDS_DESCRIPTION
    )
    private final FieldMask retrievableFields;

    @Schema(
        description = PROMOTION_DESCRIPTION
    )
    private final List<Promotion> promotions;

    @Schema(
        description = PRODUCT_VARIANTS_FIELD_DESCRIPTION
    )
    private final List<ProductDto> variants;

    @Schema(
        description = PRODUCT_LOCAL_INVENTORIES_DESCRIPTION
    )
    private final List<ProductLocalInventory> localInventories;

    public ProductDto(Product product) {
        this.name = product.getName();
        this.id = product.getId();
        this.type = product.getType();
        this.primaryProductId = product.getPrimaryProductId();
        this.collectionMemberIds = product.getCollectionMemberIds();
        this.gtin = product.getGtin();
        this.categories = product.getCategories();
        this.title = product.getTitle();
        this.brands = product.getBrands();
        this.description = product.getDescription();
        this.languageCode = product.getLanguageCode();
        this.attributes = product.getAttributes();
        this.tags = product.getTags();
        this.priceInfo = product.getPriceInfo();
        this.availableTime = product.getAvailableTime();
        this.rating = product.getRating();
        this.availability = product.getAvailability();
        this.availableQuantity = product.getAvailableQuantity();
        this.fulfillmentInfos = isEmpty(product.getFulfillmentInfos()) ? null : product.getFulfillmentInfos();
        this.uri = product.getUri();
        this.images = isEmpty(product.getImages()) ? null : product.getImages();
        this.audience = product.getAudience();
        this.colorInfo = product.getColorInfo();
        this.sizes = product.getSizes();
        this.materials = product.getMaterials();
        this.patterns = product.getPatterns();
        this.conditions = product.getConditions();
        this.publishTime = product.getPublishTime();
        this.retrievableFields = product.getRetrievableFields();
        this.promotions = isEmpty(product.getPromotions()) ? null : product.getPromotions();
        this.variants = isEmpty(product.getVariants())
            ? null
            : product.getVariants().stream().map(ProductDto::new).toList();
        this.localInventories = product.getLocalInventories();
    }
}
