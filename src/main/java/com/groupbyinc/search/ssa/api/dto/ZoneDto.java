package com.groupbyinc.search.ssa.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.ZONE_CONTENT_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ZONE_CONTENT_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ZONE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ZONE_HTML_CONTENT_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ZONE_HTML_CONTENT_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ZONE_NAME_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ZONE_NAME_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ZONE_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ZONE_TYPE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ZONE_TYPE_FIELD_EXAMPLE;

@Schema(
    title = ZONE_TITLE,
    description = ZONE_DESCRIPTION
)
public record ZoneDto(

    @Schema(
        description = ZONE_NAME_FIELD_DESCRIPTION,
        example = ZONE_NAME_FIELD_EXAMPLE
    )
    String name,

    @Schema(
        description = ZONE_TYPE_DESCRIPTION,
        example = ZONE_TYPE_FIELD_EXAMPLE
    )
    ZoneDtoType type,

    @Schema(
        description = ZONE_CONTENT_FIELD_DESCRIPTION,
        example = ZONE_CONTENT_FIELD_EXAMPLE
    )
    String content,

    @Schema(
        description = ZONE_CONTENT_FIELD_DESCRIPTION,
        example = ZONE_CONTENT_FIELD_EXAMPLE
    )
    String richContent,

    @Schema(
        description = ZONE_HTML_CONTENT_FIELD_DESCRIPTION,
        example = ZONE_HTML_CONTENT_FIELD_EXAMPLE
    )
    String html
    ) {

    @Builder
    public ZoneDto {
    }

    public static ZoneDto ofContent(String name, ZoneDtoType type, String content) {
        return new ZoneDtoBuilder().name(name).type(type).content(content).build();
    }

    public static ZoneDto ofRichContent(String name, ZoneDtoType type, String richContent) {
        return new ZoneDtoBuilder().name(name).type(type).richContent(richContent).build();
    }

    public static ZoneDto ofHTML(String name, ZoneDtoType type, String html) {
        return new ZoneDtoBuilder().name(name).type(type).html(html).build();
    }

}
