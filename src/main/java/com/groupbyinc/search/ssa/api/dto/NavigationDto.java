package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.core.Metadata;
import com.groupbyinc.search.ssa.core.navigation.Navigation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Value;

import java.util.List;
import java.util.stream.Collectors;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.NAVIGATION_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NAVIGATION_DISPLAY_NAME_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NAVIGATION_DISPLAY_NAME_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NAVIGATION_NAME_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NAVIGATION_NAME_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NAVIGATION_OR_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NAVIGATION_TITLE;

@Schema(
    title = NAVIGATION_TITLE,
    description = NAVIGATION_DESCRIPTION
)
@Value
public class NavigationDto {

    @Schema(
        description = NAVIGATION_NAME_FIELD_DESCRIPTION,
        example = NAVIGATION_NAME_FIELD_EXAMPLE
    )
    String name;

    @Schema(
        description = NAVIGATION_DISPLAY_NAME_FIELD_DESCRIPTION,
        example = NAVIGATION_DISPLAY_NAME_FIELD_EXAMPLE
    )
    String displayName;

    NavigationTypeDto type;

    List<RefinementDto> refinements;

    @Schema(description = NAVIGATION_OR_FIELD_DESCRIPTION)
    boolean or;

    String source;

    List<Metadata> metadata;

    /** Place id for inventory navigations. */
    String placeId;

    /** Flag to confirm that navigation is pinned. */
    boolean pinned;

    public static NavigationDto fromDomain(Navigation navigation) {
        var type = switch (navigation.getType()) {
            case VALUE -> NavigationTypeDto.Value;
            case RANGE -> NavigationTypeDto.Range;
        };

        var refinements = navigation.getRefinements().stream()
            .map(RefinementDto::fromDomain)
            .collect(Collectors.toList());

        return new NavigationDto(
            navigation.getField(),
            navigation.getName(),
            type,
            refinements,
            navigation.isMultiSelect(),
            navigation.getSource(),
            navigation.getMetadata(),
            navigation.getPlaceId(),
            navigation.isPinned()
        );
    }
}
