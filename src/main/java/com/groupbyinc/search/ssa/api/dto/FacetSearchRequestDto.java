package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.core.navigation.Facet;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Builder;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_SEARCH_REQUEST_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_SEARCH_REQUEST_TITLE;

@Introspected
@Builder(toBuilder = true)
@Schema(title = FACET_SEARCH_REQUEST_TITLE, description = FACET_SEARCH_REQUEST_DESCRIPTION)
public record FacetSearchRequestDto(Facet facet, @Valid SearchRequestDto originalRequest) {

}
