package com.groupbyinc.search.ssa.api.validation.biasing.constraints;

import jakarta.validation.Constraint;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD})
@Constraint(validatedBy = {})
@Retention(RetentionPolicy.RUNTIME)
public @interface NumericContentRangeConstraint {

    /*
     * A message template can be provided in a hard-coded manner as above. If none
     * is specified, Micronaut tries to find a message using ClassName.message
     * using the MessageSource interface, see: CustomValidationMessages.
     */
    String message() default "Invalid 'Range' object passed.";

}
