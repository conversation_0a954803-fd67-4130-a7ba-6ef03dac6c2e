package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.core.SearchMetadata;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Value;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_METADATA_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_METADATA_RETAIL_TIME_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_METADATA_RETAIL_TIME_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_METADATA_SEARCH_ATTRIBUTION_TOKEN_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_METADATA_SEARCH_ATTRIBUTION_TOKEN_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_METADATA_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_METADATA_TOTAL_TIME_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_METADATA_TOTAL_TIME_FIELD_EXAMPLE;

@Schema(
    title = SEARCH_METADATA_TITLE,
    description = SEARCH_METADATA_DESCRIPTION
)
@Value
public class SearchMetadataDto {

    @Schema(
        description = SEARCH_METADATA_SEARCH_ATTRIBUTION_TOKEN_FIELD_DESCRIPTION,
        nullable = true,
        example = SEARCH_METADATA_SEARCH_ATTRIBUTION_TOKEN_FIELD_EXAMPLE
    )
    String searchAttributionToken;

    @Schema(hidden = true)
    boolean cached;

    @Schema(
        description = SEARCH_METADATA_TOTAL_TIME_FIELD_DESCRIPTION,
        example = SEARCH_METADATA_TOTAL_TIME_FIELD_EXAMPLE
    )
    Long totalTime;

    @Schema(
        description = SEARCH_METADATA_RETAIL_TIME_FIELD_DESCRIPTION,
        example = SEARCH_METADATA_RETAIL_TIME_FIELD_EXAMPLE
    )
    Long retailTime;

    public static SearchMetadataDto fromDomain(SearchMetadata searchMetadata) {
        return new SearchMetadataDto(
            searchMetadata.getAttributionToken(),
            false,
            searchMetadata.getTotalTime() != null ? searchMetadata.getTotalTime().toMillis() : null,
            searchMetadata.getRetailTime() != null ? searchMetadata.getRetailTime().toMillis() : null
        );
    }
}
