package com.groupbyinc.search.ssa.api.builders;

import com.groupbyinc.search.ssa.api.dto.Experiment;
import com.groupbyinc.search.ssa.api.dto.FacetSearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.FacetSearchResponseDto;
import com.groupbyinc.search.ssa.api.dto.NavigationDto;
import com.groupbyinc.search.ssa.api.dto.PageInfoDto;
import com.groupbyinc.search.ssa.api.dto.RecordDto;
import com.groupbyinc.search.ssa.api.dto.SearchMetadataDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SearchResponseDto;
import com.groupbyinc.search.ssa.api.dto.TemplateDto;
import com.groupbyinc.search.ssa.api.dto.tiles.TileDto;
import com.groupbyinc.search.ssa.core.Metadata;
import com.groupbyinc.search.ssa.core.Pagination;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfile;
import com.groupbyinc.search.ssa.core.navigation.Navigation;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;
import com.groupbyinc.search.ssa.core.tiles.Tile;

import io.micronaut.context.annotation.Context;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.retail.util.ConversationUtils.isFollowupConversation;
import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultList;
import static com.groupbyinc.search.ssa.util.debug.DebugHelpers.buildDebugDto;

@Context
public class SearchResponseBuilder {

    public SearchResponseDto buildSearchResponseDto(SearchParameters searchParameters,
                                                    SearchResults searchResults,
                                                    SearchRequestDto searchRequest) {
        var context = getRequestContext();

        var builder = SearchResponseDto.builder()
            .id(context.getRequestId())
            .area(context.getArea())
            .query(searchResults.getQuery())
            .correctedQuery(searchResults.getCorrectedQuery())
            .biasingProfile(convertBiasingProfileToString(searchResults))
            .filter(searchResults.getFilter())
            .records(convertRecordsToDtos(searchResults.getRecords()))
            .sponsoredRecords(convertRecordsToDtos(searchResults.getSponsoredRecords()))
            .totalRecordCount(searchResults.getNumTotalRecords())
            .metadata(
                SearchMetadataDto.fromDomain(
                    searchResults.getMetadata()
                )
            )
            .siteParams(
                addSiteFilterToSiteParams(
                    searchResults.getSiteParams(),
                    searchResults.getSiteFilter()
                )
            )
            .pageInfo(
                convertPaginationToDto(
                    searchParameters.getOriginalPagination(),
                    searchResults
                )
            )
            .availableNavigation(
                convertNavigationsToDtos(searchResults.getNavigations())
            )
            .selectedNavigation(
                convertNavigationsToDtos(searchResults.getSelectedNavigations())
            )
            .redirect(covertRedirectToDto(searchResults))
            .redirectMetadata(searchResults.getRedirectMetadata())
            .template(convertTemplateToDto(searchResults))
            .empty(searchResults.isNoResults())
            .warnings(getRequestContext().getWarnings())
            .includeExpandedResults(searchResults.getIncludeExpandedResults())
            .facetLimit(searchResults.getFacetLimit())
            .rewrites(
                Stream.concat(
                    notNullOrDefaultList(searchRequest.getRewrites()).stream(),
                    notNullOrDefaultList(searchResults.getRewrites()).stream()
                ).toList()
            )
            .tiles(convertTilesToDtos(searchResults.getTiles()))
            .engineSource(searchResults.getSearchEngine())
            .requestServed(searchResults.getRequestServed())
            .searchStrategies(searchResults.getSearchStrategies())
            .conversationalSearchResult(searchResults.getConversationalSearchResult());

        replaceOriginalRequestQueryIfRequired(searchResults, searchRequest, searchParameters);
        builder.originalRequest(searchRequest);

        if (searchResults.getRuleVariantName() != null && !searchResults.getRuleVariantName().isEmpty()) {
            builder.experiments(
                List.of(
                    new Experiment(
                        "search_rule_test",
                        searchResults.getRuleVariantName()
                    )
                )
            );
        }

        if (context.getRequestOptions().debug()) {
            builder.debug(buildDebugDto(context.getDebugDetails()));
        }
        return builder.build();
    }

    /**
     * If Google response {@link SearchResults} contains 'conversationalSearchResult.refinedQuery', then
     * 'response.originalRequest.query' and 'response.query' need to be replaced with 'refinedQuery' value.
     * <br/>
     * 'response.query' update is done here
     * {@link com.groupbyinc.search.ssa.retail.util.ConversationUtils#populateConversationalResponse}
     * <br/>
     * Under this method update is performed for initial request 'query', which later will populate 'originalRequest'.
     *
     * @param searchResults Google response
     * @param searchRequest initial request
     */
    private void replaceOriginalRequestQueryIfRequired(SearchResults searchResults,
                                                       SearchRequestDto searchRequest,
                                                       SearchParameters searchParameters) {
        if (isFollowupConversation(searchParameters)
            && searchRequest.getQuery() != null
            && !searchRequest.getQuery().equalsIgnoreCase(searchResults.getQuery())) {
            searchRequest.setQuery(searchResults.getQuery());
        }
    }

    public FacetSearchResponseDto buildFacetSearchResponseDto(FacetSearchRequestDto request,
                                                              SearchResults searchResults) {
        var result = FacetSearchResponseDto
            .builder()
            .metadata(SearchMetadataDto.fromDomain(searchResults.getMetadata()))
            .originalRequest(request.originalRequest())
            .availableNavigation(extractRequestedNavigation(request, searchResults));

        var context = getRequestContext();
        if (context.getRequestOptions().debug()) {
            result.debug(buildDebugDto(context.getDebugDetails()));
        }

        return result.build();
    }

    private NavigationDto extractRequestedNavigation(FacetSearchRequestDto request, SearchResults searchResults) {
        var navigation = searchResults
            .getNavigations()
            .stream()
            .map(NavigationDto::fromDomain)
            .filter(n -> n.getName().equals(request.facet().navigationName()))
            .findFirst()
            .orElse(null);

        // If a request selected refinements contains the same refinement as refinement for which
        // we need to search facets, we need to exclude this selected facet from a response to not
        // show to it the user because it is already selected.
        if (navigation != null && request.originalRequest().getRefinements() != null) {
            navigation
                .getRefinements()
                .removeIf(refinement -> request
                    .originalRequest()
                    .getRefinements()
                    .stream()
                    .anyMatch(ref -> ref.getValue().equals(refinement.getValue()))
                );
        }

        return navigation;
    }

    private String convertBiasingProfileToString(SearchResults searchResults) {
        return searchResults
            .getBiasingProfile()
            .flatMap(BiasingProfile::getName)
            .orElse(null);
    }

    private List<RecordDto> convertRecordsToDtos(List<Record> records) {
        if (records == null) {
            return List.of();
        }

        return records
            .stream()
            .map(RecordDto::fromDomain)
            .collect(Collectors.toList());
    }

    private TemplateDto convertTemplateToDto(SearchResults searchResults) {
        return searchResults
            .getTemplate()
            .map(TemplateDto::fromDomain)
            .orElse(null);
    }

    private PageInfoDto convertPaginationToDto(Pagination pagination, SearchResults searchResults) {

        if (searchResults.getRecords().isEmpty() && pagination.getOffset() == 0) {
            return new PageInfoDto(0L, 0L);
        } else if (searchResults.getRecords().isEmpty() && pagination.getOffset() != 0) {
            // Process an edge case when google returns an empty search for page bigger than the first one
            return new PageInfoDto(
                pagination.getOffset(),
                pagination.getOffset() + pagination.getSize()
            );
        }

        return new PageInfoDto(
            pagination.getOffset() + 1L,
            Math.min(pagination.getOffset() + pagination.getSize(), searchResults.getNumTotalRecords())
        );
    }

    private String covertRedirectToDto(SearchResults searchResults) {
        return searchResults.getRedirectUrl().orElse(null);
    }

    /**
     * Generate metadata from siteFilter {@link SiteFilterConfiguration}  and append to existing siteParams
     *
     * @param siteParams existing site parameters
     * @param siteFilter {@link SiteFilterConfiguration}
     *
     * @return List<Metadata>
     */
    public List<Metadata> addSiteFilterToSiteParams(
        List<Metadata> siteParams, SiteFilterConfiguration siteFilter
    ) {
        String siteFilterName = null;
        Metadata siteFilterMetadata = null;
        if (siteFilter != null) {
            siteFilterName = siteFilter.name().toLowerCase();
        }
        if (siteFilterName != null) {
            siteFilterMetadata = new Metadata("siteFilter", siteFilterName);
        }

        if (siteParams != null && siteFilterMetadata != null) {
            var mutableSiteParams = new ArrayList<>(siteParams);
            mutableSiteParams.add(siteFilterMetadata);
            return mutableSiteParams;
        }

        if (siteParams != null) {
            return siteParams;
        }

        if (siteFilterMetadata != null) {
            return List.of(siteFilterMetadata);
        }
        return List.of();
    }

    private List<NavigationDto> convertNavigationsToDtos(List<Navigation> navigations) {

        return navigations.stream()
            .map(NavigationDto::fromDomain)
            .collect(Collectors.toList());
    }

    private List<TileDto> convertTilesToDtos(List<Tile> tiles) {
        // if tiles has not been explicitly requested or not supported object won't appear in response
        return tiles == null ? null : tiles.stream().map(TileDto::fromDomain).toList();
    }

}
