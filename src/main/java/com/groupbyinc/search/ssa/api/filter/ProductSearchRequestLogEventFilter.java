package com.groupbyinc.search.ssa.api.filter;

import io.micronaut.context.annotation.Requires;
import io.micronaut.core.annotation.Order;
import io.micronaut.core.propagation.MutablePropagatedContext;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.RequestFilter;
import io.micronaut.http.annotation.ServerFilter;
import io.micronaut.http.filter.FilterPatternStyle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.groupbyinc.search.ssa.api.filter.FilterPriority.LOG_EVENTS;

@Slf4j
@Order(LOG_EVENTS)
@RequiredArgsConstructor
@Requires(notEnv = "test")
@SuppressWarnings("unused")
@ServerFilter(patternStyle = FilterPatternStyle.REGEX, value = "/api/search/product(/)?")
public class ProductSearchRequestLogEventFilter extends RequestLogEventWriter {

    @RequestFilter
    public void logEvent(
        HttpRequest<?> request,
        MutablePropagatedContext mutablePropagatedContext,
        @Body byte[] body
    ) {

        writeLog(request, body);
    }

    @Override
    protected String logEventType() {
        return EventType.QUERY.name();
    }

    @Override
    protected String logEventSubType(HttpRequest<?> request, byte[] body) {
        return EventSubtype.PRODUCT_DETAIL_PAGE.name();
    }
}