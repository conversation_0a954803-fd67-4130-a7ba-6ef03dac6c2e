package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.api.validation.biasing.constraints.NumericContentConstraint;
import com.groupbyinc.search.ssa.api.validation.biasing.constraints.NumericContentRangeConstraint;
import com.groupbyinc.search.ssa.core.biasing.NumericContent;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Value;

import java.util.List;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.NUMERIC_CONTENT_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NUMERIC_CONTENT_RANGES_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NUMERIC_CONTENT_RANGES_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NUMERIC_CONTENT_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NUMERIC_CONTENT_VALUES_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NUMERIC_CONTENT_VALUES_TITLE;

import static java.util.stream.Collectors.toList;

@Value
@Schema(
    title = NUMERIC_CONTENT_TITLE,
    description = NUMERIC_CONTENT_DESCRIPTION
)
@Introspected
@NumericContentConstraint
public class NumericContentDto {

    @Schema(
        title = NUMERIC_CONTENT_VALUES_TITLE,
        nullable = true,
        description = NUMERIC_CONTENT_VALUES_DESCRIPTION
    )
    List<Double> values;

    @Schema(
        title = NUMERIC_CONTENT_RANGES_TITLE,
        nullable = true,
        description = NUMERIC_CONTENT_RANGES_DESCRIPTION
    )
    @Valid
    @NumericContentRangeConstraint
    List<RangeDto> ranges;

    public NumericContent toDomain() {
        var builder = NumericContent.builder();
        builder.values(values);

        if (ranges != null) {
            builder.ranges(ranges.stream().map(RangeDto::toDomain).collect(toList()));
        }

        return builder.build();
    }
}
