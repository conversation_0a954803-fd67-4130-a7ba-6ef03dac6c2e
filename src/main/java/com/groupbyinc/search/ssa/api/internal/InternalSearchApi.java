package com.groupbyinc.search.ssa.api.internal;

import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SearchResponseDto;
import com.groupbyinc.search.ssa.application.core.search.SearchService;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.github.resilience4j.micronaut.annotation.Bulkhead;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.groupbyinc.search.ssa.api.validation.RequestValidationUtils.validateRequest;

import static io.micronaut.http.MediaType.APPLICATION_JSON;

@Slf4j
@Controller("/internal/search")
@RequiredArgsConstructor
public class InternalSearchApi {

    private final SearchService searchService;

    @Hidden
    @Bulkhead(name = "search")
    @Post(consumes = APPLICATION_JSON, produces = APPLICATION_JSON)
    public HttpResponse<SearchResponseDto> search(@Body SearchRequestDto request) {
        validateRequest(request);

        return HttpResponse.ok(
            searchService.search(request)
        );
    }

}
