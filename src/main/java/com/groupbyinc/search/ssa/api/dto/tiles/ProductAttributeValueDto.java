package com.groupbyinc.search.ssa.api.dto.tiles;

import io.swagger.v3.oas.annotations.media.Schema;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_ATTRIBUTE_VALUE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_ATTRIBUTE_VALUE_EXAMPLE;

@Schema(
    description = PRODUCT_ATTRIBUTE_VALUE_DESCRIPTION,
    example = PRODUCT_ATTRIBUTE_VALUE_EXAMPLE
)
public record ProductAttributeValueDto(String name, String value) {

}