package com.groupbyinc.search.ssa.api.error;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Replaces;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.exceptions.HttpStatusException;
import io.micronaut.http.server.exceptions.ExceptionHandler;
import io.micronaut.http.server.exceptions.HttpStatusHandler;
import io.micronaut.http.server.exceptions.UnsatisfiedRouteHandler;
import io.micronaut.security.authentication.AuthenticationExceptionHandler;
import io.micronaut.security.authentication.DefaultAuthorizationExceptionHandler;
import io.micronaut.validation.exceptions.ConstraintExceptionHandler;
import io.micronaut.web.router.exceptions.UnsatisfiedRouteException;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;

@Factory
@RequiredArgsConstructor
public class ExceptionHandlerFactory {

    private final GlobalApiExceptionHandler globalApiExceptionHandler;

    @Context
    @Replaces(ConstraintExceptionHandler.class)
    public ExceptionHandler<ConstraintViolationException, HttpResponse<?>> constraintExceptionHandler() {
        return globalApiExceptionHandler::handle;
    }

    @Context
    @Replaces(AuthenticationExceptionHandler.class)
    public ExceptionHandler<AuthorizationException, HttpResponse<?>> authorizationExceptionHandler() {
        return globalApiExceptionHandler::handle;
    }

    @Context
    @Replaces(HttpStatusHandler.class)
    public ExceptionHandler<HttpStatusException, HttpResponse<?>> httpStatusHandler() {
        return globalApiExceptionHandler::handle;
    }

    @Context
    @Replaces(UnsatisfiedRouteHandler.class)
    public ExceptionHandler<UnsatisfiedRouteException, HttpResponse<?>> unsatisfiedRouteHandler() {
        return globalApiExceptionHandler::handle;
    }

    @Context
    @Replaces(DefaultAuthorizationExceptionHandler.class)
    public ExceptionHandler<AuthorizationException, HttpResponse<?>> defaultAuthorizationExceptionHandler() {
        return globalApiExceptionHandler::handle;
    }

}
