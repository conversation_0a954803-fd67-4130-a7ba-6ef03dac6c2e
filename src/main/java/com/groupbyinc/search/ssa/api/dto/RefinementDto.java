package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.core.navigation.NavigationRefinement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Value;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_COUNT_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_COUNT_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_DESCRIPTION_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_DESCRIPTION_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_DESCRIPTION_FIELD_TYPE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_HIGH_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_HIGH_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_HIGH_FIELD_TYPE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_LOW_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_LOW_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_LOW_FIELD_TYPE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_VALUE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.REFINEMENT_VALUE_FIELD_EXAMPLE;

@Schema(
    title = REFINEMENT_TITLE,
    description = REFINEMENT_DESCRIPTION
)
@Value
public class RefinementDto {

    NavigationTypeDto type;

    @Schema(
        description = REFINEMENT_COUNT_FIELD_DESCRIPTION,
        example = REFINEMENT_COUNT_FIELD_EXAMPLE
    )
    Long count;

    @Schema(
        description = REFINEMENT_VALUE_FIELD_DESCRIPTION,
        example = REFINEMENT_VALUE_FIELD_EXAMPLE
    )
    String value;

    @Schema(
        description = REFINEMENT_LOW_FIELD_DESCRIPTION,
        type = REFINEMENT_LOW_FIELD_TYPE,
        example = REFINEMENT_LOW_FIELD_EXAMPLE
    )
    Double low;

    @Schema(
        description = REFINEMENT_HIGH_FIELD_DESCRIPTION,
        type = REFINEMENT_HIGH_FIELD_TYPE,
        example = REFINEMENT_HIGH_FIELD_EXAMPLE
    )
    Double high;

    @Schema(
        description = REFINEMENT_DESCRIPTION_FIELD_DESCRIPTION,
        type = REFINEMENT_DESCRIPTION_FIELD_TYPE,
        example = REFINEMENT_DESCRIPTION_FIELD_EXAMPLE
    )
    String description;

    boolean pinned;

    public static RefinementDto fromDomain(NavigationRefinement refinement) {

        return switch (refinement.getNavigationType()) {
            case RANGE -> new RefinementDto(
                NavigationTypeDto.Range,
                refinement.getCount(),
                null,
                refinement.getRange().low(),
                refinement.getRange().high(),
                refinement.getRange().description(),
                refinement.isPinned()
            );
            case VALUE -> new RefinementDto(
                NavigationTypeDto.Value,
                refinement.getCount(),
                refinement.getValue(),
                null,
                null,
                null,
                refinement.isPinned()
            );
        };
    }
}
