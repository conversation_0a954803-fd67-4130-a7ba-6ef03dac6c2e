package com.groupbyinc.search.ssa.api;

import com.groupbyinc.search.ssa.api.dto.crm.UserAttributeDto;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.RequestOptions;
import com.groupbyinc.search.ssa.features.LDContextBuilder;
import com.groupbyinc.search.ssa.util.debug.DebugDetails;

import com.launchdarkly.sdk.LDContext;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.core.propagation.PropagatedContextElement;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.ParametersAreNonnullByDefault;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArrayList;

import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireDefined;
import static com.groupbyinc.utils.validation.ValidationUtils.requireFormat;

import static java.util.Objects.requireNonNullElse;

/**
 * Combines mandatory contextual search parameters.
 */
@Data
@Slf4j
@ParametersAreNonnullByDefault
public class RequestContext implements PropagatedContextElement {

    private static final String AREA_FORMAT = "[a-zA-Z]+";
    private static final String COLLECTION_FORMAT = "^[a-zA-Z]+[-a-zA-Z0-9]+[a-zA-Z0-9]$";

    /**
     * Merchandiser for whom the search is being performed. This has to be specified and be all alpha.
     */
    private final Merchandiser merchandiser;

    /** Name of the area being searched. If specified, this must be all alpha. */
    private final String area;

    /**
     * Name of the collection being searched. If specified, this must be formatted correctly.
     */
    private final String collection;

    /**
     * Unique identifier identifying the shopper.
     */
    private final String visitorId;

    /**
     * Unique identifier identifying the request.
     */
    private final String requestId;

    /**
     * Unique identifier identifying the user on customer side.
     */
    private final String loginId;

    /**
     * The user attributes that could be used for personalization of search.
     */
    private final List<UserAttributeDto> userAttributes;

    /**
     * Feature flags context
     */
    private final LDContext ldContext;

    /**
     * Any request options like: skipCache or etc.
     */
    private final RequestOptions requestOptions;

    /**
     * Any error messages associated with the request.
     */
    private final List<String> warnings = new CopyOnWriteArrayList<>();

    /**
     * Object to store request related debug information.
     * This object will be populated only if the request contains a debug flag.
     */
    private final DebugDetails debugDetails = new DebugDetails();

    public RequestContext(Merchandiser merchandiser,
                          String requestId,
                          String area,
                          String collection,
                          @Nullable String visitorId,
                          @Nullable String loginId,
                          @Nullable List<UserAttributeDto> userAttributes,
                          String environment,
                          RequestOptions requestOptions) {
        this.merchandiser = requireDefined(merchandiser, "Context merchandiser");
        this.collection = requireFormat(collection, "Context collection", COLLECTION_FORMAT, MANDATORY);
        this.area = requireFormat(area, "Context area", AREA_FORMAT, MANDATORY);

        this.requestOptions = requestOptions;

        this.loginId = loginId;
        this.userAttributes = userAttributes;
        this.requestId = requestId;
        this.visitorId = generateNewVisitorIdIfNeeded(visitorId);

        this.ldContext = LDContextBuilder
            .builder(merchandiser.merchandiserId(), environment)
            .setArea(area)
            .setCollection(collection)
            .setLoginId(loginId)
            .build();
    }

    public RequestContext(Merchandiser merchandiser,
                          String requestId,
                          String collection,
                          String environment,
                          RequestOptions requestOptions) {
        this.merchandiser = requireDefined(merchandiser, "Context merchandiser");
        this.collection = requireFormat(collection, "Context collection", COLLECTION_FORMAT, MANDATORY);

        this.area = null;
        this.loginId = null;
        this.userAttributes = null;
        this.visitorId = null;
        this.requestId = requestId;

        this.requestOptions = requestOptions;

        this.ldContext = LDContextBuilder
            .builder(merchandiser.merchandiserId(), environment)
            .setCollection(collection)
            .build();
    }

    private String generateNewVisitorIdIfNeeded(@Nullable String visitorId) {
        if (StringUtils.isBlank(visitorId)) {
            var generatedVisitorId = UUID.randomUUID().toString();
            log.debug("VisitorId missed. Generate new one '{}' for requestId '{}'.", generatedVisitorId, requestId);
            return generatedVisitorId;
        }
        return visitorId;
    }

    public String getUniqueUserId() {
        return requireNonNullElse(loginId, visitorId);
    }

    public void addWarning(String warning) {
        warnings.add(warning);
    }

}
