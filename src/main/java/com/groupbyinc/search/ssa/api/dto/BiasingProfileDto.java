package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.core.biasing.BiasingProfile;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Value;

import java.util.List;
import java.util.stream.Collectors;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.BIASING_PROFILE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.BIASING_PROFILE_TITLE;

@Value
@Schema(
    title = BIASING_PROFILE_TITLE,
    description = BIASING_PROFILE_DESCRIPTION,
    nullable = true
)
@Introspected
public class BiasingProfileDto {

    @Valid
    List<BiasDto> biases;

    public BiasingProfile toDomain() {

        return new BiasingProfile(
            null,
            biases.stream()
                .map(BiasDto::toDomain)
                .collect(Collectors.toList())
        );
    }
}
