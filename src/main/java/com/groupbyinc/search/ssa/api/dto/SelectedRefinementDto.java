package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import static com.groupbyinc.search.ssa.util.StringUtils.SOURCE_DYNAMIC;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_HIGH_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_HIGH_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_HIGH_FIELD_TYPE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_LOW_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_LOW_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_LOW_FIELD_TYPE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_NAVIGATION_NAME_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_NAVIGATION_NAME_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_OR_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_OR_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_OR_FIELD_TYPE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_SOURCE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_SOURCE_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_SOURCE_FIELD_TYPE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_TYPE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_TYPE_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SELECTED_REFINEMENT_VALUE_FIELD_DESCRIPTION;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

@Schema(
    title = SELECTED_REFINEMENT_TITLE,
    description = SELECTED_REFINEMENT_DESCRIPTION
)
public record SelectedRefinementDto(
    @Schema(
        description = SELECTED_REFINEMENT_NAVIGATION_NAME_FIELD_DESCRIPTION,
        requiredMode = REQUIRED,
        example = SELECTED_REFINEMENT_NAVIGATION_NAME_FIELD_EXAMPLE
    )
    @Getter
    String navigationName,

    @Schema(
        description = SELECTED_REFINEMENT_TYPE_FIELD_DESCRIPTION,
        requiredMode = REQUIRED,
        example = SELECTED_REFINEMENT_TYPE_FIELD_EXAMPLE
    )
    NavigationTypeDto type,

    @Schema(
        description = SELECTED_REFINEMENT_VALUE_FIELD_DESCRIPTION
    )
    @Getter
    String value,

    @Schema(
        description = SELECTED_REFINEMENT_LOW_FIELD_DESCRIPTION,
        type = SELECTED_REFINEMENT_LOW_FIELD_TYPE,
        example = SELECTED_REFINEMENT_LOW_FIELD_EXAMPLE
    )
    Double low,

    @Schema(
        description = SELECTED_REFINEMENT_HIGH_FIELD_DESCRIPTION,
        type = SELECTED_REFINEMENT_HIGH_FIELD_TYPE,
        example = SELECTED_REFINEMENT_HIGH_FIELD_EXAMPLE
    )
    Double high,

    @Schema(
        description = SELECTED_REFINEMENT_SOURCE_FIELD_DESCRIPTION,
        type = SELECTED_REFINEMENT_SOURCE_FIELD_TYPE,
        example = SELECTED_REFINEMENT_SOURCE_FIELD_EXAMPLE
    )
    String source,

    @Schema(
        description = SELECTED_REFINEMENT_OR_FIELD_DESCRIPTION,
        type = SELECTED_REFINEMENT_OR_FIELD_TYPE,
        example = SELECTED_REFINEMENT_OR_FIELD_EXAMPLE
    )
    Boolean or
) {

    public SelectedRefinementDto(String navigationName, NavigationTypeDto type, String value, Boolean or) {
        this(navigationName, type, value, null, null, null, or);
    }

    public SelectedRefinement toDomain() {
        var type = switch (this.type) {
            case Value -> NavigationType.VALUE;
            case Range -> NavigationType.RANGE;
        };

        return new SelectedRefinement(
            this.navigationName,
            type,
            type == NavigationType.RANGE ? new Range(low, high, null) : null,
            value,
            SOURCE_DYNAMIC.equals(source),
            this.or
        );
    }

}
