package com.groupbyinc.search.ssa.api.dto.conversation;

import io.swagger.v3.oas.annotations.media.Schema;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_USER_ANSWER_ATTRIBUTE_NAME_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_USER_ANSWER_ATTRIBUTE_VALUE_FIELD_DESCRIPTION;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

public record ProductAttributeValueDto(

    @Schema(
        description = SEARCH_REQUEST_USER_ANSWER_ATTRIBUTE_NAME_FIELD_DESCRIPTION,
        requiredMode = REQUIRED)
    String name,

    @Schema(
        description = SEARCH_REQUEST_USER_ANSWER_ATTRIBUTE_VALUE_FIELD_DESCRIPTION,
        requiredMode = REQUIRED)
    String value) {
}
