package com.groupbyinc.search.ssa.api.utils;

import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;

import com.google.cloud.retail.v2.SearchRequest;
import io.micronaut.core.util.CollectionUtils;

import java.util.List;

import static io.micronaut.core.util.StringUtils.hasText;
import static io.micronaut.core.util.StringUtils.isNotEmpty;
import static java.util.Collections.singletonList;

public class SearchUtils {

    public static final List<String> DEFAULT_PAGE_CATEGORIES = singletonList("UNSPECIFIED");

    /**
     * When pageCategories is empty for a browse event, replace with {@link SearchUtils#DEFAULT_PAGE_CATEGORIES}.
     * Condition for replacement:
     * <ul>
     *     <li>It's a browse request (search query is empty)</li>
     *     <li>Original page categories is empty</li>
     *     <li>Original page categories contains empty element</li>
     * </ul>
     *
     * @param sr search request {@link SearchRequest}
     *
     * @return List<String> page categories
     */
    public static List<String> getRequestPageCategories(SearchRequestDto sr) {
        if (isNotEmpty(sr.getQuery()) || notEmptyPageCategories(sr.getPageCategories())) {
            return sr.getPageCategories();
        }
        return DEFAULT_PAGE_CATEGORIES;
    }

    private static boolean notEmptyPageCategories(List<String> pageCategories) {
        return CollectionUtils.isNotEmpty(pageCategories) && hasText(pageCategories.getFirst());
    }

}
