package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.RecordLabel;
import com.groupbyinc.search.ssa.core.RecordSponsoredInfo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Value;

import java.util.Map;
import java.util.Set;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_LABELS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_COLLECTION_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_COLLECTION_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_ID_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_ID_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_ID_FIELD_NAME;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_METADATA_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_METADATA_FIELD_NAME;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_SPONSORED_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_SPONSORED_INFO_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_TITLE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_TITLE_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_TITLE_FIELD_NAME;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_URL_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_URL_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RECORD_URL_FIELD_NAME;

@Schema(
    title = RECORD_TITLE,
    description = RECORD_DESCRIPTION
)
@Value
public class RecordDto {

    @Schema(
        name = RECORD_ID_FIELD_NAME,
        description = RECORD_ID_FIELD_DESCRIPTION,
        example = RECORD_ID_FIELD_EXAMPLE
    )
    @JsonProperty("_id")
    String id;

    @Schema(
        name = RECORD_URL_FIELD_NAME,
        description = RECORD_URL_FIELD_DESCRIPTION,
        example = RECORD_URL_FIELD_EXAMPLE
    )
    @JsonProperty("_u")
    String url;

    @Schema(
        name = RECORD_TITLE_FIELD_NAME,
        description = RECORD_TITLE_FIELD_DESCRIPTION,
        example = RECORD_TITLE_FIELD_EXAMPLE
    )
    @JsonProperty("_t")
    String title;

    @Schema(
        description = RECORD_COLLECTION_FIELD_DESCRIPTION,
        example = RECORD_COLLECTION_FIELD_EXAMPLE
    )
    String collection;

    @Schema(
        name = RECORD_METADATA_FIELD_NAME,
        description = RECORD_METADATA_FIELD_DESCRIPTION
    )
    @JsonProperty("allMeta")
    Map<String, Object> metadata;

    @Hidden
    @Deprecated(forRemoval = true)
    RecordLabel label;

    @Schema(
        description = RECORD_SPONSORED_FIELD_DESCRIPTION,
        nullable = true
    )
    Boolean sponsored;

    @Schema(
        description = RECORD_SPONSORED_INFO_FIELD_DESCRIPTION,
        nullable = true
    )
    RecordSponsoredInfo sponsoredInfo;

    @Hidden
    @JsonIgnore
    String primaryProductId;

    @Schema(
        description = RECORD_LABELS_FIELD_DESCRIPTION,
        nullable = true
    )
    Set<RecordLabel> labels;

    public static RecordDto fromDomain(Record record) {
        return new RecordDto(
            record.getId(),
            record.getUrl(),
            record.getTitle(),
            record.getCollection(),
            record.getMetadata(),
            record.getLabel(),
            record.getSponsored(),
            record.getSponsoredInfo(),
            record.getPrimaryProductId(),
            record.getLabels()
        );
    }

}
