package com.groupbyinc.search.ssa.api.filter;

import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.application.logging.LogSamplingConfig;
import com.groupbyinc.search.ssa.application.logging.LoggingContext;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.features.LDContextBuilder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.launchdarkly.sdk.LDContext;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.annotation.Order;
import io.micronaut.core.propagation.MutablePropagatedContext;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.RequestFilter;
import io.micronaut.http.annotation.ServerFilter;
import io.micronaut.http.filter.FilterPatternStyle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import static com.groupbyinc.search.ssa.api.filter.FilterPriority.LOG_SAMPLING;
import static com.groupbyinc.search.ssa.api.utils.HttpUtils.parseRequestBody;
import static com.groupbyinc.search.ssa.application.logging.LoggingContext.APP_NAME_MDC_KEY;
import static com.groupbyinc.search.ssa.application.logging.LoggingContext.REQUEST_SAMPLED_MDC_KEY;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.LOG_SAMPLING_CONFIG;
import static com.groupbyinc.search.ssa.util.Constants.GROUPBY_CUSTOMER_ID_HEADER;

@Slf4j
@Order(LOG_SAMPLING)
@RequiredArgsConstructor
@Requires(notEnv = "test")
@SuppressWarnings("unused")
@ServerFilter(patternStyle = FilterPatternStyle.REGEX, value = "/api/search(/)?")
public class LogSamplingRequestFilter {

    @Value("${micronaut.application.name}")
    private String appName;

    @Value("${default-log-sampling-rate:1.0}")
    private double defaultSamplingRate;

    private final ObjectMapper objectMapper;
    private final FeaturesManager featuresManager;

    @RequestFilter
    public void setMdc(HttpRequest<?> request,
                       MutablePropagatedContext mutablePropagatedContext,
                       @Body byte[] body) {
        var searchRequestDto = parseRequestBody(objectMapper, body, SearchRequestDto.class, log);

        boolean isSampled = true;

        if (searchRequestDto.isPresent() && !searchRequestDto.get().isDebug()) {
            var customerId = request.getHeaders().get(GROUPBY_CUSTOMER_ID_HEADER);

            var logSamplingConfig = featuresManager.getObjectFlagConfiguration(
                buildLDContextFromRequest(searchRequestDto.get(), customerId),
                LOG_SAMPLING_CONFIG,
                LogSamplingConfig.class,
                LogSamplingConfig.DEFAULT
            );

            double samplingRate = logSamplingConfig.getRateForKeyOrDefault(appName)
                .orElse(defaultSamplingRate);

            isSampled = Math.random() < samplingRate;
        }
        MDC.put(REQUEST_SAMPLED_MDC_KEY, isSampled ? Boolean.TRUE.toString() : Boolean.FALSE.toString());
        MDC.put(APP_NAME_MDC_KEY, appName);
        LoggingContext.propagateMDC(mutablePropagatedContext);
    }

    private LDContext buildLDContextFromRequest(SearchRequestDto searchRequestDto, String customerId) {
        return LDContextBuilder
            .builder(customerId, featuresManager.getLdContextEnvironment())
            .setArea(searchRequestDto.getArea())
            .setCollection(searchRequestDto.getCollection())
            .build();
    }

}
