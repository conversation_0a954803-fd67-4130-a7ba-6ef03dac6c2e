package com.groupbyinc.search.ssa.api.validation.biasing;

import com.groupbyinc.search.ssa.api.dto.BiasDto;
import com.groupbyinc.search.ssa.api.dto.NumericContentDto;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.features.LDContextBuilder;

import io.micronaut.core.util.CollectionUtils;
import io.micronaut.validation.validator.constraints.ConstraintValidatorContext;
import lombok.AllArgsConstructor;

import static com.groupbyinc.search.ssa.api.validation.biasing.message.BiasValidationMessages.BIAS_BOOST_NULL_TEMPLATE;
import static com.groupbyinc.search.ssa.api.validation.biasing.message.BiasValidationMessages.BIAS_NUMERIC_CONTENT_BOTH_EMPTY_TEMPLATE;
import static com.groupbyinc.search.ssa.api.validation.biasing.message.BiasValidationMessages.BIAS_NUMERIC_CONTENT_EMPTY_TEMPLATE;
import static com.groupbyinc.search.ssa.api.validation.biasing.message.BiasValidationMessages.BIAS_NUMERIC_TEXT_CONTENT_FILLED_TEMPLATE;
import static com.groupbyinc.search.ssa.api.validation.biasing.message.BiasValidationMessages.BIAS_STRENGTH_RANGE_TEMPLATE;
import static com.groupbyinc.search.ssa.api.validation.biasing.message.BiasValidationMessages.BIAS_TEXT_CONTENT_EMPTY_TEMPLATE;
import static com.groupbyinc.search.ssa.api.validation.biasing.message.BiasValidationMessages.BIAS_TEXT_NUMERIC_CONTENT_FILLED_TEMPLATE;
import static com.groupbyinc.search.ssa.api.validation.biasing.message.BiasValidationMessages.BIAS_TYPE_NULL_TEMPLATE;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_NEW_BIASING;

@AllArgsConstructor
public class BiasValidationService {

    private final FeaturesManager featuresManager;

    /**
     * Used to validate bias object
     *
     * @param biasDto to validate
     * @param context object that needs to be populated with a related error message in case if attribute not valid.
     * @return {@code true} if passed model valid, {@code false} otherwise.
     */
    public boolean validateBiasModel(BiasDto biasDto, ConstraintValidatorContext context) {
        if (!isNewBiasingEnabled()) {
            if (biasDto.getContent() == null) {
                // content should be either some text or an empty string but not null
                context.messageTemplate(BIAS_TEXT_CONTENT_EMPTY_TEMPLATE);
                return false;
            }

            return true;
        }

        if (biasDto.getStrength() == null && biasDto.getStrengthValue() == null) {
            context.messageTemplate(BIAS_BOOST_NULL_TEMPLATE);
            return false;
        }

        if (biasDto.getStrengthValue() != null) {
            if (biasDto.getStrengthValue() < -1 || biasDto.getStrengthValue() > 1) {
                context.messageTemplate(BIAS_STRENGTH_RANGE_TEMPLATE);
                return false;
            }
        }

        var type = biasDto.getType();
        if (type == null) {
            context.messageTemplate(BIAS_TYPE_NULL_TEMPLATE);
            return false;
        }

        switch (type) {
            case TEXTUAL -> {
                if (biasDto.getContent() != null) {

                    if (biasDto.getNumericContent() == null) {
                        return true;
                    }

                    context.messageTemplate(BIAS_TEXT_NUMERIC_CONTENT_FILLED_TEMPLATE);
                    return false;
                }

                context.messageTemplate(BIAS_TEXT_CONTENT_EMPTY_TEMPLATE);
                return false;
            }
            case NUMERIC -> {
                if (biasDto.getNumericContent() != null) {

                    if (biasDto.getContent() == null) {
                        return true;
                    }

                    context.messageTemplate(BIAS_NUMERIC_TEXT_CONTENT_FILLED_TEMPLATE);
                    return false;
                }

                context.messageTemplate(BIAS_NUMERIC_CONTENT_EMPTY_TEMPLATE);
                return false;
            }
        }

        return true;
    }

    /**
     * Used to validate a bias numeric content object, when a feature flag is enabled:
     * - invalid if neither value nor range exists
     * - valid otherwise
     *
     * @param content numeric content to validate
     * @param context object that needs to be populated with a related error message in case if content not valid.
     * @return {@code true} if passed content valid, {@code false} otherwise.
     */
    public boolean validateNumericContent(NumericContentDto content, ConstraintValidatorContext context) {
        if (isNewBiasingEnabled()) {
            var isValuePresent = CollectionUtils.isNotEmpty(content.getValues());
            var isRangePresent = CollectionUtils.isNotEmpty(content.getRanges());

            if (!isValuePresent && !isRangePresent) {
                context.messageTemplate(BIAS_NUMERIC_CONTENT_BOTH_EMPTY_TEMPLATE);
                return false;
            }
        }

        return true;
    }

    private boolean isNewBiasingEnabled() {
        return featuresManager.getBooleanFlagConfiguration(
            LDContextBuilder.builder().build(),
            ENABLE_NEW_BIASING
        );
    }
}
