package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.core.template.Template;

import com.groupbyinc.search.ssa.core.template.zone.TemplateZoneBase;
import com.groupbyinc.search.ssa.core.template.zone.TemplateZoneContent;
import com.groupbyinc.search.ssa.core.template.zone.TemplateZoneHTML;
import com.groupbyinc.search.ssa.core.template.zone.TemplateZoneRichContent;
import com.groupbyinc.search.ssa.core.trigger.TriggerSet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Value;

import java.util.ArrayList;
import java.util.List;

import static com.groupbyinc.search.ssa.core.zone.ZoneType.CONTENT;
import static com.groupbyinc.search.ssa.core.zone.ZoneType.HTML;
import static com.groupbyinc.search.ssa.core.zone.ZoneType.RICH_CONTENT;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TEMPLATE_RULE_ID_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TEMPLATE_RULE_ID_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TEMPLATE_TRIGGER_SET_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TEMPLATE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TEMPLATE_NAME_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TEMPLATE_NAME_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TEMPLATE_RULE_NAME_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TEMPLATE_RULE_NAME_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TEMPLATE_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TEMPLATE_ZONES_FIELD_DESCRIPTION;

@Schema(
    title = TEMPLATE_TITLE,
    description = TEMPLATE_DESCRIPTION
)
@Value
public class TemplateDto {

    @Schema(
        description = TEMPLATE_NAME_FIELD_DESCRIPTION,
        example = TEMPLATE_NAME_FIELD_EXAMPLE
    )
    String name;

    @Schema(
        description = TEMPLATE_RULE_NAME_FIELD_DESCRIPTION,
        example = TEMPLATE_RULE_NAME_FIELD_EXAMPLE
    )
    String ruleName;

    @Schema(
        description = TEMPLATE_RULE_ID_FIELD_DESCRIPTION,
        example = TEMPLATE_RULE_ID_FIELD_EXAMPLE
    )
    Integer ruleId;

    @Schema(
        description = TEMPLATE_TRIGGER_SET_FIELD_DESCRIPTION
    )
    TriggerSet triggerSet;

    @Schema(description = TEMPLATE_ZONES_FIELD_DESCRIPTION)
    List<ZoneDto> zones;

    public TemplateDto(String name, String ruleName, Integer ruleId, TriggerSet triggerSet) {
        this.name = name;
        this.ruleName = ruleName;
        this.ruleId = ruleId;
        this.triggerSet = triggerSet;
        this.zones = new ArrayList<>();
    }

    public static TemplateDto fromDomain(Template template) {
        var templateDto = new TemplateDto(
            template.getName(),
            template.getRuleName(),
            template.getRuleId(),
            template.getTriggerSet()
        );

        template.getZones().forEach(z -> {
            var zone = resolveZone(z);
            if (zone != null) {
                templateDto.addZone(zone);
            }
        });

        return templateDto;
    }

    private static ZoneDto resolveZone(TemplateZoneBase templateZoneBase) {

        return switch (templateZoneBase.getType()) {
            case CONTENT -> ZoneDto.ofContent(
                templateZoneBase.getName(),
                ZoneDtoType.fromDomain(CONTENT),
                ((TemplateZoneContent) templateZoneBase).getContent()
            );
            case RICH_CONTENT -> ZoneDto.ofRichContent(
                templateZoneBase.getName(),
                ZoneDtoType.fromDomain(RICH_CONTENT),
                ((TemplateZoneRichContent) templateZoneBase).getRichContent()
            );
            case HTML -> ZoneDto.ofHTML(
                templateZoneBase.getName(),
                ZoneDtoType.fromDomain(HTML),
                ((TemplateZoneHTML) templateZoneBase).getHtmlContentDecoded()
            );

            // For new types.
            default -> null;
        };
    }

    private void addZone(ZoneDto zone) {
        zones.add(zone);
    }
}
