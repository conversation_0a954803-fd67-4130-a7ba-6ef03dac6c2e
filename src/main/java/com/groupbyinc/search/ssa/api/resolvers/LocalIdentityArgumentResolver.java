package com.groupbyinc.search.ssa.api.resolvers;

import com.groupbyinc.search.ssa.core.authz.Identity;
import com.groupbyinc.search.ssa.core.authz.Role;
import io.micronaut.context.annotation.Replaces;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.convert.ArgumentConversionContext;
import io.micronaut.core.type.Argument;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.bind.binders.TypedRequestArgumentBinder;
import jakarta.inject.Singleton;

import java.util.Optional;
import java.util.Set;

import static com.groupbyinc.search.ssa.util.Constants.GROUPBY_CUSTOMER_ID_HEADER;
import static io.micronaut.core.bind.ArgumentBinder.BindingResult.UNSATISFIED;

/**
 * Infers and resolves arguments of type {@link Identity} on API controllers methods for local development. This is a workaround for local development
 * and should <b>never</b> find its way into any deployed environment.
 */
@Singleton
@Requires(env = {"local"})
@Replaces(bean = IdentityArgumentResolver.class)
class LocalIdentityArgumentResolver implements TypedRequestArgumentBinder<Identity> {
    private final boolean singleTenant;
    private final String tenantName;

    public LocalIdentityArgumentResolver(@Value("${singletenant.enabled}") boolean singleTenant,
                                         @Value("${singletenant.name}") String tenantName) {
        this.singleTenant = singleTenant;
        this.tenantName = tenantName;
    }

    @Override
    public Argument<Identity> argumentType() {
        return Argument.of(Identity.class);
    }

    /**
     * Infers the value for an argument of type {@link Identity} based on the GroupBy Customer ID header.
     *
     * @param context     Argument conversion context which contains info about the argument being resolved and essential conversion utilities.
     * @param httpRequest Original HTTP request that is routed to the controller method for which the argument is being resolved.
     * @return Successful binding result if the identify can be inferred based on the GroupBy Customer ID header, or unsatisfied binding result
     * otherwise.
     */
    @Override
    public BindingResult<Identity> bind(ArgumentConversionContext<Identity> context, HttpRequest<?> httpRequest) {

        var identity = httpRequest.getHeaders()
            .findFirst(GROUPBY_CUSTOMER_ID_HEADER)
            .or(() -> Optional.ofNullable(singleTenant ? tenantName : null))
            .map(this::extractIdentity)
            .orElse(null);

        //noinspection unchecked
        return identity != null
            ? () -> Optional.of(identity)
            : UNSATISFIED;
    }

    private Identity extractIdentity(String customerId) {

        return new Identity(
            customerId,
            customerId,
            Set.of(Role.CUSTOMER_DEFAULT)
        );
    }
}
