package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.application.core.search.productvisibility.Score;

import io.swagger.v3.oas.annotations.media.Schema;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.SCORE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SCORE_MAX_ALLOWED_CAPPED_SCORE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SCORE_MAX_POSSIBLE_SCORE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SCORE_MULTIPLIER;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SCORE_NEW;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SCORE_ORIGINAL;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SCORE_TARGET;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SCORE_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SCORE_TOTAL;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SCORE_VISIBILITY_CAP;

@Schema(
    title = SCORE_TITLE,
    description = SCORE_DESCRIPTION
)
public record VisibilityScoreDto(
    @Schema(description = SCORE_TOTAL)
    double totalScore,
    @Schema(description = SCORE_ORIGINAL)
    double originalScore,
    @Schema(description = SCORE_NEW)
    double newScore,
    @Schema(description = SCORE_TARGET)
    double targetScore,
    @Schema(description = SCORE_MULTIPLIER)
    double appliedMultiplier,
    @Schema(description = SCORE_VISIBILITY_CAP)
    double visibilityCap,
    @Schema(description = SCORE_MAX_POSSIBLE_SCORE)
    double maxPossibleScore,
    @Schema(description = SCORE_MAX_ALLOWED_CAPPED_SCORE)
    double maxAllowedScore
) {
    public static VisibilityScoreDto fromScore(Score score) {
        if (score == null) {
            return null;
        }
        return new VisibilityScoreDto(
            score.getTotalScore(),
            score.getScore(),
            score.getNewScore(),
            score.getTargetScore(),
            score.getAppliedMultiplier(),
            score.getVisibilityCap(),
            score.getMaxPossibleScore(),
            score.getMaxAllowedScore()
        );
    }

}
