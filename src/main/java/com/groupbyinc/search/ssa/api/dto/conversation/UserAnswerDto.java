package com.groupbyinc.search.ssa.api.dto.conversation;

import io.swagger.v3.oas.annotations.media.Schema;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_SELECTED_ANSWER_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_TEXT_ANSWER_FIELD_DESCRIPTION;

public record UserAnswerDto(

    @Schema(
        description = SEARCH_REQUEST_SELECTED_ANSWER_FIELD_DESCRIPTION,
        nullable = true)
    SelectedAnswerDto selectedAnswer,

    @Schema(
        description = SEARCH_REQUEST_TEXT_ANSWER_FIELD_DESCRIPTION,
        nullable = true)
    String textAnswer) {

    public UserAnswerDto(SelectedAnswerDto selectedAnswer) {
        this(selectedAnswer, null);
    }

    public UserAnswerDto(String textAnswer) {
        this(null, textAnswer);
    }
}
