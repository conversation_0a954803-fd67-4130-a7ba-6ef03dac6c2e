package com.groupbyinc.search.ssa.api.filter;

import com.groupbyinc.search.ssa.application.logging.LoggingContext;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micronaut.context.annotation.Requires;
import io.micronaut.core.annotation.Order;
import io.micronaut.core.propagation.MutablePropagatedContext;
import io.micronaut.http.HttpMethod;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.MutableHttpResponse;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.RequestFilter;
import io.micronaut.http.annotation.ResponseFilter;
import io.micronaut.http.annotation.ServerFilter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.Map;
import java.util.UUID;

import static com.groupbyinc.search.ssa.api.filter.FilterPriority.TRACING;
import static com.groupbyinc.search.ssa.application.logging.LoggingContext.AREA_MDC_KEY;
import static com.groupbyinc.search.ssa.application.logging.LoggingContext.COLLECTION_MDC_KEY;
import static com.groupbyinc.search.ssa.application.logging.LoggingContext.CUSTOMER_MDC_KEY;
import static com.groupbyinc.search.ssa.application.logging.LoggingContext.REQUEST_ID;
import static com.groupbyinc.search.ssa.util.Constants.GROUPBY_CUSTOMER_ID_HEADER;

@Slf4j
@Order(TRACING)
@RequiredArgsConstructor
@Requires(notEnv = "test")
@SuppressWarnings("unused")
@ServerFilter(value = "/*/search/**")
public class TracingFilter {
    private final ObjectMapper mapper;

    @RequestFilter
    public void doFilter(HttpRequest<?> request, MutablePropagatedContext propagatedContext, @Body byte[] body) {
        setRequestTrackingContext(request);
        setCustomerTrackingContext(request, body);

        LoggingContext.propagateMDC(propagatedContext);
    }

    @ResponseFilter
    public void handleResponse(HttpRequest<?> request, MutableHttpResponse<?> response) {
        MDC.clear();
    }

    private void setRequestTrackingContext(HttpRequest<?> request) {
        var reqId = UUID.randomUUID().toString();
        request.setAttribute(REQUEST_ID, reqId);
        MDC.put(REQUEST_ID, reqId);
    }

    private void setCustomerTrackingContext(HttpRequest<?> request, byte[] body) {
        // customer always in headers
        var customerId = request.getHeaders().get(GROUPBY_CUSTOMER_ID_HEADER);
        if (customerId != null) {
            MDC.put(CUSTOMER_MDC_KEY, customerId);
        }
        if (request.getMethod() == HttpMethod.POST) {
            setSearchMdc(body);
        } else {
            setPdpMdc(request);
        }
    }

    private void setSearchMdc(byte[] body) {
        var reqMap = toMap(body);
        if (reqMap.get(COLLECTION_MDC_KEY) instanceof String collection) {
            MDC.put(COLLECTION_MDC_KEY, collection);
        }
        if (reqMap.get(AREA_MDC_KEY) instanceof String area) {
            MDC.put(AREA_MDC_KEY, area);
        }
    }

    private void setPdpMdc(HttpRequest<?> request) {
        if (request.getParameters().get(COLLECTION_MDC_KEY) instanceof String collection) {
            MDC.put(COLLECTION_MDC_KEY, collection);
        }
    }

    private Map<String, Object> toMap(byte[] body) {
        try (InputStream inputStream = new ByteArrayInputStream(body)) {
            return mapper.readValue(inputStream, new TypeReference<>() {});
        } catch (IOException e) {
            log.error("Failed to parse request body", e);
        }
        return Collections.emptyMap();
    }

}

