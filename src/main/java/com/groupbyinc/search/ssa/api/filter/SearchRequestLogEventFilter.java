package com.groupbyinc.search.ssa.api.filter;

import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.micronaut.context.annotation.Requires;
import io.micronaut.core.annotation.Order;
import io.micronaut.core.propagation.MutablePropagatedContext;
import io.micronaut.core.util.StringUtils;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.RequestFilter;
import io.micronaut.http.annotation.ServerFilter;
import io.micronaut.http.filter.FilterPatternStyle;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.groupbyinc.search.ssa.api.filter.FilterPriority.LOG_EVENTS;
import static com.groupbyinc.search.ssa.api.utils.HttpUtils.parseRequestBody;

@Slf4j
@Order(LOG_EVENTS)
@RequiredArgsConstructor
@Requires(notEnv = "test")
@SuppressWarnings("unused")
@ServerFilter(patternStyle = FilterPatternStyle.REGEX, value = "/api/search(/)?")
public class SearchRequestLogEventFilter extends RequestLogEventWriter {
    private final ObjectMapper objectMapper;

    @RequestFilter
    public void logEvent(
        HttpRequest<?> request,
        MutablePropagatedContext mutablePropagatedContext,
        @Body byte[] body
    ) {

        writeLog(request, body);
    }

    @Override
    protected String logEventType() {
        return EventType.QUERY.name();
    }

    @Override
    protected String logEventSubType(HttpRequest<?> request, byte[] body) {
        SearchRequestDto requestBody = extractSearchRequest(objectMapper, request, body);
        if (StringUtils.isEmpty(requestBody.getQuery())) {
            return EventSubtype.BROWSE.name();
        }

        return EventSubtype.PRODUCT_SEARCH.name();
    }

    private static SearchRequestDto extractSearchRequest(
        ObjectMapper mapper,
        HttpRequest<?> httpRequest,
        byte[] body
    ) {

        return parseRequestBody(mapper, body, SearchRequestDto.class, log)
            .orElseThrow(() -> new IllegalArgumentException("Failed to parse request body into SearchRequestDto"));
    }
}
