package com.groupbyinc.search.ssa.api.validation;

public interface Constants {

    /**
     * Special regexp to validate product id. Products ob Google side must have
     * IDs in this format.
     */
    String PRODUCT_ID_REGEX = "^[A-Za-z0-9_-]+$";

    /**
     * Min position number that a product can be pinned to the top.
     */
    int PIN_TO_TOP_MIN_POSITION = 1;

    /**
     * Max position number that a product can be pinned to the top.
     */
    int PIN_TO_TOP_MAX_POSITION = 24;

}
