package com.groupbyinc.search.ssa.api.validation.context;

import com.groupbyinc.search.ssa.api.RequestContext;

import java.util.Optional;

@FunctionalInterface
public interface ConfigurationResolver<T> {

    /**
     * Resolve configuration based on passed request context.
     *
     * @param context request metadata.
     *
     * @return configurations resolved by the passed request context
     */
    Optional<T> resolve(RequestContext context);

}
