package com.groupbyinc.search.ssa.api.internal;

import com.groupbyinc.search.ssa.api.dto.product.ProductDto;
import com.groupbyinc.search.ssa.application.logging.LoggingContext;
import com.groupbyinc.search.ssa.application.core.pdp.ProductSearchStrategy;
import com.groupbyinc.search.ssa.metrics.opentelemetry.MeterRegistry;

import io.github.resilience4j.micronaut.annotation.Bulkhead;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Get;
import io.micronaut.http.annotation.QueryValue;
import io.micronaut.scheduling.TaskExecutors;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_TAG;

@Slf4j
@Controller("/internal/search/product")
@RequiredArgsConstructor
@Tag(name = PRODUCT_TAG)
@ExecuteOn(TaskExecutors.BLOCKING)
public class InternalProductApi {
    private final MeterRegistry meterRegistry;
    private final ProductSearchStrategy productSearchStrategy;

    @Hidden
    @Bulkhead(name = "pdp")
    @Get(consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public HttpResponse<ProductDto> getByProductIds(
        @QueryValue String productId,
        @QueryValue(value = "variantIds") @Nullable List<String> requestVariantIds
    ) {
        var response = Optional.ofNullable(meterRegistry.act(() -> {
            var context = getRequestContext();
            LoggingContext.set(context);

            return productSearchStrategy
                .getStrategyFor(context)
                .getProductDetails(productId, requestVariantIds);
        }));

        return response
            .flatMap(opt -> opt.map(product -> HttpResponse.ok(new ProductDto(product))))
            .orElseGet(HttpResponse::notFound);
    }

}
