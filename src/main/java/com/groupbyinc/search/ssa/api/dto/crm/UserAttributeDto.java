package com.groupbyinc.search.ssa.api.dto.crm;

import com.groupbyinc.search.ssa.core.crm.UserAttribute;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.util.List;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_USER_ATTRIBUTE_KEY_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_USER_ATTRIBUTE_KEY_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_USER_ATTRIBUTE_VALUES_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_USER_ATTRIBUTE_VALUES_EXAMPLE;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

@Introspected
public record UserAttributeDto(
    @Schema(
        description = SEARCH_REQUEST_USER_ATTRIBUTE_KEY_DESCRIPTION,
        example = SEARCH_REQUEST_USER_ATTRIBUTE_KEY_EXAMPLE,
        requiredMode = REQUIRED)
    @NotNull
    String key,

    @Schema(
        description = SEARCH_REQUEST_USER_ATTRIBUTE_VALUES_DESCRIPTION,
        example = SEARCH_REQUEST_USER_ATTRIBUTE_VALUES_EXAMPLE,
        requiredMode = REQUIRED)
    @NotNull
    List<String> values
) {

    public UserAttribute toDomain() {
        return new UserAttribute(key, values);
    }
}
