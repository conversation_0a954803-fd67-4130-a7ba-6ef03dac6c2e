package com.groupbyinc.search.ssa.api.resolvers;

import com.groupbyinc.search.ssa.core.authz.Identity;
import com.groupbyinc.search.ssa.core.authz.Role;

import io.micronaut.context.annotation.Context;
import io.micronaut.core.convert.ArgumentConversionContext;
import io.micronaut.core.type.Argument;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.bind.binders.TypedRequestArgumentBinder;
import io.micronaut.security.authentication.Authentication;

import java.util.List;
import java.util.Optional;

import static io.micronaut.core.bind.ArgumentBinder.BindingResult.UNSATISFIED;
import static java.util.stream.Collectors.toUnmodifiableSet;

/**
 * Infers and resolves arguments of type {@link Identity} on API controllers methods.
 */
@Context
class IdentityArgumentResolver implements TypedRequestArgumentBinder<Identity> {

    @Override
    public Argument<Identity> argumentType() {
        return Argument.of(Identity.class);
    }

    /**
     * Infers the value for an argument of type {@link Identity} based on the authentication context of a request. The authentication context is
     * expected to be backed by a set of claims on a verified JWT.
     *
     * @param context     Argument conversion context which contains info about the argument being resolved and essential conversion utilities.
     * @param httpRequest Original HTTP request that is routed to the controller method for which the argument is being resolved.
     *
     * @return Successful binding result if the identify can be inferred based on the authentication context, or unsatisfied binding result otherwise.
     */
    @Override
    public BindingResult<Identity> bind(ArgumentConversionContext<Identity> context, HttpRequest<?> httpRequest) {

        var identity = httpRequest.getUserPrincipal(Authentication.class)
            .map(this::extractIdentity)
            .orElse(null);

        //noinspection unchecked
        return identity != null
            ? () -> Optional.of(identity)
            : UNSATISFIED;
    }

    private Identity extractIdentity(Authentication authentication) {

        var claims = authentication.getAttributes();

        @SuppressWarnings("unchecked")
        var roleNames = (List<String>) claims.get("roles");
        var roles = roleNames.stream().map(Role::of).collect(toUnmodifiableSet());

        return new Identity(
            claims.get("sub").toString(),
            claims.get("company").toString(),
            roles
        );
    }
}
