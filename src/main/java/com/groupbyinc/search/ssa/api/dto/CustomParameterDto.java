package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.core.CustomParameter;

import io.swagger.v3.oas.annotations.media.Schema;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.CUSTOM_PARAMETER_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CUSTOM_PARAMETER_KEY_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CUSTOM_PARAMETER_KEY_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CUSTOM_PARAMETER_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CUSTOM_PARAMETER_VALUE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CUSTOM_PARAMETER_VALUE_FIELD_EXAMPLE;

@Schema(
    title = CUSTOM_PARAMETER_TITLE,
    description = CUSTOM_PARAMETER_DESCRIPTION
)
public record CustomParameterDto(
    @Schema(
        description = CUSTOM_PARAMETER_KEY_FIELD_DESCRIPTION,
        required = true,
        example = CUSTOM_PARAMETER_KEY_FIELD_EXAMPLE)
    String key,

    @Schema(
        description = CUSTOM_PARAMETER_VALUE_FIELD_DESCRIPTION,
        required = true,
        example = CUSTOM_PARAMETER_VALUE_FIELD_EXAMPLE
    )
    String value) {

    public CustomParameter toDomain() {
        return new CustomParameter(key, value);
    }

}
