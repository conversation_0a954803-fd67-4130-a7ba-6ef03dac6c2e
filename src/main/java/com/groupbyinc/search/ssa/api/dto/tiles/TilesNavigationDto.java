package com.groupbyinc.search.ssa.api.dto.tiles;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_APPLIED_TILES;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_TILE_NAVIGATION_REQUESTED_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TILE_NAVIGATION_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TILE_NAVIGATION_TITLE;

/**
 * Represents a tile as it appears in a search result.
 */
@Schema(
    title = TILE_NAVIGATION_TITLE,
    description = TILE_NAVIGATION_DESCRIPTION
)
public record TilesNavigationDto(
    @Schema(
        description = SEARCH_REQUEST_TILE_NAVIGATION_REQUESTED_DESCRIPTION,
        nullable = true
    )
    Boolean tileNavigationRequested,

    @Schema(
        description = SEARCH_REQUEST_APPLIED_TILES,
        nullable = true
    )
    List<ProductAttributeValueDto> appliedTiles
) {

}
