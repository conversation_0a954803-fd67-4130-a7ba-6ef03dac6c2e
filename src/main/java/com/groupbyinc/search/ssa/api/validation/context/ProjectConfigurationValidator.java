package com.groupbyinc.search.ssa.api.validation.context;

import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.core.project.ProjectConfiguration;

import static com.groupbyinc.search.ssa.api.validation.context.RequestContextValidationException.collectionNotFound;

/**
 * Validate that passed collection valid
 */
public class ProjectConfigurationValidator implements ContextValidator {

    private final ConfigurationResolver<ProjectConfiguration> projectConfigurationResolver;

    public ProjectConfigurationValidator(ConfigurationResolver<ProjectConfiguration> projectConfigurationResolver) {
        this.projectConfigurationResolver = projectConfigurationResolver;
    }

    @Override
    public void validate(RequestContext context) throws RequestContextValidationException {
        projectConfigurationResolver
            .resolve(context)
            .orElseThrow(() -> collectionNotFound(context.getCollection()));
    }

}
