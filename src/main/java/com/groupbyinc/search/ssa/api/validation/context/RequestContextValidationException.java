package com.groupbyinc.search.ssa.api.validation.context;

import com.groupbyinc.search.ssa.core.Merchandiser;

/**
 * Custom runtime exception that should be thrown when a search pre-condition fails.
 */
public class RequestContextValidationException extends RuntimeException {

    private RequestContextValidationException(String message) {
        super(message);
    }

    public static RequestContextValidationException tenantNotFound(Merchandiser merchandiser) {
        return new RequestContextValidationException(
            "Tenant '%s' could not be found.".formatted(merchandiser.merchandiserId())
        );
    }

    public static RequestContextValidationException tenantNotActive(Merchandiser merchandiser) {
        return new RequestContextValidationException(
            "Tenant '%s' is not active.".formatted(merchandiser.merchandiserId())
        );
    }

    public static RequestContextValidationException areaNotFound(String area, String collection) {
        return new RequestContextValidationException(
            "Area '%s' could not be found or can not be used with passed collection: '%s'".formatted(area, collection)
        );
    }

    public static RequestContextValidationException collectionNotFound(String collection) {
        return new RequestContextValidationException(
            "Collection '%s' could not be found.".formatted(collection)
        );
    }

}
