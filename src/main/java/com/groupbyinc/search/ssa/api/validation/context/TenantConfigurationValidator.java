package com.groupbyinc.search.ssa.api.validation.context;

import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.core.tenant.TenantConfiguration;

import static com.groupbyinc.search.ssa.api.validation.context.RequestContextValidationException.tenantNotActive;
import static com.groupbyinc.search.ssa.api.validation.context.RequestContextValidationException.tenantNotFound;

/**
 * Validator that requires the tenant to exist and be active.
 */
public class TenantConfigurationValidator implements ContextValidator {

    private final ConfigurationResolver<TenantConfiguration> tenantConfigurationResolver;

    public TenantConfigurationValidator(ConfigurationResolver<TenantConfiguration> tenantConfigurationResolver) {
        this.tenantConfigurationResolver = tenantConfigurationResolver;
    }

    @Override
    public void validate(RequestContext context) throws RequestContextValidationException {
        var tenantConfiguration = tenantConfigurationResolver
            .resolve(context)
            .orElseThrow(() -> tenantNotFound(context.getMerchandiser()));

        if (!tenantConfiguration.enabled()) {
            throw tenantNotActive(context.getMerchandiser());
        }
    }

}
