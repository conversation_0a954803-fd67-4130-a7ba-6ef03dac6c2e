package com.groupbyinc.search.ssa.api.filter;

import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.api.dto.FacetSearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.crm.UserAttributeDto;
import com.groupbyinc.search.ssa.api.validation.context.RequestContextValidator;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.RequestOptions;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.retail.util.v2alpha.UserAttributesHelper;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.annotation.Order;
import io.micronaut.core.propagation.MutablePropagatedContext;
import io.micronaut.http.HttpMethod;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.RequestFilter;
import io.micronaut.http.annotation.ServerFilter;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.security.NoSuchAlgorithmException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.groupbyinc.search.ssa.api.filter.FilterPriority.REQUEST_CONTEXT;
import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestId;
import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getSkipCacheHeader;
import static com.groupbyinc.search.ssa.api.utils.HttpUtils.parseRequestBody;
import static com.groupbyinc.search.ssa.util.Constants.GROUPBY_CUSTOMER_ID_HEADER;
import static com.groupbyinc.search.ssa.util.StringUtils.getHash;

@Slf4j
@Setter
@Order(REQUEST_CONTEXT)
@RequiredArgsConstructor
@SuppressWarnings("unused")
@ServerFilter(value = "/*/search/**")
public class RequestContextFilter {

    private static final String COLLECTION_REQUEST_PARAM = "collection";

    @Value("${security.hash.salt}")
    private String salt;

    @Value("${singletenant.name}")
    private String tenantName;

    @Value("${singletenant.enabled}")
    private boolean singleTenant;

    private final ObjectMapper objectMapper;
    private final FeaturesManager featuresManager;
    private final RequestContextValidator requestContextValidator;
    private final UserAttributesHelper userAttributesHelper;
    private final Validator validator;

    @RequestFilter
    public void addContextToRequest(HttpRequest<?> request,
                                    @Body byte[] body,
                                    MutablePropagatedContext mutablePropagatedContext) {
        if (request.getMethod().equals(HttpMethod.GET)) {
            mutablePropagatedContext.add(buildContextFromGetRequest(request));
        }

        if (request.getMethod().equals(HttpMethod.POST)) {
            var context = buildContextFromPostRequest(request, body);

            requestContextValidator.validate(context);

            mutablePropagatedContext.add(context);
        }
    }

    private RequestContext buildContextFromGetRequest(HttpRequest<?> httpRequest) {
        var collection = httpRequest.getParameters().get(COLLECTION_REQUEST_PARAM);

        if (collection == null) {
            throw new IllegalArgumentException("Missing 'collection' parameter");
        }

        return new RequestContext(
            buildMerchandiser(httpRequest),
            getRequestId(httpRequest),
            collection,
            featuresManager.getLdContextEnvironment(),
            new RequestOptions(
                getSkipCacheHeader(httpRequest),
                false
            )
        );
    }

    private RequestContext buildContextFromPostRequest(HttpRequest<?> httpRequest, byte[] body) {
        var parsedRequestBody = extractSearchRequest(objectMapper, httpRequest, body);

        var requestId = getRequestId(httpRequest);
        String loginId = null;
        List<UserAttributeDto> userAttributesMergedAndEncrypted = null;
        try {
            if (parsedRequestBody.getLoginId() != null) {
                loginId = getHash(salt, parsedRequestBody.getLoginId());
            }
            userAttributesMergedAndEncrypted = processUserAttributes(parsedRequestBody, userAttributesMergedAndEncrypted);
        } catch (NoSuchAlgorithmException e) {
            log.error("Error while hash 'loginId', requestId: {}", requestId);
            loginId = parsedRequestBody.getLoginId();
        }

        return new RequestContext(
            buildMerchandiser(httpRequest),
            requestId,
            parsedRequestBody.getArea(),
            parsedRequestBody.getCollection(),
            parsedRequestBody.getVisitorId(),
            loginId,
            userAttributesMergedAndEncrypted,
            featuresManager.getLdContextEnvironment(),
            new RequestOptions(
                getSkipCacheHeader(httpRequest),
                parsedRequestBody.isDebug()
            )
        );
    }

    private List<UserAttributeDto> processUserAttributes(
        SearchRequestDto parsedRequestBody,
        List<UserAttributeDto> userAttributesMergedAndEncrypted
    ) {
        var userAttributes = parsedRequestBody.getUserAttributes();
        if (userAttributes != null && !userAttributes.isEmpty()) {
            validateUserAttributes(userAttributes);
            userAttributesMergedAndEncrypted = userAttributesHelper.mergeAndEncrypt(userAttributes);
        }
        return userAttributesMergedAndEncrypted;
    }

    private void validateUserAttributes(List<UserAttributeDto> userAttributes) {
        Set<ConstraintViolation<?>> allViolations = new HashSet<>();
        for (UserAttributeDto userAttribute : userAttributes) {
            Set<ConstraintViolation<UserAttributeDto>> violations = validator.validate(userAttribute);
            allViolations.addAll(violations);
        }
        if (!allViolations.isEmpty()) {
            throw new ConstraintViolationException(allViolations);
        }
    }

    private static SearchRequestDto extractSearchRequest(ObjectMapper mapper,
                                                         HttpRequest<?> httpRequest,
                                                         byte[] body) {
        if (httpRequest.getUri().getPath().contains("/facet")) {
            return parseRequestBody(mapper, body, FacetSearchRequestDto.class, log).orElseThrow().originalRequest();
        }

        return parseRequestBody(mapper, body, SearchRequestDto.class, log)
            .orElseThrow(() -> new IllegalArgumentException("Failed to parse request body into SearchRequestDto"));
    }

    private Merchandiser buildMerchandiser(HttpRequest<?> httpRequest) {
        var headerValue = singleTenant
            ? tenantName
            : httpRequest.getHeaders().get(GROUPBY_CUSTOMER_ID_HEADER);

        return Merchandiser.of(headerValue);
    }

}
