package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.core.navigation.Range;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Value;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.*;

@Value
@Schema(
    title = RANGE_TITLE,
    description = RANGE_DESCRIPTION
)
@Introspected
public class RangeDto {

    @Schema(
        title = RANGE_HIGH_TITLE,
        description = RANGE_HIGH_DESCRIPTION
    )
    Double low;

    @Schema(
        title = RANGE_LOW_TITLE,
        description = RANGE_LOW_DESCRIPTION
    )
    Double high;

    @Schema(
        title = RANGE_DESCRIPTION_TITLE,
        description = RANGE_DESCRIPTION_DESCRIPTION
    )
    String description;

    public Range toDomain() {
        return new Range(low, high, description);
    }

}
