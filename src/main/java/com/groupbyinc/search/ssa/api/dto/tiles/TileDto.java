package com.groupbyinc.search.ssa.api.dto.tiles;

import com.groupbyinc.search.ssa.core.tiles.Tile;

import io.swagger.v3.oas.annotations.media.Schema;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_ATTRIBUTE_VALUE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_ATTRIBUTE_VALUE_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_REPRESENTATIVE_ID_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_REPRESENTATIVE_ID_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TILE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TILE_TITLE;

/**
 * Represents a tile as it appears in a search result.
 */
@Schema(
    title = TILE_TITLE,
    description = TILE_DESCRIPTION
)
public record TileDto(
    @Schema(
        description = PRODUCT_ATTRIBUTE_VALUE_DESCRIPTION,
        example = PRODUCT_ATTRIBUTE_VALUE_EXAMPLE
    )
    ProductAttributeValueDto productAttributeValue,

    @Schema(
        description = PRODUCT_REPRESENTATIVE_ID_DESCRIPTION,
        example = PRODUCT_REPRESENTATIVE_ID_EXAMPLE
    )
    String representativeProductId
) {

    public static TileDto fromDomain(Tile tile) {
        return new TileDto(new ProductAttributeValueDto(tile.productAttributeValue().name(),
            tile.productAttributeValue().value()),
            tile.representativeProductId());
    }
}
