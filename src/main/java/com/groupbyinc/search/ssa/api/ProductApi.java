package com.groupbyinc.search.ssa.api;

import com.groupbyinc.search.ssa.api.dto.ErrorDto;
import com.groupbyinc.search.ssa.api.dto.product.ProductDto;
import com.groupbyinc.search.ssa.application.core.pdp.ProductSearchStrategy;
import com.groupbyinc.search.ssa.core.authz.Identity;
import com.groupbyinc.search.ssa.metrics.opentelemetry.MeterRegistry;

import io.github.resilience4j.micronaut.annotation.Bulkhead;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.http.HttpHeaders;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Get;
import io.micronaut.http.annotation.QueryValue;
import io.micronaut.scheduling.TaskExecutors;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.micronaut.security.annotation.Secured;
import io.micronaut.security.rules.SecurityRule;
import io.micronaut.tracing.annotation.NewSpan;
import io.micronaut.tracing.annotation.SpanTag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.api.validation.RequestValidationUtils.validateIdentity;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.AUTHORIZATION_ERROR_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.BAD_REQUEST_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CLIENT_KEY_SECURITY_REQUIREMENT;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CODE_200;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CODE_400;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CODE_403;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CODE_500;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.COLLECTION_PARAMETER_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.COLLECTION_PARAMETER_NAME;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CONNECTION_HEADER_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CONTENT_LENGTH_HEADER_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CONTENT_TYPE_HEADER_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.DATE_HEADER_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.GROUP_BY_INC_EMPLOYEE_SECURITY_REQUIREMENT;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.INTERNAL_ERROR_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.METHOD_GET;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_DETAILS_SKIP_CACHE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_DETAILS_SKIP_CACHE_NAME;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_FOUND_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_ID_PARAMETER_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_ID_PARAMETER_NAME;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_SEARCH_OPERATION_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_SEARCH_SUMMARY;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_TAG;

@Slf4j
@Controller("/api/search/product")
@RequiredArgsConstructor
@Tag(name = PRODUCT_TAG)
@ExecuteOn(TaskExecutors.BLOCKING)
@Secured(SecurityRule.IS_AUTHENTICATED)
public class ProductApi {

    private final ProductSearchStrategy productSearchStrategy;
    private final MeterRegistry meterRegistry;

    /**
     * Perform a product search against the GroupBy Retail Search API.
     *
     * @param productId             Required. Full resource name of [Product][google.cloud.retail.v2.Product], such as
     *                              `projects/&#42;/locations/global/catalogs/default_catalog/branches/default_branch/products/some_product_id`.
     *                              <p>If the caller does not have permission to access the
     *                              [Product][google.cloud.retail.v2.Product], regardless of whether it exists,
     *                              a PERMISSION_DENIED error is returned.
     *                              <p>If the requested [Product][google.cloud.retail.v2.Product] does not exist, a
     *                              NOT_FOUND error is returned.
     * @param prioritizedVariantIds Not required. If the product has variant list and the request specifies
     *                              the variantIds, requested variants will be the first in the response.
     */
    @Operation(
        summary = PRODUCT_SEARCH_SUMMARY,
        description = PRODUCT_SEARCH_OPERATION_DESCRIPTION,
        method = METHOD_GET,
        parameters = {
            @Parameter(
                name = COLLECTION_PARAMETER_NAME,
                description = COLLECTION_PARAMETER_DESCRIPTION,
                in = ParameterIn.QUERY,
                required = true,
                schema = @Schema(implementation = String.class)
            ),
            @Parameter(
                name = PRODUCT_ID_PARAMETER_NAME,
                description = PRODUCT_ID_PARAMETER_DESCRIPTION,
                in = ParameterIn.QUERY,
                required = true,
                schema = @Schema(implementation = String.class)
            ),
            @Parameter(
                name = PRODUCT_DETAILS_SKIP_CACHE_NAME,
                description = PRODUCT_DETAILS_SKIP_CACHE_DESCRIPTION,
                in = ParameterIn.HEADER,
                schema = @Schema(implementation = Boolean.class)
            )
        }
    )
    @ApiResponse(
        responseCode = CODE_200,
        description = PRODUCT_FOUND_DESCRIPTION,
        content = @Content(
            mediaType = MediaType.APPLICATION_JSON,
            schema = @Schema(implementation = ProductDto.class)
        ),
        headers = {
            @Header(
                name = HttpHeaders.CONTENT_TYPE,
                description = CONTENT_TYPE_HEADER_DESCRIPTION,
                schema = @Schema(implementation = String.class)
            ),
            @Header(
                name = HttpHeaders.DATE,
                description = DATE_HEADER_DESCRIPTION,
                schema = @Schema(implementation = String.class)
            ),
            @Header(
                name = HttpHeaders.CONTENT_LENGTH,
                description = CONTENT_LENGTH_HEADER_DESCRIPTION,
                schema = @Schema(implementation = String.class)
            ),
            @Header(
                name = HttpHeaders.CONNECTION,
                description = CONNECTION_HEADER_DESCRIPTION,
                schema = @Schema(implementation = String.class)
            )
        }
    )
    @ApiResponse(
        responseCode = CODE_400,
        description = BAD_REQUEST_DESCRIPTION,
        content = @Content(schema = @Schema(implementation = ErrorDto.class))
    )
    @ApiResponse(
        responseCode = CODE_403,
        description = AUTHORIZATION_ERROR_DESCRIPTION,
        content = @Content(schema = @Schema(implementation = ErrorDto.class))
    )
    @ApiResponse(
        responseCode = CODE_500,
        description = INTERNAL_ERROR_DESCRIPTION,
        content = @Content(schema = @Schema(implementation = ErrorDto.class))
    )
    @SecurityRequirements({
        @SecurityRequirement(name = CLIENT_KEY_SECURITY_REQUIREMENT),
        @SecurityRequirement(name = GROUP_BY_INC_EMPLOYEE_SECURITY_REQUIREMENT)
    })
    @Get(consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @NewSpan("search/product")
    @Bulkhead(name = "pdp")
    public HttpResponse<ProductDto> getByProductIds(
        @Parameter(hidden = true) Identity identity,
        @SpanTag("productId") @QueryValue String productId,
        @SpanTag("variantIds") @QueryValue(value = "variantIds") @Nullable List<String> prioritizedVariantIds
    ) {
        var context = getRequestContext();
        var response = Optional.ofNullable(meterRegistry.act(() -> {
            validateIdentity(identity, context.getMerchandiser());

            return productSearchStrategy
                .getStrategyFor(context)
                .getProductDetails(productId, prioritizedVariantIds);
        }));

        return response
            .flatMap(opt -> opt.map(product -> HttpResponse.ok(new ProductDto(product))))
            .orElseGet(HttpResponse::notFound);
    }

}
