package com.groupbyinc.search.ssa.api.validation;

import com.groupbyinc.search.ssa.api.dto.FacetSearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.PinnedProductDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.error.AuthorizationException;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.authz.Identity;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.retail.exception.SiteSearchRetailClientException;

import io.micronaut.core.util.CollectionUtils;
import io.micronaut.validation.validator.constraints.ConstraintValidatorContext;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.groupbyinc.search.ssa.api.validation.CustomValidationMessages.FACET_MISSING_MESSAGE;
import static com.groupbyinc.search.ssa.api.validation.CustomValidationMessages.FACET_NAVIGATION_NAME_MISSING_MESSAGE;
import static com.groupbyinc.search.ssa.api.validation.CustomValidationMessages.PAGE_CATEGORY_SIZE_MESSAGE;
import static com.groupbyinc.search.ssa.api.validation.CustomValidationMessages.PINNED_PRODUCTS_IDS_TEMPLATE;
import static com.groupbyinc.search.ssa.api.validation.CustomValidationMessages.PINNED_PRODUCTS_POSITIONS_TEMPLATE;
import static com.groupbyinc.search.ssa.api.validation.CustomValidationMessages.REQUEST_MISSING_MESSAGE;
import static com.groupbyinc.search.ssa.api.validation.biasing.message.BiasValidationMessages.RANGE_HIGH_MUST_BE_BIGGER_THAN_LOW_TEMPLATE;
import static com.groupbyinc.search.ssa.api.validation.biasing.message.BiasValidationMessages.RANGE_LOW_AND_HIGH_NULL_TEMPLATE;
import static com.groupbyinc.search.ssa.api.validation.biasing.message.BiasValidationMessages.RANGE_SIZE_TEMPLATE;

public interface RequestValidationUtils {

    int MAX_RANGE_DIAPASONS_COUNT = 100;

    static void validateIdentity(Identity identity, Merchandiser merchandiser) {
        if (identity == null || !identity.canPerformSearchFor(merchandiser)) {
            throw new AuthorizationException(identity, "Search is not authorized.");
        }
    }

    static void validateRequest(SearchRequestDto request) {
        if (request == null) {
            throw new SiteSearchRetailClientException(REQUEST_MISSING_MESSAGE);
        }
        if (request.getPageCategories() != null && request.getPageCategories().size() > 1) {
            throw new SiteSearchRetailClientException(PAGE_CATEGORY_SIZE_MESSAGE);
        }
    }

    static void validateFacetRequest(FacetSearchRequestDto request) {
        if (request.facet() == null) {
            throw new SiteSearchRetailClientException(FACET_MISSING_MESSAGE);
        }

        if (request.facet().navigationName() == null) {
            throw new SiteSearchRetailClientException(FACET_NAVIGATION_NAME_MISSING_MESSAGE);
        }

        validateRequest(request.originalRequest());
    }

    /**
     * Used to validate range. Base on requirements` "range.low" mast be less than "range.high" if both of them exist.
     * <p>
     * NOTE: This method used in selected refinement trigger and range filter in a rule model, and requires both 'low'
     * and 'high' to be not null at the same time.
     *
     * @param range   to validate
     * @param context object which needs to be populated with a related error message in case if permission not valid.
     *
     * @return true if range valid, false otherwise.
     */
    static boolean validateRange(Range range, ConstraintValidatorContext context) {
        if (range == null) {
            return true;
        }

        if (range.low() == null && range.high() == null) {
            context.messageTemplate(RANGE_LOW_AND_HIGH_NULL_TEMPLATE);
            return false;
        }

        if (range.low() != null && range.high() != null && range.high() <= range.low()) {
            context.messageTemplate(RANGE_HIGH_MUST_BE_BIGGER_THAN_LOW_TEMPLATE);
            return false;
        }

        return true;
    }

    /**
     * Used to validate a list of ranges. <br/><br/> NOTE: This method used in a navigation model with a range type
     * and validate list of ranges.
     * Passed list with ranges represents a chain of buckets, where 'low' of each next bucket must be equal or bigger
     * than 'high' of the previous bucket.
     * <p>
     * Valid examples:
     * <pre>{@code
     * [ -10 -> 1, 1 -> 3, 3 -> 5, 5 -> 100 ]
     *
     * [ null -> 1, 1 -> 3, 3 -> 5, 5 -> null ]
     *
     * [ null -> 1, 1 -> null ]
     * }</pre>
     * <p>
     * Not valid examples:
     * <pre>{@code
     * [ null -> null ]
     * }</pre>
     *
     * @param ranges  to validate
     * @param context object which needs to be populated with a related error message in case if permission not valid.
     *
     * @return true if range valid, false otherwise.
     */
    static boolean validateRanges(List<Range> ranges, ConstraintValidatorContext context) {
        if (ranges == null) {
            // case when type is "VALUE" will be validated wia validateNavigation();
            return true;
        }

        if (CollectionUtils.isEmpty(ranges) || ranges.size() > MAX_RANGE_DIAPASONS_COUNT) {
            context.messageTemplate(RANGE_SIZE_TEMPLATE);
            return false;
        }

        for (Range r : ranges) {
            if (!validateRange(r, context)) {
                return false;
            }
        }

        return true;
    }

    static boolean validatePinnedProducts(List<PinnedProductDto> pinned, ConstraintValidatorContext context) {
        if (pinned == null) {
            return true;
        }

        Set<String> ids = pinned.stream().map(PinnedProductDto::productId).collect(Collectors.toSet());
        if (ids.size() < pinned.size()) {
            context.messageTemplate(PINNED_PRODUCTS_IDS_TEMPLATE);
            return false;
        }

        Set<Integer> positions = pinned.stream().map(PinnedProductDto::position).collect(Collectors.toSet());
        if (positions.size() < pinned.size()) {
            context.messageTemplate(PINNED_PRODUCTS_POSITIONS_TEMPLATE);
            return false;
        }

        return true;
    }

}
