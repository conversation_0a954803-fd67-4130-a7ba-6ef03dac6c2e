package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.core.rule.PinnedProduct;

import io.micronaut.core.annotation.Introspected;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

import static com.groupbyinc.search.ssa.api.validation.Constants.PIN_TO_TOP_MAX_POSITION;
import static com.groupbyinc.search.ssa.api.validation.Constants.PIN_TO_TOP_MIN_POSITION;
import static com.groupbyinc.search.ssa.api.validation.Constants.PRODUCT_ID_REGEX;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PINNED_PRODUCT_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PINNED_PRODUCT_ID_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PINNED_PRODUCT_POSITION_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PINNED_PRODUCT_POSITION_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PINNED_PRODUCT_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_INCLUDE_OUT_OF_STOCK_FIELD_DESCRIPTION;

@Introspected
@Schema(title = PINNED_PRODUCT_TITLE, description = PINNED_PRODUCT_DESCRIPTION)
public record PinnedProductDto(

    @NotNull
    @Min(PIN_TO_TOP_MIN_POSITION)
    @Max(PIN_TO_TOP_MAX_POSITION)
    @Schema(
        description = PINNED_PRODUCT_POSITION_DESCRIPTION,
        example = PINNED_PRODUCT_POSITION_EXAMPLE,
        required = true
    )
    Integer position,

    @NotBlank
    @Pattern(regexp = PRODUCT_ID_REGEX)
    @Schema(description = PINNED_PRODUCT_ID_DESCRIPTION, pattern = PRODUCT_ID_REGEX, required = true)
    String productId,

    @Schema(description = SEARCH_REQUEST_INCLUDE_OUT_OF_STOCK_FIELD_DESCRIPTION, defaultValue = "true")
    boolean includeOutOfStock) {

    public PinnedProduct toDomain() {
        return new PinnedProduct(position, productId, includeOutOfStock);
    }

}
