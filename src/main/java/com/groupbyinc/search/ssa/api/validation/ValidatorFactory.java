package com.groupbyinc.search.ssa.api.validation;

import com.groupbyinc.search.ssa.api.validation.context.ConfigurationResolver;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.area.AreaConfiguration;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.api.dto.PinnedProductDto;
import com.groupbyinc.search.ssa.api.validation.context.AreaCollectionValidator;
import com.groupbyinc.search.ssa.api.validation.context.ProjectConfigurationValidator;
import com.groupbyinc.search.ssa.api.validation.context.ContextValidator;
import com.groupbyinc.search.ssa.api.validation.context.TenantConfigurationValidator;
import com.groupbyinc.search.ssa.core.project.ProjectConfiguration;
import com.groupbyinc.search.ssa.core.tenant.TenantConfiguration;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Factory;
import io.micronaut.core.annotation.Order;
import io.micronaut.validation.validator.constraints.ConstraintValidator;
import lombok.RequiredArgsConstructor;

import java.util.Collection;
import java.util.List;

import static com.groupbyinc.search.ssa.api.validation.RequestValidationUtils.validatePinnedProducts;
import static com.groupbyinc.search.ssa.api.validation.RequestValidationUtils.validateRanges;

@Factory
@RequiredArgsConstructor
public class ValidatorFactory {

    //region: Context validation section, order of beans is important.
    @Context
    @Order(1)
    public ContextValidator tenantConfigurationSearchPreCondition(ConfigurationManager configurationManager) {
        ConfigurationResolver<TenantConfiguration> resolver = context -> configurationManager
            .getTenant(context.getMerchandiser());

        return new TenantConfigurationValidator(resolver);
    }

    @Context
    @Order(2)
    public ContextValidator projectConfigurationSearchPreCondition(ConfigurationManager configurationManager) {
        ConfigurationResolver<ProjectConfiguration> resolver = context -> configurationManager
            .getProjectConfiguration(context.getMerchandiser(), context.getCollection());

        return new ProjectConfigurationValidator(resolver);
    }

    @Context
    @Order(3)
    public ContextValidator areaConfigurationSearchPreCondition(ConfigurationManager configurationManager) {
        ConfigurationResolver<AreaConfiguration> resolver = context -> configurationManager
            .getArea(context.getMerchandiser(), context.getCollection(), context.getArea());

        return new AreaCollectionValidator(resolver);
    }
    // endregion

    @Context
    ConstraintValidator<CollectionNullElementsConstraint, Collection<?>> collectionValidator() {
        return (collection, annotationMetadata, context) ->
            collection == null || !collection.contains(null);
    }

    @Context
    ConstraintValidator<RangeConstraint, List<Range>> rangesValidator() {
        return (ranges, annotationMetadata, context) -> validateRanges(ranges, context);
    }

    @Context
    ConstraintValidator<PinnedProductsConstraint, List<PinnedProductDto>> pinnedProductsValidator() {
        return (pinned, annotationMetadata, context) -> validatePinnedProducts(pinned, context);
    }

}
