package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.api.dto.tiles.TileDto;
import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngineType;
import com.groupbyinc.search.ssa.core.Metadata;
import com.groupbyinc.search.ssa.core.conversation.ConversationalSearchResult;
import com.groupbyinc.search.ssa.core.request.RequestServed;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Value;

import java.util.List;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_LIMIT;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.INCLUDE_EXPANDED_RESULTS_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_AREA_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_AREA_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_BIASING_PROFILE_APPLIED_ID_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_BIASING_PROFILE_APPLIED_ID_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_BIASING_PROFILE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_BIASING_PROFILE_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_CORRECTED_QUERY_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_CORRECTED_QUERY_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_EMPTY_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_ID_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_ID_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_QUERY_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_QUERY_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_RECORDS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_REDIRECT_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_REDIRECT_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_SPONSORED_RECORDS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_TILES_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_TOTAL_RECORD_COUNT_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_TOTAL_RECORD_COUNT_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_WARNINGS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REWRITES_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REWRITES_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_CONVERSATIONAL_SEARCH_RESULT;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_ORIGINAL_QUERY_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_QUERY_FIELD_EXAMPLE;

@Schema(
    title = SEARCH_RESPONSE_TITLE,
    description = SEARCH_RESPONSE_DESCRIPTION
)
@Value
@EqualsAndHashCode
@Builder(toBuilder = true)
public class SearchResponseDto {

    @Schema(
        description = SEARCH_RESPONSE_ID_FIELD_DESCRIPTION,
        example = SEARCH_RESPONSE_ID_FIELD_EXAMPLE
    )
    String id;

    @Schema(
        description = SEARCH_RESPONSE_AREA_FIELD_DESCRIPTION,
        example = SEARCH_RESPONSE_AREA_FIELD_EXAMPLE
    )
    String area;

    @Schema(
        description = SEARCH_RESPONSE_QUERY_FIELD_DESCRIPTION,
        example = SEARCH_RESPONSE_QUERY_FIELD_EXAMPLE
    )
    String query;

    @Schema(
        description = SEARCH_RESPONSE_ORIGINAL_QUERY_FIELD_DESCRIPTION,
        nullable = true,
        example = SEARCH_REQUEST_QUERY_FIELD_EXAMPLE,
        hidden = true
    )
    String originalQuery;

    @Schema(
        description = SEARCH_RESPONSE_CORRECTED_QUERY_FIELD_DESCRIPTION,
        example = SEARCH_RESPONSE_CORRECTED_QUERY_FIELD_EXAMPLE
    )
    String correctedQuery;

    @Schema(
        description = SEARCH_RESPONSE_BIASING_PROFILE_FIELD_DESCRIPTION,
        example = SEARCH_RESPONSE_BIASING_PROFILE_FIELD_EXAMPLE
    )
    String biasingProfile;

    @Schema(
        description = SEARCH_RESPONSE_BIASING_PROFILE_APPLIED_ID_FIELD_DESCRIPTION,
        example = SEARCH_RESPONSE_BIASING_PROFILE_APPLIED_ID_FIELD_EXAMPLE
    )
    Integer biasingProfileAppliedId;

    String filter;

    SearchRequestDto originalRequest;

    @Schema(description = SEARCH_RESPONSE_RECORDS_FIELD_DESCRIPTION)
    List<RecordDto> records;

    @Schema(description = SEARCH_RESPONSE_SPONSORED_RECORDS_FIELD_DESCRIPTION, nullable = true)
    List<RecordDto> sponsoredRecords;

    @Schema(
        description = SEARCH_RESPONSE_TOTAL_RECORD_COUNT_FIELD_DESCRIPTION,
        example = SEARCH_RESPONSE_TOTAL_RECORD_COUNT_FIELD_EXAMPLE
    )
    Long totalRecordCount;

    SearchMetadataDto metadata;

    PageInfoDto pageInfo;

    List<NavigationDto> availableNavigation;

    List<NavigationDto> selectedNavigation;

    @Schema(
        description = SEARCH_RESPONSE_REDIRECT_FIELD_DESCRIPTION,
        example = SEARCH_RESPONSE_REDIRECT_FIELD_EXAMPLE
    )
    String redirect;

    List<Experiment> experiments;

    TemplateDto template;

    @Schema(
        description = SEARCH_RESPONSE_EMPTY_FIELD_DESCRIPTION
    )
    boolean empty;

    List<Metadata> siteParams;

    DebugDto debug;

    @Schema(
        description = SEARCH_RESPONSE_WARNINGS_FIELD_DESCRIPTION
    )
    List<String> warnings;

    @Schema(description = INCLUDE_EXPANDED_RESULTS_DESCRIPTION)
    Boolean includeExpandedResults;

    @Schema(description = FACET_LIMIT)
    Integer facetLimit;

    List<Metadata> redirectMetadata;

    @Schema(
        description = SEARCH_REWRITES_DESCRIPTION, example = SEARCH_REWRITES_EXAMPLE)
    List<String> rewrites;

    @Schema(
        description = SEARCH_RESPONSE_TILES_DESCRIPTION
    )
    List<TileDto> tiles;

    List<String> searchStrategies;
    SearchEngineType engineSource;
    RequestServed requestServed;
    @Schema(description = SEARCH_RESPONSE_CONVERSATIONAL_SEARCH_RESULT)
    ConversationalSearchResult conversationalSearchResult;


    public SearchResponseDto log() {
        var builder = SearchResponseDto.builder()
            .id(this.id)
            .area(this.area)
            .query(this.query)
            .correctedQuery(this.correctedQuery)
            .biasingProfile(this.biasingProfile)
            .biasingProfileAppliedId(this.biasingProfileAppliedId)
            .filter(this.filter)
            .totalRecordCount(this.totalRecordCount)
            .metadata(this.metadata)
            .pageInfo(this.pageInfo)
            .redirect(this.redirect)
            .warnings(this.warnings)
            .includeExpandedResults(this.includeExpandedResults)
            .facetLimit(this.facetLimit)
            .requestServed(requestServed)
            .engineSource(this.engineSource)
            .searchStrategies(this.searchStrategies)
            .tiles(this.tiles)
            .searchStrategies(this.searchStrategies)
            .conversationalSearchResult(conversationalSearchResult);

        if (this.experiments != null) {
            builder.experiments(
                this.experiments
            );
        }
        if (this.template != null) {
            builder.template(
                new TemplateDto(
                    this.template.getName(),
                    this.template.getRuleName(),
                    this.template.getRuleId(),
                    this.template.getTriggerSet()
                )
            );
        }

        if (this.debug != null) {
            builder.debug(this.debug);
        }

        return builder.build();
    }

}
