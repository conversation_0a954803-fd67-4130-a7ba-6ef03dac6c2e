package com.groupbyinc.search.ssa.api.validation.biasing.factory;

import com.groupbyinc.search.ssa.api.dto.BiasDto;
import com.groupbyinc.search.ssa.api.dto.NumericContentDto;
import com.groupbyinc.search.ssa.api.dto.RangeDto;
import com.groupbyinc.search.ssa.api.validation.biasing.BiasValidationService;
import com.groupbyinc.search.ssa.api.validation.biasing.constraints.BiasConstraint;
import com.groupbyinc.search.ssa.api.validation.biasing.constraints.NumericContentConstraint;
import com.groupbyinc.search.ssa.api.validation.biasing.constraints.NumericContentRangeConstraint;
import com.groupbyinc.search.ssa.features.FeaturesManager;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Factory;
import io.micronaut.validation.validator.constraints.ConstraintValidator;
import lombok.RequiredArgsConstructor;

import java.util.List;

import static com.groupbyinc.search.ssa.api.validation.RequestValidationUtils.validateRanges;

@Factory
@RequiredArgsConstructor
public class BiasValidationFactory {

    @Context
    public BiasValidationService biasValidationService(FeaturesManager featuresManager) {
        return new BiasValidationService(featuresManager);
    }

    @Context
    public ConstraintValidator<BiasConstraint, BiasDto> biasValidator(BiasValidationService biasValidationService) {
        return (biasModel, annotationMetadata, context) -> biasValidationService.validateBiasModel(
            biasModel,
            context
        );
    }

    @Context
    ConstraintValidator<NumericContentRangeConstraint, List<RangeDto>> rangesBiasingValidator() {
        return (ranges, annotationMetadata, context) -> {
            if (ranges == null) {
                return true;
            }

            return validateRanges(ranges.stream().map(RangeDto::toDomain).toList(), context);
        };
    }

    @Context
    public ConstraintValidator<NumericContentConstraint, NumericContentDto> numericContentValidator(
        BiasValidationService biasValidationService
    ) {
        return (content, annotationMetadata, context) -> biasValidationService.validateNumericContent(
            content,
            context
        );
    }

}
