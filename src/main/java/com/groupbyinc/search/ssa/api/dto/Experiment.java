package com.groupbyinc.search.ssa.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Value;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.EXPERIMENT_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.EXPERIMENT_ID_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.EXPERIMENT_ID_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.EXPERIMENT_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.EXPERIMENT_VARIANT_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.EXPERIMENT_VARIANT_FIELD_EXAMPLE;

/**
 * DTO to keep information about Rule based Experiment
 */
@Schema(
    title = EXPERIMENT_TITLE,
    description = EXPERIMENT_DESCRIPTION
)
@Value
public class Experiment {
    @Schema(
        description = EXPERIMENT_ID_FIELD_DESCRIPTION,
        example = EXPERIMENT_ID_FIELD_EXAMPLE
    )
    String experimentId;

    @Schema(
        description = EXPERIMENT_VARIANT_FIELD_DESCRIPTION,
        example = EXPERIMENT_VARIANT_FIELD_EXAMPLE
    )
    String experimentVariant;
}
