package com.groupbyinc.search.ssa.api.error;

import com.groupbyinc.search.ssa.api.dto.DebugDto;
import com.groupbyinc.search.ssa.api.dto.ErrorDto;
import com.groupbyinc.search.ssa.api.dto.VisibilityScoreDto;
import com.groupbyinc.search.ssa.api.utils.HttpUtils;

import com.google.api.client.util.Strings;
import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Primary;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.HttpStatus;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.server.exceptions.ExceptionHandler;
import lombok.extern.slf4j.Slf4j;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;

/**
 * This global exception handler sits at the API layer and catches all uncaught exceptions originated from any component
 * that is invoked because of an incoming HTTP request. By relying on {@link ErrorDto}, this global handler ensures that
 * all returned exceptions are properly logged for future reference and diagnostics, and transformed to HTTP responses
 * with a standard format.
 */
@Slf4j
@Context
@Primary
@Controller
public class GlobalApiExceptionHandler implements ExceptionHandler<Throwable, HttpResponse<?>> {

    /**
     * Handles an internal error propagated all the way up to the API layer by transforming it to a correct HTTP
     * response.
     *
     * @param originalRequest The HTTP request that caused the issue.
     * @param internalError   The internal error that is internally thrown but not caught.
     *
     * @return An HTTP response with the appropriate status and body (see {@link ErrorDto}).
     */
    @Override
    public HttpResponse<ErrorDto> handle(HttpRequest originalRequest, Throwable internalError) {
        var responseStatus = HttpUtils.determineHttpStatus(internalError);
        var errorResponse = HttpResponse
            .<ErrorDto>status(responseStatus)
            .body(buildResponseBody(originalRequest, internalError, responseStatus));
        log(internalError, errorResponse);
        return errorResponse;
    }

    /**
     * Builds the body of am error response.
     */
    private static ErrorDto buildResponseBody(HttpRequest<?> originalHttpRequest,
                                              Throwable error,
                                              HttpStatus responseStatus) {
        DebugDto debugDto = null;
        try {
            var debugDetails = getRequestContext().getDebugDetails();
            debugDto = new DebugDto(
                debugDetails.getDebugInfos(),
                VisibilityScoreDto.fromScore(debugDetails.getVisibilityScore())
            );
        } catch (Exception ignored) {
            // case when there is no request context because we failed before it is created
        }

        return new ErrorDto(
            HttpUtils.getRequestId(originalHttpRequest),
            originalHttpRequest.getMethod().name(),
            originalHttpRequest.getPath(),
            generateFilteredErrorMessage(responseStatus, error),
            debugDto
        );
    }

    /**
     * Generates a filtered error message to return to clients that do not expose internal details.
     */
    private static String generateFilteredErrorMessage(HttpStatus responseStatus, Throwable error) {
        if (responseStatus.getCode() == HttpStatus.TOO_MANY_REQUESTS.getCode()) {
            return "Too many parallel request. Wait for infrastructure to adjust.";
        }
        if (responseStatus.getCode() == HttpStatus.UNAUTHORIZED.getCode()) {
            return "Missing valid authentication.";
        }
        if (Strings.isNullOrEmpty(error.getMessage())) {
            return "Unknown error occurred.";
        }
        if (responseStatus.getCode() >= HttpStatus.INTERNAL_SERVER_ERROR.getCode()) {
            return "There was an internal error processing the search request: " + error.getMessage();
        }

        return error.getMessage();
    }

    /**
     * Logs a detailed message explaining the internal issue that can be cross-referenced with error responses using
     * {@link ErrorDto#trackingId()}.
     */
    private static void log(Throwable internalError, HttpResponse<ErrorDto> errorResponse) {

        var trackingId = errorResponse.getBody().map(ErrorDto::trackingId).orElse("<NO TRACKING ID>");
        var httpStatus = errorResponse.getStatus();
        var fullStatus = httpStatus.getCode() + " " + httpStatus.getReason();

        if (httpStatus.getCode() >= 500) {
            log.error("HTTP {} with tracking ID of '{}': {}", fullStatus, trackingId, internalError.getMessage(), internalError);
        } else {
            log.warn("HTTP {} with tracking ID of '{}': {}", fullStatus, trackingId, internalError.getMessage(), internalError);
        }
    }
}
