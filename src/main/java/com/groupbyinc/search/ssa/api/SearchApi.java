package com.groupbyinc.search.ssa.api;

import com.groupbyinc.search.ssa.api.dto.ErrorDto;
import com.groupbyinc.search.ssa.api.dto.FacetSearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.FacetSearchResponseDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SearchResponseDto;
import com.groupbyinc.search.ssa.application.core.search.SearchService;
import com.groupbyinc.search.ssa.core.authz.Identity;
import com.groupbyinc.search.ssa.metrics.opentelemetry.MeterRegistry;

import io.github.resilience4j.micronaut.annotation.Bulkhead;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.http.HttpHeaders;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Controller;
import io.micronaut.http.annotation.Post;
import io.micronaut.scheduling.TaskExecutors;
import io.micronaut.scheduling.annotation.ExecuteOn;
import io.micronaut.security.annotation.Secured;
import io.micronaut.security.rules.SecurityRule;
import io.micronaut.tracing.annotation.NewSpan;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.api.validation.RequestValidationUtils.validateFacetRequest;
import static com.groupbyinc.search.ssa.api.validation.RequestValidationUtils.validateIdentity;
import static com.groupbyinc.search.ssa.api.validation.RequestValidationUtils.validateRequest;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.AUTHORIZATION_ERROR_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.BAD_REQUEST_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CLIENT_KEY_SECURITY_REQUIREMENT;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CODE_200;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CODE_400;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CODE_403;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CODE_500;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CONNECTION_HEADER_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CONTENT_LENGTH_HEADER_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CONTENT_TYPE_HEADER_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.DATE_HEADER_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_SEARCH_ACCEPTED_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_SEARCH_OPERATION_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_SEARCH_REQUEST_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.GROUP_BY_HEADER_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.GROUP_BY_HEADER_NAME;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.GROUP_BY_INC_EMPLOYEE_SECURITY_REQUIREMENT;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.INTERNAL_ERROR_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.METHOD_POST;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_DETAILS_SKIP_CACHE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_DETAILS_SKIP_CACHE_NAME;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_ACCEPTED_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_OPERATION_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_SUMMARY;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_TAG;

@Slf4j
@Tag(name = SEARCH_TAG)
@Controller
@ExecuteOn(TaskExecutors.BLOCKING)
@Secured(SecurityRule.IS_AUTHENTICATED)
@SecurityRequirements({
    @SecurityRequirement(name = CLIENT_KEY_SECURITY_REQUIREMENT),
    @SecurityRequirement(name = GROUP_BY_INC_EMPLOYEE_SECURITY_REQUIREMENT)
})
@RequiredArgsConstructor
public class SearchApi {

    private final SearchService searchService;
    private final MeterRegistry meterRegistry;

    @Operation(
        summary = SEARCH_SUMMARY,
        description = SEARCH_OPERATION_DESCRIPTION,
        method = METHOD_POST,
        parameters = {
            @Parameter(
                name = GROUP_BY_HEADER_NAME,
                description = GROUP_BY_HEADER_DESCRIPTION,
                in = ParameterIn.HEADER,
                required = true,
                schema = @Schema(implementation = String.class)
            ),
            @Parameter(
                name = PRODUCT_DETAILS_SKIP_CACHE_NAME,
                description = PRODUCT_DETAILS_SKIP_CACHE_DESCRIPTION,
                in = ParameterIn.HEADER,
                schema = @Schema(implementation = Boolean.class)
            )
        },
        requestBody = @RequestBody(
            required = true,
            description = SEARCH_REQUEST_DESCRIPTION,
            content = {
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = SearchRequestDto.class)
                )
            }
        )
    )
    @ApiResponse(
        responseCode = CODE_200,
        description = SEARCH_ACCEPTED_DESCRIPTION,
        content = @Content(
            mediaType = MediaType.APPLICATION_JSON,
            schema = @Schema(implementation = SearchResponseDto.class)
        ),
        headers = {
            @Header(
                name = HttpHeaders.CONTENT_TYPE,
                description = CONTENT_TYPE_HEADER_DESCRIPTION,
                schema = @Schema(implementation = String.class)
            ),
            @Header(
                name = HttpHeaders.DATE,
                description = DATE_HEADER_DESCRIPTION,
                schema = @Schema(implementation = String.class)
            ),
            @Header(
                name = HttpHeaders.CONTENT_LENGTH,
                description = CONTENT_LENGTH_HEADER_DESCRIPTION,
                schema = @Schema(implementation = String.class)
            ),
            @Header(
                name = HttpHeaders.CONNECTION,
                description = CONNECTION_HEADER_DESCRIPTION,
                schema = @Schema(implementation = String.class)
            )
        }
    )
    @ApiResponse(
        responseCode = CODE_400,
        description = BAD_REQUEST_DESCRIPTION,
        content = @Content(schema = @Schema(implementation = ErrorDto.class))
    )
    @ApiResponse(
        responseCode = CODE_403,
        description = AUTHORIZATION_ERROR_DESCRIPTION,
        content = @Content(schema = @Schema(implementation = ErrorDto.class))
    )
    @ApiResponse(
        responseCode = CODE_500,
        description = INTERNAL_ERROR_DESCRIPTION,
        content = @Content(schema = @Schema(implementation = ErrorDto.class))
    )
    @Post(value = "/api/search", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @NewSpan("site-search-api/search")
    @Bulkhead(name = "search")
    HttpResponse<SearchResponseDto> search(@Parameter(hidden = true) @NonNull Identity identity,
                                           @Valid @Body SearchRequestDto searchRequest) {
        var response = meterRegistry.act(() -> {
            validateIdentity(identity, getRequestContext().getMerchandiser());
            validateRequest(searchRequest);

            return searchService.search(searchRequest);
        });
        return HttpResponse.ok(response);
    }

    @Operation(
        summary = SEARCH_SUMMARY,
        description = FACET_SEARCH_OPERATION_DESCRIPTION,
        method = METHOD_POST,
        parameters = {
            @Parameter(
                name = GROUP_BY_HEADER_NAME,
                description = GROUP_BY_HEADER_DESCRIPTION,
                in = ParameterIn.HEADER,
                required = true,
                schema = @Schema(implementation = String.class)
            )
        },
        requestBody = @RequestBody(
            required = true,
            description = FACET_SEARCH_REQUEST_DESCRIPTION,
            content = {
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = FacetSearchRequestDto.class)
                )
            }
        )
    )
    @ApiResponse(
        responseCode = CODE_200,
        description = FACET_SEARCH_ACCEPTED_DESCRIPTION,
        content = @Content(
            mediaType = MediaType.APPLICATION_JSON,
            schema = @Schema(implementation = FacetSearchResponseDto.class)
        ),
        headers = {
            @Header(
                name = HttpHeaders.CONTENT_TYPE,
                description = CONTENT_TYPE_HEADER_DESCRIPTION,
                schema = @Schema(implementation = String.class)
            ),
            @Header(
                name = HttpHeaders.DATE,
                description = DATE_HEADER_DESCRIPTION,
                schema = @Schema(implementation = String.class)
            ),
            @Header(
                name = HttpHeaders.CONTENT_LENGTH,
                description = CONTENT_LENGTH_HEADER_DESCRIPTION,
                schema = @Schema(implementation = String.class)
            ),
            @Header(
                name = HttpHeaders.CONNECTION,
                description = CONNECTION_HEADER_DESCRIPTION,
                schema = @Schema(implementation = String.class)
            )
        }
    )
    @ApiResponse(
        responseCode = CODE_400,
        description = BAD_REQUEST_DESCRIPTION,
        content = @Content(schema = @Schema(implementation = ErrorDto.class))
    )
    @ApiResponse(
        responseCode = CODE_403,
        description = AUTHORIZATION_ERROR_DESCRIPTION,
        content = @Content(schema = @Schema(implementation = ErrorDto.class))
    )
    @ApiResponse(
        responseCode = CODE_500,
        description = INTERNAL_ERROR_DESCRIPTION,
        content = @Content(schema = @Schema(implementation = ErrorDto.class))
    )
    @Post(value = "/api/search/facet", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @NewSpan("site-search-api/search/facet")
    @Bulkhead(name = "search")
    HttpResponse<FacetSearchResponseDto> facetSearch(@Parameter(hidden = true) @NonNull Identity identity,
                                                     @Valid @Body FacetSearchRequestDto searchRequest) {
        var response = meterRegistry.act(() -> {
            validateIdentity(identity, getRequestContext().getMerchandiser());
            validateFacetRequest(searchRequest);

            return searchService.facetSearch(searchRequest);
        });
        return HttpResponse.ok(response);
    }

}
