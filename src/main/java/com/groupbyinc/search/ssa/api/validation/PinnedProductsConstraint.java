package com.groupbyinc.search.ssa.api.validation;

import jakarta.validation.Constraint;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Constraint(validatedBy = {})
@Retention(RetentionPolicy.RUNTIME)
public @interface PinnedProductsConstraint {

    String message() default "Passed 'pinnedProducts' are not valid.";

}
