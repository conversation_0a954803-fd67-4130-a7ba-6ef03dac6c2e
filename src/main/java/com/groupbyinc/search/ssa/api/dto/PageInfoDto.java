package com.groupbyinc.search.ssa.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Value;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.PAGE_INFO_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PAGE_INFO_RECORD_END_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PAGE_INFO_RECORD_END_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PAGE_INFO_RECORD_END_FIELD_MINIMUM;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PAGE_INFO_RECORD_START_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PAGE_INFO_RECORD_START_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PAGE_INFO_RECORD_START_FIELD_MINIMUM;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PAGE_INFO_TITLE;

@Schema(
    title = PAGE_INFO_TITLE,
    description = PAGE_INFO_DESCRIPTION
)
@Value
public class PageInfoDto {

    @Schema(
        description = PAGE_INFO_RECORD_START_FIELD_DESCRIPTION,
        minimum = PAGE_INFO_RECORD_START_FIELD_MINIMUM,
        example = PAGE_INFO_RECORD_START_FIELD_EXAMPLE
    )
    Long recordStart;

    @Schema(
        description = PAGE_INFO_RECORD_END_FIELD_DESCRIPTION,
        minimum = PAGE_INFO_RECORD_END_FIELD_MINIMUM,
        example = PAGE_INFO_RECORD_END_FIELD_EXAMPLE
    )
    Long recordEnd;
}
