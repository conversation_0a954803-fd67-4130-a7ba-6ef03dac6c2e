package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.core.Sort;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Value;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.SORT_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SORT_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SORT_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SORT_ORDER_DEFAULT_VALUE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SORT_ORDER_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SORT_ORDER_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SORT_ORDER_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SORT_TITLE;

@Value
@Schema(
    title = SORT_TITLE,
    description = SORT_DESCRIPTION
)
public class SortDto {

    @Schema(
        description = SORT_FIELD_DESCRIPTION,
        example = SORT_FIELD_EXAMPLE,
        required = true
    )
    String field;

    OrderDto order;

    @Schema(
        title = SORT_ORDER_TITLE,
        description = SORT_ORDER_DESCRIPTION,
        nullable = true,
        defaultValue = SORT_ORDER_DEFAULT_VALUE,
        example = SORT_ORDER_EXAMPLE
    )
    public enum OrderDto {
        Ascending,
        Descending
    }

    public Sort toDomain() {

        if (order == null) {
            return new Sort(field, null);
        }

        return switch (order) {
            case Ascending -> new Sort(field, com.groupbyinc.search.ssa.core.Order.ASCENDING);
            case Descending -> new Sort(field, com.groupbyinc.search.ssa.core.Order.DESCENDING);
        };
    }
}
