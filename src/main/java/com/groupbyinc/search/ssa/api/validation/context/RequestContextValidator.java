package com.groupbyinc.search.ssa.api.validation.context;

import com.groupbyinc.search.ssa.api.RequestContext;

import io.micronaut.context.annotation.Context;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * Class for validating all the {@link ContextValidator} singletons the application is aware of.
 */
@Context
@RequiredArgsConstructor
public class RequestContextValidator {

    private final List<ContextValidator> searchValidators;

    /**
     * Validate request context using existing validators.
     *
     * @param context request metadata.
     *
     * @throws RequestContextValidationException if passed request context was not valid
     */
    public void validate(RequestContext context) throws RequestContextValidationException {
        searchValidators.forEach(searchValidator -> searchValidator.validate(context));
    }

}
