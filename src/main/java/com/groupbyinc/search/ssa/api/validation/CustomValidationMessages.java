package com.groupbyinc.search.ssa.api.validation;

import io.micronaut.context.StaticMessageSource;

public class CustomValidationMessages extends StaticMessageSource {

    public static final String REQUEST_MISSING_MESSAGE =
        "S4R request is not valid. $.originalRequest is missing.";

    public static final String PAGE_CATEGORY_SIZE_MESSAGE =
        "S4R request is not valid. $.pageCategories.size is more than 1.";

    public static final String FACET_MISSING_MESSAGE =
        "S4R request is not valid. '$.facet' is missing.";

    public static final String FACET_NAVIGATION_NAME_MISSING_MESSAGE =
        "S4R request is not valid. '$.facet.navigationName' is missing.";

    public static final String PINNED_PRODUCTS_IDS = "Request 'pinnedProducts' has non unique product ids.";
    public static final String PINNED_PRODUCTS_IDS_KEY = "pinned.products.non.unique.ids";
    public static final String PINNED_PRODUCTS_IDS_TEMPLATE = "{pinned.products.non.unique.ids}";

    public static final String PINNED_PRODUCTS_POSITIONS = "Request 'pinnedProducts' has non unique positions to pin.";
    public static final String PINNED_PRODUCTS_POSITIONS_KEY = "pinned.products.non.unique.positions";
    public static final String PINNED_PRODUCTS_POSITIONS_TEMPLATE = "{pinned.products.non.unique.positions}";

    public CustomValidationMessages() {

        // Pinned Products
        addMessage(PINNED_PRODUCTS_IDS_KEY, PINNED_PRODUCTS_IDS);
        addMessage(PINNED_PRODUCTS_POSITIONS_KEY, PINNED_PRODUCTS_POSITIONS);
    }

}
