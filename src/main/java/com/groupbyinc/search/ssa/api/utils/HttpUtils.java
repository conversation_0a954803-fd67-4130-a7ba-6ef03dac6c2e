package com.groupbyinc.search.ssa.api.utils;

import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.api.error.AuthorizationException;
import com.groupbyinc.search.ssa.api.validation.context.RequestContextValidationException;
import com.groupbyinc.search.ssa.productcatalog.exception.SiteSearchProductCatalogException;
import com.groupbyinc.search.ssa.retail.exception.SiteSearchRetailClientException;
import com.groupbyinc.search.ssa.retail.exception.SiteSearchRetailServerException;
import com.groupbyinc.utils.validation.ValidationException;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.gax.rpc.InvalidArgumentException;
import io.github.resilience4j.bulkhead.BulkheadFullException;
import io.micronaut.core.propagation.PropagatedContext;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.HttpStatus;
import io.micronaut.web.router.exceptions.UnsatisfiedBodyRouteException;
import io.micronaut.web.router.exceptions.UnsatisfiedPartRouteException;
import io.micronaut.web.router.exceptions.UnsatisfiedPathVariableRouteException;
import io.micronaut.web.router.exceptions.UnsatisfiedRouteException;
import jakarta.validation.ConstraintViolationException;
import lombok.experimental.UtilityClass;
import org.slf4j.Logger;
import org.slf4j.MDC;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static com.groupbyinc.search.ssa.application.logging.LoggingContext.REQUEST_ID;
import static com.groupbyinc.search.ssa.util.Constants.GROUPBY_SKIP_CACHE_HEADER;

@UtilityClass
public class HttpUtils {

    private static final String BODY_PARSE_ERROR = "Not able to parse request body, or it is null";
    private static final Map<Class<? extends Throwable>, HttpStatus> ERROR_STATUS_MAP = new HashMap<>();

    static {
        ERROR_STATUS_MAP.put(ValidationException.class, HttpStatus.BAD_REQUEST);
        ERROR_STATUS_MAP.put(UnsatisfiedBodyRouteException.class, HttpStatus.BAD_REQUEST);
        ERROR_STATUS_MAP.put(RequestContextValidationException.class, HttpStatus.BAD_REQUEST);
        ERROR_STATUS_MAP.put(UnsatisfiedRouteException.class, HttpStatus.BAD_REQUEST);
        ERROR_STATUS_MAP.put(UnsatisfiedPartRouteException.class, HttpStatus.BAD_REQUEST);
        ERROR_STATUS_MAP.put(UnsatisfiedPathVariableRouteException.class, HttpStatus.BAD_REQUEST);
        ERROR_STATUS_MAP.put(AuthorizationException.class, HttpStatus.FORBIDDEN);
        ERROR_STATUS_MAP.put(io.micronaut.security.authentication.AuthorizationException.class, HttpStatus.UNAUTHORIZED);
        ERROR_STATUS_MAP.put(InvalidArgumentException.class, HttpStatus.BAD_REQUEST);
        ERROR_STATUS_MAP.put(IllegalArgumentException.class, HttpStatus.BAD_REQUEST);
        ERROR_STATUS_MAP.put(SiteSearchRetailClientException.class, HttpStatus.BAD_REQUEST);
        ERROR_STATUS_MAP.put(SiteSearchRetailServerException.class, HttpStatus.INTERNAL_SERVER_ERROR);
        ERROR_STATUS_MAP.put(ConstraintViolationException.class, HttpStatus.BAD_REQUEST);
        ERROR_STATUS_MAP.put(BulkheadFullException.class, HttpStatus.TOO_MANY_REQUESTS);
        ERROR_STATUS_MAP.put(SiteSearchProductCatalogException.class, HttpStatus.FAILED_DEPENDENCY);
    }

    /**
     * Determines the appropriate HTTP status code for an error response that is being generated due to an exception.
     *
     * @param internalError Internal error that is propagated to the API layer and needs to be transformed to an HTTP response.
     *
     * @return Appropriate HTTP status code for the error response.
     */
    public static HttpStatus determineHttpStatus(Throwable internalError) {
        return ERROR_STATUS_MAP.getOrDefault(
            internalError.getClass(),
            HttpStatus.INTERNAL_SERVER_ERROR
        );
    }

    /**
     * Extracts request ID from http request attributes or MDC map
     */
    public static String getRequestId(HttpRequest<?> request) {
        return (String) request.getAttribute(REQUEST_ID).orElse(MDC.get(REQUEST_ID));
    }


    public static boolean getSkipCacheHeader(HttpRequest<?> httpRequest) {
        if (httpRequest.getHeaders().contains(GROUPBY_SKIP_CACHE_HEADER)) {
            return Boolean.parseBoolean(httpRequest.getHeaders().get(GROUPBY_SKIP_CACHE_HEADER));
        }

        return false;
    }

    public static <T> Optional<T> parseRequestBody(ObjectMapper mapper, byte[] body, Class<T> clazz, Logger log) {
        try {
            return Optional.of(mapper.readValue(body, clazz));
        } catch (IOException e) {
            log.warn(BODY_PARSE_ERROR, e);
            return Optional.empty();
        }
    }

    public static RequestContext getRequestContext() {
        return PropagatedContext
            .getOrEmpty()
            .find(RequestContext.class)
            .orElseThrow(() -> new IllegalStateException("No request context found."));
    }

}
