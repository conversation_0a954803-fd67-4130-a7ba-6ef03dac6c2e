package com.groupbyinc.search.ssa.api.validation.biasing.message;

import io.micronaut.context.StaticMessageSource;
import io.micronaut.context.annotation.Context;

/**
 * Class which is store all bias related validation messages and templates.
 */
@Context
public class BiasValidationMessages extends StaticMessageSource {

    public static final String BIAS_TYPE_NULL = "Bias type must not be null.";
    public static final String BIAS_TYPE_NULL_KEY = "bias.type.pattern.null";
    public static final String BIAS_TYPE_NULL_TEMPLATE = "{bias.type.pattern.null}";

    public static final String BIAS_BOOST_NULL = "Bias boost(strength or strengthValue) must not be null.";
    public static final String BIAS_BOOST_NULL_KEY = "bias.boost.null";
    public static final String BIAS_BOOST_NULL_TEMPLATE = "{bias.boost.null}";

    public static final String BIAS_STRENGTH_RANGE = "Bias strengthValue must not be between -1 and 1.";
    public static final String BIAS_STRENGTH_RANGE_KEY = "bias.strength.value.range";
    public static final String BIAS_STRENGTH_RANGE_TEMPLATE = "{bias.strength.value.range}";

    public static final String BIAS_TEXT_CONTENT_EMPTY = "Bias text content must not be empty.";
    public static final String BIAS_TEXT_CONTENT_EMPTY_KEY = "bias.text.content.pattern.empty";
    public static final String BIAS_TEXT_CONTENT_EMPTY_TEMPLATE = "{bias.text.content.pattern.empty}";

    public static final String BIAS_TEXT_NUMERIC_CONTENT_FILLED = "Bias numeric content must be empty for textual field.";
    public static final String BIAS_TEXT_NUMERIC_CONTENT_FILLED_KEY = "bias.text.numeric.content.filled";
    public static final String BIAS_TEXT_NUMERIC_CONTENT_FILLED_TEMPLATE = "{bias.text.numeric.content.filled}";

    public static final String BIAS_NUMERIC_CONTENT_EMPTY = "Bias numeric content must not be empty.";
    public static final String BIAS_NUMERIC_CONTENT_EMPTY_KEY = "bias.numeric.content.pattern.empty";
    public static final String BIAS_NUMERIC_CONTENT_EMPTY_TEMPLATE = "{bias.numeric.content.pattern.empty}";

    public static final String BIAS_NUMERIC_TEXT_CONTENT_FILLED = "Bias text content must be empty for numeric field.";
    public static final String BIAS_NUMERIC_TEXT_CONTENT_FILLED_KEY = "bias.numeric.text.content.filled";
    public static final String BIAS_NUMERIC_TEXT_CONTENT_FILLED_TEMPLATE = "{bias.numeric.text.content.filled}";

    public static final String BIAS_NUMERIC_CONTENT_BOTH_EMPTY = "Both ranges and values can not be empty at the same time";
    public static final String BIAS_NUMERIC_CONTENT_BOTH_EMPTY_KEY = "bias.numeric.content.pattern.empty";
    public static final String BIAS_NUMERIC_CONTENT_BOTH_EMPTY_TEMPLATE = "{bias.numeric.content.pattern.empty}";

    // Range
    public static final String RANGE_LOW_AND_HIGH_NULL = "Range. Low and High must not be null in same time.";
    public static final String RANGE_LOW_AND_HIGH_NULL_KEY = "range.low.and.high.null";
    public static final String RANGE_LOW_AND_HIGH_NULL_TEMPLATE = "{range.low.and.high.null}";

    public static final String RANGE_HIGH_MUST_BE_BIGGER_THAN_LOW = "Range. High must be bigger than low.";
    public static final String RANGE_HIGH_MUST_BE_BIGGER_THAN_LOW_KEY = "range.high.to.small";
    public static final String RANGE_HIGH_MUST_BE_BIGGER_THAN_LOW_TEMPLATE = "{range.high.to.small}";

    public static final String RANGE_SIZE = "Range. Number of Ranges must be between 1 and 30.";
    public static final String RANGE_SIZE_KEY = "range.size";
    public static final String RANGE_SIZE_TEMPLATE = "{range.size}";

    public BiasValidationMessages() {
        addMessage(BIAS_TYPE_NULL_KEY, BIAS_TYPE_NULL);
        addMessage(BIAS_BOOST_NULL_KEY, BIAS_BOOST_NULL);
        addMessage(BIAS_STRENGTH_RANGE_KEY, BIAS_STRENGTH_RANGE);
        addMessage(BIAS_TEXT_CONTENT_EMPTY_KEY, BIAS_TEXT_CONTENT_EMPTY);
        addMessage(BIAS_TEXT_NUMERIC_CONTENT_FILLED_KEY, BIAS_TEXT_NUMERIC_CONTENT_FILLED);
        addMessage(BIAS_NUMERIC_CONTENT_EMPTY_KEY, BIAS_NUMERIC_CONTENT_EMPTY);
        addMessage(BIAS_NUMERIC_TEXT_CONTENT_FILLED_KEY, BIAS_NUMERIC_TEXT_CONTENT_FILLED);
        addMessage(BIAS_NUMERIC_CONTENT_BOTH_EMPTY_KEY, BIAS_NUMERIC_CONTENT_BOTH_EMPTY);

        addMessage(RANGE_LOW_AND_HIGH_NULL_KEY, RANGE_LOW_AND_HIGH_NULL);
        addMessage(RANGE_HIGH_MUST_BE_BIGGER_THAN_LOW_KEY, RANGE_HIGH_MUST_BE_BIGGER_THAN_LOW);
        addMessage(RANGE_SIZE_KEY, RANGE_SIZE);
    }
}
