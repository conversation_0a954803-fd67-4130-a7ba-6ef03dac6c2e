package com.groupbyinc.search.ssa.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_SEARCH_RESPONSE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_SEARCH_RESPONSE_TITLE;

@Builder(toBuilder = true)
@Schema(title = FACET_SEARCH_RESPONSE_TITLE, description = FACET_SEARCH_RESPONSE_DESCRIPTION)
public record FacetSearchResponseDto (
    SearchRequestDto originalRequest,
    NavigationDto availableNavigation,
    SearchMetadataDto metadata,
    DebugDto debug) {

}
