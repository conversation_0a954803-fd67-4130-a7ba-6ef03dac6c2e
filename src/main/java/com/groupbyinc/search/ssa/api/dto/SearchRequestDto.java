package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.api.dto.conversation.ConversationalSearchConfigDto;
import com.groupbyinc.search.ssa.api.dto.crm.UserAttributeDto;
import com.groupbyinc.search.ssa.api.dto.tiles.TilesNavigationDto;
import com.groupbyinc.search.ssa.api.validation.CollectionNullElementsConstraint;
import com.groupbyinc.search.ssa.api.validation.PinnedProductsConstraint;
import com.groupbyinc.search.ssa.core.SpellCorrectionMode;
import com.groupbyinc.search.ssa.core.SponsoredRecordsRequest;
import com.groupbyinc.search.ssa.core.rule.Overwrites;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.core.annotation.Nullable;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.groupbyinc.search.ssa.api.validation.Constants.PIN_TO_TOP_MAX_POSITION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_LIMIT;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.INCLUDE_EXPANDED_RESULTS_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PINNED_PRODUCTS_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PIN_UNEXPANDED_RESULTS_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_AREA_FIELD_DEFAULT_VALUE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_AREA_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_AREA_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_BIASING_PROFILE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_COLLECTION_FIELD_DEFAULT_VALUE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_COLLECTION_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_COLLECTION_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_CONVERSATIONAL_SEARCH_CONFIG_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_DEBUG_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_DYNAMIC_FACET_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_ENABLE_PART_NUMBER_FALLBACK_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_ENABLE_PART_NUMBER_SEARCH_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_ENABLE_TOPSORT_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_ENABLE_TOPSORT_V2_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_EXCLUDED_NAVIGATIONS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_INCLUDED_NAVIGATIONS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_INVENTORY_STORE_ID_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_LOGIN_ID_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_OVERWRITES_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_PAGE_CATEGORIES_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_PAGE_CATEGORIES_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_PAGE_SIZE_FIELD_DEFAULT_VALUE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_PAGE_SIZE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_PAGE_SIZE_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_PAGE_SIZE_FIELD_MIN_VALUE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_PRE_FILTER_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_PRE_FILTER_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_PRE_FILTER_FIELD_EXTERNAL_DOCS_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_PRE_FILTER_FIELD_EXTERNAL_DOCS_URL;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_QUERY_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_QUERY_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_RESPONSE_MASK_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_RESPONSE_MASK_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_SITE_FILTER_NAME_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_SKIP_FIELD_DEFAULT_VALUE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_SKIP_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_SKIP_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_SKIP_FIELD_MIN_VALUE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_SPONSORED_RECORDS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_TILES_NAVIGATION_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_VARIANT_ROLLUP_KEYS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_VARIANT_ROLLUP_KEYS_FIELD_EXTERNAL_DOCS_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_VARIANT_ROLLUP_KEYS_FIELD_EXTERNAL_DOCS_URL;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_VISITOR_ID_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_VISITOR_ID_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REWRITES_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REWRITES_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SPELL_CORRECTION_SPEC_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_ENABLE_PART_NUMBER_EXPANSION_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_USER_ATTRIBUTES_DESCRIPTION;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.NOT_REQUIRED;

@Schema(
    title = SEARCH_REQUEST_TITLE,
    description = SEARCH_REQUEST_DESCRIPTION
)
@Getter
@Setter
@AllArgsConstructor
@Builder(toBuilder = true)
@Introspected
@EqualsAndHashCode
public class SearchRequestDto {

    @Schema(
        description = SEARCH_REQUEST_QUERY_FIELD_DESCRIPTION,
        nullable = true,
        example = SEARCH_REQUEST_QUERY_FIELD_EXAMPLE
    )
    private String query;

    @Schema(
        description = SEARCH_REQUEST_AREA_FIELD_DESCRIPTION,
        nullable = true,
        defaultValue = SEARCH_REQUEST_AREA_FIELD_DEFAULT_VALUE,
        example = SEARCH_REQUEST_AREA_FIELD_EXAMPLE
    )
    private final String area;

    @Schema(
        description = SEARCH_REQUEST_COLLECTION_FIELD_DESCRIPTION,
        nullable = true,
        defaultValue = SEARCH_REQUEST_COLLECTION_FIELD_DEFAULT_VALUE,
        example = SEARCH_REQUEST_COLLECTION_FIELD_EXAMPLE
    )
    private final String collection;

    @Schema(
        description = SEARCH_REQUEST_VISITOR_ID_FIELD_DESCRIPTION,
        nullable = true,
        example = SEARCH_REQUEST_VISITOR_ID_FIELD_EXAMPLE
    )
    @JsonAlias({ "visitorId", "visitorID" })
    private final String visitorId;

    @CollectionNullElementsConstraint
    private List<SelectedRefinementDto> refinements;

    @Schema(
        description = SEARCH_REQUEST_PAGE_SIZE_FIELD_DESCRIPTION,
        nullable = true,
        defaultValue = SEARCH_REQUEST_PAGE_SIZE_FIELD_DEFAULT_VALUE,
        minimum = SEARCH_REQUEST_PAGE_SIZE_FIELD_MIN_VALUE,
        example = SEARCH_REQUEST_PAGE_SIZE_FIELD_EXAMPLE
    )
    private final Integer pageSize;

    @Schema(
        description = SEARCH_REQUEST_SKIP_FIELD_DESCRIPTION,
        nullable = true,
        defaultValue = SEARCH_REQUEST_SKIP_FIELD_DEFAULT_VALUE,
        minimum = SEARCH_REQUEST_SKIP_FIELD_MIN_VALUE,
        example = SEARCH_REQUEST_SKIP_FIELD_EXAMPLE
    )
    private final Long skip;

    @Schema(
        description = SEARCH_REQUEST_BIASING_PROFILE_FIELD_DESCRIPTION,
        nullable = true
    )
    private final String biasingProfile;

    @Valid
    private final BiasingProfileDto biasing;

    @CollectionNullElementsConstraint
    private final List<CustomParameterDto> customUrlParams;

    @JsonAlias({ "sort", "sorts" })
    @CollectionNullElementsConstraint
    private final List<SortDto> sorts;

    @JsonDeserialize(as = ArrayList.class)
    @Schema(
        description = SEARCH_REQUEST_INCLUDED_NAVIGATIONS_FIELD_DESCRIPTION,
        nullable = true
    )
    @CollectionNullElementsConstraint
    private final List<String> includedNavigations;

    @Schema(
        description = SEARCH_REQUEST_EXCLUDED_NAVIGATIONS_FIELD_DESCRIPTION,
        nullable = true
    )
    private final Set<String> excludedNavigations;

    @Schema(
        description = SEARCH_REQUEST_DYNAMIC_FACET_FIELD_DESCRIPTION,
        nullable = true,
        deprecated = true
    )
    private final Boolean dynamicFacet;

    @Schema(
        description = SEARCH_REQUEST_VARIANT_ROLLUP_KEYS_FIELD_DESCRIPTION,
        nullable = true,
        externalDocs = @ExternalDocumentation(
            description = SEARCH_REQUEST_VARIANT_ROLLUP_KEYS_FIELD_EXTERNAL_DOCS_DESCRIPTION,
            url = SEARCH_REQUEST_VARIANT_ROLLUP_KEYS_FIELD_EXTERNAL_DOCS_URL
        )
    )
    @CollectionNullElementsConstraint
    private final List<String> variantRollupKeys;

    @Schema(
        description = SEARCH_REQUEST_PRE_FILTER_FIELD_DESCRIPTION,
        nullable = true,
        example = SEARCH_REQUEST_PRE_FILTER_FIELD_EXAMPLE,
        externalDocs = @ExternalDocumentation(
            description = SEARCH_REQUEST_PRE_FILTER_FIELD_EXTERNAL_DOCS_DESCRIPTION,
            url = SEARCH_REQUEST_PRE_FILTER_FIELD_EXTERNAL_DOCS_URL
        )
    )
    private final String preFilter;

    @Schema(
        description = SEARCH_REQUEST_SITE_FILTER_NAME_FIELD_DESCRIPTION,
        nullable = true
    )
    private final String site;

    @Schema(
        description = SEARCH_REQUEST_RESPONSE_MASK_FIELD_DESCRIPTION,
        nullable = true,
        example = SEARCH_REQUEST_RESPONSE_MASK_FIELD_EXAMPLE
    )
    @JsonAlias({ "fields", "responseMask" })
    @CollectionNullElementsConstraint
    private final List<String> responseMask;

    @Schema(
        description = SEARCH_REQUEST_PAGE_CATEGORIES_FIELD_DESCRIPTION,
        nullable = true,
        example = SEARCH_REQUEST_PAGE_CATEGORIES_FIELD_EXAMPLE
    )
    @CollectionNullElementsConstraint
    private final List<String> pageCategories;

    @Schema(
        description = SPELL_CORRECTION_SPEC_DESCRIPTION,
        nullable = true,
        implementation = SpellCorrectionMode.class
    )
    private final SpellCorrectionMode spellCorrectionMode;

    @Schema(
        description = INCLUDE_EXPANDED_RESULTS_DESCRIPTION,
        nullable = true
    )
    private final Boolean includeExpandedResults;

    @Schema(
        description = PIN_UNEXPANDED_RESULTS_DESCRIPTION,
        nullable = true
    )
    private final Boolean pinUnexpandedResults;

    @Schema(
        description = SEARCH_REQUEST_DEBUG_FIELD_DESCRIPTION,
        nullable = true
    )
    private final boolean debug;

    @Nullable
    @Schema(description = FACET_LIMIT, nullable = true)
    private final Integer facetLimit;

    @Nullable
    @Schema(description = SEARCH_REQUEST_LOGIN_ID_FIELD_DESCRIPTION, nullable = true)
    // WARNING!!! This field should not be used in code. See RequestContext#loginId
    private final String loginId;

    @Nullable
    @Hidden
    @Schema(description = SEARCH_REQUEST_OVERWRITES_FIELD_DESCRIPTION, nullable = true)
    private Overwrites overwrites;

    @Valid
    @Nullable
    @Schema(
        description = PINNED_PRODUCTS_DESCRIPTION,
        maxLength = PIN_TO_TOP_MAX_POSITION,
        nullable = true
    )
    @PinnedProductsConstraint
    @CollectionNullElementsConstraint
    @Size(max = PIN_TO_TOP_MAX_POSITION)
    private List<PinnedProductDto> pinnedProducts;

    @Schema(
        description = SEARCH_REWRITES_DESCRIPTION,
        nullable = true,
        example = SEARCH_REWRITES_EXAMPLE
    )
    @CollectionNullElementsConstraint
    private final List<String> rewrites;

    @Schema(
        description = SEARCH_REQUEST_SPONSORED_RECORDS_FIELD_DESCRIPTION,
        nullable = true
    )
    private SponsoredRecordsRequest sponsoredRecords;

    @Schema(description = SEARCH_REQUEST_ENABLE_TOPSORT_FIELD_DESCRIPTION)
    private final boolean enableTopsort;

    @Schema(description = SEARCH_REQUEST_ENABLE_TOPSORT_V2_FIELD_DESCRIPTION)
    private final boolean enableTopsortV2;

    @Nullable
    @Schema(description = SEARCH_REQUEST_INVENTORY_STORE_ID_FIELD_DESCRIPTION, nullable = true)
    private String inventoryStoreId;

    @Schema(
        description = SEARCH_REQUEST_CONVERSATIONAL_SEARCH_CONFIG_DESCRIPTION,
        nullable = true
    )
    private ConversationalSearchConfigDto conversationalSearchConfig;

    @Schema(
        description = SEARCH_REQUEST_TILES_NAVIGATION_DESCRIPTION,
        nullable = true
    )
    private TilesNavigationDto tilesNavigation;

    @Nullable
    @Schema(
        description = SEARCH_REQUEST_ENABLE_PART_NUMBER_SEARCH_DESCRIPTION,
        nullable = true,
        requiredMode = NOT_REQUIRED
    )
    private final Boolean enablePartNumberSearch;

    @Nullable
    @Schema(
        description = SEARCH_REQUEST_ENABLE_PART_NUMBER_EXPANSION_DESCRIPTION,
        nullable = true,
        defaultValue = "true",
        requiredMode = NOT_REQUIRED
    )
    private final Boolean enablePartNumberExpansion;

    @Nullable
    @Schema(
        description = SEARCH_REQUEST_ENABLE_PART_NUMBER_FALLBACK_DESCRIPTION,
        nullable = true,
        defaultValue = "true",
        requiredMode = NOT_REQUIRED
    )
    private final Boolean enablePartNumberFallback;

    @Nullable
    @Schema(
        description = SEARCH_REQUEST_USER_ATTRIBUTES_DESCRIPTION,
        nullable = true
    )
    @Valid
    private List<UserAttributeDto> userAttributes;
}
