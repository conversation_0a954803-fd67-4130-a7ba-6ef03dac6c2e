
package com.groupbyinc.search.ssa.api.filter;

import io.micronaut.context.annotation.Requires;
import io.micronaut.context.annotation.Value;
import io.micronaut.http.HttpRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

import static com.groupbyinc.search.ssa.application.logging.LoggingContext.APP_NAME_MDC_KEY;
import static com.groupbyinc.search.ssa.application.logging.LoggingContext.EVENT_SUBTYPE_MDC_KEY;
import static com.groupbyinc.search.ssa.application.logging.LoggingContext.EVENT_TYPE_MDC_KEY;
import static com.groupbyinc.search.ssa.application.logging.LoggingContext.REQUEST_SAMPLED_MDC_KEY;
import static com.groupbyinc.search.ssa.application.logging.LoggingContext.SERVER_TIME_MDC_KEY;

@Slf4j
@RequiredArgsConstructor
@Requires(notEnv = "test")
@SuppressWarnings("unused")
public abstract class RequestLogEventWriter {

    public enum EventType { QUERY }

    public enum EventSubtype {
        PRODUCT_SEARCH,
        FACET,
        PRODUCT_DETAIL_PAGE,
        BROWSE,
        TILES_NAVIGATION,
        CONVERSATIONAL_SEARCH
    }

    private DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS z")
        .withZone(ZoneId.systemDefault());

    @Value("${micronaut.application.name}")
    private String appName;

    protected final void writeLog(HttpRequest<?> request, byte[] body) {
        Instant now = Instant.now();

        String logEventSubType = logEventSubType(request, body);
        MDC.put(APP_NAME_MDC_KEY, appName);
        MDC.put(EVENT_TYPE_MDC_KEY, logEventType());
        MDC.put(EVENT_SUBTYPE_MDC_KEY, logEventSubType);
        MDC.put(SERVER_TIME_MDC_KEY, DATE_TIME_FORMATTER.format(now));
        MDC.put(REQUEST_SAMPLED_MDC_KEY, Boolean.FALSE.toString());

        log.info("Search event : {}.{}", logEventType(), logEventSubType);
    }

    protected abstract String logEventType();

    protected abstract String logEventSubType(HttpRequest<?> request, byte[] body);
}
