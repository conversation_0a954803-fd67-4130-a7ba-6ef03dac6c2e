package com.groupbyinc.search.ssa.api.validation.context;

import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.core.area.AreaConfiguration;

import static com.groupbyinc.search.ssa.api.validation.context.RequestContextValidationException.areaNotFound;

/**
 * Validate that passed area exists and allowed to be used with a passed collection.
 */
public class AreaCollectionValidator implements ContextValidator {

    private final ConfigurationResolver<AreaConfiguration> areaConfigurationResolver;

    public AreaCollectionValidator(ConfigurationResolver<AreaConfiguration> areaConfigurationResolver) {
        this.areaConfigurationResolver = areaConfigurationResolver;
    }

    @Override
    public void validate(RequestContext context) throws RequestContextValidationException {
        areaConfigurationResolver
            .resolve(context)
            .orElseThrow(() -> areaNotFound(context.getArea(), context.getCollection()));
    }

}
