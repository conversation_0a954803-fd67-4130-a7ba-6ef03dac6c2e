package com.groupbyinc.search.ssa.api.dto;

import com.groupbyinc.search.ssa.core.zone.ZoneType;

import io.swagger.v3.oas.annotations.media.Schema;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.ZONE_TYPE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ZONE_TYPE_TITLE;

@Schema(title = ZONE_TYPE_TITLE, description = ZONE_TYPE_DESCRIPTION)
public enum ZoneDtoType {

    Content,
    Rich_Content,
    Products,
    Generated_Content,
    HTML;

    public static ZoneDtoType fromDomain(ZoneType type) {
        return switch (type) {
            case CONTENT -> Content;
            case RICH_CONTENT -> Rich_Content;
            case QUERY, EXPECT_QUERY -> Products;
            case GENERATED_CONTENT -> Generated_Content;
            case HTML -> HTML;
        };
    }

}
