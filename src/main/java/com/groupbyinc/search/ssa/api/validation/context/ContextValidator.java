package com.groupbyinc.search.ssa.api.validation.context;

import com.groupbyinc.search.ssa.api.RequestContext;

@FunctionalInterface
public interface ContextValidator {

    /**
     * Validate passed request context object
     *
     * @param context request metadata.
     *
     * @throws RequestContextValidationException if passed request context not valid.
     */
    void validate(RequestContext context) throws RequestContextValidationException;

}
