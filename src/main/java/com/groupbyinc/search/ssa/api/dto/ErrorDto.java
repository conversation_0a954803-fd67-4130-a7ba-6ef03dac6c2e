package com.groupbyinc.search.ssa.api.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.ERROR_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ERROR_MESSAGE_DEBUG_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ERROR_MESSAGE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ERROR_MESSAGE_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ERROR_METHOD_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ERROR_METHOD_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ERROR_PATH_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ERROR_PATH_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ERROR_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ERROR_TRACKING_ID_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.ERROR_TRACKING_ID_FIELD_EXAMPLE;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(title = ERROR_TITLE, description = ERROR_DESCRIPTION)
public record ErrorDto(

    @Schema(description = ERROR_TRACKING_ID_FIELD_DESCRIPTION, example = ERROR_TRACKING_ID_FIELD_EXAMPLE)
    String trackingId,

    @Schema(description = ERROR_METHOD_FIELD_DESCRIPTION, example = ERROR_METHOD_FIELD_EXAMPLE)
    String method,

    @Schema(description = ERROR_PATH_FIELD_DESCRIPTION, example = ERROR_PATH_FIELD_EXAMPLE)
    String path,

    @Schema(description = ERROR_MESSAGE_FIELD_DESCRIPTION, example = ERROR_MESSAGE_FIELD_EXAMPLE)
    String message,

    @Schema(description = ERROR_MESSAGE_DEBUG_DESCRIPTION)
    DebugDto debug) {

}
