package com.groupbyinc.search.ssa.metrics.opentelemetry.interceptors;

import com.groupbyinc.search.ssa.application.logging.LoggingContext;
import com.groupbyinc.search.ssa.metrics.opentelemetry.ContextUtils;
import com.groupbyinc.search.ssa.metrics.opentelemetry.MeterRegistry;

import io.micronaut.aop.InterceptorBean;
import io.micronaut.aop.MethodInterceptor;
import io.micronaut.aop.MethodInvocationContext;
import io.micronaut.context.annotation.Value;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import javax.annotation.Nullable;

/**
 * Interceptor process a given method in terms of OpenTelemetry for API invocations
 */
@Slf4j
@Singleton
@RequiredArgsConstructor
@InterceptorBean(ApiLatencyMetricsCollector.class)
public class ApiLatencyMetricsCollectorInterceptor implements MethodInterceptor<Object, Object> {

    private final MeterRegistry meterRegistry;

    @Value("${otel.metrics.enabled}")
    private boolean enabledMetrics;

    /**
     * Process a given method in terms of OpenTelemetry
     *
     * @param context The aop context
     * @return result of intercepted method
     */
    @Nullable
    @Override
    public Object intercept(MethodInvocationContext<Object, Object> context) {
        if (!enabledMetrics) {
            return context.proceed();
        }

        // Add extra surrounding context
        ContextUtils.attachContextForApiCall(
            MDC.get(LoggingContext.CUSTOMER_MDC_KEY),
            MDC.get(LoggingContext.COLLECTION_MDC_KEY),
            MDC.get(LoggingContext.AREA_MDC_KEY)
        );

        var apiType = "%s_%s".formatted(
            context.getDeclaringType().getSimpleName(),
            context.getExecutableMethod().getName()
        );

        return meterRegistry.actOnAPICall(apiType, context::proceed);
    }
}
