package com.groupbyinc.search.ssa.metrics.opentelemetry;

import com.groupbyinc.search.ssa.api.dto.FacetSearchResponseDto;
import com.groupbyinc.search.ssa.api.dto.SearchResponseDto;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.ProductSearchResultWrapper;
import com.groupbyinc.search.ssa.core.request.RequestType;

import io.micronaut.core.annotation.Nullable;
import io.micronaut.core.util.CollectionUtils;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.ContextStorage;
import lombok.experimental.UtilityClass;

import java.util.List;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.core.request.RequestType.BROWSE;
import static com.groupbyinc.search.ssa.core.request.RequestType.FACET_SEARCH;
import static com.groupbyinc.search.ssa.core.request.RequestType.PIN_TO_TOP;
import static com.groupbyinc.search.ssa.core.request.RequestType.SEARCH;
import static com.groupbyinc.search.ssa.core.request.RequestType.UNKNOWN;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AREA_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_CUSTOM_URL_PARAMS_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_PRE_FILTERS_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_REFINEMENTS_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_TOTAL_RECORD_COUNT_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.COLLECTION_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.CORRECTED_QUERY_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.CUSTOMER_ID_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.HAS_RESULTS_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.HAS_SUPPLIED_LOGIN_ID_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.HAS_SUPPLIED_VISITOR_ID_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.PAGE_CATEGORIES_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.REQUEST_SIZE_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.REQUEST_TYPE_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.RESPONSE_SERVED_LABEL_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.RESPONSE_SIZE_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.SEARCH_STRATEGY_LABEL_CONTEXT;

import static com.google.api.client.util.Objects.equal;
import static java.nio.charset.StandardCharsets.UTF_8;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.countMatches;

@UtilityClass
public class ContextUtils {

    public static void attachContextForSearch(
        Merchandiser merchandiser,
        String collection,
        SearchResponseDto result
    ) {
        if (isNotEmpty(Context.current())) {
            var updatedContext = Context.current()
                .with(CUSTOMER_ID_CONTEXT, merchandiser.merchandiserId())
                .with(COLLECTION_CONTEXT, collection);

            var requestType = UNKNOWN;

            if (isNotEmpty(result)) {
                var originalRequest = result.getOriginalRequest();
                if (isNotEmpty(originalRequest) && isNotEmpty(originalRequest.getQuery())) {
                    requestType = SEARCH;
                } else {
                    requestType = BROWSE;
                }
                var requestServed = "";
                if (result.getRequestServed() != null) {
                    requestServed = result.getRequestServed().name();
                }
                var searchStrategies = "";
                if (CollectionUtils.isNotEmpty(result.getSearchStrategies())) {
                    searchStrategies = String.join("|", result.getSearchStrategies());
                }
                updatedContext = updatedContext
                    .with(AREA_CONTEXT, result.getArea())
                    .with(PAGE_CATEGORIES_CONTEXT, isNotEmpty(originalRequest.getPageCategories()))
                    .with(CORRECTED_QUERY_CONTEXT, equal(originalRequest.getQuery(), result.getCorrectedQuery()))
                    .with(REQUEST_TYPE_CONTEXT, requestType.name())
                    .with(HAS_RESULTS_CONTEXT, isNotEmpty(result.getRecords()))
                    .with(HAS_SUPPLIED_VISITOR_ID_CONTEXT, isNotEmpty(originalRequest.getVisitorId()))
                    .with(HAS_SUPPLIED_LOGIN_ID_CONTEXT, isNotEmpty(originalRequest.getLoginId()))
                    .with(REQUEST_SIZE_CONTEXT, calculateLength(originalRequest))
                    .with(RESPONSE_SIZE_CONTEXT, calculateLength(result))
                    .with(AVERAGE_REFINEMENTS_CONTEXT, calculateSize(originalRequest.getRefinements()))
                    .with(AVERAGE_PRE_FILTERS_CONTEXT, calculateFiltersFromRaw(originalRequest.getPreFilter()))
                    .with(AVERAGE_CUSTOM_URL_PARAMS_CONTEXT, calculateSize(originalRequest.getCustomUrlParams()))
                    .with(RESPONSE_SERVED_LABEL_CONTEXT, requestServed)
                    .with(SEARCH_STRATEGY_LABEL_CONTEXT, searchStrategies)
                    .with(
                        AVERAGE_TOTAL_RECORD_COUNT_CONTEXT,
                        (double) (isEmpty(result.getTotalRecordCount()) ? 0 : result.getTotalRecordCount())
                    );
            }

            ContextStorage.defaultStorage().attach(updatedContext);
        }
    }

    public static void attachContextForFacetSearch(
        Merchandiser merchandiser,
        String collection,
        @Nullable FacetSearchResponseDto result
    ) {
        if (isNotEmpty(Context.current())) {
            var updatedContext = Context
                .current()
                .with(CUSTOMER_ID_CONTEXT, merchandiser.merchandiserId())
                .with(COLLECTION_CONTEXT, collection)
                .with(REQUEST_TYPE_CONTEXT, FACET_SEARCH.name());

            if (isNotEmpty(result)) {
                updatedContext = updatedContext
                    .with(HAS_RESULTS_CONTEXT, isNotEmpty(result.availableNavigation()))
                    .with(RESPONSE_SIZE_CONTEXT, calculateLength(result));
                if (isNotEmpty(result.originalRequest())) {
                    var originalRequest = result.originalRequest();
                    updatedContext = updatedContext
                        .with(AREA_CONTEXT, originalRequest.getArea())
                        .with(HAS_SUPPLIED_VISITOR_ID_CONTEXT, isNotEmpty(originalRequest.getVisitorId()))
                        .with(HAS_SUPPLIED_LOGIN_ID_CONTEXT, isNotEmpty(originalRequest.getLoginId()))
                        .with(PAGE_CATEGORIES_CONTEXT, isNotEmpty(originalRequest.getPageCategories()))
                        .with(REQUEST_SIZE_CONTEXT, calculateLength(originalRequest))
                        .with(AVERAGE_REFINEMENTS_CONTEXT, calculateSize(originalRequest.getRefinements()))
                        .with(AVERAGE_PRE_FILTERS_CONTEXT, calculateFiltersFromRaw(result.originalRequest().getPreFilter()))
                        .with(AVERAGE_CUSTOM_URL_PARAMS_CONTEXT, calculateSize(originalRequest.getCustomUrlParams()));
                }
            }
            ContextStorage.defaultStorage().attach(updatedContext);
        }
    }

    public static void attachContextForProductLookup(
        Merchandiser merchandiser,
        String collection
    ) {
        if (isNotEmpty(Context.current())) {
            var updatedContext = Context
                .current()
                .with(CUSTOMER_ID_CONTEXT, merchandiser.merchandiserId())
                .with(COLLECTION_CONTEXT, collection)
                .with(REQUEST_TYPE_CONTEXT, RequestType.PRODUCT_LOOKUP.name());

            ContextStorage.defaultStorage().attach(updatedContext);
        }
    }

    public static void attachContextForApiCall(
        String merchandiser,
        String collection,
        String area
    ) {
        if (isNotEmpty(Context.current())) {
            var updatedContext = Context.current()
                .with(CUSTOMER_ID_CONTEXT, merchandiser)
                .with(COLLECTION_CONTEXT, collection)
                .with(AREA_CONTEXT, area);
            ContextStorage.defaultStorage().attach(updatedContext);
        }
    }

    public static void attachContextForPinToTopSearch(ProductSearchResultWrapper result) {
        var context = getRequestContext();
        if (isNotEmpty(Context.current())) {
            var updatedContext = Context.current()
                .with(CUSTOMER_ID_CONTEXT, context.getMerchandiser().merchandiserId())
                .with(COLLECTION_CONTEXT, context.getCollection())
                .with(REQUEST_TYPE_CONTEXT, PIN_TO_TOP.name());
            if (isNotEmpty(result)) {
                updatedContext = updatedContext
                    .with(AREA_CONTEXT, context.getArea())
                    .with(HAS_RESULTS_CONTEXT, isNotEmpty(result.records()));
                if (result.servedFrom() != null) {
                    updatedContext = updatedContext
                        .with(RESPONSE_SERVED_LABEL_CONTEXT, result.servedFrom().name());
                }
            }
            ContextStorage.defaultStorage().attach(updatedContext);
        }
    }

    private static <T> double calculateLength(T obj) {
        return obj == null ? 0 : obj.toString().getBytes(UTF_8).length;
    }

    private static <T> double calculateSize(List<T> obj) {
        return obj == null ? 0 : obj.size();
    }

    private static double calculateFiltersFromRaw(String raw) {
        return raw == null ? 0 : countMatches(raw, "IN(") + countMatches(raw, "ANY(");
    }
}
