package com.groupbyinc.search.ssa.metrics.opentelemetry;

import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.annotation.Value;
import io.opentelemetry.api.metrics.DoubleHistogram;
import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.api.metrics.Meter;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.ContextKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.ACTIVE_IO_THREADS;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.ACTIVE_IO_THREADS_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.ACTIVE_IO_THREADS_DESCRIPTION;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.ALL_COUNTERS;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.ALL_HISTOGRAMS;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_CUSTOM_URL_PARAMS;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_CUSTOM_URL_PARAMS_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_CUSTOM_URL_PARAMS_DESCRIPTION;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_PRE_FILTERS;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_PRE_FILTERS_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_PRE_FILTERS_DESCRIPTION;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_REFINEMENTS;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_REFINEMENTS_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_REFINEMENTS_DESCRIPTION;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_TOTAL_RECORD_COUNT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_TOTAL_RECORD_COUNT_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.AVERAGE_TOTAL_RECORD_COUNT_DESCRIPTION;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.BYTES_UNIT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.COUNTER_UNIT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.MILLISECONDS_UNIT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.PROCESSED_REQUESTS;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.PROCESSED_REQUESTS_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.PROCESSED_REQUESTS_DESCRIPTION;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.REQUEST_LATENCY;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.REQUEST_LATENCY_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.REQUEST_LATENCY_DESCRIPTION;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.REQUEST_SIZE;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.REQUEST_SIZE_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.REQUEST_SIZE_DESCRIPTION;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.RESPONSE_SIZE;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.RESPONSE_SIZE_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.RESPONSE_SIZE_DESCRIPTION;

/**
 * Factory class responsible for creation of {@link LongCounter} and {@link DoubleHistogram} singletons. The main idea
 * is to have several single maps with required counters and histograms initiated on the start, hidden under application
 * properties, to decrease the complexity of required cycles during a customer request - the singleton object is simply
 * grabbed by name and is used without additional conditional checks.
 * <p>
 * The builder names used must be equals to {@link ContextKey} keys used in cross-cutting context. This is because of
 * the realisation under the hood.
 * <p>
 * When the request flow adds the new custom attribute with specified type to a {@link Context} the specified
 * appropriate {@link LongCounter} or {@link DoubleHistogram} being selected from specific map and used with passed
 * parameters. See {@link MeterRegistry} implementation for details.
 */
@Slf4j
@Factory
@RequiredArgsConstructor
public class MetricsFactory {

    /**
     * Meter with defined Prometheus exporter.
     */
    private final Meter meter;

    @Value("${otel.metrics.enabled:false}")
    private boolean enabled;

    @Value("${otel.metrics.histograms:}")
    private List<String> enabledHistograms;

    @Value("${otel.metrics.counters:}")
    private List<String> enabledCounters;

    private final io.micrometer.core.instrument.MeterRegistry micronautMeterRegistry;


    /**
     * @return singleton map with {@link LongCounter} objects
     */
    @io.micronaut.context.annotation.Context
    @Requires(notEnv = { "test" })
    public Map<ContextKey<String>, LongCounter> metricLongCounters() {
        Map<ContextKey<String>, LongCounter> counters = new HashMap<>();
        var registers = enabledCounters.contains("*") ? ALL_COUNTERS : enabledCounters;
        if (enabled && registers.contains(PROCESSED_REQUESTS)) {
            log.info("OpenTelemetry is enabled. Registering counters: {}", registers);
            var processedRequestCounter = meter
                .counterBuilder(PROCESSED_REQUESTS)
                .setDescription(PROCESSED_REQUESTS_DESCRIPTION)
                .setUnit(COUNTER_UNIT)
                .build();
            counters.put(PROCESSED_REQUESTS_CONTEXT, processedRequestCounter);
        }
        return counters;
    }

    /**
     * @return singleton map with {@link DoubleHistogram} objects
     */
    @io.micronaut.context.annotation.Context
    @Requires(notEnv = { "test" })
    public Map<ContextKey<Double>, DoubleHistogram> metricDoubleHistograms() {
        Map<ContextKey<Double>, DoubleHistogram> histograms = new HashMap<>();
        var registers = enabledHistograms.contains("*") ? ALL_HISTOGRAMS : enabledHistograms;
        if (enabled) {
            log.info("OpenTelemetry is enabled. Registering histograms: {}", registers);
            updateCarrier(registers, REQUEST_LATENCY, REQUEST_LATENCY_CONTEXT, REQUEST_LATENCY_DESCRIPTION, MILLISECONDS_UNIT, histograms);
            updateCarrier(registers, REQUEST_SIZE, REQUEST_SIZE_CONTEXT, REQUEST_SIZE_DESCRIPTION, BYTES_UNIT, histograms);
            updateCarrier(registers, RESPONSE_SIZE, RESPONSE_SIZE_CONTEXT, RESPONSE_SIZE_DESCRIPTION, BYTES_UNIT, histograms);
            updateCarrier(registers, AVERAGE_REFINEMENTS, AVERAGE_REFINEMENTS_CONTEXT, AVERAGE_REFINEMENTS_DESCRIPTION, COUNTER_UNIT, histograms);
            updateCarrier(registers, AVERAGE_PRE_FILTERS, AVERAGE_PRE_FILTERS_CONTEXT, AVERAGE_PRE_FILTERS_DESCRIPTION, COUNTER_UNIT, histograms);
            updateCarrier(registers, AVERAGE_CUSTOM_URL_PARAMS, AVERAGE_CUSTOM_URL_PARAMS_CONTEXT, AVERAGE_CUSTOM_URL_PARAMS_DESCRIPTION, COUNTER_UNIT, histograms);
            updateCarrier(registers, AVERAGE_TOTAL_RECORD_COUNT, AVERAGE_TOTAL_RECORD_COUNT_CONTEXT, AVERAGE_TOTAL_RECORD_COUNT_DESCRIPTION, COUNTER_UNIT, histograms);
            updateCarrier(registers, ACTIVE_IO_THREADS, ACTIVE_IO_THREADS_CONTEXT, ACTIVE_IO_THREADS_DESCRIPTION, COUNTER_UNIT, histograms);
        }
        return histograms;
    }

    private void updateCarrier(
        List<String> properties,
        String name,
        ContextKey<Double> contextName,
        String description,
        String unit,
        Map<ContextKey<Double>, DoubleHistogram> map
    ) {
        if (properties.contains(name)) {
            var histogram = meter
                .histogramBuilder(name)
                .setDescription(description)
                .setUnit(unit)
                .build();
            map.put(contextName, histogram);
        }
    }
}
