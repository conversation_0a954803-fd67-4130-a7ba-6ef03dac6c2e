package com.groupbyinc.search.ssa.metrics.opentelemetry;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Value;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.metrics.Meter;
import io.opentelemetry.exporter.prometheus.PrometheusHttpServer;
import io.opentelemetry.sdk.OpenTelemetrySdk;
import io.opentelemetry.sdk.metrics.SdkMeterProvider;

@Factory
public class MeterFactory {

    @Value("${otel.export.prometheus.port:9090}")
    private Integer prometheusExporterPort;

    @Value("${otel.export.prometheus.host:0.0.0.0}")
    private String prometheusExporterHost;

    @Context
    public OpenTelemetry openTelemetry() {
        var prometheusExporter = PrometheusHttpServer.builder()
            .setPort(prometheusExporterPort)
            .setHost(prometheusExporterHost)
            .build();

        var meterProvider = SdkMeterProvider.builder()
            .registerMetricReader(prometheusExporter)
            .build();

        return OpenTelemetrySdk.builder()
            .setMeterProvider(meterProvider)
            .buildAndRegisterGlobal();
    }

    @Context
    public Meter meter(
        OpenTelemetry openTelemetry,
        @Value("${meter.provider.instrumentation_scope:com.groupbyinc.search}") String instrumentationScope
    ) {
        return openTelemetry.meterBuilder(instrumentationScope)
            .build();
    }

}
