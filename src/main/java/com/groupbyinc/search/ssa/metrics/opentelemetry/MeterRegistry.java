package com.groupbyinc.search.ssa.metrics.opentelemetry;

import com.groupbyinc.search.ssa.api.utils.HttpUtils;

import io.micronaut.http.client.exceptions.HttpClientResponseException;
import io.micronaut.http.client.exceptions.ReadTimeoutException;
import io.micronaut.scheduling.annotation.Scheduled;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.metrics.DoubleHistogram;
import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.ContextKey;
import io.opentelemetry.context.ContextStorage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.function.Predicate;
import java.util.function.Supplier;

import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.ACTIVE_IO_THREADS_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.API_TYPE_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.APPLICATION_STARTUP_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.CUSTOM_BOOLEAN_CONTEXT_ATTRIBUTES;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.CUSTOM_STRING_CONTEXT_ATTRIBUTES;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.DOMAIN;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.DOMAIN_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.PASSED_VALIDATION_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.REQUEST_LATENCY_CONTEXT;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.Constants.STATUS_CODE_CONTEXT;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CODE_200;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CODE_500;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.CODE_504;

/**
 * Meter registry to wrap specific method and to pass specific attributes set via {@link Context} to defined exporter.
 */
@io.micronaut.context.annotation.Context
@RequiredArgsConstructor
@Slf4j
public class MeterRegistry {

    /**
     * Singleton carriers. See {@link MetricsFactory} for more details.
     */
    private final Map<ContextKey<String>, LongCounter> metricLongCounters;
    private final Map<ContextKey<Double>, DoubleHistogram> metricDoubleHistograms;

    private final io.micrometer.core.instrument.MeterRegistry micronautMeterRegistry;


    /**
     * Process counters and histograms for an Application startup
     */
    public void actOnApplicationStartup() {
        updateContext(APPLICATION_STARTUP_CONTEXT, 1L);
        recordSilently();
    }

    @Scheduled(fixedDelay = "5s")
    public void recordActiveRequests() {
        try {
            var histIO = metricDoubleHistograms.get(ACTIVE_IO_THREADS_CONTEXT);
            if (histIO != null) {
                histIO.record(micronautMeterRegistry.get("executor.active").gauge().value());
            }
        } catch (Exception e) {
            log.warn("Cannot record active requests histogram", e);
        }
    }

    /**
     * The entry-point method to wrap the supplier, measure the execution time and to pass context to exporter. Can be
     * used for any action requires execution and exporting attributes to Metrics Registry systems. This method calls
     * #recordSilently() which does not stop the thread the supplier is executed in. Which means the metrics export is
     * processed asynchronously.
     *
     * @param f   function to execute (the service logic)
     * @param <T> type
     *
     * @return function result or throw exception if such
     */
    public <T> T act(Supplier<T> f) {
        var passedValidation = true;
        final long r = monotonicTime();
        try {
            if (f != null) {
                updateContext(STATUS_CODE_CONTEXT, CODE_200);
                return f.get();
            } else {
                throw new IllegalArgumentException("Supplier argument cannot be null.");
            }
        } catch (Exception e) {
            passedValidation = false;
            updateContext(
                STATUS_CODE_CONTEXT,
                String.valueOf(HttpUtils.determineHttpStatus(e).getCode())
            );
            throw e;
        } finally {
            updateContext(REQUEST_LATENCY_CONTEXT, (double) monotonicTime() - r);
            updateContext(PASSED_VALIDATION_CONTEXT, passedValidation);
            recordSilently();
        }
    }

    /**
     * Wraps API executable supplier, measures execution time, populates attributes and passes event to exporter. Unlike
     * {@link MeterRegistry#act(Supplier)} does not use context due to the fact that there could be multiple API calls
     * within one request scope utilizing same metric event fields.
     *
     * @param apiType type of API call
     * @param f       function to execute
     * @param <T>     type
     *
     * @return function result or throw exception if such
     */
    public <T> T actOnAPICall(String apiType, Supplier<T> f) {
        final long t = monotonicTime();
        var passedValidation = true;
        var attrsBuilder = Attributes.builder().put(DOMAIN_CONTEXT.toString(), DOMAIN);
        try {
            if (f != null) {
                T exec = f.get();
                attrsBuilder.put(STATUS_CODE_CONTEXT.toString(), CODE_200);
                return exec;
            } else {
                throw new IllegalArgumentException("Supplier argument cannot be null.");
            }
        } catch (HttpClientResponseException e) {
            passedValidation = false;
            attrsBuilder.put(STATUS_CODE_CONTEXT.toString(), e.getStatus().getCode());
            throw e;
        } catch (ReadTimeoutException e) {
            passedValidation = false;
            attrsBuilder.put(STATUS_CODE_CONTEXT.toString(), CODE_504);
            throw e;
        } catch (RuntimeException e) {
            passedValidation = false;
            attrsBuilder.put(STATUS_CODE_CONTEXT.toString(), CODE_500);
            throw e;
        } finally {
            attrsBuilder
                .put(API_TYPE_CONTEXT.toString(), apiType)
                .put(PASSED_VALIDATION_CONTEXT.toString(), passedValidation);

            recordSilently(
                metricDoubleHistograms.get(REQUEST_LATENCY_CONTEXT),
                (double) monotonicTime() - t,
                attrsBuilder.build()
            );
        }
    }

    public <T> T timer(ContextKey<Double> contextKey, Supplier<T> f) {
        final long r = monotonicTime();
        try {
            if (f != null) {
                return f.get();
            } else {
                throw new IllegalArgumentException("Supplier argument cannot be null.");
            }
        } finally {
            updateContext(contextKey, (double) monotonicTime() - r);
        }
    }

    /**
     * Initially, the default storage is used. This is visible in scope of specific http-request only as it uses local
     * thread variables to store and bundle context between application modules. The storage type may be changed to
     * downstream carriers to between processes (e.g. to downstream required attributes via http-headers). This case, if
     * it was done not properly, the existing context for the specific thread may be null.
     *
     * @param key   object to set
     * @param value object to set
     * @param <V>   type of {@link ContextKey}
     */
    private <V> void updateContext(ContextKey<V> key, V value) {
        var context = Context.current();
        if (context != null) {
            var updated = context
                .with(key, value);
            ContextStorage.defaultStorage().attach(updated);
        }
    }

    /**
     * Takes all the known measurement singletons registered as {@link LongCounter} and {@link DoubleHistogram},
     * iterates over it and uses to pass the context attributes to exporter with it. Generally, if the map (or maps)
     * contains registered counter or histogram, but the value for this instrument is not present in context - it simply
     * will not be passed to exporter.
     */
    private void recordSilently() {
        var context = Context.current();

        metricLongCounters.forEach((key, value) ->
            value.add(1, propagateFromContext(ContextAttribute::isEnabledForCounters)));

        if (context != null) {
            metricDoubleHistograms.forEach((key, value) -> {
                var valFromContext = context.get(key);
                if (valFromContext != null) {
                    value.record(valFromContext, propagateFromContext(ContextAttribute::isEnabledForHistograms));
                }
            });
        }
    }

    /**
     * Writes to exporter single {@link DoubleHistogram} event with provided value and attributes, that will be attached
     * to default attributes taken from context. This API is used in case when using context is not applicable, i.e.
     * there are multiple metric events sharing it names or attributes in scope of single request (and single context).
     */
    private void recordSilently(DoubleHistogram histogram, Double value, Attributes attachAttributes) {
        var context = Context.current();
        if (context != null && histogram != null) {
            var contextAttributes = propagateFromContext(ContextAttribute::isEnabledForHistograms);
            histogram.record(value, contextAttributes.toBuilder().putAll(attachAttributes).build());
        }
    }

    /**
     * @return attributes with mandatory and optional (if present) key-value pairs
     */
    private Attributes propagateFromContext(Predicate<ContextAttribute<?>> predicate) {
        var context = Context.current();
        var attributesBuilder = Attributes.builder().put(DOMAIN_CONTEXT.toString(), DOMAIN);
        if (context != null) {
            CUSTOM_STRING_CONTEXT_ATTRIBUTES
                .stream()
                .filter(predicate)
                .forEach(attr -> {
                    var value = context.get(attr.getContextKey());
                    if (value != null) {
                        attributesBuilder.put(attr.getContextKey().toString(), value);
                    }
                });
            CUSTOM_BOOLEAN_CONTEXT_ATTRIBUTES
                .stream()
                .filter(predicate)
                .forEach(attr -> {
                    var value = context.get(attr.getContextKey());
                    if (value != null) {
                        attributesBuilder.put(attr.getContextKey().toString(), value);
                    }
                });
        }
        return attributesBuilder.build();
    }

    public static long monotonicTime() {
        return System.currentTimeMillis();
    }
}
