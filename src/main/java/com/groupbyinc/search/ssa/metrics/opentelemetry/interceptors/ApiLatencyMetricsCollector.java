package com.groupbyinc.search.ssa.metrics.opentelemetry.interceptors;

import io.micronaut.aop.Around;

import java.lang.annotation.Documented;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.METHOD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * Annotation to process a given method in terms of OpenTelemetry for API invocations
 */
@Around
@Documented
@Target(METHOD)
@Retention(RUNTIME)
@Inherited
public @interface ApiLatencyMetricsCollector {

}
