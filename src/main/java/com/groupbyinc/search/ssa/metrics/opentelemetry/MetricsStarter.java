package com.groupbyinc.search.ssa.metrics.opentelemetry;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.event.ApplicationEventListener;
import io.micronaut.discovery.event.ServiceReadyEvent;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Context
@AllArgsConstructor
@Requires(notEnv = {"test", "local"})
public class MetricsStarter implements ApplicationEventListener<ServiceReadyEvent> {

    private final MeterRegistry meterRegistry;

    @Override
    public void onApplicationEvent(ServiceReadyEvent event) {
        log.info("Init Metrics On Startup");
        meterRegistry.actOnApplicationStartup();
    }

}
