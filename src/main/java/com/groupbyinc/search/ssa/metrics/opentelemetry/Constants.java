package com.groupbyinc.search.ssa.metrics.opentelemetry;

import io.opentelemetry.context.ContextKey;
import lombok.experimental.UtilityClass;

import java.util.List;

@UtilityClass
public class Constants {

    /**
     * Site wide domain/service identifier.
     */
    public static final String DOMAIN = "site-search";

    // Counters and histogram units.
    public static final String COUNTER_UNIT = "1";
    public static final String MILLISECONDS_UNIT = "ms";
    public static final String BYTES_UNIT = "bytes";

    // The name of propagation attributes equals to the name of counter in OpenTelemetry.
    public static final String PROCESSED_REQUESTS = "processedRequests";
    public static final List<String> ALL_COUNTERS = List.of(PROCESSED_REQUESTS);

    // The name of propagation attributes equals to the name of histogram in OpenTelemetry.
    public static final String ERROR_RATE = "errorRate";
    public static final String REQUEST_LATENCY = "requestLatency";
    public static final String REQUEST_SIZE = "requestSize";
    public static final String RESPONSE_SIZE = "responseSize";
    public static final String AVERAGE_REFINEMENTS = "averageRefinements";
    public static final String APPLICATION_STARTUP = "applicationStartup";
    public static final String AVERAGE_PRE_FILTERS = "averagePreFilters";
    public static final String AVERAGE_CUSTOM_URL_PARAMS = "averageCustomUrlParams";
    public static final String AVERAGE_TOTAL_RECORD_COUNT = "averageTotalRecordCount";
    public static final String ACTIVE_IO_THREADS = "activeIO";
    public static final String RESPONSE_SERVED_LABEL = "responseServed";
    public static final String SEARCH_STRATEGY_LABEL = "searchStrategy";
    public static final List<String> ALL_HISTOGRAMS = List.of(
        ERROR_RATE,
        REQUEST_LATENCY,
        REQUEST_SIZE,
        RESPONSE_SIZE,
        AVERAGE_REFINEMENTS,
        AVERAGE_PRE_FILTERS,
        AVERAGE_CUSTOM_URL_PARAMS,
        AVERAGE_TOTAL_RECORD_COUNT,
        ACTIVE_IO_THREADS
    );

    // The name of propagation attributes equals to the name of metric in OpenTelemetry.
    public static final ContextKey<Long> APPLICATION_STARTUP_CONTEXT = ContextKey.named(APPLICATION_STARTUP);
    public static final ContextKey<String> PROCESSED_REQUESTS_CONTEXT = ContextKey.named(PROCESSED_REQUESTS);
    public static final ContextKey<Double> REQUEST_LATENCY_CONTEXT = ContextKey.named(REQUEST_LATENCY);
    public static final ContextKey<Double> REQUEST_SIZE_CONTEXT = ContextKey.named(REQUEST_SIZE);
    public static final ContextKey<Double> RESPONSE_SIZE_CONTEXT = ContextKey.named(RESPONSE_SIZE);
    public static final ContextKey<Double> AVERAGE_REFINEMENTS_CONTEXT = ContextKey.named(AVERAGE_REFINEMENTS);
    public static final ContextKey<Double> AVERAGE_PRE_FILTERS_CONTEXT = ContextKey.named(AVERAGE_PRE_FILTERS);
    public static final ContextKey<Double> AVERAGE_CUSTOM_URL_PARAMS_CONTEXT = ContextKey.named(AVERAGE_CUSTOM_URL_PARAMS);
    public static final ContextKey<Double> AVERAGE_TOTAL_RECORD_COUNT_CONTEXT = ContextKey.named(AVERAGE_TOTAL_RECORD_COUNT);
    public static final ContextKey<Double> ACTIVE_IO_THREADS_CONTEXT = ContextKey.named(ACTIVE_IO_THREADS);

    // Mandatory application attributes.
    public static final ContextKey<String> DOMAIN_CONTEXT = ContextKey.named("domain");
    public static final ContextKey<String> CUSTOMER_ID_CONTEXT = ContextKey.named("customerId");
    public static final ContextKey<String> REQUEST_TYPE_CONTEXT = ContextKey.named("requestType");
    public static final ContextKey<String> COLLECTION_CONTEXT = ContextKey.named("collection");
    public static final ContextKey<Boolean> PAGE_CATEGORIES_CONTEXT = ContextKey.named("pageCategories");
    public static final ContextKey<Boolean> CORRECTED_QUERY_CONTEXT = ContextKey.named("correctedQuery");
    public static final ContextKey<String> RESPONSE_SERVED_LABEL_CONTEXT = ContextKey.named(RESPONSE_SERVED_LABEL);
    public static final ContextKey<String> SEARCH_STRATEGY_LABEL_CONTEXT = ContextKey.named(SEARCH_STRATEGY_LABEL);
    // Optional application attributes.
    public static final ContextKey<String> AREA_CONTEXT = ContextKey.named("area");
    public static final ContextKey<Boolean> HAS_RESULTS_CONTEXT = ContextKey.named("hasResults");
    public static final ContextKey<Boolean> HAS_SUPPLIED_VISITOR_ID_CONTEXT = ContextKey.named("hasSuppliedVisitorId");
    public static final ContextKey<Boolean> HAS_SUPPLIED_LOGIN_ID_CONTEXT = ContextKey.named("hasSuppliedLoginId");
    public static final ContextKey<Boolean> PASSED_VALIDATION_CONTEXT = ContextKey.named("passedValidation");
    public static final ContextKey<String> STATUS_CODE_CONTEXT = ContextKey.named("statusCode");
    public static final ContextKey<String> API_TYPE_CONTEXT = ContextKey.named("apiType");

    // Propagation string attributes.
    // Newly added propagation attributes must be present in the current list.
    public static final List<ContextAttribute<String>> CUSTOM_STRING_CONTEXT_ATTRIBUTES = List.of(
        new ContextAttribute<>(CUSTOMER_ID_CONTEXT, true, true),
        new ContextAttribute<>(REQUEST_TYPE_CONTEXT, true, true),
        new ContextAttribute<>(COLLECTION_CONTEXT, true, true),
        new ContextAttribute<>(AREA_CONTEXT, true, true),
        new ContextAttribute<>(STATUS_CODE_CONTEXT, true, true),
        new ContextAttribute<>(API_TYPE_CONTEXT, true, true),
        new ContextAttribute<>(RESPONSE_SERVED_LABEL_CONTEXT, true, true)
    );

    // Propagation boolean attributes.
    // Newly added propagation attributes must be present in the current list.
    public static final List<ContextAttribute<Boolean>> CUSTOM_BOOLEAN_CONTEXT_ATTRIBUTES = List.of(
        new ContextAttribute<>(HAS_RESULTS_CONTEXT, true, false),
        new ContextAttribute<>(HAS_SUPPLIED_VISITOR_ID_CONTEXT, true, false),
        new ContextAttribute<>(HAS_SUPPLIED_LOGIN_ID_CONTEXT, true, false),
        new ContextAttribute<>(PASSED_VALIDATION_CONTEXT, true, false),
        new ContextAttribute<>(PAGE_CATEGORIES_CONTEXT, true, false),
        new ContextAttribute<>(CORRECTED_QUERY_CONTEXT, true, false)
    );

    // Descriptions.
    public static final String PROCESSED_REQUESTS_DESCRIPTION = "The total number of processed requests in instrumentation scope.";
    public static final String REQUEST_LATENCY_DESCRIPTION = "Request latency.";
    public static final String REQUEST_SIZE_DESCRIPTION = "Request size.";
    public static final String RESPONSE_SIZE_DESCRIPTION = "Response size.";
    public static final String AVERAGE_REFINEMENTS_DESCRIPTION = "Average number of refinements.";
    public static final String AVERAGE_PRE_FILTERS_DESCRIPTION = "Average number of preFilters.";
    public static final String AVERAGE_CUSTOM_URL_PARAMS_DESCRIPTION = "Average number of customUrlParams.";
    public static final String AVERAGE_TOTAL_RECORD_COUNT_DESCRIPTION = "Average number of totalRecordCount.";
    public static final String ACTIVE_IO_THREADS_DESCRIPTION = "Number of active micronaut IO threads.";
}
