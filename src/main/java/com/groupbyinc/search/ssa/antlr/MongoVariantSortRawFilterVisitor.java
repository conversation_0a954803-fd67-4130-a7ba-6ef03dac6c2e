package com.groupbyinc.search.ssa.antlr;

import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortComparisonExpression;
import com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortRangeExpression;
import com.groupbyinc.search.ssa.mongo.filtering.variants.expression.text.MongoVariantSortTextExpression;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import java.util.List;
import java.util.Map;

import static com.groupbyinc.search.ssa.application.core.search.filtering.FilterService.LOGICAL_AND;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_AND;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_NOT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_OR;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.LOCAL_INVENTORIES_PREFIX;
import static com.groupbyinc.search.ssa.util.AttributeUtils.transformField;
import static com.groupbyinc.search.ssa.util.StringUtils.EMPTY;

/**
 * This class provides an implementation of {@link FilterBaseVisitor}, we're only
 * handling a subset of available methods that we wish to override when visited.
 */
@Slf4j
@RequiredArgsConstructor
public class MongoVariantSortRawFilterVisitor extends FilterBaseVisitor<Document> {

    private final Map<String, AttributeConfiguration> attributes;

    @Override
    public Document visitNegationExpression(FilterParser.NegationExpressionContext ctx) {
        return new Document($_NOT, this.visit(ctx.expression()));
    }

    @Override
    public Document visitParenthesisExpression(FilterParser.ParenthesisExpressionContext ctx) {
        return this.visit(ctx.expression());
    }

    @Override
    public Document visitLogicalExpression(FilterParser.LogicalExpressionContext ctx) {
        var operator = $_OR;
        if (LOGICAL_AND.equals(ctx.operator.getText())) {
            operator = $_AND;
        }

        return new Document(
            operator,
            List.of(
                this.visit(ctx.left),
                this.visit(ctx.right)
            )
        );
    }

    @Override
    public Document visitTextExpression(FilterParser.TextExpressionContext ctx) {
        return getFilterForTextExpression(ctx.textField().getText(), null, ctx.anyValues().literal(), false);
    }

    @Override
    public Document visitInventoryTextExpression(FilterParser.InventoryTextExpressionContext ctx) {
        return getFilterForTextExpression(
            ctx.inventoryFacet().textField().getText(),
            ctx.inventoryFacet().placeId().getText(),
            ctx.anyValues().literal(),
            true
        );
    }

    @Override
    public Document visitRangeExpression(FilterParser.RangeExpressionContext ctx) {
        return getFilterForRangeExpression(ctx.numericalField().identifier().getText(), null, ctx.values, false);
    }

    @Override
    public Document visitInventoryRangeExpression(FilterParser.InventoryRangeExpressionContext ctx) {
        return getFilterForRangeExpression(
            ctx.inventoryFacet().textField().getText(),
            ctx.inventoryFacet().placeId().getText(),
            ctx.values,
            true
        );
    }

    @Override
    public Document visitComparisonExpression(FilterParser.ComparisonExpressionContext ctx) {
        var operator = ctx.comparison().op.getText();
        var value = Double.valueOf(ctx.values.getText());
        var facet = transformField(ctx.numericalField().identifier().getText(), attributes, false);

        return new MongoVariantSortComparisonExpression(facet, operator, value).toFilter();
    }

    @Override
    public Document visitInventoryComparisonExpression(FilterParser.InventoryComparisonExpressionContext ctx) {
        var operator = ctx.comparison().op.getText();
        var value = Double.valueOf(ctx.values.getText());

        var field = transformField(ctx.inventoryFacet().textField().getText(), attributes, true)
            .replace(LOCAL_INVENTORIES_PREFIX, EMPTY);

        return new MongoVariantSortComparisonExpression(
            field,
            ctx.inventoryFacet().placeId().getText(),
            operator,
            value
        ).toFilter();
    }

    private Document getFilterForTextExpression(String facet,
                                                String placeId,
                                                List<FilterParser.LiteralContext> values,
                                                boolean inventory) {
        var parsedValues = values
            .stream()
            .map(lit -> lit.getText().replaceAll("^\"|\"$", ""))
            .toList();

        var field =  transformField(facet, attributes, inventory);
        if (inventory) {
            field = facet.replace(LOCAL_INVENTORIES_PREFIX, EMPTY);
        }

        return new MongoVariantSortTextExpression(
            field,
            placeId,
            parsedValues
        ).toFilter();
    }

    private Document getFilterForRangeExpression(String parsedFacet,
                                                 String placeId,
                                                 FilterParser.InValuesContext inValues,
                                                 boolean inventory) {
        var lowerBound = inValues.lowerBound().numberLiteral().getText();
        var upperBound = inValues.upperBound().numberLiteral().getText();
        var facet = transformField(parsedFacet, attributes, inventory);
        if (inventory) {
            facet = facet.replace(LOCAL_INVENTORIES_PREFIX, EMPTY);
        }

        return MongoVariantSortRangeExpression.fromRaw(facet, placeId, lowerBound, upperBound).toFilter();
    }

}
