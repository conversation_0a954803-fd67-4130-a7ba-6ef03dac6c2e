package com.groupbyinc.search.ssa.antlr;

import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.LOCAL_INVENTORIES_PLACE_ID_PATH;
import static com.groupbyinc.search.ssa.util.AttributeUtils.transformField;

/**
 * This class provides an implementation of {@link FilterBaseVisitor}, we're only
 * handling a subset of available methods that we wish to override when visited.
 */
@Slf4j
@RequiredArgsConstructor
public class MongoRawFilterFieldsCollectorVisitor extends FilterBaseVisitor<Set<String>> {

    private final Map<String, AttributeConfiguration> attributes;

    @Override
    public Set<String> visitNegationExpression(FilterParser.NegationExpressionContext ctx) {
        return this.visit(ctx.expression());
    }

    @Override
    public Set<String> visitParenthesisExpression(FilterParser.ParenthesisExpressionContext ctx) {
        return this.visit(ctx.expression());
    }

    @Override
    public Set<String> visitLogicalExpression(FilterParser.LogicalExpressionContext ctx) {
        var fields = new HashSet<String>();
        fields.addAll(this.visit(ctx.left));
        fields.addAll(this.visit(ctx.right));
        return fields;
    }

    @Override
    public Set<String> visitTextExpression(FilterParser.TextExpressionContext ctx) {
        return Set.of(transformField(ctx.textField().getText(), attributes, false));
    }

    @Override
    public Set<String> visitInventoryTextExpression(FilterParser.InventoryTextExpressionContext ctx) {
        return Set.of(
            transformField(
                ctx.inventoryFacet().textField().getText(),
                attributes,
                true
            ),
            LOCAL_INVENTORIES_PLACE_ID_PATH
        );
    }

    @Override
    public Set<String> visitRangeExpression(FilterParser.RangeExpressionContext ctx) {
        return Set.of(transformField(ctx.numericalField().identifier().getText(), attributes, false));
    }

    @Override
    public Set<String> visitInventoryRangeExpression(FilterParser.InventoryRangeExpressionContext ctx) {
        return Set.of(
            transformField(
                ctx.inventoryFacet().textField().getText(),
                attributes,
                true
            ),
            LOCAL_INVENTORIES_PLACE_ID_PATH
        );
    }

    @Override
    public Set<String> visitComparisonExpression(FilterParser.ComparisonExpressionContext ctx) {
        return Set.of(transformField(ctx.numericalField().identifier().getText(), attributes, false));
    }

    @Override
    public Set<String> visitInventoryComparisonExpression(FilterParser.InventoryComparisonExpressionContext ctx) {
        return Set.of(
            transformField(
                ctx.inventoryFacet().textField().getText(),
                attributes,
                true
            ),
            LOCAL_INVENTORIES_PLACE_ID_PATH
        );
    }

}
