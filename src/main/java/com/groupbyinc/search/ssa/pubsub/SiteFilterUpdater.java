package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.SiteFilterMessage;
import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;

import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;

@Singleton
@RequiredArgsConstructor
public class SiteFilterUpdater implements ConfigUpdater {

    private final ConfigurationManager configurationManager;

    @Override
    public void update(SiteSearchConfigurationUpdateMessage updateMessage) {
        var newSiteFilters = updateMessage.getSiteFilterList().stream()
            .map(this::map)
            .toList();
        configurationManager.updateSiteFilterConfig(newSiteFilters);
    }

    @Override
    public boolean isApplicable(SiteSearchConfigurationUpdateMessage updateMessage) {
        return updateMessage.getSiteFilterList().size() > 0;
    }

    private SiteFilterConfiguration map(SiteFilterMessage siteFilterUpdate) {
        return SiteFilterConfiguration.builder()
            .id(siteFilterUpdate.getId())
            .name(siteFilterUpdate.getName())
            .rawFilter(siteFilterUpdate.getRawFilter())
            .collectionId(siteFilterUpdate.getCollectionId())
            .messageType(siteFilterUpdate.getMessageType())
            .build();
    }
}
