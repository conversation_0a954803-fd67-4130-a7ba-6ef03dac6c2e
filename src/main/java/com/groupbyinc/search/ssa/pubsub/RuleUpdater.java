package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.RuleMessage;
import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.filter.AttributeFilter;
import com.groupbyinc.search.ssa.core.filter.ProductIdFilter;
import com.groupbyinc.search.ssa.core.filter.RangeFilter;
import com.groupbyinc.search.ssa.core.filter.SearchFilter;
import com.groupbyinc.search.ssa.core.filter.ValueFilter;
import com.groupbyinc.search.ssa.core.rule.ExperimentVariant;
import com.groupbyinc.search.ssa.core.rule.PinnedProduct;
import com.groupbyinc.search.ssa.core.rule.PinnedRefinement;
import com.groupbyinc.search.ssa.core.rule.ProductIdsBucket;
import com.groupbyinc.search.ssa.core.rule.ProductVisibilityBias;
import com.groupbyinc.search.ssa.core.rule.Refinement;
import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
import com.groupbyinc.search.ssa.core.rule.RuleTemplate;
import com.groupbyinc.search.ssa.core.rule.RuleTemplateSection;
import com.groupbyinc.search.ssa.core.rule.RuleType;
import com.groupbyinc.search.ssa.core.rule.RuleVariant;
import com.groupbyinc.search.ssa.core.trigger.CustomParameterTrigger;
import com.groupbyinc.search.ssa.core.trigger.QueryPatternTrigger;
import com.groupbyinc.search.ssa.core.trigger.SelectedRefinementTrigger;
import com.groupbyinc.search.ssa.core.trigger.TriggerSet;
import com.groupbyinc.search.ssa.core.zone.ZoneType;

import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.groupbyinc.search.ssa.pubsub.MessageMapperUtils.buildRange;

@Singleton
@RequiredArgsConstructor
public class RuleUpdater implements ConfigUpdater {

    private final ConfigurationManager configurationManager;

    public RuleConfiguration map(RuleMessage ruleUpdate) {
        var rc = RuleConfiguration.builder()
            .id(ruleUpdate.getId())
            .name(ruleUpdate.getName())
            .areaId(ruleUpdate.getAreaId())
            .priority(ruleUpdate.getPriority())
            .includedNavigations(ruleUpdate.getIncludedNavigationsList())
            .messageType(ruleUpdate.getMessageType())
            .searchFilters(getSearchFilters(ruleUpdate.getSearchFilterList()))
            .attributeFilters(getAttributeFilters(ruleUpdate.getAttributeFiltersList()))
            .triggerSets(getTriggerSets(ruleUpdate.getTriggerSetList()))
            .pinnedRefinements(getPinnedRefinements(ruleUpdate.getPinnedRefinementsList()))
            .boostedProductBuckets(
                ruleUpdate
                    .getBoostedProductsList()
                    .stream()
                    .map(e -> new ProductIdsBucket(e.getProductIdsList()))
                    .toList()
            )
            .buriedProductBuckets(
                ruleUpdate
                    .getBuriedProductsList()
                    .stream()
                    .map(e -> new ProductIdsBucket(e.getProductIdsList()))
                    .toList()
            )
            .pinnedProducts(
                ruleUpdate
                    .getPinnedProductsList()
                    .stream()
                    .map(e -> new PinnedProduct(e.getPosition(), e.getProductId(), e.getIncludeOutOfStock()))
                    .toList()
            )
            .type(RuleType.valueOf(ruleUpdate.getType().name()))
            .variants(getVariants(ruleUpdate.getExperimentVariantsList()));

        if (ruleUpdate.hasBiasingProfileName()) {
            rc.biasingProfileName(ruleUpdate.getBiasingProfileName());
        }

        if (ruleUpdate.getActiveTimeRange().getActiveHoursEnabled()) {
            rc.activeHoursEnabled(true);
            if (ruleUpdate.getActiveTimeRange().hasActiveFrom()) {
                rc.activeFrom(ruleUpdate.getActiveTimeRange().getActiveFrom());
            }
            if (ruleUpdate.getActiveTimeRange().hasActiveTo()) {
                rc.activeTo(ruleUpdate.getActiveTimeRange().getActiveTo());
            }
        }

        if (ruleUpdate.hasTemplate()) {
            rc.template(getRuleTemplate(ruleUpdate.getTemplate()));
        }

        if(ruleUpdate.hasProductIdFilter()){
            rc.productIdFilter(getProductIdFilters(ruleUpdate.getProductIdFilter()));
        }

        if (ruleUpdate.hasProductVisibilityBias()) {
            rc.productVisibilityBias(getProductVisibilityBias(ruleUpdate.getProductVisibilityBias()));
        }

        return rc.build();
    }

    @Override
    public void update(SiteSearchConfigurationUpdateMessage updateMessage) {
        var newProfiles = updateMessage.getRuleList().stream()
            .map(this::map)
            .toList();
        configurationManager.updateRuleConfig(newProfiles);
    }

    @Override
    public boolean isApplicable(SiteSearchConfigurationUpdateMessage updateMessage) {
        return updateMessage.getRuleCount() > 0;
    }


    private List<ValueFilter> getValueFilters(List<RuleMessage.ValueFilterMessage> valueFilters) {
        return valueFilters.stream()
            .map(f -> {
                var builder = ValueFilter.builder()
                    .field(f.getField())
                    .exclude(f.getExclude());

                if (f.getType() == RuleMessage.ValueFilterMessage.ValueFilterType.TEXTUAL) {
                    builder
                        .type(ValueFilter.ValueFilterType.TEXTUAL)
                        .value(f.getValue());
                } else {
                    builder
                        .type(ValueFilter.ValueFilterType.NUMERIC)
                        .numberValue(f.getNumberValue());
                }

                return builder.build();
            }).collect(Collectors.toList());
    }

    private List<SearchFilter> getSearchFilters(List<RuleMessage.SearchFilterMessage> searchFilters) {
        return searchFilters.stream()
            .map(f -> SearchFilter.builder().value(f.getValue()).build())
            .collect(Collectors.toList());
    }

    private List<RangeFilter> getRangeFilters(List<RuleMessage.RangeFilterMessage> rangeFilters) {
        return rangeFilters.stream()
            .map(f -> RangeFilter.builder()
                .field(f.getField())
                .range(buildRange(f.getRange()))
                .build()
            ).collect(Collectors.toList());
    }

    private List<AttributeFilter> getAttributeFilters(
        List<RuleMessage.AttributeFilterMessage> attributeFilterMessages) {
        return attributeFilterMessages.stream()
            .map(afm -> new AttributeFilter(
                getValueFilters(afm.getValueFilterList()),
                getRangeFilters(afm.getRangeFilterList()))
            )
            .collect(Collectors.toList());
    }

    private ProductIdFilter getProductIdFilters(RuleMessage.ProductIdFilterMessage productIdFilterMessage) {
        // Proceed as before if productIdFilterMessage is not null
        return new ProductIdFilter(
            productIdFilterMessage.getIncludedProductIdsList(),
            productIdFilterMessage.getExcludedProductIdsList());
    }

    private Set<TriggerSet> getTriggerSets(List<RuleMessage.TriggerSetMessage> triggerSets) {
        return triggerSets.stream()
            .map(t -> TriggerSet
                .builder()
                .queryPatternTriggers(t.getQueryPatternTriggerList()
                    .stream()
                    .map(qp -> QueryPatternTrigger
                        .builder()
                        .type(QueryPatternTrigger.Type.valueOf(qp.getType().name()))
                        .value(qp.getValue())
                        .build()
                    )
                    .collect(Collectors.toList())
                )
                .customParameterTriggers(t.getCustomParameterTriggerList()
                    .stream()
                    .map(cp -> new CustomParameterTrigger(cp.getKey(), cp.getValue()))
                    .collect(Collectors.toList())
                )
                .selectedRefinementTriggers(t.getSelectedRefinementTriggerList()
                    .stream()
                    .map(this::createSelectedRefinementTrigger)
                    .collect(Collectors.toList())
                )
                .build()
            )
            .collect(Collectors.toSet());
    }

    private SelectedRefinementTrigger createSelectedRefinementTrigger(
        RuleMessage.TriggerSetMessage.SelectedRefinementMessage message) {
        return switch (SelectedRefinementTrigger.Type.valueOf(message.getType().name())) {
            case VALUE -> new SelectedRefinementTrigger(
                message.getField(),
                message.getValue()
            );
            case RANGE -> new SelectedRefinementTrigger(
                message.getField(),
                buildRange(message.getRange())
            );
            case NAVIGATION_SELECTED -> new SelectedRefinementTrigger(message.getField());
        };
    }

    private RuleTemplate getRuleTemplate(RuleMessage.RuleTemplateMessage message) {
        return new RuleTemplate(
            message.getName(),
            message.getEnableExactMatching(),
            message.getSectionList()
                .stream()
                .map(e -> {
                    Integer zoneId = null;
                    if (e.hasZoneId()) {
                        zoneId = e.getZoneId();
                    }
                    String content = null;
                    if (e.hasZoneContent()) {
                        content = e.getZoneContent();
                    }
                    return new RuleTemplateSection(
                        zoneId,
                        e.getName(),
                        content,
                        ZoneType.valueOf(e.getType().name()));
                })
                .toList()
        );
    }

    private List<PinnedRefinement> getPinnedRefinements(List<RuleMessage.PinnedRefinementMessage> pinnedRefinements) {
        return pinnedRefinements.stream()
            .map(n -> new PinnedRefinement(
                n.getNavigation(),
                n.getRefinementList()
                    .stream()
                    .map(r -> new Refinement(r.getValue(), r.getPriority()))
                    .toList()
            )).collect(Collectors.toList());
    }

    private List<ExperimentVariant> getVariants(List<RuleMessage.ExperimentVariantMessage> messages) {
        return messages.stream()
            .map(variant -> {
                var percentage = variant.hasVariantTriggerPercentage()
                    ? variant.getVariantTriggerPercentage()
                    : null;

                return ExperimentVariant
                    .builder()
                    .name(variant.getName())
                    .ruleVariant(buildRuleVariant(variant))
                    .variantTriggerPercentage(percentage)
                    .build();
            })
            .collect(Collectors.toList());
    }

    private RuleVariant buildRuleVariant(RuleMessage.ExperimentVariantMessage experimentVariantMessage) {
        RuleVariant ruleVariant = null;
        if(experimentVariantMessage.hasVariantMessage()) {
            var variantMessage = experimentVariantMessage.getVariantMessage();

            String biasingProfileName = null;
            if (variantMessage.hasBiasingProfileName()) {
                biasingProfileName = variantMessage.getBiasingProfileName();
            }

            List<String> includedNavigations = variantMessage.getIncludedNavigationsList();

            RuleTemplate template = null;
            if (variantMessage.hasTemplate()) {
                template = getRuleTemplate(variantMessage.getTemplate());
            }

            List<ProductIdsBucket> boosted =
                variantMessage
                    .getBoostedProductsList()
                    .stream()
                    .map(e ->
                        new ProductIdsBucket(
                            e.getProductIdsList()
                        )
                    )
                    .toList();

            List<ProductIdsBucket> buried =
                variantMessage
                    .getBuriedProductsList()
                    .stream()
                    .map(e ->
                        new ProductIdsBucket(
                            e.getProductIdsList()
                        )
                    )
                    .toList();

            List<PinnedRefinement> pinnedRefinements =
                getPinnedRefinements(variantMessage.getPinnedRefinementsList());

            List<PinnedProduct> pinnedProducts = variantMessage.getPinnedProductsList()
                .stream()
                .map(e -> new PinnedProduct(e.getPosition(), e.getProductId(), e.getIncludeOutOfStock()))
                .toList();

            var searchFilters = getSearchFilters(variantMessage.getSearchFilterList());
            var attributeFilters = getAttributeFilters(variantMessage.getAttributeFiltersList());
            var producIdFilter = getProductIdFilters(variantMessage.getProductIdFilter());

            ProductVisibilityBias productVisibilityBias = null;
            if (variantMessage.hasProductVisibilityBias()) {
                productVisibilityBias = getProductVisibilityBias(variantMessage.getProductVisibilityBias());
            }

            ruleVariant = new RuleVariant(
                biasingProfileName,
                includedNavigations,
                template,
                boosted,
                buried,
                pinnedRefinements,
                pinnedProducts,
                searchFilters,
                attributeFilters,
                producIdFilter,
                productVisibilityBias
            );
        }
        return ruleVariant;
    }

    private ProductVisibilityBias getProductVisibilityBias(RuleMessage.ProductVisibilityBias productVisibilityBias) {
        var visibilityPercentageCap = productVisibilityBias.hasVisibilityPercentageCap()
            ? productVisibilityBias.getVisibilityPercentageCap()
            : null;

        return new ProductVisibilityBias(
            productVisibilityBias.getAttribute(),
            productVisibilityBias.getValuesList(),
            productVisibilityBias.getNumberValuesList(),
            productVisibilityBias.getMultiplier(),
            productVisibilityBias.getPercentageOffset(),
            visibilityPercentageCap
        );
    }
}
