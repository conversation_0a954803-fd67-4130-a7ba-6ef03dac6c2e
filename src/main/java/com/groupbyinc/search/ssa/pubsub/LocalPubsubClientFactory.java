package com.groupbyinc.search.ssa.pubsub;

import com.google.api.gax.core.NoCredentialsProvider;
import com.google.api.gax.grpc.GrpcTransportChannel;
import com.google.api.gax.rpc.FixedTransportChannelProvider;
import com.groupbyinc.utils.micronaut.gcp.config.GcpProperties;
import com.groupbyinc.utils.pubsub.client.PubsubClient;
import io.grpc.ManagedChannelBuilder;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Replaces;
import io.micronaut.context.annotation.Requires;

import jakarta.inject.Singleton;

@Factory
@Requires(env = "local")
public class LocalPubsubClientFactory {
    /**
     * Client for local connection only, expects pubsub emulator started at port 11234
     */
    @Singleton
    @Replaces(PubsubClient.class)
    public PubsubClient pubsubClient(GcpProperties pubsubGcpProperties){
        GrpcTransportChannel transportProvide = GrpcTransportChannel.create(
            ManagedChannelBuilder.forTarget("localhost:11234").usePlaintext().build()
        );
        FixedTransportChannelProvider transportChannelProvider = FixedTransportChannelProvider.create(transportProvide);
        return new PubsubClient(
            pubsubGcpProperties.getProjectId(),
            NoCredentialsProvider.create(),
            transportChannelProvider
        );
    }
}
