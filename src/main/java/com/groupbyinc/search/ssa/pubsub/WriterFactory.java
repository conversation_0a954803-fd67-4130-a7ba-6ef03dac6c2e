package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.utils.pubsub.client.PubsubClient;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.gax.batching.BatchingSettings;
import com.google.cloud.pubsub.v1.Publisher;
import com.google.pubsub.v1.Topic;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Value;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.threeten.bp.Duration;

import javax.annotation.Nullable;
import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Pubsub factory to spawn GCP publishers.
 */
@Slf4j
@Factory
public class WriterFactory {

    private final PubsubClient pubsubClient;
    private final int parallelThreads;
    // wisdom direct search
    private final String directSearchTopic;
    private final String directSearchGcpProjectId;
    private final Long countThreshold; // sdk default : 100 message
    private final Long requestByteThreshold; // sdk default : 1000 bytes
    private final Long delayThreshold; // sdk default : 1 ms
    private final Boolean wisdomDirectSearchEnabled;
    // keeping publishers so they can be closed on application shutdown
    private final List<Publisher> registeredPublishers = new ArrayList<>();


    public WriterFactory(
        PubsubClient pubsubClient,
        @Value("${gcp.pubsub.parallelThreads:1}") int parallelThreads,
        @Nullable @Value("${gcp.pubsub.direct-search.topic}") String directSearchTopic,
        @Nullable @Value("${gcp.pubsub.direct-search.project-id}") String directSearchGcpProjectId,
        @Value("${gcp.pubsub.direct-search.count-threshold:100}") Long countThreshold,
        @Value("${gcp.pubsub.direct-search.request-byte-threshold:5000}") Long requestByteThreshold,
        @Value("${gcp.pubsub.direct-search.delay-threshold:50}") Long delayThreshold,
        @Value("${direct-search-beacon.enabled:false}") Boolean wisdomDirectSearchEnabled
    ) {
        this.pubsubClient = pubsubClient;
        this.parallelThreads = parallelThreads;
        this.directSearchTopic = directSearchTopic;
        this.directSearchGcpProjectId = directSearchGcpProjectId;
        this.countThreshold = countThreshold;
        this.requestByteThreshold = requestByteThreshold;
        this.delayThreshold = delayThreshold;
        this.wisdomDirectSearchEnabled = wisdomDirectSearchEnabled;
    }

    @Singleton
    public PubsubWriterService pubsubWriterService(ObjectMapper objectMapper) {
        var directSearchPublisher = getDirectSearchPublisher();

        if (directSearchPublisher != null) {
            registeredPublishers.add(directSearchPublisher);
        }

        return new PubsubWriterService(
            objectMapper,
            directSearchPublisher
        );
    }

    private Publisher getDirectSearchPublisher() {
        try {
            if (!wisdomDirectSearchEnabled) {
                log.warn("Wisdom direct search is disabled. Skipping topic initialization.");
                return null;
            }
            if (StringUtils.isBlank(directSearchTopic)) {
                log.warn("Wisdom direct search topic is not provided. Skipping topic initialization.");
                return null;
            }
            return getPublisher(
                directSearchGcpProjectId,
                directSearchTopic,
                false,
                BatchingSettings.newBuilder()
                    .setElementCountThreshold(countThreshold)
                    .setRequestByteThreshold(requestByteThreshold)
                    .setDelayThreshold(Duration.ofMillis(delayThreshold))
                    .build()
            );
        } catch (Exception e) {
            log.warn("Failed to create direct search publisher: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Creates new pubsub {@link Publisher} for given topic name.
     *
     * @param projectId        Gcp project ID to search for topic from. This is temporary parameter to support
     *                         legacy system on groupby-cloud-1701. Later on we want to get rid of gcp project
     *                         branching and only use current project where site-search (and wisdom) is deployed.
     * @param topicName        Topic name as a string in a short form.
     * @param shouldCreate     bool flag to indicate that topic should be created if it does not exist.
     * @param batchingSettings Additional {@link BatchingSettings} to use when writing the topic.
     *
     * @return pubsub {@link Publisher}
     */
    private Publisher getPublisher(
        String projectId,
        String topicName,
        boolean shouldCreate,
        BatchingSettings batchingSettings
    ) {
        log.info("Trying to init pubsub publisher for topic: {}/{}.",
            StringUtils.isNotBlank(projectId) ? projectId : "[default]", topicName);

        var topic = getTopic(projectId, topicName, shouldCreate);
        var publisher = pubsubClient.newPublisher(topic, parallelThreads, batchingSettings);
        log.info("Pubsub publisher initialized, topic: {}, batching settings: {}.",
            publisher.getTopicNameString(), publisher.getBatchingSettings());
        return publisher;
    }

    private Topic getTopic(String projectId, String topicName, boolean shouldCreate) {
        if (StringUtils.isBlank(topicName)) {
            throw new IllegalStateException("Topic name cannot be empty.");
        }
        if (shouldCreate) {
            if (StringUtils.isNotBlank(projectId)) {
                log.info("Auto-creating topics is not supported for custom GCP projects. Looking for existing topic.");
                return pubsubClient.findTopic(projectId, topicName)
                    .orElseThrow(() -> new IllegalStateException(
                        "Topic %s not found. GCP project override: [%s].".formatted(topicName, projectId))
                    );
            }
            return pubsubClient.ensureTopicExists(topicName);
        }

        // Specifying projectId is only viable for customers that use Wisdom services in groupby-cloud-1701.
        // For current and future single-tenant (and multi-tenant global1) customers projectId should be empty.
        // todo: projectId to be removed once everyone is moved to single-tenant or global1
        var topic = StringUtils.isNotBlank(projectId)
            ? pubsubClient.findTopic(projectId, topicName)
            : pubsubClient.findTopic(topicName);

        return topic.orElseThrow(() -> new IllegalStateException(
            "Topic %s not found. GCP project override: [%s].".formatted(topicName, projectId))
        );
    }

    @PreDestroy
    void shutdown() {
        registeredPublishers.stream()
            .peek(Publisher::shutdown)
            .forEach(publisher -> {
                try {
                    publisher.awaitTermination(10, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    log.error("Failed to shutdown publisher: {}", e.getMessage(), e);
                }
            });
    }
}
