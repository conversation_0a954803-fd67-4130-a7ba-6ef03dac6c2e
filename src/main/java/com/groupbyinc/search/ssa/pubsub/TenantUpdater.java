package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.proto.commandcenter.config.TenantMessage;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.tenant.TenantConfiguration;

import com.groupbyinc.search.ssa.core.tenant.TenantSettings;
import io.micronaut.core.annotation.Order;
import lombok.RequiredArgsConstructor;

import jakarta.inject.Singleton;

@Singleton
/*
 * We may need to hardcode order of beans for PubSub message handlers, tenant
 * updater should always be first and project configuration updater must be second
 * and area updater must be third, because of dependence of other configs from
 * area id and tenant id.
 *
 * Default order in micronaut it is '0' - no order. We will set '-3' for tenant
 * updater and '-2' for project configuration updater and '-1' for area updater
 * to be in top of list in correct order.
 */
@Order(-3)
@RequiredArgsConstructor
public class TenantUpdater implements ConfigUpdater {

    private final ConfigurationManager configurationManager;

    private TenantConfiguration map (TenantMessage message) {
        return TenantConfiguration.builder()
            .enabled(message.getEnabled())
            .name(message.getName())
            .messageType(message.getMessageType())
            .settings(getTenantSettings(message))
            .id(message.getId())
            .build();
    }

    @Override
    public void update(SiteSearchConfigurationUpdateMessage updateMessage) {
        var newProfiles = updateMessage.getTenantList().stream()
            .map(this::map)
            .toList();
        configurationManager.updateTenantConfig(newProfiles);
    }

    @Override
    public boolean isApplicable(SiteSearchConfigurationUpdateMessage updateMessage) {
        return updateMessage.getTenantCount() > 0;
    }

    private TenantSettings getTenantSettings(TenantMessage message) {
        if (message.hasTenantSettings()) {
            return new TenantSettings(
                message.getTenantSettings().getIncludeExpandedResults(),
                message.getTenantSettings().getFacetLimit()
            );
        }
        return null;
    }

}
