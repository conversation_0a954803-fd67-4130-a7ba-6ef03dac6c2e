package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.RedirectMessage;
import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.Metadata;
import com.groupbyinc.search.ssa.core.redirect.RedirectConfiguration;

import com.groupbyinc.search.ssa.core.trigger.QueryPatternTrigger;
import lombok.RequiredArgsConstructor;

import jakarta.inject.Singleton;

import java.util.stream.Collectors;

@Singleton
@RequiredArgsConstructor
public class RedirectConfigUpdater implements ConfigUpdater {

    private final ConfigurationManager configurationManager;

    @Override
    public void update(SiteSearchConfigurationUpdateMessage updateMessage) {
        var newRedirects = updateMessage.getRedirectList().stream()
                .map(this::map)
                .collect(Collectors.toList());
        configurationManager.updateRedirectConfig(newRedirects);
    }

    @Override
    public boolean isApplicable(SiteSearchConfigurationUpdateMessage updateMessage) {
        return updateMessage.getRedirectCount() > 0;
    }

    private RedirectConfiguration map(RedirectMessage message) {
        boolean activeHoursEnabled = message.getActiveTimeRange().getActiveHoursEnabled();
        Long activeFrom = null;
        Long activeTo = null;

        if (activeHoursEnabled) {
            if (message.getActiveTimeRange().hasActiveFrom()) {
                activeFrom = message.getActiveTimeRange().getActiveFrom();
            }
            if (message.getActiveTimeRange().hasActiveTo()) {
                activeTo = message.getActiveTimeRange().getActiveTo();
            }
        }

        var triggers = message.getTriggersList()
            .stream()
            .map(m -> QueryPatternTrigger.builder()
                .type(QueryPatternTrigger.Type.valueOf(m.getType().name()))
                .value(m.getValue())
                .build())
            .toList();

        return new RedirectConfiguration(
            message.getId(),
            message.getAreaId(),
            message.getUrl(),
            message.getPriority(),
            activeHoursEnabled,
            activeFrom,
            activeTo,
            triggers,
            message.getMessageType(),
            message.getMetadataList()
                .stream()
                .map(m -> new Metadata(m.getField(), m.getValue()))
                .toList()
        );
    }

}
