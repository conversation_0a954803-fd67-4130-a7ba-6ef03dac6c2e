package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.NavigationMessage;
import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.Metadata;
import com.groupbyinc.search.ssa.core.Order;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.navigation.NavigationSort;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.rule.Refinement;

import lombok.RequiredArgsConstructor;

import jakarta.inject.Singleton;

@Singleton
@RequiredArgsConstructor
public class NavigationUpdater implements ConfigUpdater {

    private final ConfigurationManager configurationManager;

    public NavigationConfiguration map(NavigationMessage navigationUpdate) {
        var message = NavigationConfiguration.builder()
            .id(navigationUpdate.getId())
            .name(navigationUpdate.getName())
            .field(navigationUpdate.getField())
            .areaId(navigationUpdate.getAreaId())
            .priority(navigationUpdate.getPriority())
            .type(NavigationType.valueOf(navigationUpdate.getType().name()))
            .ranges(
                navigationUpdate
                    .getRangesList()
                    .stream()
                    .map(MessageMapperUtils::buildRange)
                    .toList()
            )
            .multiSelect(navigationUpdate.getMultiSelect())
            .messageType(navigationUpdate.getMessageType())
            .metadata(navigationUpdate.getMetadataList()
                .stream()
                .map(m -> new Metadata(m.getField(), m.getValue()))
                .toList())
            .pinnedRefinements(
                navigationUpdate
                    .getPinnedRefinementsList()
                    .stream()
                    .map(p -> new Refinement(p.getValue(), p.getPriority()))
                    .toList()
            );

            if (navigationUpdate.hasSort()) {
                message.sort(
                    new NavigationSort(
                        NavigationSort.SortField.valueOf(navigationUpdate.getSort().getField().name()),
                        Order.valueOf(navigationUpdate.getSort().getOrderType().name())
                    )
                );
            }

            return message.build();
    }

    @Override
    public void update(SiteSearchConfigurationUpdateMessage updateMessage) {
        var newProfiles = updateMessage.getNavigationList().stream()
            .map(this::map)
            .toList();
        configurationManager.updateNavigationConfig(newProfiles);
    }

    @Override
    public boolean isApplicable(SiteSearchConfigurationUpdateMessage updateMessage) {
        return updateMessage.getNavigationCount() > 0;
    }

}
