package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.proto.commandcenter.config.ZoneMessage;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.zone.ZoneConfiguration;
import com.groupbyinc.search.ssa.core.zone.ZoneType;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;

import java.util.stream.Collectors;

@Singleton
@RequiredArgsConstructor
public class ZoneConfigUpdater implements ConfigUpdater {

    private final ConfigurationManager configurationManager;

    @Override
    public void update(SiteSearchConfigurationUpdateMessage updateMessage) {
        var newZones = updateMessage.getZoneList().stream()
                .map(this::map)
                .collect(Collectors.toList());
        configurationManager.updateZoneConfig(newZones);
    }

    @Override
    public boolean isApplicable(SiteSearchConfigurationUpdateMessage updateMessage) {
        return updateMessage.getZoneCount() > 0;
    }

    private ZoneConfiguration map(ZoneMessage message) {
        return new ZoneConfiguration(
            message.getId(),
            message.getAreaId(),
            message.getName(),
            message.getValue(),
            ZoneType.valueOf(message.getType().name()),
            message.getMessageType()
        );
    }

}
