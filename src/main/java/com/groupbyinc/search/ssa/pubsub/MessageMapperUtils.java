package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.RangeMessage;
import com.groupbyinc.search.ssa.core.navigation.Range;

public final class MessageMapperUtils {

    private MessageMapperUtils() {}

    /**
     * Used to construct range. Range 'high' and 'low' are optional, they
     * will be added to the message only if present.
     *
     * @param range range message.
     * @return {@link Range} object.
     */
    public static Range buildRange(RangeMessage range) {
        Double low = null;
        if (range.hasLow()) {
            low = range.getLow();
        }

        Double high = null;
        if (range.hasHigh()) {
            high = range.getHigh();
        }

        String description = null;
        if (range.hasDescription()) {
            description = range.getDescription();
        }

        boolean exclude = false;
        if(range.hasExclude()) {
            exclude = range.getExclude();
        }

        return new Range(low, high, description, exclude);
    }
}
