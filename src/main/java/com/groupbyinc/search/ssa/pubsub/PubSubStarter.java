package com.groupbyinc.search.ssa.pubsub;

import io.micronaut.context.BeanContext;
import io.micronaut.scheduling.annotation.Scheduled;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 *  Delayed initialization of PubSub to reduce startup time.
 */
@Singleton
@Slf4j
@RequiredArgsConstructor
public class PubSubStarter {

    private final BeanContext beanContext;

    @Scheduled(initialDelay = "1s")
    public void onAppStarted() {
        log.info("Init PubSub");
        beanContext.getBean(SubscriberFactory.class);
        beanContext.getBean(WriterFactory.class);
    }
}
