package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.AttributeMessage;
import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.Metadata;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;

import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;

import java.time.Instant;
import java.time.ZoneOffset;

@Singleton
@RequiredArgsConstructor
public class AttributeUpdater implements ConfigUpdater {

    private final ConfigurationManager configurationManager;

    public AttributeConfiguration map(AttributeMessage attribute) {
        var attributeConfigurationBuilder = AttributeConfiguration.builder()
            .key(attribute.getKey())
            .path(attribute.getPath())
            .retrievable(attribute.getRetrievable())
            .indexable(attribute.getIndexable())
            .searchable(attribute.getSearchable())
            .partNumberSearchable(attribute.getPartNumberSearchable())
            .dynamicFacetable(attribute.getDynamicFacetable())
            .messageType(attribute.getMessageType())
            .type(attribute.getType())
            .attributeGroup(attribute.getAttributeGroup())
            .displayName(attribute.getDisplayName())
            .collectionId(attribute.getCollectionId())
            .metadata(attribute.getMetadataList()
                .stream()
                .map(m -> new Metadata(m.getField(), m.getValue()))
                .toList());

        if (attribute.hasLastModifiedTimestamp()) {
            var localDateTime =Instant.ofEpochMilli(attribute.getLastModifiedTimestamp())
                .atZone(ZoneOffset.UTC)
                .toLocalDateTime();
            attributeConfigurationBuilder.lastModifiedDate(localDateTime);
        }
        if (attribute.hasLastModifiedField()) {
            attributeConfigurationBuilder.lastModifiedField(attribute.getLastModifiedField());
        }
        return attributeConfigurationBuilder.build();
    }

    @Override
    public void update(SiteSearchConfigurationUpdateMessage updateMessage) {
        var newProfiles = updateMessage.getAttributeList()
            .stream()
            .map(this::map)
            .toList();
        configurationManager.updateAttributeConfig(newProfiles);
    }

    @Override
    public boolean isApplicable(SiteSearchConfigurationUpdateMessage updateMessage) {
        return updateMessage.getAttributeCount() > 0;
    }

}
