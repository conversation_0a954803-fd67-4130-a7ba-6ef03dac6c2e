package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.BiasingProfileMessage;
import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.biasing.Bias;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfileConfiguration;
import com.groupbyinc.search.ssa.core.biasing.NumericContent;
import com.groupbyinc.search.ssa.core.navigation.Range;
import io.micronaut.core.util.CollectionUtils;
import lombok.RequiredArgsConstructor;

import jakarta.inject.Singleton;

@Singleton
@RequiredArgsConstructor
public class BiasingProfileUpdater implements ConfigUpdater {

    private final ConfigurationManager configurationManager;

    public BiasingProfileConfiguration map(BiasingProfileMessage profileUpdate) {
        return BiasingProfileConfiguration.builder()
            .id(profileUpdate.getId())
            .name(profileUpdate.getName())
            .areaDefault(profileUpdate.getAreaDefault())
            .biases(profileUpdate.getBiasesList()
                .stream()
                .map(this::createBias)
                .toList())
            .areaId(profileUpdate.getAreaId())
            .messageType(profileUpdate.getMessageType())
            .build();
    }

    @Override
    public void update(SiteSearchConfigurationUpdateMessage updateMessage) {
        var newProfiles = updateMessage.getBiasingProfileList()
            .stream()
            .map(this::map)
            .toList();
        configurationManager.updateBiasingProfileConfig(newProfiles);
    }

    @Override
    public boolean isApplicable(SiteSearchConfigurationUpdateMessage updateMessage) {
        return updateMessage.getBiasingProfileCount() > 0;
    }

    private Bias createBias(BiasingProfileMessage.BiasMessage message) {
        var builder = Bias
            .builder()
            .field(message.getField())
            .strength(Bias.Strength.valueOf(message.getStrength().name()));

        if (!message.hasType()) {
            builder.content(message.getContent());
        } else {
            builder.type(Bias.Type.valueOf(message.getType().name()));

            switch (message.getType()) {
                case TEXTUAL -> builder.content(message.getContent());
                case NUMERIC -> builder.numericContent(createNumericContent(message.getNumericContent()));
            }
        }

        return builder.build();
    }

    private NumericContent createNumericContent(BiasingProfileMessage.BiasMessage.NumericContentMessage message) {
        var builder = NumericContent.builder();

        if (CollectionUtils.isNotEmpty(message.getValuesList())) {
            builder.values(message.getValuesList());
        }

        if (CollectionUtils.isNotEmpty(message.getRangesList())) {
            builder.ranges(
                message.getRangesList().stream()
                    .map(range -> new Range(range.getLow(), range.getHigh(), range.getDescription()))
                    .toList()
            );
        }

        return builder.build();
    }
}
