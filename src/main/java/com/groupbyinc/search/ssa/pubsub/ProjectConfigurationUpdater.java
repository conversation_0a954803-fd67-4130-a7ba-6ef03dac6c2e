package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.ProjectConfigurationMessage;
import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.project.ProjectConfiguration;

import io.micronaut.core.annotation.Order;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;

/*
 * We may need to hardcode order of beans for PubSub message handlers, tenant
 * updater should always be first and project configuration updater must be second
 * and area updater must be third, because of dependence of other configs from
 * area id and tenant id.
 *
 * Default order in micronaut it is '0' - no order. We will set '-3' for tenant
 * updater and '-2' for project configuration updater and '-1' for area updater
 * to be in top of list in correct order.
 */
@Order(-2)
@Singleton
@RequiredArgsConstructor
public class ProjectConfigurationUpdater implements ConfigUpdater {

    private final ConfigurationManager configurationManager;

    public ProjectConfiguration map(ProjectConfigurationMessage projectConfigurationUpdate) {
        return ProjectConfiguration.builder()
            .id(projectConfigurationUpdate.getId())
            .collection(projectConfigurationUpdate.getCollection())
            .projectId(projectConfigurationUpdate.getProjectId())
            .messageType(projectConfigurationUpdate.getMessageType())
            .tenantId(projectConfigurationUpdate.getTenantId())
            .build();
    }

    @Override
    public void update(SiteSearchConfigurationUpdateMessage updateMessage) {
        var newProfiles = updateMessage.getProjectConfigurationList().stream()
            .map(this::map)
            .toList();
        configurationManager.updateProjectConfigurationConfig(newProfiles);
    }

    @Override
    public boolean isApplicable(SiteSearchConfigurationUpdateMessage updateMessage) {
        return updateMessage.getProjectConfigurationCount() > 0;
    }

}
