package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.AreaMessage;
import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.Metadata;
import com.groupbyinc.search.ssa.core.area.AreaConfiguration;
import io.micronaut.core.annotation.Order;
import lombok.RequiredArgsConstructor;

import jakarta.inject.Singleton;

@Singleton
/*
 * We may need to hardcode order of beans for PubSub message handlers, tenant
 * updater should always be first and project configuration updater must be second
 * and area updater must be third, because of dependence of other configs from
 * area id and tenant id.
 *
 * Default order in micronaut it is '0' - no order. We will set '-3' for tenant
 * updater and '-2' for project configuration updater and '-1' for area updater
 * to be in top of list in correct order.
 */
@Order(-1)
@RequiredArgsConstructor
public class AreaUpdater implements ConfigUpdater {

    private final ConfigurationManager configurationManager;

    public AreaConfiguration map(AreaMessage areaUpdate) {
        var builder = AreaConfiguration.builder()
            .id(areaUpdate.getId())
            .name(areaUpdate.getName())
            .messageType(areaUpdate.getMessageType())
            .tenantId(areaUpdate.getTenantId())
            .metadata(areaUpdate.getMetadataList()
                .stream()
                .map(m -> new Metadata(m.getField(), m.getValue()))
                .toList());

        if (areaUpdate.hasCollectionId()) {
            builder.collectionId(areaUpdate.getCollectionId());
        }

        if (areaUpdate.hasServingConfigName()) {
            builder.servingConfigName(areaUpdate.getServingConfigName());
        }

        if (areaUpdate.hasSiteFilterId()) {
            builder.siteFilterId(areaUpdate.getSiteFilterId());
        }

        return builder.build();
    }

    @Override
    public void update(SiteSearchConfigurationUpdateMessage updateMessage) {
        var newProfiles = updateMessage.getAreaList().stream()
            .map(this::map)
            .toList();
        configurationManager.updateAreaConfig(newProfiles);
    }

    @Override
    public boolean isApplicable(SiteSearchConfigurationUpdateMessage updateMessage) {
        return updateMessage.getAreaCount() > 0;
    }

}
