package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.proto.ingestion.commandcenter.IngestionSuccess;
import com.groupbyinc.search.ssa.application.cache.event.InvalidateCacheEvent;
import com.groupbyinc.utils.pubsub.client.PubsubClient;

import com.google.cloud.pubsub.v1.AckReplyConsumer;
import com.google.cloud.pubsub.v1.MessageReceiver;
import com.google.cloud.pubsub.v1.Subscriber;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.pubsub.v1.PubsubMessage;
import io.micronaut.context.annotation.Value;
import io.micronaut.context.event.ApplicationEventPublisher;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Singleton
@Slf4j
public class SubscriberFactory {

    private final PubsubClient pubsubClient;
    private final Collection<ConfigUpdater> updaters;
    private final String configTopic;
    private final String configSubscription;
    private final int ackDelay;
    private final int parallelThreads;
    private final int subscriptionExpirationTime;
    private final String ingestionSuccessTopic;
    private final String ingestionSuccessSubscription;
    private final ApplicationEventPublisher<InvalidateCacheEvent> invalidateCacheEventPublisher;

    private final List<Subscriber> subscribers = new ArrayList<>();

    @SuppressWarnings("all")
    public SubscriberFactory(
        PubsubClient pubsubClient,
        Collection<ConfigUpdater> updaters,
        @Value("${gcp.pubsub.ackDelay:600}") int ackDelay,
        @Value("${gcp.pubsub.subscription.expirationTime:86400}") int subscriptionExpirationTime,
        @Value("${gcp.pubsub.parallelThreads:1}") int parallelThreads,
        @Value("${gcp.pubsub.config.topic}") String configTopic,
        @Value("${gcp.pubsub.config.subscription}") String subscriptionPrefix,
        @Value("${hostname:}") String hostname,
        @Value("${gcp.pubsub.ingestion.topic}") String ingestionSuccessTopic,
        @Value("${gcp.pubsub.ingestion.subscription}") String ingestionSuccessSubscription,
        ApplicationEventPublisher<InvalidateCacheEvent> invalidateCacheEventPublisher
    ) {
        this.pubsubClient = pubsubClient;
        this.updaters = updaters;
        this.ackDelay = ackDelay;
        this.subscriptionExpirationTime = subscriptionExpirationTime;
        this.parallelThreads = parallelThreads;
        this.configTopic = configTopic;
        // add hostname suffix to make subscription unique
        int dashIndex = hostname.lastIndexOf('-');
        String envSuffix = dashIndex > 0
            ? hostname.substring(dashIndex)
            : "";
        this.configSubscription = subscriptionPrefix + envSuffix;

        this.ingestionSuccessTopic = ingestionSuccessTopic;
        this.ingestionSuccessSubscription = ingestionSuccessSubscription;
        this.invalidateCacheEventPublisher = invalidateCacheEventPublisher;
    }

    private synchronized void processConfig(PubsubMessage message, AckReplyConsumer ackReplyConsumer) {
        updateConfig(message, ackReplyConsumer);
    }

    private void updateConfig(PubsubMessage message, AckReplyConsumer ackReplyConsumer) {
        try {
            var updateMessage = SiteSearchConfigurationUpdateMessage.parseFrom(message.getData());
            log.info("Configuration message received: {}.", updateMessage.toString().replace("\n", ""));
            updaters.stream()
                .filter(u -> u.isApplicable(updateMessage))
                .forEach(u -> u.update(updateMessage));
            ackReplyConsumer.ack();
        } catch (InvalidProtocolBufferException e) {
            log.warn("Failed to read configuration update.", e);
            ackReplyConsumer.ack();
        } catch (Exception e) {
            // ack messages that wasn't processed properly
            log.warn("Failed to update configuration.", e);
            ackReplyConsumer.ack();
        }
    }

    private void invalidateCache(PubsubMessage message, AckReplyConsumer ackReplyConsumer) {
        try {
            var ingestionSuccess = IngestionSuccess.parseFrom(message.getData());
            log.info("Ingestion success message received: {}.", ingestionSuccess.toString().replace("\n", ""));
            invalidateCacheEventPublisher.publishEventAsync(
                new InvalidateCacheEvent(
                    ingestionSuccess.getTaskInfo().getMerchandiserId(),
                    ingestionSuccess.getTaskInfo().getCollection()
                )
            );
            ackReplyConsumer.ack();
        } catch (InvalidProtocolBufferException e) {
            log.warn("Failed to read configuration update.", e);
            ackReplyConsumer.ack();
        } catch (Exception e) {
            // ack messages that wasn't processed properly
            log.warn("Failed to update configuration.", e);
            ackReplyConsumer.ack();
        }
    }

    @PostConstruct
    void init() {
        subscribers.add(subscribe(configTopic, configSubscription, this::processConfig));
        subscribers.add(subscribe(ingestionSuccessTopic, ingestionSuccessSubscription, this::invalidateCache));
        log.info("PubSub subscribers started.");
    }

    @PreDestroy
    void shutdown() throws TimeoutException {
        for (var subscriber : subscribers) {
            subscriber.stopAsync().awaitTerminated(1, TimeUnit.SECONDS);
        }
        pubsubClient.close();
    }


    private Subscriber subscribe(String configTopic, String configSubscription, MessageReceiver messageReceiver) {
        var topic = pubsubClient.ensureTopicExists(configTopic);
        log.info("Finish PubSub topic verification. Topic: {}", topic.getName());

        var subscription = pubsubClient.ensureSubscriptionExists(
            topic,
            configSubscription,
            ackDelay,
            subscriptionExpirationTime,
            /* enableOrdering= */ true
        );
        log.info("Finish PubSub subscription verification. Subscription: {}", subscription.getName());

        var subscriber = pubsubClient.newSubscriber(subscription, messageReceiver, parallelThreads);
        subscriber.startAsync();
        return subscriber;
    }

}
