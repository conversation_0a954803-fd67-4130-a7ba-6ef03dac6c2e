package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.search.ssa.api.dto.SearchResponseDto;
import com.groupbyinc.search.ssa.beacon.client.DirectSearchBeaconRequest;
import com.groupbyinc.search.ssa.util.Constants;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.core.ApiFuture;
import com.google.api.core.ApiFutureCallback;
import com.google.api.core.ApiFutures;
import com.google.cloud.pubsub.v1.Publisher;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.ByteString;
import com.google.pubsub.v1.PubsubMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import static com.groupbyinc.search.ssa.metrics.opentelemetry.MeterRegistry.monotonicTime;

/**
 * Service to encapsulate pubsub publish logic
 */
@Slf4j
@RequiredArgsConstructor
public class PubsubWriterService {

    private final ObjectMapper objectMapper;
    private final Publisher directSearchPublisher;

    /**
     * Sends a direct search message to a pubsub topic. Operation is I/O blocking. Returns push success flag.
     *
     * @param data {@link DirectSearchBeaconRequest} instance.
     *
     * @return boolean flag indicating push success.
     */
    public boolean sendDirectBeacon(DirectSearchBeaconRequest<SearchResponseDto> data) {
        if (data == null) {
            return false;
        }
        if (directSearchPublisher == null) {
            log.warn("Pubsub direct search publisher is not configured.");
            return false;
        }

        try {
            var future = publish(
                directSearchPublisher,
                PubsubMessage.newBuilder()
                    .setData(ByteString.copyFrom(objectMapper.writeValueAsBytes(data)))
                    .putAttributes(Constants.GROUPBY_CUSTOMER_ID_HEADER, data.customerId())
                    .build()
            );
            return StringUtils.isNotBlank(future.get());
        } catch (Exception e) {
            log.warn("Failed to send pubsub direct search beacon: {}", e.getMessage());
            return false;
        }
    }

    private ApiFuture<String> publish(Publisher publisher, PubsubMessage message) {
        log.debug("Sending message to the topic {}", publisher.getTopicName().getTopic());
        var start = monotonicTime();
        try {
            var future = publisher.publish(message);
            ApiFutures.addCallback(
                future,
                new PubsubCallback(start, publisher.getTopicName().getTopic()),
                MoreExecutors.directExecutor()
            );
            return future;
        } catch (Exception e) {
            log.error("Failed to publish message to Pubsub topic {}. Took: {} ms.",
                publisher.getTopicName().getTopic(),
                monotonicTime() - start,
                e
            );
            throw new IllegalStateException(e);
        }
    }


    private record PubsubCallback(long start, String topicName) implements ApiFutureCallback<String> {

        @Override
        public void onFailure(Throwable t) {
            log.error("Failed to publish message to Pubsub topic {}. Took: {} ms.",
                topicName, monotonicTime() - start, t);
        }

        @Override
        public void onSuccess(String messageId) {
            log.debug("Published message #{} to Pubsub topic {} successfully. Took: {} ms.",
                messageId, topicName, monotonicTime() - start);
        }
    }
}
