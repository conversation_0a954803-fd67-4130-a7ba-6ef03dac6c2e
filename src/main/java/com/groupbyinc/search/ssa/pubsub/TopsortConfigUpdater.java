package com.groupbyinc.search.ssa.pubsub;

import com.groupbyinc.proto.commandcenter.config.SiteSearchConfigurationUpdateMessage;
import com.groupbyinc.proto.commandcenter.config.TopsortConfigMessage;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.topsort.model.TopsortConfiguration;

import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;

@Singleton
@RequiredArgsConstructor
public class TopsortConfigUpdater implements ConfigUpdater {
    private final ConfigurationManager configurationManager;

    public TopsortConfiguration map(TopsortConfigMessage msg) {
        return TopsortConfiguration.builder()
            .messageType(msg.getMessageType())
            .id(msg.getId())
            .areaId(msg.getAreaId())
            .enabled(msg.getEnabled())
            .apiKey(msg.getApiKey())
            .build();
    }

    @Override
    public void update(SiteSearchConfigurationUpdateMessage updateMessage) {
        var newProfiles = updateMessage.getTopsortConfigList().stream()
            .map(this::map)
            .toList();
        configurationManager.updateTopsortConfig(newProfiles);
    }

    @Override
    public boolean isApplicable(SiteSearchConfigurationUpdateMessage updateMessage) {
        return updateMessage.getTopsortConfigCount() > 0;
    }

}
