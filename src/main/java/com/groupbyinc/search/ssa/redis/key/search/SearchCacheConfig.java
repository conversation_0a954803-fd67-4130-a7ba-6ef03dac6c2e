package com.groupbyinc.search.ssa.redis.key.search;

import com.groupbyinc.search.ssa.application.cache.CacheConfig;
import com.groupbyinc.search.ssa.features.FeatureFlag;

public record SearchCacheConfig(int ttl,
                                boolean enabled,
                                boolean enableCacheDetails,
                                boolean includeVisitorId,
                                boolean includeLoginId,
                                boolean enableSaytCache,
                                int saytPageSize,
                                boolean enableUserAttributes) implements CacheConfig, FeatureFlag {

    public static SearchCacheConfig DEFAULT = new SearchCacheConfig(
        60,
        false,
        false,
        false,
        false,
        false,
        0,
        false
    );

    //By default, object mapper pas a "0" if their no such property in a JSON.
    public SearchCacheConfig {
        if (ttl <= 0) {
            ttl = 60;
        }
    }

}
