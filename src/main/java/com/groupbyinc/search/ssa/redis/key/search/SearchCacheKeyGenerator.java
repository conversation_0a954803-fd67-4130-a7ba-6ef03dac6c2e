package com.groupbyinc.search.ssa.redis.key.search;

import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.application.cache.CacheKeyGenerator;
import com.groupbyinc.search.ssa.core.SearchParameters;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Value;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorTransformFunctions.USER_ATTRIBUTE_STRING;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.BUILDER_INITIAL_CAPACITY;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.appendBaseFields;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.appendCollectionOfObjects;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.appendCommonSearchFields;
import static com.groupbyinc.search.ssa.util.StringUtils.UNDERSCORE;
import static com.groupbyinc.search.ssa.util.StringUtils.getHashSafe;

@Slf4j
@Context
@RequiredArgsConstructor
public class SearchCacheKeyGenerator implements CacheKeyGenerator<SearchCacheConfig> {


    @Value("${security.hash.salt}")
    private String salt;

    /**
     * Generate a cache key for search requests.
     * <p>
     * Fields used in a key generation:
     * <pre>{@code
     * searchParameters.getQuery()
     * searchParameters.getFacet()
     * searchParameters.getSorts()
     * searchParameters.getDebug()
     * searchParameters.getLoginId()
     * searchParameters.getVisitorId()
     * searchParameters.getPreFilter()
     * searchParameters.getSearchMode()
     * searchParameters.getRefinements()
     * searchParameters.getRefinements()
     * searchParameters.getResponseMask()
     * searchParameters.getPageCategories()
     * searchParameters.getProductIdFilter()
     * searchParameters.getAttributeFilters()
     * searchParameters.getContext().getArea()
     * searchParameters.getVariantRollupKeys()
     * searchParameters.getPinnedRefinements()
     * searchParameters.getSpellCorrectionMode()
     * searchParameters.getExcludedNavigations()
     * searchParameters.getIncludedNavigations()
     * searchParameters.getPinUnexpandedResults()
     * searchParameters.getPagination().getSize()
     * searchParameters.getBuriedProductBuckets()
     * searchParameters.getBoostedProductBuckets()
     * searchParameters.getIncludeExpandedResults()
     * searchParameters.getPagination().getOffset()
     * searchParameters.getContext().getCollection()
     * searchParameters.getContext().getMerchandiser()
     * searchParameters.getBiasingProfile().get().getBiases()
     * searchParameters.getMerchandisingConfiguration().getSiteFilter()
     * }</pre>
     * <p>
     * Generated key:
     * <pre>{@code tenant + collection + (hashed & base64 encoded) fields.}</pre>
     *
     * @param searchParameters object with request related configurations.
     *
     * @return generated cache key.
     */
    @Override
    public String generateCacheKey(SearchParameters searchParameters, SearchCacheConfig config) {
        var context = getRequestContext();
        var builder = new StringBuilder(BUILDER_INITIAL_CAPACITY);

        appendBaseFields(builder, searchParameters);
        appendCommonSearchFields(builder, searchParameters);

        builder
            .append(searchParameters.getQuery())
            .append(searchParameters.getSpellCorrectionMode())
            .append(searchParameters.getPinUnexpandedResults())
            .append(searchParameters.getIncludeExpandedResults());

        appendPersonalization(builder, searchParameters, config, context);

        var suffix = builder.toString();

        if (config.enableCacheDetails()) {
            log.info("Search Cache key suffix: {}", suffix);
        }

        return context.getMerchandiser().merchandiserId()
            + context.getCollection()
            + UNDERSCORE
            + getHashSafe(salt, suffix);
    }

    private void appendPersonalization(StringBuilder builder,
                                       SearchParameters searchParameters,
                                       SearchCacheConfig config,
                                       RequestContext context) {
        var pageSize = searchParameters.getPagination().getSize();

        var originalPageSize = pageSize - searchParameters.getPinnedProducts().size();
        if(config.enableUserAttributes()){
            appendCollectionOfObjects(builder, context.getUserAttributes(), USER_ATTRIBUTE_STRING);
        }
        if (config.enableSaytCache() && originalPageSize == config.saytPageSize()) {
            return;
        }

        if(config.includeLoginId()) {
            builder.append(context.getLoginId());
        }

        if(config.includeVisitorId()) {
            builder.append(context.getVisitorId());
        }
    }

}
