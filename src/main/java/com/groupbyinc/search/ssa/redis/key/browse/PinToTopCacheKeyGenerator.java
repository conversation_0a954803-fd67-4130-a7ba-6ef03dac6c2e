package com.groupbyinc.search.ssa.redis.key.browse;

import com.groupbyinc.search.ssa.application.cache.CacheKeyGenerator;
import com.groupbyinc.search.ssa.core.SearchParameters;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Value;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.BUILDER_INITIAL_CAPACITY;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.appendBaseFields;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.appendCollectionOfStrings;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.appendSiteFilter;
import static com.groupbyinc.search.ssa.util.StringUtils.UNDERSCORE;
import static com.groupbyinc.search.ssa.util.StringUtils.getHashSafe;

@Slf4j
@Context
@RequiredArgsConstructor
public class PinToTopCacheKeyGenerator implements CacheKeyGenerator<BrowseCacheConfig> {

    @Value("${security.hash.salt}")
    private String salt;

    /**
     * Generate a cache key for a pin to top requests.
     * <p>
     * Fields used in a key generation:
     * <pre>{@code
     * searchParameters.getPreFilter()
     * searchParameters.getSearchMode()
     * searchParameters.getResponseMask()
     * searchParameters.getVariantRollupKeys()
     * searchParameters.getContext().getArea()
     * searchParameters.getPagination().getSize()
     * searchParameters.getPagination().getOffset()
     * searchParameters.getContext().getCollection()
     * searchParameters.getContext().getMerchandiser()
     * searchParameters.getMerchandisingConfiguration().getSiteFilter()
     * }</pre>
     * <p>
     * Generated key:
     * <pre>{@code tenant + collection + (hashed & base64 encoded) fields.}</pre>
     *
     * @param searchParameters object with request related configurations.
     *
     * @return generated cache key.
     */
    @Override
    public String generateCacheKey(SearchParameters searchParameters, BrowseCacheConfig config) {
        var builder = new StringBuilder(BUILDER_INITIAL_CAPACITY);

        appendBaseFields(builder, searchParameters);
        appendSiteFilter(builder, searchParameters);
        appendCollectionOfStrings(builder, searchParameters.getResponseMask());
        appendCollectionOfStrings(builder, searchParameters.getVariantRollupKeys());

        var suffix = builder.toString();
        var context = getRequestContext();

        if (config.enableCacheDetails()) {
            log.info("PinToTop Cache key suffix: {}", suffix);
        }

        return context.getMerchandiser().merchandiserId()
            + context.getCollection()
            + UNDERSCORE
            + getHashSafe(salt, suffix);
    }

}
