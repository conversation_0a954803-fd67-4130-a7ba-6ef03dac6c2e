package com.groupbyinc.search.ssa.redis.key;

import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;

import io.micronaut.core.annotation.Nullable;

import java.util.Arrays;
import java.util.Collection;
import java.util.function.Function;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorTransformFunctions.ATTRIBUTE_FILTER_STRING;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorTransformFunctions.BIAS_STRING;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorTransformFunctions.PINNED_REFINEMENT_STRING;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorTransformFunctions.PRODUCT_ID_BUCKET_STRING;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorTransformFunctions.PRODUCT_ID_FILTER_STRING;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorTransformFunctions.SELECTED_REFINEMENT_STRING;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorTransformFunctions.SORT_STRING;

import static java.lang.Boolean.TRUE;

/**
 * Utility methods used during cache key generation.
 */
public class KeyGeneratorUtils {

    public static final int BUILDER_INITIAL_CAPACITY = 8192;
    public static final int BUILDER_INITIAL_CAPACITY_SMALL = 1024;

    /**
     * Sort a passed collection of strings in a natural orger and append to the given string builder.
     *
     * @param builder    to append collection elements.
     * @param collection to sort and append to the passed builder.
     */
    public static void appendCollectionOfStrings(StringBuilder builder, @Nullable Collection<String> collection) {
        if (collection == null || collection.isEmpty()) {
            return;
        }
        collection.stream().sorted().forEach(builder::append);
    }

    /**
     * Convert a passed collection elements into their string representation using given function, sort them in a natural order and append to the
     * given string builder.
     *
     * @param builder          to append collection elements.
     * @param collection       with elements to append to the cache key builder.
     * @param toStringFunction to string transform function, used to convert collection elements into a string.
     * @param <T>              type of elements stored in a passed collection.
     */
    public static <T> void appendCollectionOfObjects(
        StringBuilder builder,
        @Nullable Collection<T> collection,
        Function<T, String> toStringFunction
    ) {
        if (collection == null || collection.isEmpty()) {
            return;
        }
        collection.stream().map(toStringFunction).sorted().forEach(builder::append);
    }

    /**
     * Sort passed array of strings in a natural orger and append to the given string builder.
     *
     * @param builder to append collection elements.
     * @param array   to sort and append to the passed builder.
     */
    public static void appendArrayOfStrings(StringBuilder builder, String[] array) {
        if (array == null) {
            return;
        }
        Arrays.sort(array);
        for (var string : array) {
            builder.append(string);
        }
    }

    /**
     * Append base fields to the cache key builder.
     *
     * @param builder          to append.
     * @param searchParameters object with request related fields.
     */
    public static void appendBaseFields(StringBuilder builder, SearchParameters searchParameters) {
        var context = getRequestContext();

        builder
            .append(searchParameters.getPreFilter())
            .append(searchParameters.getSearchMode())
            .append(context.getArea())
            .append(searchParameters.getPagination().getSize())
            .append(searchParameters.getPagination().getOffset());
    }

    /**
     * Append a site filter to the cache key builder.
     *
     * @param builder          to append.
     * @param searchParameters object to extract site filter.
     */
    public static void appendSiteFilter(StringBuilder builder, SearchParameters searchParameters) {
        if (searchParameters.getMerchandisingConfiguration().siteFilter() != null) {
            builder.append(searchParameters.getMerchandisingConfiguration().siteFilter().rawFilter());
        }
    }

    /**
     * Append a dynamic navigation keys to the cache key builder.
     *
     * @param builder          to append.
     * @param searchParameters object to extract attributes configurations.
     */
    public static void appendDynamicNavigations(StringBuilder builder, SearchParameters searchParameters) {
        if (searchParameters.getMerchandisingConfiguration().attributeConfigurations() != null &&
            TRUE.equals(searchParameters.getDynamicFacet())
        ) {
            var dynamicNavigations = searchParameters.getMerchandisingConfiguration()
                .attributeConfigurations()
                .values()
                .stream()
                .filter(AttributeConfiguration::dynamicFacetable)
                .map(AttributeConfiguration::key)
                .sorted()
                .toList();

            appendCollectionOfStrings(builder, dynamicNavigations);
        }
    }

    public static void appendCommonSearchFields(StringBuilder builder, SearchParameters searchParameters) {
        builder.append(getRequestContext().getRequestOptions().debug());

        appendCollectionOfStrings(builder, searchParameters.getResponseMask());
        appendCollectionOfStrings(builder, searchParameters.getPageCategories());
        appendCollectionOfStrings(builder, searchParameters.getVariantRollupKeys());
        appendCollectionOfStrings(builder, searchParameters.getIncludedNavigations());
        appendCollectionOfStrings(builder, searchParameters.getExcludedNavigations());

        appendCollectionOfObjects(builder, searchParameters.getSorts(), SORT_STRING);
        appendCollectionOfObjects(builder, searchParameters.getRefinements(), SELECTED_REFINEMENT_STRING);
        appendCollectionOfObjects(builder, searchParameters.getAttributeFilters(), ATTRIBUTE_FILTER_STRING);
        appendCollectionOfObjects(builder, searchParameters.getPinnedRefinements(), PINNED_REFINEMENT_STRING);
        appendCollectionOfObjects(builder, searchParameters.getBuriedProductBuckets(), PRODUCT_ID_BUCKET_STRING);
        appendCollectionOfObjects(builder, searchParameters.getBoostedProductBuckets(), PRODUCT_ID_BUCKET_STRING);

        if (searchParameters.getProductIdFilter() != null) {
            builder.append(PRODUCT_ID_FILTER_STRING.apply(searchParameters.getProductIdFilter()));
        }

        if (searchParameters.getBiasingProfile() != null) {
            appendCollectionOfObjects(builder, searchParameters.getBiasingProfile().getBiases(), BIAS_STRING);
        }

        appendSiteFilter(builder, searchParameters);
        appendDynamicNavigations(builder, searchParameters);
    }

}
