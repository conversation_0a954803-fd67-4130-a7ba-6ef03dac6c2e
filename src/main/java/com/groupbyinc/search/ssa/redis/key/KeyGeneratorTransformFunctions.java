package com.groupbyinc.search.ssa.redis.key;

import com.groupbyinc.search.ssa.api.dto.crm.UserAttributeDto;
import com.groupbyinc.search.ssa.core.Sort;
import com.groupbyinc.search.ssa.core.biasing.Bias;
import com.groupbyinc.search.ssa.core.crm.UserAttribute;
import com.groupbyinc.search.ssa.core.filter.AttributeFilter;
import com.groupbyinc.search.ssa.core.filter.ProductIdFilter;
import com.groupbyinc.search.ssa.core.filter.RangeFilter;
import com.groupbyinc.search.ssa.core.filter.ValueFilter;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.core.rule.PinnedRefinement;
import com.groupbyinc.search.ssa.core.rule.ProductIdsBucket;
import com.groupbyinc.search.ssa.core.rule.Refinement;

import java.util.function.Function;

import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.BUILDER_INITIAL_CAPACITY_SMALL;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.appendArrayOfStrings;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.appendCollectionOfObjects;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.appendCollectionOfStrings;
import static com.groupbyinc.search.ssa.util.StringUtils.EMPTY;

/**
 * Functions which are transform different objects into a string representation which is used to build a cache key.
 */
public interface KeyGeneratorTransformFunctions {

    Function<Range, String> RANGE_STRING = r -> EMPTY + r.low() + r.high();

    Function<SelectedRefinement, String> SELECTED_REFINEMENT_STRING = sr -> {
        var builder = new StringBuilder(BUILDER_INITIAL_CAPACITY_SMALL);

        if (sr.getType() == NavigationType.RANGE) {
            builder.append(RANGE_STRING.apply(sr.getRange()));
        } else {
            builder.append(sr.getValue());
        }

        return builder.append(sr.getField()).toString();
    };

    Function<Refinement, String> REFINEMENT_STRING = r -> r.value() + r.priority();

    Function<PinnedRefinement, String> PINNED_REFINEMENT_STRING = pr -> {
       var builder = new StringBuilder(BUILDER_INITIAL_CAPACITY_SMALL).append(pr.navigation());
        appendCollectionOfObjects(builder, pr.refinements(), REFINEMENT_STRING);

        return builder.toString();
    };

    Function<Sort, String> SORT_STRING = s -> s.getField() + s.getOrder().name();

    Function<ProductIdsBucket, String> PRODUCT_ID_BUCKET_STRING = pib -> {
        var builder = new StringBuilder(BUILDER_INITIAL_CAPACITY_SMALL);
        appendCollectionOfStrings(builder, pib.getProducts());
        return builder.toString();
    };

    Function<Bias, String> BIAS_STRING = b -> {
        var builder = new StringBuilder(BUILDER_INITIAL_CAPACITY_SMALL);

        builder.append(b.getField()).append(b.getStrength().getValue());

        if (b.getType() == Bias.Type.NUMERIC) {
            appendCollectionOfObjects(builder, b.getNumericContent().values(), String::valueOf);
            appendCollectionOfObjects(builder, b.getNumericContent().ranges(), RANGE_STRING);
        } else {
            appendArrayOfStrings(builder, b.getContentParsed());
        }

        return builder.toString();
    };

    Function<ProductIdFilter, String> PRODUCT_ID_FILTER_STRING = pif -> {
        var builder = new StringBuilder(BUILDER_INITIAL_CAPACITY_SMALL);
        appendCollectionOfStrings(builder, pif.includedProductIds());
        appendCollectionOfStrings(builder, pif.excludedProductIds());
        return builder.toString();
    };

    Function<ValueFilter, String> VALUE_FILTER_STRING = vf -> vf.getField()
        + vf.isExclude() + vf.getValue() + vf.getNumberValue();

    Function<RangeFilter, String> RANGE_FILTER_STRING = rf -> rf.getField() + RANGE_STRING.apply(rf.getRange());

    Function<AttributeFilter, String> ATTRIBUTE_FILTER_STRING = af -> {
        var builder = new StringBuilder(BUILDER_INITIAL_CAPACITY_SMALL);
        appendCollectionOfObjects(builder, af.valueFilters(), VALUE_FILTER_STRING);
        appendCollectionOfObjects(builder, af.rangeFilters(), RANGE_FILTER_STRING);
        return builder.toString();
    };

    Function<UserAttributeDto, String> USER_ATTRIBUTE_STRING = userAttribute -> {
        var builder = new StringBuilder(BUILDER_INITIAL_CAPACITY_SMALL).append(userAttribute.key());
        appendCollectionOfStrings(builder, userAttribute.values());
        return builder.toString();
    };
}
