package com.groupbyinc.search.ssa.redis.key.browse;

import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.application.cache.CacheKeyGenerator;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.redis.key.search.SearchCacheConfig;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Value;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorTransformFunctions.USER_ATTRIBUTE_STRING;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.BUILDER_INITIAL_CAPACITY;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.appendBaseFields;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.appendCollectionOfObjects;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.appendCommonSearchFields;
import static com.groupbyinc.search.ssa.util.StringUtils.UNDERSCORE;
import static com.groupbyinc.search.ssa.util.StringUtils.getHashSafe;

@Slf4j
@Context
@RequiredArgsConstructor
public class BrowseCacheKeyGenerator implements CacheKeyGenerator<BrowseCacheConfig> {

    @Value("${security.hash.salt}")
    private String salt;

    /**
     * Generate a cache key for a browse requests.
     * <p>
     * Fields used in a key generation:
     * <pre>{@code
     * searchParameters.getFacet()
     * searchParameters.getSorts()
     * searchParameters.getDebug()
     * searchParameters.getPreFilter()
     * searchParameters.getSearchMode()
     * searchParameters.getRefinements()
     * searchParameters.getResponseMask()
     * searchParameters.getPageCategories()
     * searchParameters.getProductIdFilter()
     * searchParameters.getAttributeFilters()
     * searchParameters.getContext().getArea()
     * searchParameters.getVariantRollupKeys()
     * searchParameters.getPinnedRefinements()
     * searchParameters.getExcludedNavigations()
     * searchParameters.getIncludedNavigations()
     * searchParameters.getPagination().getSize()
     * searchParameters.getBuriedProductBuckets()
     * searchParameters.getBoostedProductBuckets()
     * searchParameters.getPagination().getOffset()
     * searchParameters.getContext().getCollection()
     * searchParameters.getContext().getMerchandiser()
     * searchParameters.getBiasingProfile().get().getBiases()
     * searchParameters.getMerchandisingConfiguration().getSiteFilter()
     * searchParameters.getMerchandisingConfiguration().getAttributesConfiguration()
     * }</pre>
     * <p>
     * Generated key:
     * <pre>{@code tenant + collection + (hashed & base64 encoded) fields.}</pre>
     *
     * @param searchParameters object with request related configurations.
     *
     * @return generated cache key.
     */
    @Override
    public String generateCacheKey(SearchParameters searchParameters, BrowseCacheConfig config) {
        var builder = new StringBuilder(BUILDER_INITIAL_CAPACITY);

        appendBaseFields(builder, searchParameters);
        appendCommonSearchFields(builder, searchParameters);

        var suffix = builder.toString();
        var context = getRequestContext();

        if (config.enableCacheDetails()) {
            log.info("Browse Cache key suffix: {}", suffix);
        }

        appendPersonalization(builder, config, context);

        return context.getMerchandiser().merchandiserId()
            + context.getCollection()
            + UNDERSCORE
            + getHashSafe(salt, suffix);
    }

    private void appendPersonalization(StringBuilder builder,
                                       BrowseCacheConfig config,
                                       RequestContext context) {
        if(config.enableUserAttributes()){
            appendCollectionOfObjects(builder, context.getUserAttributes(), USER_ATTRIBUTE_STRING);
        }
    }
}
