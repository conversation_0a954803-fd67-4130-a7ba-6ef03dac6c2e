package com.groupbyinc.search.ssa.redis;

import io.lettuce.core.api.StatefulRedisConnection;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.env.Environment;
import jakarta.inject.Named;
import jakarta.inject.Singleton;

import static com.groupbyinc.search.ssa.redis.CacheSource.BROWSE;
import static com.groupbyinc.search.ssa.redis.CacheSource.MONGO;
import static com.groupbyinc.search.ssa.redis.CacheSource.SEARCH;

@Factory
@SuppressWarnings("all")
@Requires(notEnv = Environment.TEST)
public class CacheFactory {

    @Singleton
    @Named("browse")
    public RedisCache browse(@Named("browse") StatefulRedisConnection<String, String> browse) {
        return new RedisCache(BROWSE, browse);
    }

    @Singleton
    @Named("search")
    public RedisCache search(@Named("search") StatefulRedisConnection<String, String> search) {
        return new RedisCache(SEARCH, search);
    }

    @Singleton
    @Named("mongo")
    public RedisCache mongo(@Named("mongo") StatefulRedisConnection<String, String> mongo) {
        return new RedisCache(MONGO, mongo);
    }

}
