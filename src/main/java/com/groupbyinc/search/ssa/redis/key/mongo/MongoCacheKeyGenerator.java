package com.groupbyinc.search.ssa.redis.key.mongo;

import com.groupbyinc.search.ssa.application.cache.CacheKeyGenerator;
import com.groupbyinc.search.ssa.core.SearchParameters;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Value;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.BUILDER_INITIAL_CAPACITY;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.appendBaseFields;
import static com.groupbyinc.search.ssa.redis.key.KeyGeneratorUtils.appendCommonSearchFields;
import static com.groupbyinc.search.ssa.util.StringUtils.UNDERSCORE;
import static com.groupbyinc.search.ssa.util.StringUtils.getHashSafe;

import static io.micronaut.core.util.StringUtils.isNotEmpty;

@Slf4j
@Context
@RequiredArgsConstructor
public class MongoCacheKeyGenerator implements CacheKeyGenerator<MongoCacheConfig> {

    @Value("${security.hash.salt}")
    private String salt;

    /**
     * Generate a cache key for a mongo requests.
     * <p>
     * Fields used in a key generation:
     * <pre>{@code
     * searchParameters.getFacet()
     * searchParameters.getSorts()
     * searchParameters.getDebug()
     * searchParameters.getQuery() - if it is part number search.
     * searchParameters.getPreFilter()
     * searchParameters.getSearchMode()
     * searchParameters.getRefinements()
     * searchParameters.getResponseMask()
     * searchParameters.getPageCategories()
     * searchParameters.getProductIdFilter()
     * searchParameters.getAttributeFilters()
     * searchParameters.getContext().getArea()
     * searchParameters.getVariantRollupKeys()
     * searchParameters.getPinnedRefinements()
     * searchParameters.getExcludedNavigations()
     * searchParameters.getIncludedNavigations()
     * searchParameters.getPagination().getSize()
     * searchParameters.getBuriedProductBuckets()
     * searchParameters.getBoostedProductBuckets()
     * searchParameters.getPagination().getOffset()
     * searchParameters.getContext().getCollection()
     * searchParameters.getContext().getMerchandiser()
     * searchParameters.getBiasingProfile().get().getBiases()
     * searchParameters.getMerchandisingConfiguration().getSiteFilter()
     * searchParameters.getMerchandisingConfiguration().getAttributesConfiguration()
     * }</pre>
     * <p>
     * Generated key:
     * <pre>{@code tenant + collection + (hashed & base64 encoded) fields.}</pre>
     *
     * @param searchParameters object with request related configurations.
     *
     * @return generated cache key.
     */
    @Override
    public String generateCacheKey(SearchParameters searchParameters, MongoCacheConfig config) {
        var builder = new StringBuilder(BUILDER_INITIAL_CAPACITY);

        appendBaseFields(builder, searchParameters);
        appendCommonSearchFields(builder, searchParameters);

        if(!searchParameters.getPartNumberSearchableAttributes().isEmpty() && isNotEmpty(searchParameters.getQuery())) {
            builder.append(searchParameters.getQuery());
        }

        var suffix = builder.toString();
        var context = getRequestContext();

        if (config.enableCacheDetails()) {
            log.info("Mongo Cache key suffix: {}", suffix);
        }

        return context.getMerchandiser().merchandiserId()
            + context.getCollection()
            + UNDERSCORE
            + getHashSafe(salt, suffix);
    }

}
