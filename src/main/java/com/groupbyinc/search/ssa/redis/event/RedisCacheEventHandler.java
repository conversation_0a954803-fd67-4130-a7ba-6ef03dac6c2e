package com.groupbyinc.search.ssa.redis.event;

import com.groupbyinc.search.ssa.application.cache.CacheConfig;
import com.groupbyinc.search.ssa.application.cache.event.InvalidateCacheEvent;
import com.groupbyinc.search.ssa.application.cache.event.SaveToCacheEvent;
import com.groupbyinc.search.ssa.redis.CacheSource;
import com.groupbyinc.search.ssa.redis.RedisCache;

import io.micronaut.runtime.event.annotation.EventListener;
import io.micronaut.scheduling.annotation.Async;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

import static com.groupbyinc.search.ssa.redis.CacheSource.BROWSE;
import static com.groupbyinc.search.ssa.redis.CacheSource.MONGO;
import static com.groupbyinc.search.ssa.redis.CacheSource.PIN_TO_TOP;
import static com.groupbyinc.search.ssa.redis.CacheSource.SEARCH;

@Singleton
@RequiredArgsConstructor
public class RedisCacheEventHandler {

    private final Map<CacheSource, RedisCache> caches = new HashMap<>();

    public RedisCacheEventHandler(@Named("browse") RedisCache browse,
                                  @Named("search") RedisCache search,
                                  @Named("mongo") RedisCache mongo) {
        caches.put(SEARCH, search);
        caches.put(BROWSE, browse);
        caches.put(PIN_TO_TOP, browse);

        caches.put(MONGO, mongo);
    }

    @Async
    @EventListener
    public void handleSaveToCacheEvent(SaveToCacheEvent<? extends CacheConfig> event) {
        caches.get(event.source()).save(event.key(), event.value(), event.config());
    }

    @Async
    @EventListener
    public void handleInvalidateCacheEvent(InvalidateCacheEvent event) {
        caches.values().forEach(c -> c.invalidateByTenantAndCollection(event.tenant(), event.collection()));
    }

}
