package com.groupbyinc.search.ssa.redis.key.browse;

import com.groupbyinc.search.ssa.application.cache.CacheConfig;
import com.groupbyinc.search.ssa.features.FeatureFlag;

public record BrowseCacheConfig(int ttl,
                                boolean enabled,
                                boolean enableCacheDetails,
                                boolean enableUserAttributes) implements CacheConfig, FeatureFlag {

    public static BrowseCacheConfig DEFAULT = new BrowseCacheConfig(60, false, false, false);

    //By default, object mapper pas a "0" if their no such property in a JSON.
    public BrowseCacheConfig {
        if (ttl <= 0) {
            ttl = 60;
        }
    }

}
