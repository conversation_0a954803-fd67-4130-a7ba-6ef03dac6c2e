package com.groupbyinc.search.ssa.redis;

import com.groupbyinc.search.ssa.application.cache.Cache;
import com.groupbyinc.search.ssa.application.cache.CacheConfig;

import io.lettuce.core.ScanArgs;
import io.lettuce.core.SetArgs;
import io.lettuce.core.api.StatefulRedisConnection;

import io.micronaut.core.annotation.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.ParametersAreNonnullByDefault;
import java.time.Duration;
import java.util.List;

import static com.groupbyinc.search.ssa.util.StringUtils.EMPTY;

@Slf4j
@RequiredArgsConstructor
@ParametersAreNonnullByDefault
public class RedisCache implements Cache {

    // (tenantcollection_)
    private static final String KEY_PREFIX_TEMPLATE = "%s%s_*";

    private final CacheSource source;
    private final StatefulRedisConnection<String, String> redisConnection;

    @Override
    public void save(String key, String value, CacheConfig config) {
        redisConnection
            .reactive()
            .set(key, value, SetArgs.Builder.ex(Duration.ofSeconds(config.ttl())))
            .doOnSuccess(m -> log.trace("Response saved in a cache: {}, value: {}", source, value))
            .doOnError(e -> log.error("Fail to write into cache {}, value: {}.", source, value, e))
            .subscribe();
    }

    @NonNull
    public String get(String key) {
        var value = redisConnection.sync().get(key);
        if (value != null) {
            return value;
        }
        return EMPTY;
    }

    public void invalidateByTenantAndCollection(String tenant, String collection) {
        var prefix = KEY_PREFIX_TEMPLATE.formatted(tenant, collection);

        var keys = getKeysFromCacheByPrefix(prefix);

        if (!keys.isEmpty()) {
            redisConnection
                .async()
                .del(keys.toArray(String[]::new));
        }

        log.info("Cache {} invalidated after upload into {} collection of {} tenant.", source, tenant, collection);
    }

    private List<String> getKeysFromCacheByPrefix(String prefix) {
        return redisConnection
            .sync()
            .scan(ScanArgs.Builder.matches(prefix))
            .getKeys();
    }

}
