package com.groupbyinc.search.ssa.redis.key.mongo;

import com.groupbyinc.search.ssa.application.cache.CacheConfig;
import com.groupbyinc.search.ssa.features.FeatureFlag;

public record MongoCacheConfig(int ttl,
                               boolean enabled,
                               boolean enableCacheDetails) implements CacheConfig, FeatureFlag {

    public static MongoCacheConfig DEFAULT = new MongoCacheConfig(60, false, false);

    //By default, object mapper pas a "0" if their no such property in a JSON.
    public MongoCacheConfig {
        if (ttl <= 0) {
            ttl = 60;
        }
    }

}
