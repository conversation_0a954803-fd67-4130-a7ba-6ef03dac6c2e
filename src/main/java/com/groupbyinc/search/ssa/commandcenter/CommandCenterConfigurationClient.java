package com.groupbyinc.search.ssa.commandcenter;

import com.groupbyinc.search.ssa.core.AllConfigurations;

import io.micronaut.http.annotation.Get;
import io.micronaut.http.annotation.Header;
import io.micronaut.http.client.annotation.Client;

import static io.micronaut.http.HttpHeaders.ACCEPT_ENCODING;

@Client("${commandcenter.client.url}")
public interface CommandCenterConfigurationClient {

    @Get("/site-search")
    @Header(name = ACCEPT_ENCODING, value = "gzip")
    AllConfigurations getAllConfigurations();

}
