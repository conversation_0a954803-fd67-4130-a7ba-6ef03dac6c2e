package com.groupbyinc.search.ssa.productcatalog;

import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;

import io.micronaut.context.annotation.Context;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;

import static com.google.common.base.Strings.isNullOrEmpty;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.RANGE;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.VALUE;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_ATTRIBUTES;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_VARIANTS;

import static io.micrometer.common.util.StringUtils.isBlank;
import static java.util.function.Predicate.not;

@Context
public class ProductCatalogFilterService {

    public static final int VARIANTS_LIMIT = 5;

    // https://cloud.google.com/retail/docs/reference/rest/v2/projects.locations.catalogs.branches.products
    private static final Set<String> SYSTEM_ATTRIBUTES = Set.of(
        "uri",
        "gtin",
        "cost",
        "title",
        "sizes",
        "price",
        "brands",
        "colors",
        "rating",
        "images",
        "genders",
        "patterns",
        "discount",
        "ageGroups",
        "materials",
        "productId",
        "conditions",
        "categories",
        "shipToStore",
        "description",
        "ratingCount",
        "availability",
        "currencyCode",
        "originalPrice",
        "colorFamilies",
        "pickupInStore",
        "nextDayDelivery",
        "ratingHistogram",
        "sameDayDelivery",
        "customFulfillment1",
        "customFulfillment2",
        "customFulfillment3",
        "customFulfillment4",
        "customFulfillment5"
    );

    private static final Set<String> STRING_SYSTEM_ATTRIBUTES = Set.of(
        "uri",
        "gtin",
        "title",
        "productId",
        "shipToStore",
        "description",
        "availability",
        "pickupInStore",
        "nextDayDelivery",
        "sameDayDelivery",
        "customFulfillment1",
        "customFulfillment2",
        "customFulfillment3",
        "customFulfillment4",
        "customFulfillment5"
    );

    private static final Set<String> STRING_ARRAY_SYSTEM_ATTRIBUTES = Set.of(
        "sizes",
        "brands",
        "genders",
        "patterns",
        "ageGroups",
        "materials",
        "categories",
        "conditions"
    );

    // PriceInfo
    // https://cloud.google.com/retail/docs/reference/rest/v2/projects.locations.catalogs.branches.products#priceinfo
    private static final Set<String> PRICE_SYSTEM_ATTRIBUTES = Set.of(
        "cost",
        "price",
        "originalPrice",
        "currencyCode"
    );

    // ColorInfo
    // https://cloud.google.com/retail/docs/reference/rest/v2/projects.locations.catalogs.branches.products?hl=en#colorinfo
    private static final Set<String> COLOR_SYSTEM_ATTRIBUTES = Set.of(
        "colors",
        "colorFamilies"
    );

    // Rating
    // https://cloud.google.com/retail/docs/reference/rest/v2/projects.locations.catalogs.branches.products#rating
    private static final Set<String> RATING_SYSTEM_ATTRIBUTES = Set.of(
        "rating",
        "ratingCount",
        "ratingHistogram"
    );

    private static final String TEXT = "text";
    private static final String COST = "cost";
    private static final String PRICE = "price";
    private static final String IMAGES = "images";
    private static final String RATING = "rating";
    private static final String NUMBERS = "numbers";
    private static final String DISCOUNT = "discount";
    private static final String PRICE_INFO = "priceInfo";
    private static final String COLOR_INFO = "colorInfo";
    private static final String RATING_COUNT = "ratingCount";
    private static final String CURRENCY_CODE = "currencyCode";
    private static final String ORIGINAL_PRICE = "originalPrice";
    private static final String RATING_HISTOGRAM = "ratingHistogram";

    private static final Predicate<Record> INVALID_RECORD_FILTER = (record) -> {
        if (isBlank(record.getTitle())) {
            getRequestContext().addWarning(
                "Product#%s had no title and was filtered out.".formatted(record.getProductId())
            );
            return false;
        }
        return true;
    };

    private static final Predicate<Record> INVALID_SPONSORED_RECORD_FILTER = (record) -> {
        if (isBlank(record.getTitle())) {
            getRequestContext().addWarning(
                "Sponsored Product#%s had no title and was filtered out.".formatted(record.getProductId())
            );
            return false;
        }
        return true;
    };

    public boolean filterRecordByRefinements(Record record, Map<String, List<SelectedRefinement>> refinements) {

        JSONObject metadata = new JSONObject(record.getMetadata());

        for (var refinement : refinements.entrySet()) {

            var rootProductMatches = productMatchesRefinement(metadata, refinement);
            var variants = getVariantsByRefinement(metadata, refinement);

            if (!rootProductMatches && variants.isEmpty()) {
                return false;
            } else {
                metadata.put(PRODUCT_FIELD_VARIANTS, variants);
            }
        }

        record.setMetadata(metadata.toMap());

        return true;
    }

    public List<Record> filterInvalidRecords(List<Record> records) {
        return records
            .stream()
            .filter(INVALID_RECORD_FILTER)
            .toList();
    }

    public List<Record> filterInvalidSponsoredRecords(List<Record> records) {
        return records
            .stream()
            .filter(INVALID_SPONSORED_RECORD_FILTER)
            .toList();
    }

    public void limitVariants(Record record) {
        var variants = record.getMetadata().get(PRODUCT_FIELD_VARIANTS);
        if (variants instanceof List<?> variantsList && variantsList.size() > VARIANTS_LIMIT) {
            record
                .getMetadata()
                .put(
                    PRODUCT_FIELD_VARIANTS,
                    variantsList.stream().limit(VARIANTS_LIMIT).toList()
                );
        }
    }

    private boolean productMatchesRefinement(JSONObject metadata,
                                             Map.Entry<String, List<SelectedRefinement>> refinement) {
        var trimmedAttribute = trimRefinementPrefixes(refinement.getKey());
        boolean valid;
        if (SYSTEM_ATTRIBUTES.contains(trimmedAttribute)) {
            valid = validateSystemAttribute(metadata, trimmedAttribute, refinement.getValue());
        } else {
            if (!metadata.has(PRODUCT_FIELD_ATTRIBUTES)) {
                return false;
            }
            var attributes = metadata.getJSONObject(PRODUCT_FIELD_ATTRIBUTES);
            if (!attributes.has(trimmedAttribute)) {
                return false;
            }
            var attribute = attributes.getJSONObject(trimmedAttribute);
            valid = isAttributeHasValidValue(attribute, refinement.getValue());
        }

        return valid;
    }

    private List<Map<String, Object>> getVariantsByRefinement(JSONObject metadata,
                                                              Map.Entry<String, List<SelectedRefinement>> refinement) {
        if (!metadata.has(PRODUCT_FIELD_VARIANTS)) {
            return new ArrayList<>();
        }

        List<Map<String, Object>> filtered = new ArrayList<>();
        var variants = metadata.getJSONArray(PRODUCT_FIELD_VARIANTS);

        for (var variant : variants) {
            var variantJson = (JSONObject) variant;

            if (productMatchesRefinement(variantJson, refinement)) {
                filtered.add(variantJson.toMap());
            }
        }
        return filtered;
    }

    private boolean validateSystemAttribute(JSONObject product,
                                            String key,
                                            List<SelectedRefinement> refinements) {
        if (STRING_SYSTEM_ATTRIBUTES.contains(key)) {
            var validationResult = validateStringSystemAttributes(product, key, refinements);
            if (!validationResult) {
                return false;
            }
        }
        if (STRING_ARRAY_SYSTEM_ATTRIBUTES.contains(key) && product.has(key)) {
            var arrayAttribute = product.getJSONArray(key);
            var validationResult = matchesValueRefinements(arrayAttribute, refinements);
            if (!validationResult) {
                return false;
            }
        }
        if (COLOR_SYSTEM_ATTRIBUTES.contains(key) && product.has(COLOR_INFO)) {
            var colorInfo = product.getJSONObject(COLOR_INFO);
            if (!colorInfo.has(key)) {
                return true;
            }
            var validationResult = matchesValueRefinements(colorInfo.getJSONArray(key), refinements);
            if (!validationResult) {
                return false;
            }
        }
        if (PRICE_SYSTEM_ATTRIBUTES.contains(key)) {
            var validationResult = validatePriceInfo(product, key, refinements);
            if (!validationResult) {
                return false;
            }
        }
        if (RATING_SYSTEM_ATTRIBUTES.contains(key)) {
            var validationResult = validateRating(product, key, refinements);
            if (!validationResult) {
                return false;
            }
        }
        if (DISCOUNT.equals(key)) {
            var validationResult = validateNumberSystemAttribute(product, key, refinements);
            if (!validationResult) {
                return false;
            }
        }
        if (IMAGES.equals(key)) {
            return validateImages(product, refinements);
        }
        return true;
    }

    private boolean isAttributeHasValidValue(JSONObject attribute, List<SelectedRefinement> refinements) {
        boolean valid;
        var valueRefinements = refinements.stream()
            .filter(refinement -> refinement.getType() == VALUE)
            .toList();
        var rangeRefinements = refinements.stream()
            .filter(refinement -> refinement.getType() == RANGE)
            .toList();

        if (attribute.has(TEXT)) {
            var textValues = attribute.getJSONArray(TEXT);
            valid = matchesValueRefinements(textValues, valueRefinements);
        } else if (attribute.has(NUMBERS)) {
            var numberValues = attribute.getJSONArray(NUMBERS);
            valid = matchesRangeRefinements(numberValues, rangeRefinements);
        } else {
            return true;
        }
        return valid;
    }

    private boolean validatePriceInfo(JSONObject product, String key, List<SelectedRefinement> refinements) {
        if (!product.has(PRICE_INFO)) {
            return true;
        }

        var priceInfo = product.getJSONObject(PRICE_INFO);
        if (key.equals(COST) || key.equals(CURRENCY_CODE)) {
            return validateStringSystemAttributes(priceInfo, key, refinements);
        }
        if (key.equals(PRICE) || key.equals(ORIGINAL_PRICE)) {
            return validateNumberSystemAttribute(priceInfo, key, refinements);
        }
        return true;
    }

    private boolean validateRating(JSONObject product, String key, List<SelectedRefinement> refinements) {
        if (!product.has(RATING)) {
            return true;
        }

        var rating = product.getJSONObject(RATING);
        if (key.equals(RATING_COUNT) || key.equals(RATING)) {
            return validateNumberSystemAttribute(rating, key, refinements);
        }
        // Rating Histogram is a list of integers with a fixed size of 5, numbers of ratings for each star.
        // Google Retail API does not support filtering by this field.
        if (key.equals(RATING_HISTOGRAM)) {
            rating.has(RATING_HISTOGRAM);
        }
        return true;
    }

    private boolean validateImages(JSONObject product, List<SelectedRefinement> refinements) {
        if (!product.has(IMAGES)) {
            return true;
        }

        return matchesValueRefinements(product.getJSONArray(IMAGES), refinements);
    }

    private boolean validateStringSystemAttributes(JSONObject product,
                                                   String key,
                                                   List<SelectedRefinement> refinements) {
        if (!product.has(key)) {
            return true;
        }
        return matchesValueRefinements(product.getString(key), refinements);
    }

    private boolean validateNumberSystemAttribute(JSONObject product,
                                                  String key,
                                                  List<SelectedRefinement> refinements) {
        if (!product.has(key)) {
            return true;
        }

        return matchesRangeRefinements(product.getNumber(key), refinements);
    }

    /**
     * Matches range refinements against an attribute.
     *
     * @param attribute   The attribute to match against.
     * @param refinements The refinements to match against.
     * @return true if the attribute matches the refinements, false otherwise.
     */
    private static boolean matchesRangeRefinements(Object attribute, List<SelectedRefinement> refinements) {
        Predicate<Range> rangePredicate = switch (attribute) {
            case Number number -> refinementRange -> {
                var value = number.doubleValue();
                return refinementRange.low() <= value && refinementRange.high() >= value;
            };
            case JSONArray jsonArray -> refinementRange -> {
                for (Object value : jsonArray) {
                    var doubleValue = Double.valueOf(String.valueOf(value));
                    if (refinementRange.low() <= doubleValue && refinementRange.high() >= doubleValue) {
                        return true;
                    }
                }
                return false;
            };
            default -> throw new IllegalArgumentException("Unsupported attribute type: " + attribute.getClass());
        };

        return refinementsMatchPredicate(refinements, rangePredicate);
    }

    /**
     * Matches value refinements against an attribute.
     *
     * @param attribute   The attribute to match against.
     * @param refinements The refinements to match against.
     * @return true if the attribute matches the refinements, false otherwise.
     */
    private static boolean matchesValueRefinements(Object attribute, List<SelectedRefinement> refinements) {
        Predicate<String> valuePredicate = switch (attribute) {
            case String string -> refinementValue -> refinementValue.equals(string);
            case JSONArray jsonArray -> refinementValue -> {
                for (Object value : jsonArray) {
                    if (value.equals(refinementValue)) {
                        return true;
                    }
                }
                return false;
            };
            default -> throw new IllegalArgumentException("Unsupported attribute type: " + attribute.getClass());
        };

        return refinementsMatchPredicate(refinements, valuePredicate);
    }

    @SuppressWarnings("unchecked")
    private static <T> boolean refinementsMatchPredicate(List<SelectedRefinement> refinements,
                                                         Predicate<T> predicate) {
        Function<SelectedRefinement, T> refinementValueExtractor =
            switch (refinements.getFirst().getType()) {
                case VALUE -> refinement -> (T) refinement.getValue();
                case RANGE -> refinement -> (T) refinement.getRange();
            };

        var requiredRefinementValues = refinements
            .stream()
            .filter(not(SelectedRefinement::isOr))
            .map(refinementValueExtractor)
            .toList();

        var hasRequiredValues = requiredRefinementValues
            .stream()
            // true for empty stream
            .allMatch(predicate);

        if (!requiredRefinementValues.isEmpty()) {
            return hasRequiredValues;
        }

        var nonRequiredRefinementValues = refinements
            .stream()
            .filter(SelectedRefinement::isOr)
            .map(refinementValueExtractor)
            .toList();

        return nonRequiredRefinementValues
            .stream()
            // false for empty stream
            .anyMatch(predicate);
    }

    // Trim prefix such that only the value after "." is returned, ex. converts "priceInfo.price"
    // to "price". Doing this because we don't consider these prefixes in our attribute lookups,
    // which can cause bugs if we don't remove them before performing lookups.
    private static String trimRefinementPrefixes(String attributeName) {
        if (isNullOrEmpty(attributeName)) {
            return attributeName;
        }
        int dotIndex = attributeName.indexOf(".");
        if (dotIndex != -1) {
            return attributeName.substring(dotIndex + 1);
        }
        return attributeName;
    }
}
