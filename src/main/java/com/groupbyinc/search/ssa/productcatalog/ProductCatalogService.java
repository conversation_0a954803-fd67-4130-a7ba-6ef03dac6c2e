package com.groupbyinc.search.ssa.productcatalog;

import com.groupbyinc.search.ssa.application.logging.LoggingContext;
import com.groupbyinc.search.ssa.application.core.pdp.ProductSearch;
import com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKey;
import com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKey.RollupKeyType;
import com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKeysParser;
import com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKeysProcessor;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.ProductSearchResultWrapper;
import com.groupbyinc.search.ssa.core.RecordLabel;
import com.groupbyinc.search.ssa.core.RecordSponsoredInfo;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.Rewrites;
import com.groupbyinc.search.ssa.core.SearchMode;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.core.product.Product;
import com.groupbyinc.search.ssa.core.product.util.ProductUtils;
import com.groupbyinc.search.ssa.core.request.RequestServed;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.metrics.opentelemetry.interceptors.ApiLatencyMetricsCollector;
import com.groupbyinc.search.ssa.productcatalog.client.ProductCatalogClient;
import com.groupbyinc.search.ssa.productcatalog.exception.SiteSearchProductCatalogException;
import com.groupbyinc.search.ssa.productcatalog.model.LocalInventoryFilter;
import com.groupbyinc.search.ssa.productcatalog.model.ProductCatalogPdpRequest;
import com.groupbyinc.search.ssa.productcatalog.model.ProductCatalogRequest;
import com.groupbyinc.search.ssa.productcatalog.model.ProductRequest;

import com.google.common.annotations.VisibleForTesting;
import io.micronaut.context.annotation.Context;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.core.propagation.PropagatedContext;
import io.micronaut.core.util.CollectionUtils;
import io.micronaut.core.util.StringUtils;
import io.micronaut.http.client.exceptions.HttpClientResponseException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.application.core.search.engine.SearchEngineType.GOOGLE;
import static com.groupbyinc.search.ssa.application.core.search.strategy.multiproduct.MultiProductSearchService.NOT_PRODUCT_ID_REFINEMENT;
import static com.groupbyinc.search.ssa.application.logging.LogSamplingFilter.SAMPLE_MARKER;
import static com.groupbyinc.search.ssa.core.SearchMode.PRODUCT_SEARCH;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_VARIANTS;
import static com.groupbyinc.search.ssa.core.product.util.ProductUtils.prioritizeVariants;
import static com.groupbyinc.search.ssa.core.request.RequestServed.FETCH;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_FETCH;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_MISSING_PRODUCTS_REMOVAL;
import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultList;
import static com.groupbyinc.search.ssa.util.StringUtils.castToStringSafely;
import static com.groupbyinc.search.ssa.util.debug.DataCatalogDebugInfo.REGULAR_SUFFIX;
import static com.groupbyinc.search.ssa.util.debug.DataCatalogDebugInfo.SPONSORED_SUFFIX;
import static com.groupbyinc.search.ssa.util.debug.DebugHelpers.saveDataCatalogRequestToDebug;
import static com.groupbyinc.search.ssa.util.debug.DebugHelpers.saveDataCatalogResponseToDebug;

import static io.micronaut.core.util.CollectionUtils.isNotEmpty;
import static java.util.concurrent.CompletableFuture.supplyAsync;
import static java.util.stream.Collectors.toMap;

@Slf4j
@Context
@AllArgsConstructor
public class ProductCatalogService implements ProductSearch {

    private static final List<String> METADATA_IGNORE_KEYS = List.of(
        Product.PRODUCT_FIELD_TYPE
    );

    private final FeaturesManager featuresManager;
    private final ProductCatalogClient dataCatalogClient;
    private final ProductCatalogFilterService productCatalogFilterService;
    private final VariantRollupKeysParser variantRollupKeysParser;
    private final VariantRollupKeysProcessor variantRollupKeysProcessor;

    @Override
    public Optional<Product> getProductDetails(String productId, List<String> requestVariantIds) {
        var context = getRequestContext();
        LoggingContext.set(context, SearchMode.PRODUCT_SEARCH.name());

        var req = new ProductCatalogPdpRequest(
            context.getMerchandiser().merchandiserId(),
            context.getCollection(),
            new ProductRequest(productId, productId)
        );

        return dataCatalogClient.fetchPdpProduct(req, context.getRequestOptions().skipCache())
            .single()
            .map(resp -> {
                if (CollectionUtils.isEmpty(resp.records())) {
                    return Optional.<Product>empty();
                }

                var product = ProductUtils.fromMap(resp.records().getFirst());
                product.setVariants(prioritizeVariants(requestVariantIds, product.getVariants()));

                return Optional.of(product);
            }).onErrorMap(t -> {
                log.error("Product Catalog API. Can't find product by id: %s, error: %s".formatted(
                    productId,
                    t.getMessage()
                ), t);
                return new SiteSearchProductCatalogException(t.getMessage(), t);
            }).block();
    }

    /**
     * Fetches pinned products details from the product catalog API and applies pinned products filters.
     *
     * @param searchParameters search parameters
     * @param pinnedProductIds products' ids to fetch
     * @param pinnedProductsFilteringEnabled returns only related pinned products when true
     * @return the list of records populated with metadata including nested variants.
     */
    @ApiLatencyMetricsCollector
    public ProductSearchResultWrapper getPinnedProducts(SearchParameters searchParameters,
                                                        Set<String> pinnedProductIds,
                                                        boolean pinnedProductsFilteringEnabled) {

        var productDetails = getProductsDetails(searchParameters, pinnedProductIds, false);
        if (!pinnedProductsFilteringEnabled) {
            return productDetails;
        }

        var relatedPinnedProducts = getRelatedPinnedProducts(
            searchParameters,
            productDetails.records()
        );

        return ProductSearchResultWrapper.builder().records(relatedPinnedProducts).servedFrom(FETCH).build();
    }

    /**
     * Fetches products details from the product catalog API.
     *
     * @param searchParameters search parameters
     * @param productsIds      products' ids to fetch
     * @param filteringEnabled  whether to filter results according to the selected refinements
     * @return the list of records populated with metadata including nested variants.
     */
    @ApiLatencyMetricsCollector
    public ProductSearchResultWrapper getProductsDetails(@NonNull SearchParameters searchParameters,
                                                         @NonNull Set<String> productsIds,
                                                         boolean filteringEnabled) {
        if (productsIds.isEmpty()) {
            return new ProductSearchResultWrapper(List.of(), RequestServed.STATIC);
        }

        List<String> additionalFields = new ArrayList<>();
        Map<String, List<SelectedRefinement>> selectedRefinements = new HashMap<>();

        if (filteringEnabled) {
            searchParameters
                .getRefinements()
                .stream()
                .filter(NOT_PRODUCT_ID_REFINEMENT)
                .forEach(refinement ->
                    selectedRefinements.computeIfAbsent(refinement.getField(), k -> new ArrayList<>())
                        .add(refinement)
                );

            additionalFields.addAll(selectedRefinements.keySet());
        }

        var variantRollupKeys = variantRollupKeysParser.parseVariantRollupKeys(
            searchParameters.getVariantRollupKeys(),
            searchParameters.getMerchandisingConfiguration().attributeConfigurations()
        );

        var productCatalogRequest = getProductCatalogRequest(
            searchParameters,
            true,
            variantRollupKeys,
            idsToProductRequests(productsIds),
            additionalFields
        );

        saveDataCatalogRequestToDebug(productCatalogRequest, REGULAR_SUFFIX, log);

        var records = fetchProductsMetadata(productCatalogRequest);

        var validRecords = productCatalogFilterService.filterInvalidRecords(records);

        // Fulfillment rollup values should include all variants before filtering
        variantRollupKeysProcessor.setFulfillmentVariantRollupValues(validRecords, variantRollupKeys);

        var filteredRecords = filteringEnabled
            ? getFilteredRecords(selectedRefinements, validRecords)
            : validRecords;

        // Other variant rollup keys' types should respect filtering
        variantRollupKeysProcessor.setVariantRollupValues(filteredRecords, variantRollupKeys);

        filteredRecords.forEach(productCatalogFilterService::limitVariants);

        // Remove non-retrievable fields, which where used for variant rollup values
        variantRollupKeysProcessor.removeAddedNonRetrievableFields(
            filteredRecords,
            variantRollupKeys,
            searchParameters
        );

        return ProductSearchResultWrapper.builder().records(filteredRecords).servedFrom(FETCH).build();
    }

    private List<Record>getFilteredRecords(Map<String, List<SelectedRefinement>> selectedRefinements,
                                           List<Record> records) {
        if (selectedRefinements.isEmpty()) {
            return records;
        }
        try {
            return records
                .stream()
                .filter(record -> productCatalogFilterService.filterRecordByRefinements(record, selectedRefinements))
                .toList();
        } catch (Exception e) {
            getRequestContext().addWarning(
                "Error during applying of additional filters: %s".formatted(e.getMessage())
            );
            return records;
        }
    }

    private List<String> getVariantRollupFields(List<VariantRollupKey> variantRollupKeys) {

        return variantRollupKeys.stream()
            .filter(vrk -> vrk.type() != RollupKeyType.INVENTORY_ATTRIBUTE)
            .map(VariantRollupKey::attribute)
            .toList();
    }

    private LocalInventoryFilter getLocalInventoryFilter(@NonNull List<VariantRollupKey> variantRollupKeys) {
        Set<String> placeIds = new HashSet<>();
        Set<String> inventoryAttributes = new HashSet<>();

        variantRollupKeys.stream()
            .filter(variantRollupKey -> variantRollupKey.type() == RollupKeyType.INVENTORY_ATTRIBUTE)
            .forEach(variantRollupKey -> {
                placeIds.add(variantRollupKey.placeId());
                inventoryAttributes.add(variantRollupKey.attribute());
            });

        return new LocalInventoryFilter(List.copyOf(placeIds), List.copyOf(inventoryAttributes));
    }

    private List<Record> fetchProductsMetadata(ProductCatalogRequest req) {
        var context = getRequestContext();

        return dataCatalogClient.fetchProductsMetadata(req, context.getRequestOptions().skipCache())
            .single()
            .map(resp -> {
                if (isNotEmpty(resp.queryDetails().warnings())) {
                    getRequestContext().getWarnings().addAll(resp.queryDetails().warnings());
                }

                if (CollectionUtils.isEmpty(resp.records())) {
                    return List.<Record>of();
                }

                saveDataCatalogResponseToDebug(resp, REGULAR_SUFFIX, log);

                return resp.records().stream().map(recordMap -> recordFromMap(
                    context.getMerchandiser(),
                    context.getCollection(),
                    recordMap
                ))
                .toList();
            })
            .onErrorMap(t -> new SiteSearchProductCatalogException(
                "Product Catalog API. Can't get response. Error message: " + t.getMessage(), t
            )).block();
    }

    public SearchResults fillProductsWithMetadata(SearchParameters searchParameters, SearchResults searchResults) {
        if (searchResults.hasNoRecords() || !isProductCatalogUsed(searchParameters)) {
            return searchResults;
        }
        var resultBuilder = searchResults.toBuilder();
        Map<String, Map<String, Object>> catalogRecords;

        if (Boolean.TRUE.equals(searchParameters.getFetchSponsoredRecordsVariants())) {
            var sponsoredResults = searchResults.toBuilder()
                .records(Collections.emptyList())
                .sponsoredRecords(searchResults.getSponsoredRecords())
                .build();
            var notSponsoredResults = searchResults.toBuilder()
                .records(searchResults.getRecords())
                .sponsoredRecords(Collections.emptyList())
                .build();


            Supplier<Map<String, Map<String, Object>>> sponsoredTask = () -> fetchProducts(
                searchParameters,
                sponsoredResults,
                true,
                SPONSORED_SUFFIX
            );
            var sponsoredFuture = supplyAsync(PropagatedContext.wrapCurrent(sponsoredTask));

            Supplier<Map<String, Map<String, Object>>> notSponsoredTask = () -> fetchProducts(
                searchParameters,
                notSponsoredResults,
                false,
                REGULAR_SUFFIX
            );
            var notSponsoredFuture = supplyAsync(PropagatedContext.wrapCurrent(notSponsoredTask));

            var context = PropagatedContext.getOrEmpty(); // add context to the scope of lambda expression.
            catalogRecords = CompletableFuture
                .allOf(sponsoredFuture, notSponsoredFuture)
                .thenApply(v -> {
                    try (var ignore = context.propagate()) {
                        HashMap<String, Map<String, Object>> result = new HashMap<>();
                        result.putAll(notSponsoredFuture.join());
                        result.putAll(sponsoredFuture.join());
                        return result;
                    }
                }).join();
        } else {
            catalogRecords = new HashMap<>(
                fetchProducts(
                    searchParameters,
                    searchResults,
                    false,
                    REGULAR_SUFFIX
                )
            );
        }

        var missingProductsRemovalEnabled = missingProductsRemovalEnabled();
        if (CollectionUtils.isEmpty(catalogRecords) && !missingProductsRemovalEnabled) {
            return resultBuilder.build();
        }

        var records = mergeRecords(
            searchParameters,
            searchResults.getRecords(),
            catalogRecords,
            missingProductsRemovalEnabled
        );
        resultBuilder.records(productCatalogFilterService.filterInvalidRecords(records));

        if (!searchResults.getSponsoredRecords().isEmpty()) {
            var sponsoredRecords = mergeRecords(
                searchParameters,
                searchResults.getSponsoredRecords(),
                catalogRecords,
                missingProductsRemovalEnabled
            );
            resultBuilder.sponsoredRecords(
                productCatalogFilterService.filterInvalidSponsoredRecords(sponsoredRecords)
            );
        }

        var rewrites = searchResults.getRewrites();
        rewrites.add(Rewrites.PRODUCT_CATALOG.name());

        return resultBuilder.rewrites(rewrites).build();
    }

    @VisibleForTesting
    @SuppressWarnings("all")
    @ApiLatencyMetricsCollector
    Map<String, Map<String, Object>> fetchProducts(SearchParameters searchParams,
                                                   SearchResults searchResults,
                                                   boolean includeVariants,
                                                   String type) {
        var productRequests = getProductRequests(searchResults);

        List<VariantRollupKey> variantRollupKeys;

        // GoogleSearchEngine gets 'variantRollupValues' populated from Google S4R
        if (searchResults.getSearchEngine() != GOOGLE) {
            variantRollupKeys = variantRollupKeysParser.parseVariantRollupKeys(
                searchParams.getVariantRollupKeys(),
                searchParams.getMerchandisingConfiguration().attributeConfigurations()
            );
        } else {
            variantRollupKeys = List.of();
        }

        var request = getProductCatalogRequest(searchParams,includeVariants, variantRollupKeys, productRequests, null);

        if (request.products().isEmpty()) {
            return new HashMap<>();
        }

        saveDataCatalogRequestToDebug(request, type, log);

        return dataCatalogClient.fetchProductsMetadata(request, getRequestContext().getRequestOptions().skipCache())
            .single()
            .map(resp -> {
                saveDataCatalogResponseToDebug(resp, type, log);

                if (isNotEmpty(resp.queryDetails().warnings())) {
                    log.warn(SAMPLE_MARKER, "Product Catalog: {}", resp.queryDetails().warnings());
                    getRequestContext()
                        .getWarnings()
                        .addAll(resp.queryDetails().warnings().stream().sorted().toList());
                }

                Map<String, Map<String, Object>> productsById = resp.records().stream().collect(toMap(
                    this::getProductId,
                    recordMap -> recordMap,
                    (oldVal, newVal) -> newVal,
                    HashMap::new
                ));

                variantRollupKeysProcessor.setVariantRollupValues(productsById, variantRollupKeys);

                // limit variants to 5 when includeVariants is true
                if (includeVariants) {
                    productsById.values().forEach(recordMap -> {
                        var variants = (List<?>) recordMap.get(PRODUCT_FIELD_VARIANTS);
                        if (variants != null && variants.size() > ProductCatalogFilterService.VARIANTS_LIMIT) {
                            recordMap.put(
                                PRODUCT_FIELD_VARIANTS,
                                variants.subList(0, ProductCatalogFilterService.VARIANTS_LIMIT)
                            );
                        }
                    });
                }

                variantRollupKeysProcessor.removeAddedNonRetrievableFields(
                    productsById,
                    variantRollupKeys,
                    searchParams
                );

                return productsById;
            })
            .onErrorMap(HttpClientResponseException.class, e -> {
                var errResp = e.getResponse().getBody(Map.class).orElse(null);
                if (errResp != null) {
                    saveDataCatalogResponseToDebug(errResp, type, log);
                }
                var errorMsg = "Product Catalog Fetch Metadata HTTP API error: %s".formatted(e.getMessage());
                return new SiteSearchProductCatalogException(errorMsg, e);
            })
            .onErrorMap(t -> {
                var errorMsg = "Product Catalog Fetch Metadata error: %s".formatted(t.getMessage());
                return new SiteSearchProductCatalogException(errorMsg, t);
            })
            .block();
    }

    private boolean isProductCatalogUsed(SearchParameters searchParameters) {
        if (searchParameters.getSkipProductCatalog()) {
            return false;
        }

        return searchParameters.getSearchMode() == PRODUCT_SEARCH && featuresManager.getBooleanFlagConfiguration(
            getRequestContext().getLdContext(),
            ENABLE_DATA_CATALOG_FETCH
        );
    }

    /**
     * Merges the original search results with metadata from Data Catalog. Records
     * from the original search results are removed when missingProductsRemovalEnabled
     * is true, and the original record if not found in the list of Data Catalog records.
     *
     * @param searchParameters search parameters
     * @param originalRecords list of records from the base search call
     * @param catalogRecords available data catalog records by product id
     * @param missingProductsRemovalEnabled products not found in data catalog are removed when true
     *
     * @return Original records merged with data catalog records
     */
    @ApiLatencyMetricsCollector
    List<Record> mergeRecords(SearchParameters searchParameters,
                              List<Record> originalRecords,
                              Map<String, Map<String, Object>> catalogRecords,
                              boolean missingProductsRemovalEnabled) {

        var excludingMissingProductCatalogFilter = getProductFilter(catalogRecords, missingProductsRemovalEnabled);

        try {
            return originalRecords
                .stream()
                .filter(excludingMissingProductCatalogFilter)
                .map(originalRecord -> {
                    var product = catalogRecords.get(originalRecord.getProductId());
                    //if no product is found in the catalog, return the original record
                    if (product == null) {
                        return originalRecord;
                    }

                    //merging metadata, using original record as a base
                    var metadata = merge(originalRecord.getMetadata(), extractMetadata(product));

                    var variants = getVariants(originalRecord).stream()
                        .map(originalVariant -> {
                            var variantId = (String) originalVariant.get(Product.PRODUCT_FIELD_ID);
                            var catalogVariant = catalogRecords.get(variantId);
                            if (catalogVariant == null) {
                                return originalVariant;
                            }
                            return merge(
                                originalVariant,
                                catalogVariant
                            );
                        }).toList();

                    if (!variants.isEmpty() || searchParameters.getResponseMask().contains(PRODUCT_FIELD_VARIANTS)) {
                        metadata.put(PRODUCT_FIELD_VARIANTS, variants);
                    }

                    var context = getRequestContext();
                    return recordFromMap(
                        context.getMerchandiser(),
                        context.getCollection(),
                        product,
                        metadata,
                        originalRecord.getLabel(),
                        originalRecord.getLabels(),
                        originalRecord.getSponsored(),
                        originalRecord.getSponsoredInfo()
                    );
                }).toList();
        } catch (Exception e) {
            getRequestContext().addWarning("Product Catalog record merging error: %s".formatted(e.getMessage()));
            return originalRecords;
        }
    }

    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> getVariants(Record record) {
        if (CollectionUtils.isEmpty(record.getMetadata())) {
            return List.of();
        }

        if (record.getMetadata().get(PRODUCT_FIELD_VARIANTS) instanceof List<?> variants) {
            return variants.stream()
                .filter(o -> o instanceof Map<?, ?>)
                .map(variantObj -> (Map<String, Object>) variantObj)
                .toList();
        }

        return List.of();
    }

    private List<String> getVariantIds(Record record) {
        return getVariants(record).stream()
            .map(variantMap -> variantMap.get(Product.PRODUCT_FIELD_ID))
            .map(idObj -> idObj instanceof String str ? str : null)
            .filter(StringUtils::isNotEmpty)
            .toList();
    }

    private List<ProductRequest> getProductRequests(SearchResults searchResults) {
        return Stream.concat(
                searchResults.getRecords().stream()
                    .map(this::getPrimaryAndVariantRequests)
                    .flatMap(Collection::stream),
                searchResults.getSponsoredRecords().stream()
                    .map(this::getPrimaryAndVariantRequests)
                    .flatMap(Collection::stream)
            )
            .distinct()
            .toList();
    }

    private ProductCatalogRequest getProductCatalogRequest(SearchParameters searchParameters,
                                                           boolean includeVariants,
                                                           @NonNull List<VariantRollupKey> variantRollupKeys,
                                                           @NonNull List<ProductRequest> productRequests,
                                                           @Nullable List<String> additionalFields) {
        var context = getRequestContext();

        if (CollectionUtils.isEmpty(variantRollupKeys)) {
            return ProductCatalogRequest.withNoInventories(
                context.getMerchandiser().merchandiserId(),
                context.getCollection(),
                productRequests,
                includeVariants,
                searchParameters.getResponseMask(),
                additionalFields
            );
        }

        var additionalFieldsToInclude = new ArrayList<>(notNullOrDefaultList(additionalFields));
        additionalFieldsToInclude.addAll(getVariantRollupFields(variantRollupKeys));

        var includeLocalInventories = variantRollupKeys.stream()
            .anyMatch(variantRollupKey -> variantRollupKey.type() == RollupKeyType.INVENTORY_ATTRIBUTE);

        var localInventoryFilter = getLocalInventoryFilter(variantRollupKeys);

        return new ProductCatalogRequest(
            context.getMerchandiser().merchandiserId(),
            context.getCollection(),
            productRequests,
            includeVariants,
            includeLocalInventories,
            searchParameters.getResponseMask(),
            additionalFieldsToInclude,
            localInventoryFilter,
            null
        );
    }

    private List<ProductRequest> getPrimaryAndVariantRequests(Record r) {
        var variantIds = getVariantIds(r);
        var primaryId = r.getProductId();

        var productRequests = new ArrayList<ProductRequest>(variantIds.size() + 1);
        productRequests.add(new ProductRequest(primaryId, primaryId));
        productRequests.addAll(
            variantIds.stream().map(varId -> new ProductRequest(varId, primaryId)).toList()
        );

        return productRequests;
    }

    private static List<ProductRequest> idsToProductRequests(Set<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return ids.stream()
            .map(id -> new ProductRequest(id, id))
            .toList();
    }

    private Record recordFromMap(Merchandiser merchandiser, String collection, Map<String, Object> recordMap) {
        return recordFromMap(merchandiser, collection, recordMap, Map.of(), null, null, null, null);
    }

    @VisibleForTesting
    Record recordFromMap(Merchandiser merchandiser,
                         String collection,
                         Map<String, Object> recordMap,
                         Map<String, Object> metadataOverride,
                         RecordLabel label,
                         Set<RecordLabel> labels,
                         Boolean sponsored,
                         RecordSponsoredInfo sponsoredInfo) {
        return Record.of(
            merchandiser,
            collection,
            castToStringSafely(recordMap.get(Product.PRODUCT_FIELD_ID)),
            castToStringSafely(recordMap.get(Product.PRODUCT_FIELD_PRIMARY_ID)),
            castToStringSafely(recordMap.get(Product.PRODUCT_FIELD_TITLE)),
            isNotEmpty(metadataOverride) ? metadataOverride : extractMetadata(recordMap),
            label,
            labels,
            sponsored,
            sponsoredInfo
        );
    }

    @VisibleForTesting
    String getProductId(Map<String, Object> recordMap) {
        return castToStringSafely(recordMap.get(Product.PRODUCT_FIELD_ID));
    }

    @VisibleForTesting
    Map<String, Object> extractMetadata(Map<String, Object> recordMap) {
        if (CollectionUtils.isEmpty(recordMap)) {
            return Map.of();
        }
        return recordMap.entrySet().stream()
            .filter(e -> !METADATA_IGNORE_KEYS.contains(e.getKey()))
            .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    @VisibleForTesting
    Map<String, Object> merge(Map<String, Object> base, Map<String, Object> other) {
        if (CollectionUtils.isEmpty(base) && CollectionUtils.isEmpty(other)) {
            return Map.of();
        }
        if (CollectionUtils.isEmpty(base)) {
            return other;
        } else if (CollectionUtils.isEmpty(other)) {
            return base;
        }

        base.putAll(other);
        return base;
    }

    private List<Record> getRelatedPinnedProducts(SearchParameters searchParameters, List<Record> pinnedProducts) {
        var selectedRefinements = getSelectedRefinements(searchParameters);
        if (selectedRefinements.isEmpty()) {
            return pinnedProducts;
        }

        var userSelectedRefinements = selectedRefinements
            .keySet()
            .stream()
            .collect(toMap(
                key -> key,
                key -> excludeMerchantRefinements(searchParameters, selectedRefinements.get(key))
            ));

        var refinements = userSelectedRefinements
            .entrySet()
            .stream()
            .filter(entry -> isNotEmpty(entry.getValue()))
            .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

        return getFilteredRecords(refinements, pinnedProducts);
    }

    private Map<String, List<SelectedRefinement>> getSelectedRefinements(SearchParameters searchParameters) {
        return searchParameters.getRefinements()
            .stream()
            .filter(NOT_PRODUCT_ID_REFINEMENT)
            .collect(Collectors.groupingBy(
                SelectedRefinement::getField,  // Key: field name from refinement
                Collectors.toList()            // Value: list of refinements for each field
            ));
    }

    private List<SelectedRefinement> excludeMerchantRefinements(SearchParameters searchParameters,
                                                                List<SelectedRefinement> selectedRefinements) {
        var triggers = searchParameters.getSelectedRefinementTriggers();
        return selectedRefinements
            .stream()
            .filter(refinement -> triggers.stream().noneMatch(t -> t.matches(refinement)))
            .toList();
    }

    private boolean missingProductsRemovalEnabled() {
        return featuresManager.getBooleanFlagConfiguration(
            getRequestContext().getLdContext(),
            ENABLE_MISSING_PRODUCTS_REMOVAL
        );
    }

    private Predicate<Record> getProductFilter(Map<String, Map<String, Object>> catalogRecords,
                                               boolean missingProductsRemovalEnabled) {
        if (missingProductsRemovalEnabled) {
            // skip products not found in data catalog
            return (originalRecord) -> {
                if(catalogRecords.containsKey(originalRecord.getProductId())){
                    // found
                    return true;
                }

                getRequestContext().addWarning(
                    "Data catalog product not found for id %s. Record removed from results".formatted(
                        originalRecord.getProductId()
                    )
                );

                log.warn("Data catalog product not found [{}]. Removed from results", originalRecord.getProductId());

                // not found
                return false;
            };
        }
        // noop
        return (originalRecord) -> true;
    }

}
