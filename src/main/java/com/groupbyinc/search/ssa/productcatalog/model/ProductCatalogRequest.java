package com.groupbyinc.search.ssa.productcatalog.model;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;

import java.util.List;

public record ProductCatalogRequest(@NonNull String tenant,
                                    @NonNull String collection,
                                    @NonNull List<ProductRequest> products,
                                    @Nullable Boolean includeVariants,
                                    @Nullable Boolean includeLocalInventory,
                                    @Nullable List<String> includedFields,
                                    @Nullable List<String> additionalFields,
                                    @Nullable LocalInventoryFilter localInventoryFilter,
                                    @Nullable Integer multiThreadBatchSize) {

    public static ProductCatalogRequest withNoInventories(String tenant,
                                                          String collection,
                                                          List<ProductRequest> products,
                                                          Boolean includeVariants,
                                                          List<String> includedFields,
                                                          List<String> additionalFields) {
        return new ProductCatalogRequest(
            tenant,
            collection,
            products,
            includeVariants,
            false,
            includedFields,
            additionalFields,
            null,
            null
        );
    }

}
