package com.groupbyinc.search.ssa.productcatalog.client;

import com.groupbyinc.search.ssa.productcatalog.model.ProductCatalogPdpRequest;
import com.groupbyinc.search.ssa.productcatalog.model.ProductCatalogRequest;
import com.groupbyinc.search.ssa.productcatalog.model.ProductCatalogResponse;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.core.async.annotation.SingleResult;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Consumes;
import io.micronaut.http.annotation.Header;
import io.micronaut.http.annotation.Post;
import io.micronaut.http.annotation.Produces;
import io.micronaut.http.client.annotation.Client;
import reactor.core.publisher.Flux;

import static com.groupbyinc.search.ssa.util.Constants.GROUPBY_SKIP_CACHE_HEADER;

import static io.micronaut.http.HttpHeaders.ACCEPT_ENCODING;

/**
 * HTTP client for Data Catalog Fetch service.
 */
@Client(id = "productcatalog")
public interface ProductCatalogClient {

    @Post("/api/query")
    @Header(name = ACCEPT_ENCODING, value = "gzip")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @SingleResult
    Flux<ProductCatalogResponse> fetchProductsMetadata(@NonNull @Body ProductCatalogRequest request,
                                                       @Nullable @Header(GROUPBY_SKIP_CACHE_HEADER) Boolean skipCache);

    @Post("/api/product")
    @Header(name = ACCEPT_ENCODING, value = "gzip")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @SingleResult
    Flux<ProductCatalogResponse> fetchPdpProduct(@NonNull @Body ProductCatalogPdpRequest request,
                                                 @Nullable @Header(GROUPBY_SKIP_CACHE_HEADER) Boolean skipCache);
}

