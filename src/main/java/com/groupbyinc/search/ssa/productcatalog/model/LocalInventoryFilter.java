package com.groupbyinc.search.ssa.productcatalog.model;


import java.util.List;

/**
 * It is used to limit local inventory data in response to the placeIds and attributes specified.
 * <p>
 * Should be set only if {@code includeLocalInventory} is  {@code true}.
 *
 * @param placeIds placeIds to include in the response from the {@code localInventories} list.
 * @param fields attributes to include in the response for every placeId specified.
 */
public record LocalInventoryFilter(List<String> placeIds, List<String> fields) {
}
