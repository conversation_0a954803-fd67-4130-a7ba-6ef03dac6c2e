package com.groupbyinc.search.ssa;

import io.micronaut.runtime.Micronaut;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.security.SecuritySchemes;

@OpenAPIDefinition(
    info = @Info(
        title = "GroupBy Retail Search",
        version = "0.0",
        description = "GroupBy Retail Search API"
    )
)
@SecuritySchemes({
    @SecurityScheme(
        name = "ClientKey",
        type = SecuritySchemeType.APIKEY,
        in = SecuritySchemeIn.HEADER,
        paramName = "Authorization",
        description = "The primary or secondary client key as obtained from Command Center."
            + " The client key `xxx-yyy-zzz` should be sent in the Authorization header as `client-key xxx-yyy-zzz`."
    ),
    @SecurityScheme(
        name = "GroupByIncEmployee",
        type = SecuritySchemeType.HTTP,
        scheme = "basic",
        description = "HTTP basic authentication for GroupBy Inc Employees."
    )
})
public class Application {

    public static void main(String[] args) {
        Micronaut.build(args)
            .banner(false)
            .mainClass(Application.class)
            .start();
    }
}
