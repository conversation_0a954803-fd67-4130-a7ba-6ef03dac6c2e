package com.groupbyinc.search.ssa.topsort;

import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.RecordSponsoredInfo;
import com.groupbyinc.search.ssa.core.Rewrites;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.SponsoredRecordsRequest;
import com.groupbyinc.search.ssa.topsort.client.TopsortClient;
import com.groupbyinc.search.ssa.topsort.model.AuctionRequest;
import com.groupbyinc.search.ssa.topsort.model.AuctionResponse;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.util.CollectionUtils;
import io.micronaut.http.client.exceptions.HttpClientResponseException;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.util.debug.DebugHelpers.saveTopSortRequestToDebug;
import static com.groupbyinc.search.ssa.util.debug.DebugHelpers.saveTopSortResponseToDebug;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * TopsortService encapsulates any logic related to using 3-rd party service for product promotion.
 *
 * @see <a href="https://app.topsort.com/developers">Topsort dashboard</a>
 */
@Slf4j
@Context
@RequiredArgsConstructor
public class TopSortService {
    private static final String AUCTION_TYPE_LISTINGS = "listings";
    static final String AUTH_BEARER_FORMAT = "Bearer %s";

    private static final Comparator<Record> SPONSORED_COMPARATOR = Comparator.comparing(record -> {
        if (record.getSponsoredInfo() == null ||
            record.getSponsoredInfo().topsort() == null) {
            return null;
        }
        return record.getSponsoredInfo().topsort().rank();
    }, Comparator.nullsLast(Comparator.naturalOrder()));

    private final TopsortClient topsortClient;
    private final ConfigurationManager configurationManager;

    @Getter
    private int minRequestPageSize;

    public TopSortService(@Value("${topsort.min-request-page-size}") int minRequestPageSize,
                          @Value("${micronaut.http.services.topsort.url}") String url,
                          TopsortClient topsortClient,
                          ConfigurationManager configurationManager) {
        this.minRequestPageSize = minRequestPageSize;
        this.topsortClient = topsortClient;
        this.configurationManager = configurationManager;

        log.info("TopsortService initialized with url: [{}], page size: [{}]", url, minRequestPageSize);
    }

    /**
     * Calls Topsort service to get a ranked list of sponsored products.
     *
     * @param searchParameters     Search request parameters.
     * @param searchResults        Search engine results, used to get a list of records for auction.
     *
     * @return mutated search response builder
     */
    public SearchResults fillWithSponsoredProducts(
        SearchParameters searchParameters,
        SearchResults searchResults,
        List<String> auctionIds,
        boolean useStrategyV2
    ) {
        if (searchResults.hasNoRecords()) {
            return searchResults;
        }
        var searchResultsBuilder = searchResults.toBuilder();

        var originalPageSize = searchParameters.getOriginalPagination().getSize();

        //eager assignments to avoid repeats on multiple method exits
        searchResultsBuilder
            .records(searchResults.getRecords().stream().limit(originalPageSize).toList());

        var apiKey = getApiKeyForEnabledConfiguration();
        if (apiKey == null) {
            getRequestContext().addWarning("Topsort. Missing valid configuration.");
            return searchResultsBuilder.build();
        }

        var sponsoredReq = searchParameters.getSponsoredRecordsRequest();
        if (sponsoredReq == null) {
            getRequestContext().addWarning("Topsort. Empty $.sponsoredRecords field.");
            return searchResultsBuilder.build();
        }

        var slots = slots(sponsoredReq, originalPageSize);
        if (slots <= 0) {
            getRequestContext()
                .addWarning("Topsort. Invalid slots number. Either count or positions[] should be specified.");
            return searchResultsBuilder.build();
        }

        var winners = createAuction(
            searchParameters,
            auctionIds,
            slots,
            apiKey
        );

        if(useStrategyV2) {
            applyWinnersV2(searchResultsBuilder, winners);
        } else {
            applyWinnersV1(searchResultsBuilder, searchResults.getRecords(), winners);
        }

        var rewrites = searchResults.getRewrites();
        rewrites.add(Rewrites.TOPSORT.name());

        return searchResultsBuilder.rewrites(rewrites).build();
    }

    public String getApiKeyForEnabledConfiguration() {
        var context = getRequestContext();
        var config = configurationManager.getTopSortConfig(
            context.getMerchandiser(),
            context.getCollection(),
            context.getArea()
        );
        if (config.isPresent() && config.get().enabled() && isNotBlank(config.get().apiKey())) {
            return AUTH_BEARER_FORMAT.formatted(config.get().apiKey());
        }
        return null;
    }

    public static int slots(SponsoredRecordsRequest sponsoredReq, int originalPageSize) {
        var count = sponsoredReq.count() != null ? sponsoredReq.count() : 0;
        var positionsCount = sponsoredReq.positionsNormalized(originalPageSize).size();

        return Math.min(count, positionsCount);
    }

    /**
     * Calls Topsort service to get ranked list of sponsored products.
     *
     * @param searchResultsBuilder Search response builder, updated with failed topsort API warnings.
     * @param primaryProductIds    Primary records ids returned by Search engine.
     * @param auctionSlots         Max number of sponsored slots to be requested.
     * @param debugEnabled         Boolean flag for debug mode which will add Topsort exchange info to the search
     *                             response.
     *
     * @return List of ranked products.
     */
    @SuppressWarnings("all")
    private List<AuctionResponse.Winner> createAuction(SearchParameters searchParameters,
                                                       List<String> primaryProductIds,
                                                       int auctionSlots,
                                                       String apiKey) {
        try {
            var request = auctionRequestFor(auctionSlots, primaryProductIds);
            saveTopSortRequestToDebug(request, log);
            var resp = topsortClient.createAuction(request, apiKey);
            saveTopSortResponseToDebug(resp, log);

            if (CollectionUtils.isEmpty(resp.results())) {
                return List.of();
            }
            var result = resp.results().getFirst();
            if (result.error()) {
                getRequestContext().addWarning("Topsort API error: auction was resolved unsuccessfully");
                return List.of();
            }

            return result.winners();
        } catch (HttpClientResponseException e) {
            var errResp = e.getResponse().getBody(AuctionResponse.class).orElse(null);
            if (errResp != null) {
                saveTopSortResponseToDebug(errResp, log);
            }
            var cause = errResp != null ? "%s: %s".formatted(errResp.errCode(), errResp.message()) : e.getMessage();
            log.error("Error calling Topsort service: {}", cause, e);
            getRequestContext().addWarning("Topsort API error: %s".formatted(cause));
            return List.of();
        } catch (Exception e) {
            log.error("Error calling Topsort service: {}", e.getMessage(), e);
            getRequestContext().addWarning("Topsort API error: %s".formatted(e.getMessage()));
            return List.of();
        }
    }

    private void applyWinnersV2(SearchResults.SearchResultsBuilder searchResultsBuilder,
                                List<AuctionResponse.Winner> winners) {
        var winnersMap = winners.stream().collect(Collectors.toMap(AuctionResponse.Winner::id, Function.identity()));
        var sponsoredProducts = new ArrayList<Record>();

        var context = getRequestContext();
        winnersMap.forEach((id, winner) -> {
            var info = new RecordSponsoredInfo(winner);
            sponsoredProducts.add(Record.forSponsored(context.getMerchandiser(), context.getCollection(), id, info));
        });
        sponsoredProducts.sort(SPONSORED_COMPARATOR);
        searchResultsBuilder.sponsoredRecords(sponsoredProducts);
    }

    /**
     * Adds sponsored products to search response. Trims a record collection back to its originally requested size.
     *
     * @param searchResultsBuilder Search response builder.
     * @param records              Records returned by Search engine.
     * @param winners              Auction winners.
     */
    private void applyWinnersV1(SearchResults.SearchResultsBuilder searchResultsBuilder,
                              List<Record> records,
                              List<AuctionResponse.Winner> winners) {
        var winnersMap = winners.stream().collect(Collectors.toMap(AuctionResponse.Winner::id, Function.identity()));
        var sponsoredProducts = new ArrayList<Record>();
        records.forEach(record -> {
            var matchedWinner = winnersMap.get(record.getPrimaryProductId());
            if (matchedWinner == null) return;
            sponsoredProducts.add(Record.copyWithSponsoredInfo(
                    record,
                    new RecordSponsoredInfo(matchedWinner)
                )
            );
        });
        sponsoredProducts.sort(SPONSORED_COMPARATOR);
        searchResultsBuilder.sponsoredRecords(sponsoredProducts);
    }

    private AuctionRequest auctionRequestFor(Integer slots, List<String> productIds) {
        return new AuctionRequest(
            List.of(
                new AuctionRequest.Auctions(
                    AUCTION_TYPE_LISTINGS,
                    slots,
                    new AuctionRequest.Products(productIds)
                ))
        );
    }

}
