package com.groupbyinc.search.ssa.topsort.model;

import com.groupbyinc.proto.commandcenter.config.MessageType;

import lombok.Builder;
import lombok.With;

@Builder
public record TopsortConfiguration(MessageType messageType,
                                   Integer id,
                                   Integer areaId,
                                   boolean enabled,
                                   @With
                                   String apiKey) {

    public static final TopsortConfiguration DISABLED = new TopsortConfiguration(null, null, null, false, null);
}
