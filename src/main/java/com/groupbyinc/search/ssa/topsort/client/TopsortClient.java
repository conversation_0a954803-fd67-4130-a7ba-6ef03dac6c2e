package com.groupbyinc.search.ssa.topsort.client;

import com.groupbyinc.search.ssa.metrics.opentelemetry.interceptors.ApiLatencyMetricsCollector;
import com.groupbyinc.search.ssa.topsort.model.AuctionRequest;
import com.groupbyinc.search.ssa.topsort.model.AuctionResponse;

import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Header;
import io.micronaut.http.annotation.Post;
import io.micronaut.http.client.annotation.Client;

import static io.micronaut.http.HttpHeaders.ACCEPT_ENCODING;
import static io.micronaut.http.HttpHeaders.AUTHORIZATION;
import static io.micronaut.http.HttpHeaders.CONTENT_TYPE;

/**
 * HTTP client for Topsort integration.
 * {@see <a href="https://docs.topsort.com/reference/integration-overview">Topsort integration guide</a>}
 */
@Client(id = "topsort")
public interface TopsortClient {

    @Post("/v2/auctions")
    @Header(name = ACCEPT_ENCODING, value = "application/json")
    @Header(name = CONTENT_TYPE, value = "application/json")
    @ApiLatencyMetricsCollector
    AuctionResponse createAuction(@Body AuctionRequest request, @Header(name = AUTHORIZATION) String authorization);
}

