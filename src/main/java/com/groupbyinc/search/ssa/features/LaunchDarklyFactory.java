package com.groupbyinc.search.ssa.features;

import com.launchdarkly.sdk.server.Components;
import com.launchdarkly.sdk.server.LDClient;
import com.launchdarkly.sdk.server.LDConfig;
import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Value;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;

@Slf4j
@Factory
public class LaunchDarklyFactory {

    @Value("${features.launch-darkly.sdk-key}")
    private String sdkKey;

    @Value("${features.launch-darkly.events-flush-minutes}")
    private long eventsFlushMinutes;

    @Value("${features.launch-darkly.pooling-interval-minutes}")
    private long poolingIntervalMinutes;

    @Context
    public LDClient createLDClient() {
        LDConfig config = new LDConfig
            .Builder()
            .dataSource(
                Components
                    .pollingDataSource()
                    .pollInterval(Duration.ofMinutes(poolingIntervalMinutes))
            )
            .events(
                Components
                    .sendEvents()
                    .flushInterval(Duration.ofMinutes(eventsFlushMinutes))
            )
            .build();

        LDClient client = new LDClient(sdkKey, config);

        if (client.isInitialized()) {
            log.info("LaunchDarkly client initialized! Feature flags are available.");
        } else {
            log.error("LaunchDarkly client NOT initialized!!! Feature flags are not available.");
        }

        return client;
    }

}
