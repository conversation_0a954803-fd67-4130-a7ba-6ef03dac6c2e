package com.groupbyinc.search.ssa.features;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.launchdarkly.sdk.LDContext;
import com.launchdarkly.sdk.LDValue;
import com.launchdarkly.sdk.server.LDClient;
import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Value;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.Map;

/**
 * Service class to manage available feature flags. Technically, it reads values
 * from application properties and adds them into response when needed. All feature
 * flags are global and apply to all customers.
 */
@Slf4j
@Context
public class FeaturesManager {

    private static final String EMPTY_JSON = "{}";
    private static final String LOCAL_ENV = "local";

    private final ObjectMapper mapper;
    private final LDClient launchDarklyClient;
    private final Map<String, Object> defaultFeatureFlags;

    @Getter
    @Value("${features.launch-darkly.environment}")
    private String ldContextEnvironment;

    public FeaturesManager(ObjectMapper mapper,
                           LDClient launchDarklyClient,
                           @Value("${features.launch-darkly.flags}") String defaultFeatureFlagsJson) {
        this.mapper = mapper;
        this.launchDarklyClient = launchDarklyClient;
        defaultFeatureFlags = getDefaultFeatureFlags(mapper, defaultFeatureFlagsJson);
    }

    /**
     * Requests boolean feature flag value from LaunchDarkly.
     *
     * @param context     context used to evaluate feature flag value.
     * @param featureName name of the feature flag.
     *
     * @return value evaluated for passed feature flag and context, default is false.
     */
    public boolean getBooleanFlagConfiguration(LDContext context, String featureName) {
        try {
            // Get the feature flag value from the application configuration .yml file for local dev.
            if (localDevelopmentConfigurationProvided(featureName)) {
                return (boolean) defaultFeatureFlags.getOrDefault(featureName, Boolean.FALSE);
            }

            return launchDarklyClient.boolVariation(
                featureName,
                context,
                (boolean) defaultFeatureFlags.getOrDefault(featureName, Boolean.FALSE)
            );
        } catch (Exception e) {
            log.warn("Fail to get feature flag value for feature {}", featureName, e);
            return false;
        }
    }

    /**
     * Requests integer feature flag value from the LaunchDarkly.
     *
     * @param ldContext    context used to evaluate feature flag value.
     * @param featureName  name of the feature flag.
     * @param defaultValue default value to be returned if failed to get the feature flag value.
     * @return value evaluated for the passed feature flag and context or default value.
     */
    public int getNumberFeatureFlagConfiguration(LDContext ldContext, String featureName, int defaultValue) {
        try {
            // Get the feature flag value from the application configuration .yml file for local dev.
            if (localDevelopmentConfigurationProvided(featureName)) {
                return (int) defaultFeatureFlags.get(featureName);
            }

            return launchDarklyClient.intVariation(
                featureName,
                ldContext,
                (int) defaultFeatureFlags.getOrDefault(featureName, defaultValue)
            );
        } catch (Exception e) {
            log.warn("Fail to get feature flag value for feature {}", featureName, e);
            return defaultValue;
        }
    }

    /**
     * Requests object feature flag value from LaunchDarkly.
     *
     * @param context     context used to evaluate feature flag value.
     * @param featureName name of the feature flag.
     * @param type        type of ogbject representing a feature flag.
     *                    Object of this type must contain a no-argument constructor
     *                    to initialize a default instance.
     * @param featureName default object to be returned in case of any error.
     *
     * @return value evaluated for passed feature flag and context.
     *
     * @param <T> type of object which is represent an object based feature flag
     */
    @SuppressWarnings("all")
    public <T extends FeatureFlag> T getObjectFlagConfiguration(LDContext context,
                                                                String flagName,
                                                                Class<T> type,
                                                                T defaultObject) {
        try {
            // Get the feature flag value from the application configuration .yml file for local dev.
            if (localDevelopmentConfigurationProvided(flagName)) {
                return mapper.convertValue(defaultFeatureFlags.getOrDefault(flagName, EMPTY_JSON), type);
            }

            var value = launchDarklyClient.jsonValueVariation(flagName, context, LDValue.ofNull());

            if (value.isNull()) {
                var appConfigDefault = defaultFeatureFlags.get(flagName);
                return appConfigDefault == null ? defaultObject: mapper.convertValue(appConfigDefault, type);
            }

            return mapper.readValue(value.toJsonString(), type);
        } catch (Exception e) {
            log.warn("Can not parse feature flag for key: " + flagName, e);
            return defaultObject;
        }
    }

    @SuppressWarnings("all")
    private Map<String, Object> getDefaultFeatureFlags(ObjectMapper mapper, String defaultFeatureFlagsJson) {
        try {
            return mapper.readValue(defaultFeatureFlagsJson, new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            log.info("Fail to convert default feature flags into a map.", e);
        }
        return Collections.emptyMap();
    }

    private boolean localDevelopmentConfigurationProvided(String featureName) {
        return ldContextEnvironment.equals(LOCAL_ENV) && defaultFeatureFlags.containsKey(featureName);
    }

    public interface FeatureFlagNames {
        String ENABLE_PIN_TO_TOP = "enablePinToTop";
        String ENABLE_TOP_SORT = "enableTopsort";
        String ENABLE_TOP_SORT_V2 = "enableTopsortV2";
        String ENABLE_DATA_CATALOG_FETCH = "enableDataCatalogFetch";
        String ENABLE_DATA_CATALOG_PDP_FETCH = "enableDataCatalogPdpFetch";
        String ENABLE_NEW_BIASING = "enableNewBiasing";
        String DISABLE_QUERY_SPLIT = "disableQuerySplit";
        String ENABLE_PUBSUB_DIRECT_SEARCH = "enablePubSubDirectSearch";
        String ENABLE_BROWSE_CACHE = "enableBrowseCache";
        String ENABLE_SEARCH_CACHE = "enableSearchCache";
        String ENABLE_PIN_TO_TOP_CACHE = "enablePinToTopCache";
        String ENABLE_MULTI_PRODUCT_SEARCH = "enableMultiProductSearch";
        String ENABLE_RESPONSE_FIELDS_FROM_FETCH = "enableResponseFieldsFromFetch";
        String ENABLE_MONGO_BROWSE_ENGINE = "enableMongoBrowseEngine";
        String ENABLE_PINNED_PRODUCTS_FILTERING = "enablePinnedProductsFiltering";
        String ENABLE_DATA_CATALOG_PIN_TO_TOP = "enableDataCatalogPinToTop";
        String ENABLE_MISSING_PRODUCTS_REMOVAL = "enableMissingProductsRemoval";
        String USE_NEW_RETAIL_FILTERING = "useNewRetailFiltering";
        String ENABLE_PART_NUMBER_SEARCH = "enablePartNumberSearch";
        String LOG_SAMPLING_CONFIG = "logSamplingConfig";
        String ENABLE_NEW_BIAS_BOOST_VALUES = "enableBiasBoostOverrides";
        String PART_NUMBER_INDEX_DELAY_SECONDS = "partNumberIndexDelaySeconds";
        String ENABLE_FACET_SAMPLE_PERCENTAGE = "enableFacetSamplePercentage";
        String ENABLE_PRODUCT_VISIBILITY_BIAS = "enableRuleProductVisibilityBias";
        String ENABLE_CRM_PERSONALIZATION = "enableCRMPersonalization";
    }

}
