package com.groupbyinc.search.ssa.features;

import com.launchdarkly.sdk.ContextKind;
import com.launchdarkly.sdk.LDContext;
import io.micronaut.core.util.StringUtils;

import javax.annotation.Nullable;
import javax.annotation.ParametersAreNonnullByDefault;

@ParametersAreNonnullByDefault
public class LDContextBuilder {

    private static final String AREA_PARAM_NAME = "area";
    private static final String LOGIN_ID_CONTEXT_NAME = "loginId";
    private static final String APPLICATION_NAME = "site-search-api";
    private static final String COLLECTION_PARAM_NAME = "collection";
    private static final String ENVIRONMENT_PARAM_NAME = "environment";
    private static final String APPLICATION_CONTEXT_NAME = "application";
    private static final String CONFIGURATION_CONTEXT_NAME = "configuration";

    private String area;
    private String loginId;
    private String customerId;
    private String collection;
    private String environment;

    private LDContextBuilder(@Nullable String customerId, @Nullable String environment) {
        this.customerId = customerId;
        this.environment = environment;
    }

    private LDContextBuilder() {
    }

    public static LDContextBuilder builder() {
        return new LDContextBuilder();
    }

    public static LDContextBuilder builder(@Nullable String customerId, @Nullable String environment) {
        return new LDContextBuilder(customerId, environment);
    }

    public LDContext build() {
        var multiContext = LDContext.multiBuilder();

        // add default context with application name
        multiContext.add(
            LDContext
                .builder(ContextKind.of(APPLICATION_CONTEXT_NAME), APPLICATION_NAME)
                .name(APPLICATION_NAME)
                .build()
        );

        if (customerId == null) {
            return multiContext.build();
        }

        var configurationContext = LDContext
            .builder(ContextKind.of(CONFIGURATION_CONTEXT_NAME), customerId)
            .name(customerId);

        if (StringUtils.isNotEmpty(collection)) {
            configurationContext
                .set(COLLECTION_PARAM_NAME, collection);
        }
        if (StringUtils.isNotEmpty(area)) {
            configurationContext
                .set(AREA_PARAM_NAME, area);
        }

        configurationContext.set(ENVIRONMENT_PARAM_NAME, environment);

        // add default context with area\collection configuration
        multiContext.add(configurationContext.build());

        // add default context loginId if available
        if (loginId != null) {
            multiContext.add(
                LDContext
                    .builder(ContextKind.of(LOGIN_ID_CONTEXT_NAME), loginId)
                    .name(loginId)
                    .build()
            );
        }

        return multiContext.build();
    }

    public LDContextBuilder setArea(@Nullable String area) {
        this.area = area;
        return this;
    }

    public LDContextBuilder setLoginId(@Nullable String loginId) {
        this.loginId = loginId;
        return this;
    }

    public LDContextBuilder setCustomerId(@Nullable String customerId) {
        this.customerId = customerId;
        return this;
    }

    public LDContextBuilder setCollection(@Nullable String collection) {
        this.collection = collection;
        return this;
    }
}
