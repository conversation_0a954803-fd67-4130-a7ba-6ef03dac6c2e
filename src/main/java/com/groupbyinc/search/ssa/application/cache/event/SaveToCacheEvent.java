package com.groupbyinc.search.ssa.application.cache.event;

import com.groupbyinc.search.ssa.application.cache.CacheConfig;
import com.groupbyinc.search.ssa.redis.CacheSource;

/**
 * Used to asynchronously store a specific key-value in a cache.
 *
 * @param key    key to store or replace in a cache.
 * @param value  value to store or replace in a cache.
 * @param config any cache related configurations.
 * @param source cache instance to save value.
 * @param <T>    type of cache configuration object.
 */
public record SaveToCacheEvent<T extends CacheConfig>(String key, String value, T config, CacheSource source) {

}
