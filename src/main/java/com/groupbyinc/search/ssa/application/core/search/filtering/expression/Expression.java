package com.groupbyinc.search.ssa.application.core.search.filtering.expression;

import com.groupbyinc.search.ssa.application.core.search.filtering.filter.ToFilter;
import io.micronaut.core.annotation.NonNull;
import lombok.Getter;

/**
 * A superclass for all expressions which can be converted into a filters.
 *
 * @param <T> type of filter object which will be created from this expression.
 */
@Getter
public abstract class Expression<T> implements ToFilter<T> {

    /**
     * Name of the facet(attribute) the expression is for.
     */
    @NonNull
    protected final String field;

    public Expression(@NonNull String field) {
        this.field = field;
    }

}
