package com.groupbyinc.search.ssa.application.core.search.variantrollupkeys;

import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.util.JSONUtils;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import io.micronaut.core.util.CollectionUtils;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKey.RollupKeyType;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_ID;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_PRIMARY_ID;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_TITLE;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_VARIANTS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.TYPE;
import static com.groupbyinc.search.ssa.util.Constants.FULFILLMENT_INFO;
import static com.groupbyinc.search.ssa.util.Constants.LOCAL_INVENTORIES;
import static com.groupbyinc.search.ssa.util.Constants.NUMBERS;
import static com.groupbyinc.search.ssa.util.Constants.PLACE_ID;
import static com.groupbyinc.search.ssa.util.Constants.PLACE_IDS;
import static com.groupbyinc.search.ssa.util.Constants.PRIMARY_TYPE;
import static com.groupbyinc.search.ssa.util.Constants.TEXT;
import static com.groupbyinc.search.ssa.util.Constants.VARIANT_ROLLUP_VALUES;
import static com.groupbyinc.search.ssa.util.Constants.VARIANT_TYPE;
import static com.groupbyinc.search.ssa.util.Constants.WILDCARD;
import static com.groupbyinc.search.ssa.util.JSONUtils.toElementsStream;
import static com.groupbyinc.search.ssa.util.JSONUtils.getNestedJsonElement;
import static com.groupbyinc.search.ssa.util.JSONUtils.toJsonObject;

import static java.util.stream.Collectors.toMap;

@Singleton
@RequiredArgsConstructor
public class VariantRollupKeysProcessor {

    public static final Set<String> DEFAULT_FIELDS = Set.of(PRODUCT_FIELD_ID, PRODUCT_FIELD_TITLE);

    private final VariantRollupValuesConverter variantRollupValuesConverter;


    /**
     * Handles only fulfillment type of variant rollup keys type for the given records.
     *
     * @param records           the records to process
     * @param variantRollupKeys all variant rollup keys
     */
    public void setFulfillmentVariantRollupValues(List<Record> records,
                                                  List<VariantRollupKey> variantRollupKeys) {
        var fulfillmentRollupKeys = variantRollupKeys.stream()
            .filter(variantRollupKey -> variantRollupKey.type() == RollupKeyType.FULFILLMENT_INFO)
            .toList();

        if (CollectionUtils.isEmpty(fulfillmentRollupKeys)) {
            return;
        }

        records.forEach(record -> processFulfillmentRollupKeys(record.getMetadata(), fulfillmentRollupKeys));
    }

    @SuppressWarnings("unchecked")
    private void processFulfillmentRollupKeys(Map<String, Object> productMetadata,
                                              List<VariantRollupKey> fulfillmentRollupKeys) {
        if (fulfillmentRollupKeys.isEmpty()) {
            return;
        }

        var variantsArray = getVariantsArray(productMetadata);

        fulfillmentRollupKeys.stream()
            .filter(variantRollupKey -> variantRollupKey.type() == RollupKeyType.FULFILLMENT_INFO)
            .forEach(variantRollupKey -> {
                var variantsCount = new AtomicInteger(0);

                toElementsStream(variantsArray)
                    .map(JsonElement::getAsJsonObject)
                    .forEach(variant -> {
                        var fulfillmentInfos = variant.getAsJsonArray(FULFILLMENT_INFO);
                        if (fulfillmentInfos == null) {
                            return;
                        }

                        var matchFound = toElementsStream(fulfillmentInfos)
                            .map(JsonElement::getAsJsonObject)
                            .anyMatch(fulfillmentInfo -> {
                                String fulfillmentType = fulfillmentInfo.get(TYPE).getAsString();
                                if (!fulfillmentType.equals(variantRollupKey.path())) {
                                    return false;
                                }
                                var placeIds = fulfillmentInfo.getAsJsonArray(PLACE_IDS);
                                if (placeIds == null) {
                                    return false;
                                }
                                return toElementsStream(placeIds)
                                    .map(JsonElement::getAsString)
                                    .anyMatch(placeId ->
                                        Objects.requireNonNull(variantRollupKey.placeId()).equals(placeId)
                                    );
                            });

                        if (matchFound) {
                            variantsCount.incrementAndGet();
                        }
                    });

                if (variantsCount.get() > 0) {
                    var variantRollupValues = (Map<String, Object>) productMetadata
                        .computeIfAbsent(VARIANT_ROLLUP_VALUES, k -> new HashMap<String, Object>());
                    variantRollupValues.put(variantRollupKey.key(), (double) variantsCount.get());
                }
            });
    }

    /**
     * Handles other than fulfillment types of variant rollup keys for the given records.
     *
     * @param records           the records to process
     * @param variantRollupKeys variant rollup keys
     */
    public void setVariantRollupValues(List<Record> records,
                                       List<VariantRollupKey> variantRollupKeys) {

        if (CollectionUtils.isEmpty(variantRollupKeys)) {
            return;
        }

        records.forEach(record -> processVariantRollupKeys(record.getMetadata(), variantRollupKeys));
    }

    @SuppressWarnings("unchecked")
    private void processVariantRollupKeys(Map<String, Object> productMetadata,
                                          List<VariantRollupKey> variantRollupKeys) {
        if (variantRollupKeys.isEmpty()) {
            return;
        }

        var variantsArray = getVariantsArray(productMetadata);

        // Process variant attributes
        variantRollupKeys.stream()
            .filter(vrk -> vrk.type() == RollupKeyType.VARIANT_ATTRIBUTE)
            .forEach(variantRollupKey -> {
                Set<Object> values = toElementsStream(variantsArray)
                    .map(JsonElement::getAsJsonObject)
                    .map(variant -> getNestedJsonElement(variant, variantRollupKey.getPathAsList()))
                    .flatMap(attributeElement -> getAttributeValues(attributeElement).stream())
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

                setValuesToProductMetadata(productMetadata, variantRollupKey, values);
            });


        // Process inventory attributes
        variantRollupKeys.stream()
            .filter(vrk -> vrk.type() == RollupKeyType.INVENTORY_ATTRIBUTE)
            .forEach(variantRollupKey -> {
                Set<Object> values = toElementsStream(variantsArray)
                    .map(JsonElement::getAsJsonObject)
                    .flatMap(getInventoryStream(variantRollupKey))
                    .flatMap(attributeElement -> getAttributeValues(attributeElement).stream())
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

                setValuesToProductMetadata(productMetadata, variantRollupKey, values);
            });

        productMetadata.computeIfPresent(VARIANT_ROLLUP_VALUES, (k, variantRollupValues) ->
            variantRollupValuesConverter.convertVariantRollupObjects((Map<String, Object>) variantRollupValues));
    }

    /**
     * Handles other than fulfillment types of variant rollup keys for the given products Map.
     *
     * @param productsById      products by id
     * @param variantRollupKeys variant rollup keys
     */
    public void setVariantRollupValues(Map<String, Map<String, Object>> productsById,
                                       List<VariantRollupKey> variantRollupKeys) {

        if (CollectionUtils.isEmpty(variantRollupKeys)) {
            return;
        }

        Map<String, Map<String, Object>> primaryProducts = new HashMap<>();
        Map<String, Map<String, Object>> variants = new HashMap<>();

        productsById.forEach((key, productMetadata) -> {
            if (productMetadata.get(TYPE).equals(PRIMARY_TYPE)) {
                primaryProducts.put(key, productMetadata);
            } else if (productMetadata.get(TYPE).equals(VARIANT_TYPE)) {
                variants.put(key, productMetadata);
            }
        });

        // Associate variants with their primary products
        Map<String, List<Map<String, Object>>> variantsByPrimaryProductId = variants.values()
            .stream()
            .collect(Collectors.groupingBy(variant -> (String) variant.get(PRODUCT_FIELD_PRIMARY_ID)));

        primaryProducts.forEach((primaryProductId, primaryProductMetadata) -> {
            List<Map<String, Object>> associatedVariants =
                // If there are no variants for the primary product, use the primary product itself
                variantsByPrimaryProductId.getOrDefault(primaryProductId, List.of(primaryProductMetadata));
            processVariantRollupKeys(primaryProductMetadata, associatedVariants, variantRollupKeys);
        });
    }

    @SuppressWarnings("unchecked")
    private void processVariantRollupKeys(Map<String, Object> productMetadata,
                                          List<Map<String, Object>> associatedVariants,
                                          List<VariantRollupKey> variantRollupKeys) {
        if (variantRollupKeys.isEmpty()) {
            return;
        }

        // Process variant attributes
        variantRollupKeys.stream()
            .filter(vrk -> vrk.type() == RollupKeyType.VARIANT_ATTRIBUTE)
            .forEach(variantRollupKey -> {
                Set<Object> values = associatedVariants.stream()
                    .map(JSONUtils::toJsonObject)
                    .map(variantJson -> getNestedJsonElement(variantJson, variantRollupKey.getPathAsList()))
                    .flatMap(attributeElement -> getAttributeValues(attributeElement).stream())
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

                setValuesToProductMetadata(productMetadata, variantRollupKey, values);
            });

        // Process inventory attributes
        variantRollupKeys.stream()
            .filter(vrk -> vrk.type() == RollupKeyType.INVENTORY_ATTRIBUTE)
            .forEach(variantRollupKey -> {
                Set<Object> values = associatedVariants.stream()
                    .map(JSONUtils::toJsonObject)
                    .flatMap(getInventoryStream(variantRollupKey))
                    .flatMap(attributeElement -> getAttributeValues(attributeElement).stream())
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

                setValuesToProductMetadata(productMetadata, variantRollupKey, values);
            });

        productMetadata.computeIfPresent(VARIANT_ROLLUP_VALUES, (k, variantRollupValues) ->
            variantRollupValuesConverter.convertVariantRollupObjects((Map<String, Object>) variantRollupValues));
    }

    @SuppressWarnings("unchecked")
    private void setValuesToProductMetadata(Map<String, Object> productMetadata,
                                            VariantRollupKey variantRollupKey,
                                            Set<Object> values) {
        if (!values.isEmpty()) {
            var variantRollupValues = (Map<String, Object>) productMetadata
                .computeIfAbsent(VARIANT_ROLLUP_VALUES, k -> new HashMap<String, Object>());
            variantRollupValues.put(variantRollupKey.key(), values);
        }
    }

    private static Function<JsonObject, Stream<? extends JsonElement>> getInventoryStream(VariantRollupKey variantRollupKey) {
        return variant -> {
            var localInventories = variant.getAsJsonArray(LOCAL_INVENTORIES);
            if (localInventories == null) {
                return Stream.empty();
            }
            return toElementsStream(localInventories)
                .map(JsonElement::getAsJsonObject)
                .filter(inventory -> {
                    var placeId = inventory.get(PLACE_ID).getAsString();
                    return Objects.requireNonNull(variantRollupKey.placeId()).equals(placeId);
                })
                .map(inventory -> getNestedJsonElement(inventory, variantRollupKey.getPathAsList()));
        };
    }


    private static JsonArray getVariantsArray(Map<String, Object> productMetadata) {

        var metadataJson = toJsonObject(productMetadata);
        var variantsArray = metadataJson.getAsJsonArray(PRODUCT_FIELD_VARIANTS);

        if (variantsArray == null) {
            // Primary product metadata as a list of the single variant
            variantsArray = new JsonArray();
            variantsArray.add(metadataJson);
        }

        return variantsArray;
    }

    private List<Object> getAttributeValues(JsonElement attributeElement) {
        if (attributeElement == null || attributeElement.isJsonNull()) {
            return List.of();
        } else if (attributeElement.isJsonArray()) {
            return toElementsStream(attributeElement.getAsJsonArray())
                .map(this::getPrimitiveValue)
                .collect(Collectors.toList());
        } else if (attributeElement.isJsonObject()) {
            var jsonObject = attributeElement.getAsJsonObject();
            if (jsonObject.has(NUMBERS)) {
                return getAttributeValues(jsonObject.get(NUMBERS));
            } else if (jsonObject.has(TEXT)) {
                return getAttributeValues(jsonObject.get(TEXT));
            } else {
                return List.of(jsonObject);
            }
        } else {
            return List.of(getPrimitiveValue(attributeElement));
        }
    }

    private Object getPrimitiveValue(JsonElement element) {
        if (element.isJsonPrimitive()) {
            var primitive = element.getAsJsonPrimitive();
            if (primitive.isNumber()) {
                return primitive.getAsDouble();
            } else if (primitive.isBoolean()) {
                return primitive.getAsBoolean();
            } else if (primitive.isString()) {
                return primitive.getAsString();
            }
        }
        return element.toString();
    }

    /**
     * Removes non-retrievable fields that where additionally fetched and used for variant rollup keys'
     * processing from the products
     *
     * @param records           the records to process
     * @param variantRollupKeys the variant rollup keys
     * @param searchParameters  search parameters
     */
    public void removeAddedNonRetrievableFields(List<Record> records,
                                                List<VariantRollupKey> variantRollupKeys,
                                                SearchParameters searchParameters) {
        if (CollectionUtils.isEmpty(variantRollupKeys)) {
            return;
        }

        var fieldsToRemove = getFieldsToRemove(variantRollupKeys, searchParameters);

        for (Record record : records) {

            var metadata = toJsonObject(record.getMetadata());
            removeFields(metadata, fieldsToRemove);

            var variantsArray = metadata.getAsJsonArray(PRODUCT_FIELD_VARIANTS);
            if (variantsArray != null) {
                for (JsonElement variant : variantsArray) {
                    var variantObj = variant.getAsJsonObject();
                    removeFields(variantObj, fieldsToRemove);
                }
            }

            // Convert the modified JsonObject back to a Map<String, Object>
            record.setMetadata(JSONUtils.toMap(metadata));
        }
    }

    /**
     * Removes non-retrievable fields that where additionally fetched and used for variant rollup keys'
     * processing from the products
     *
     * @param productsById      products as Map by id
     * @param variantRollupKeys variant rollup keys
     * @param searchParameters  search parameters
     */
    public void removeAddedNonRetrievableFields(Map<String, Map<String, Object>> productsById,
                                                List<VariantRollupKey> variantRollupKeys,
                                                SearchParameters searchParameters) {

        if (CollectionUtils.isEmpty(variantRollupKeys)) {
            return;
        }

        var fieldsToRemove = getFieldsToRemove(variantRollupKeys, searchParameters);

        productsById.forEach((id, productMap) -> {
            var product = toJsonObject(productMap);
            removeFields(product, fieldsToRemove);
            productsById.put(id, JSONUtils.toMap(product));
        });
    }

    private List<String> getFieldsToRemove(List<VariantRollupKey> variantRollupKeys, SearchParameters searchParameters) {
        var fieldsToRemoveByAttribute = variantRollupKeys.stream()
            .filter(vrk -> vrk.type() == RollupKeyType.VARIANT_ATTRIBUTE)
            .collect(
                toMap(
                    VariantRollupKey::attribute,
                    VariantRollupKey::path,
                    (path1, path2) -> path2 // If there are duplicate keys, keep the last one
                )
            );

        List<String> fieldsToRemove = new ArrayList<>();
        fieldsToRemoveByAttribute.forEach((attribute, fieldPath) -> {
            if (checkIfFieldShouldBeRemoved(searchParameters, attribute, fieldPath)) {
                fieldsToRemove.add(fieldPath);
            }
        });
        fieldsToRemove.add(LOCAL_INVENTORIES);

        var fulfillmentInfoKeys = variantRollupKeys.stream()
            .filter(vrk -> vrk.type() == RollupKeyType.FULFILLMENT_INFO)
            .toList();

        if (checkIfFulfillmentInfoShouldBeRemoved(searchParameters, fulfillmentInfoKeys)) {
            fieldsToRemove.add(FULFILLMENT_INFO);
        }

        return fieldsToRemove;
    }

    private static boolean checkIfFieldShouldBeRemoved(SearchParameters searchParameters,
                                                       String attribute,
                                                       String fieldPath) {
        if (DEFAULT_FIELDS.contains(fieldPath)) {
            return false;
        }

        var responseMask = searchParameters.getResponseMask();
        var allFieldsAllowed = responseMask.isEmpty() || responseMask.contains(WILDCARD);

        var attributesConfiguration = searchParameters.getMerchandisingConfiguration().attributeConfigurations();
        var isRetrievableField = attributesConfiguration.containsKey(attribute)
            && attributesConfiguration.get(attribute).retrievable();

        if (isRetrievableField && allFieldsAllowed) {
            return false;
        } else if (isRetrievableField) {
            return !(responseMask.contains(fieldPath) || responseMask.contains(attribute));
        }

        return true;
    }

    private boolean checkIfFulfillmentInfoShouldBeRemoved(SearchParameters searchParameters,
                                                          List<VariantRollupKey> fulfillmentInfoKeys) {

        var responseMask = searchParameters.getResponseMask();
        var allFieldsAllowed = responseMask.isEmpty() || responseMask.contains(WILDCARD);

        var attributesConfiguration = searchParameters.getMerchandisingConfiguration().attributeConfigurations();

        var fulfilmentInfoRetrievable = fulfillmentInfoKeys.stream()
            .map(VariantRollupKey::attribute)
            .anyMatch(attribute ->
                attributesConfiguration.containsKey(attribute)
                    && attributesConfiguration.get(attribute).retrievable()
            );

        var fulfilmentInfoInResponseMask = fulfillmentInfoKeys.stream()
            .anyMatch(fulfillmentInfoKey ->
                responseMask.contains(fulfillmentInfoKey.attribute())
                    || responseMask.contains(fulfillmentInfoKey.path())
            );

        if (fulfilmentInfoRetrievable && allFieldsAllowed) {
            return false;
        } else if (fulfilmentInfoRetrievable) {
            return !fulfilmentInfoInResponseMask;
        }

        return true;
    }

    private void removeFields(JsonObject jsonObject,
                              List<String> fieldsToRemove) {
        fieldsToRemove.forEach((fieldPath) -> {
            JSONUtils.removeNestedObject(jsonObject, Arrays.asList(fieldPath.split("\\.")));
        });
    }
}
