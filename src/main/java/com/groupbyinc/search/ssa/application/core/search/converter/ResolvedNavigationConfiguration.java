package com.groupbyinc.search.ssa.application.core.search.converter;

import com.groupbyinc.proto.commandcenter.config.AttributeMessage;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.navigation.NavigationSort;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;

import io.micronaut.core.annotation.Nullable;
import lombok.With;

import java.util.List;

import static com.groupbyinc.search.ssa.core.navigation.NavigationType.RANGE;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.VALUE;

/**
 * Area independent navigation configuration, which is used to create a facet spec.
 */
public record ResolvedNavigationConfiguration(String name,
                                              String field,
                                              NavigationType type,
                                              @With @Nullable List<Range> ranges,
                                              @Nullable NavigationSort sort,
                                              boolean multiSelect) {


    public static ResolvedNavigationConfiguration fromNavigationConfiguration(NavigationConfiguration c) {
        return new ResolvedNavigationConfiguration(
            c.name(),
            c.field(),
            c.type(),
            c.ranges(),
            c.sort(),
            c.multiSelect()
        );
    }

    public static ResolvedNavigationConfiguration fromAttribute(AttributeConfiguration attribute) {
        return new ResolvedNavigationConfiguration(
            attribute.displayName(),
            attribute.key(),
            attribute.type() == AttributeMessage.AttributeType.NUMERICAL ? RANGE : VALUE,
            null,
            null,
            true
        );
    }

}
