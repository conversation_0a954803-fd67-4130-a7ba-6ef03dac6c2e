package com.groupbyinc.search.ssa.application.core.search.strategy.topsort;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_TOP_SORT_V2;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import javax.annotation.ParametersAreNonnullByDefault;

import com.groupbyinc.search.ssa.api.error.ProcessingException;
import com.groupbyinc.search.ssa.application.core.search.strategy.SearchStrategy;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.navigation.NavigationRefinement;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.mongo.facet.MongoFacetConverter;
import com.groupbyinc.search.ssa.productcatalog.ProductCatalogService;
import com.groupbyinc.search.ssa.retail.filtering.RetailFacetConverter;
import com.groupbyinc.search.ssa.topsort.TopSortService;
import com.groupbyinc.search.ssa.util.debug.StrategyNameContext;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.propagation.PropagatedContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@ParametersAreNonnullByDefault
public class TopSortSearchStrategyV2 extends SearchStrategy {

    public static final String TOPSORT_SPONSORED_RECORD_ID_NAVIGATION = "attributes.gbi_topsort_sponsored_record_id";

    private final TopSortService topsortService;
    private final FeaturesManager featuresManager;
    private final ProductCatalogService productCatalogService;

    @NonNull
    @Override
    public SearchResults search(SearchParameters searchParameters) {
        log.debug("TopSortSearchStrategy V2 used for search with parameters: {}", searchParameters);

        try (var ignored = PropagatedContext.getOrEmpty().plus(new StrategyNameContext(name())).propagate()) {
            searchParameters.setSkipProductCatalog(true);
            searchParameters.setFetchSponsoredRecordsVariants(true);
            searchParameters.setIncludedNavigations(
                Stream.concat(
                    searchParameters.getIncludedNavigations().stream(),
                    Stream.of(TOPSORT_SPONSORED_RECORD_ID_NAVIGATION)
                ).toList()
            );
            searchParameters.setRetailFacetConverter(new RetailFacetConverter(searchParameters));
            searchParameters.setMongoFacetConverter(new MongoFacetConverter(searchParameters));

            var searchResults = getApplicableLinkedSearchStrategy(searchParameters)
                .orElseThrow(() -> new ProcessingException("Applicable search strategy not found."))
                .search(searchParameters);

            var topsortNav = searchResults.getNavigations().stream()
                .filter(n -> n.getField().contains(TOPSORT_SPONSORED_RECORD_ID_NAVIGATION))
                .findFirst().orElse(null);

            if (topsortNav == null) {
                log.debug("TopSort navigation not found in search results, returning original results");
                searchParameters.setSkipProductCatalog(false);
                var enrichedResults = productCatalogService.fillProductsWithMetadata(searchParameters, searchResults);
                return cleanupTopsortAttributes(enrichedResults);
            }

            try {
                var auctionIds = topsortNav.getRefinements().stream()
                    .map(NavigationRefinement::getValue)
                    .toList();

                log.debug("Found {} auction IDs from TopSort navigation", auctionIds.size());

                var sponsoredResults = topsortService
                    .fillWithSponsoredProducts(searchParameters, searchResults, auctionIds, true);
                sponsoredResults.getSearchStrategies().add(name());

                searchParameters.setSkipProductCatalog(false);
                var enrichedResults = productCatalogService.fillProductsWithMetadata(
                    searchParameters,
                    sponsoredResults
                );

                return cleanupTopsortAttributes(enrichedResults);
            } catch (Exception e) {
                log.error("Error processing TopSort results: {}", e.getMessage(), e);
                getRequestContext().addWarning("Error processing sponsored products: " + e.getMessage());
                return cleanupTopsortAttributes(searchResults);
            }
        }
    }

    private SearchResults cleanupTopsortAttributes(SearchResults searchResults) {
        var builder = searchResults.toBuilder();

        // Clean up navigations
        if (searchResults.getNavigations() != null) {
            var cleanedNavigations = new ArrayList<>(searchResults.getNavigations());
            cleanedNavigations.removeIf(n -> TOPSORT_SPONSORED_RECORD_ID_NAVIGATION.equals(n.getField()));
            builder.navigations(cleanedNavigations);
        }

        // Clean up selected navigations
        if (searchResults.getSelectedNavigations() != null) {
            var cleanedSelectedNavigations = new ArrayList<>(searchResults.getSelectedNavigations());
            cleanedSelectedNavigations.removeIf(n -> TOPSORT_SPONSORED_RECORD_ID_NAVIGATION.equals(n.getName()));
            builder.selectedNavigations(cleanedSelectedNavigations);
        }

        // Clean up records
        if (searchResults.getRecords() != null) {
            cleanupRecordList(searchResults.getRecords());
        }

        // Clean up sponsored records
        if (searchResults.getSponsoredRecords() != null) {
            cleanupRecordList(searchResults.getSponsoredRecords());
        }

        return builder.build();
    }

    private void cleanupRecordList(List<com.groupbyinc.search.ssa.core.Record> records) {
        for (com.groupbyinc.search.ssa.core.Record record : records) {
            if (record != null && record.getMetadata() != null) {
                Map<String, Object> cleanedMetadata = new LinkedHashMap<>(record.getMetadata());
                cleanedMetadata.remove(TOPSORT_SPONSORED_RECORD_ID_NAVIGATION);
                record.setMetadata(cleanedMetadata);
            }
        }
    }

    @Override
    public boolean isApplicable(SearchParameters searchParameters) {
        var checker = new TopSortApplicabilityChecker(
            featuresManager,
            topsortService,
            ENABLE_TOP_SORT_V2,
            Boolean.TRUE.equals(searchParameters.getTopSortV2Enabled())
        );
        return checker.isApplicable(searchParameters);
    }
}
