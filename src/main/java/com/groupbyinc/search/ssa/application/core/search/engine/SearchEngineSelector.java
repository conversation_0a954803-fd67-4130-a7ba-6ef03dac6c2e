package com.groupbyinc.search.ssa.application.core.search.engine;

import com.groupbyinc.search.ssa.core.SearchParameters;

import java.util.Optional;

/**
 * Responsible to select a search engine, applicable for passed search parameters.
 * Selected search engine used to query for product ids/details.
 */
public interface SearchEngineSelector {

    SearchEngine getEngine(SearchParameters searchParameters);

}
