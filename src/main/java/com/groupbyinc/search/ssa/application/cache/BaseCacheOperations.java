package com.groupbyinc.search.ssa.application.cache;

import com.groupbyinc.search.ssa.application.cache.event.SaveToCacheEvent;
import com.groupbyinc.search.ssa.core.SearchMode;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.request.RequestServed;
import com.groupbyinc.search.ssa.features.FeatureFlag;
import com.groupbyinc.search.ssa.redis.CacheResponse;
import com.groupbyinc.search.ssa.redis.CacheSource;
import com.groupbyinc.search.ssa.util.debug.DebugInfo;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micronaut.context.annotation.Context;
import io.micronaut.context.event.ApplicationEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.util.StringUtils.EMPTY;

import static io.micronaut.core.util.StringUtils.isNotEmpty;

@Slf4j
@Context
@RequiredArgsConstructor
public class BaseCacheOperations implements CacheOperations {

    private final ObjectMapper objectMapper;
    private final ApplicationEventPublisher<SaveToCacheEvent<?>> saveToCacheEventPublisher;

    public <C extends CacheConfig & FeatureFlag, T> CacheResponse<C, T> getFromCache(
        Cache cache,
        CacheKeyGenerator<C> cacheKeyGenerator,
        SearchParameters searchParameters,
        TypeReference<T> type,
        C config,
        CacheSource source
    ) {
        if (searchParameters.getSearchMode() == SearchMode.FACETED_SEARCH) {
            return new CacheResponse<>(config, Optional.empty(), Optional.empty());
        }

        try {
            var key = config.enabled() ? cacheKeyGenerator.generateCacheKey(searchParameters, config) : EMPTY;
            if (config.enabled() && !getRequestContext().getRequestOptions().skipCache()) {
                return new CacheResponse<>(config, Optional.of(key), getFromCache(cache, key, type));
            }
        } catch (Exception e) {
            log.warn("Fail to get value from a cache {}.", source, e);
        }

        return new CacheResponse<>(config, Optional.empty(), Optional.empty());
    }

    public <C extends CacheConfig, T> void saveToCache(CacheResponse<C, T> response, CacheSource source, T data) {
        if (response != null && response.config().enabled() && response.key().isPresent()) {
            publishSaveToCacheEvent(
                response.config(),
                response.key().get(),
                source,
                data
            );
        }
    }

    public <T extends CacheConfig, V> void publishSaveToCacheEvent(T config, String key, CacheSource source, V data) {
        try {
            if (key.isEmpty()) {
                return;
            }

            saveToCacheEventPublisher.publishEventAsync(
                new SaveToCacheEvent<>(
                    key,
                    objectMapper.writeValueAsString(data),
                    config,
                    source
                )
            );
        } catch (Exception e) {
            log.warn("Fail to safe into a cache: {}", source, e);
        }
    }

    public static SearchResults getResultsFromCacheResponse(CacheResponse<?, StoredObject<SearchResults>> fromCache,
                                                            DebugInfo debugInfo,
                                                            String debugKey) {
        var response = fromCache.response().orElseThrow();
        response.value().setRequestServed(RequestServed.CACHE);
        response.value().getMetadata().setCached(true);

        getRequestContext()
            .getDebugDetails()
            .getDebugInfos()
            .put(debugKey, debugInfo);

        return response.value();
    }

    private <T> Optional<T> getFromCache(Cache cache, String key, TypeReference<T> type) throws JacksonException {
        var cachedResponse = cache.get(key);
        if (isNotEmpty(cachedResponse)) {
            var parsedCachedResponse = objectMapper.readValue(cachedResponse, type);
            return Optional.of(parsedCachedResponse);
        }
        return Optional.empty();
    }

}
