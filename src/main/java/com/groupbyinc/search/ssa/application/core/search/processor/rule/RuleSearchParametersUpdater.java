package com.groupbyinc.search.ssa.application.core.search.processor.rule;

import com.groupbyinc.search.ssa.core.RecordLabel;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfile;
import com.groupbyinc.search.ssa.core.filter.SearchFilter;
import com.groupbyinc.search.ssa.core.rule.PinnedProduct;
import com.groupbyinc.search.ssa.core.rule.PinnedRefinement;
import com.groupbyinc.search.ssa.mongo.facet.MongoFacetConverter;
import com.groupbyinc.search.ssa.retail.filtering.RetailFacetConverter;

import io.micronaut.context.annotation.Context;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.core.util.CollectionUtils;
import io.micronaut.core.util.StringUtils;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.core.RecordLabel.BOOSTED;
import static com.groupbyinc.search.ssa.core.RecordLabel.BURIED;
import static com.groupbyinc.search.ssa.core.RecordLabel.PINNED;
import static com.groupbyinc.search.ssa.util.StringUtils.SPACE;

import static java.util.function.UnaryOperator.identity;
import static java.util.stream.Collectors.toMap;

@Context
@RequiredArgsConstructor
public class RuleSearchParametersUpdater {

    public void updateSearchParametersByRule(SearchParameters searchParameters, RuleProcessorResult ruleResult) {
        var ruleBPName = ruleResult
            .triggeredRule()
            .map(triggeredRule -> triggeredRule.rule().getBiasingProfileName())
            .orElse(null);
        searchParameters.setBiasingProfile(resolveBiasingProfile(searchParameters, ruleBPName));

        if (ruleResult.triggeredRule().isEmpty()) {
            return;
        }

        if (ruleResult.triggeredRule().get().variantName().isPresent()) {
            searchParameters.setRuleVariantName(ruleResult.triggeredRule().get().variantName().get());
        }

        var rule = ruleResult.triggeredRule().get().rule();
        var labeledIds = new HashMap<String, RecordLabel>();
        if (!rule.getBoostedProductBuckets().isEmpty()) {
            searchParameters.setBoostedProductBuckets(rule.getBoostedProductBuckets());
            rule.getBoostedProductBuckets()
                .stream()
                .flatMap(productIdsBucket -> productIdsBucket.getProducts().stream())
                .forEach(id -> labeledIds.put(id, BOOSTED));
        }

        if (!rule.getBuriedProductBuckets().isEmpty()) {
            searchParameters.setBuriedProductBuckets(rule.getBuriedProductBuckets());
            rule.getBuriedProductBuckets()
                .stream()
                .flatMap(productIdsBucket -> productIdsBucket.getProducts().stream())
                .forEach(id -> labeledIds.put(id, BURIED));
        }

        if (!rule.getPinnedProducts().isEmpty()) {
            searchParameters.setPinnedProducts(resolvePinnedProducts(searchParameters, rule.getPinnedProducts()));
            searchParameters.getPinnedProducts()
                .stream()
                .map(PinnedProduct::productId)
                .forEach(id -> labeledIds.put(id, PINNED));
        }
        searchParameters.setLabeledProductIds(labeledIds);

        /*
         * We have two types of included navigations
         * 1) From rule
         * 2) From search request.
         *
         * The result list must contain both of them, and rule navigations must be
         * before navigations from search.
         */
        if (!rule.getIncludedNavigations().isEmpty()) {
            searchParameters.setIncludedNavigations(
                Stream.concat(
                    searchParameters.getIncludedNavigations().stream(),
                    rule.getIncludedNavigations().stream()
                ).toList()
            );
        }

        // Inject additional search terms into the query
        if (!rule.getSearchFilters().isEmpty()) {
            var injectedQuery = rule
                .getSearchFilters()
                .stream()
                .map(SearchFilter::getValue)
                .collect(Collectors.joining(SPACE));

            var updatedQuery = searchParameters.getQuery() + SPACE + injectedQuery;
            searchParameters.setQuery(updatedQuery.trim());
        }

        // Inject additional attribute filters into the search
        if (!rule.getAttributeFilters().isEmpty()) {
            searchParameters.setAttributeFilters(rule.getAttributeFilters());
        }

        // Inject additional product ids filters into the search
        if (rule.getProductIdFilter() != null) {
            searchParameters.setProductIdFilter(rule.getProductIdFilter());
        }

        /*
         * The logic is:
         * if rule has navigations without pinned refinements, then for these
         * navigations we will use refinement order from global (area) navigation,
         * in case if related navigation exists and has pinned refinements, otherwise
         * order from Google will be used.
         *
         * In case when rules have their own pinned refinements, they would be used.
         */
        if (!rule.getPinnedRefinements().isEmpty()) {
            var fromRequestSP = searchParameters
                .getPinnedRefinements()
                .stream()
                .collect(toMap(PinnedRefinement::navigation, identity()));

            List<PinnedRefinement> resultPinnedRefinements = new ArrayList<>();
            rule.getPinnedRefinements().forEach(n -> {
                if (CollectionUtils.isNotEmpty(n.refinements())) {
                    resultPinnedRefinements.add(n);
                } else if (fromRequestSP.containsKey(n.navigation())) {
                    resultPinnedRefinements.add(fromRequestSP.get(n.navigation()));
                }
            });
            searchParameters.setPinnedRefinements(resultPinnedRefinements);
        }

        searchParameters.setRetailFacetConverter(new RetailFacetConverter(searchParameters));
        searchParameters.setMongoFacetConverter(new MongoFacetConverter(searchParameters));
    }

    private BiasingProfile resolveBiasingProfile(SearchParameters searchParameters, @Nullable String ruleBPName) {
        var merchandisingConfiguration = searchParameters.getMerchandisingConfiguration();

        if (searchParameters.getBiasingProfile() != null) {
            return searchParameters.getBiasingProfile();
        }
        if (searchParameters.getBiasingProfileName() != null) {
            var bp = merchandisingConfiguration.getBiasingProfileByName(
                searchParameters.getBiasingProfileName()
            );
            if (bp.isPresent()) {
                return bp.get();
            }
        }

        if (StringUtils.isNotEmpty(ruleBPName)) {
            return merchandisingConfiguration.getBiasingProfileByName(ruleBPName).orElse(null);
        }

        return merchandisingConfiguration.getAreaDefaultBiasingProfile().orElse(null);
    }

    private List<PinnedProduct> resolvePinnedProducts(SearchParameters requestSP, List<PinnedProduct> pinnedFromRule) {
        var rulePinnedByPosition = pinnedFromRule
            .stream()
            .collect(toMap(PinnedProduct::position, identity()));

        var reqPinnedById = requestSP.getPinnedProducts()
            .stream()
            .collect(toMap(PinnedProduct::productId, identity()));

        requestSP.getPinnedProducts()
            .forEach(reqPinnedProduct -> rulePinnedByPosition.put(reqPinnedProduct.position(), reqPinnedProduct));

        // Remove pinned products from rule that are already pinned by request
        var keysToRemove = new ArrayList<Integer>();
        rulePinnedByPosition.forEach((rulePosition, rulePinnedProduct) -> {
            var reqPinnedProduct = reqPinnedById.get(rulePinnedProduct.productId());
            if (reqPinnedProduct != null && !rulePosition.equals(reqPinnedProduct.position())) {
                keysToRemove.add(rulePosition);
            }
        });

        keysToRemove.forEach(rulePinnedByPosition::remove);

        return new ArrayList<>(rulePinnedByPosition.values());
    }

}
