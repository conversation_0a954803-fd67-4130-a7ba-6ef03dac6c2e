package com.groupbyinc.search.ssa.application.configuration;

import com.groupbyinc.search.ssa.commandcenter.CommandCenterConfigurationClient;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.area.AreaConfiguration;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfileConfiguration;
import com.groupbyinc.search.ssa.core.features.Features;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.project.ProjectConfiguration;
import com.groupbyinc.search.ssa.core.redirect.RedirectConfiguration;
import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;
import com.groupbyinc.search.ssa.core.tenant.TenantConfiguration;
import com.groupbyinc.search.ssa.core.zone.ZoneConfiguration;
import com.groupbyinc.search.ssa.topsort.model.TopsortConfiguration;
import com.groupbyinc.utils.crypto.AesCipherStrategy;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Value;
import jakarta.inject.Named;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeSet;

import static com.groupbyinc.search.ssa.application.configuration.ConfigKey.createConfigKey;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.MeterRegistry.monotonicTime;

@Factory
@Slf4j
public class ConfigurationManagerFactory {

    private final AesCipherStrategy cbcCipher;

    public ConfigurationManagerFactory(@Named("cbc") AesCipherStrategy cbcCipher) {
        this.cbcCipher = cbcCipher;
    }

    @Context
    public ConfigurationManager configurationManager(
        CommandCenterConfigurationClient allConfigurationClient,
        @Value("${commandcenter.client.url}") String ccapi
    ) throws Exception {

        var start = monotonicTime();
        log.info("Requesting CC API on ({})", ccapi);
        var config = allConfigurationClient.getAllConfigurations();
        log.info("Requesting configs complete in: ({})", monotonicTime() - start);

        // (merchandiser, tenant)
        Map<Merchandiser, TenantConfiguration> tenants = new HashMap<>();
        // (tenantId, tenant)
        Map<Integer, TenantConfiguration> tenantsById = new HashMap<>();
        if (config != null && config.tenants() != null) {
            config.tenants().forEach(t -> {
                tenantsById.put(t.id(), t);
                tenants.put(Merchandiser.of(t.name()), t);
            });
        }

        // (merchandiser, (collection, projectConfiguration))
        Map<Merchandiser, Map<String, ProjectConfiguration>> projectConfigurations = new HashMap<>();
        // (projectConfigurationId, projectConfiguration)
        Map<Integer, ProjectConfiguration> projectConfigurationsById = new HashMap<>();
        if (config != null && config.projectConfigurations() != null) {
            config.projectConfigurations().forEach(pc -> {
                var byCollection = projectConfigurations
                    .computeIfAbsent(
                        Merchandiser.of(tenantsById.get(pc.tenantId()).name()),
                        k -> new HashMap<>()
                    );
                byCollection.put(pc.collection(), pc);
                projectConfigurationsById.put(pc.id(), pc);
            });
        }

        // (areaId, topsort config)
        Map<Integer, TopsortConfiguration> topsortConfigsByArea = new HashMap<>();
        if (config != null && config.topsortConfigs() != null) {
            config.topsortConfigs().forEach(topsortConfig -> {
                if (topsortConfigsByArea.containsKey(topsortConfig.areaId())) {
                    log.warn("Multiple topsort configuration found for one area: {}", topsortConfig.areaId());
                }
                try {
                    topsortConfigsByArea.put(
                        topsortConfig.areaId(),
                        topsortConfig.withApiKey(cbcCipher.decrypt(topsortConfig.apiKey()))
                    );
                } catch (Exception e) {
                    log.error("Failed to decrypt topsort api key: {}", e.getMessage(), e);
                }
            });
        }

        // (areaId, area)
        Map<Integer, AreaConfiguration> areasById = new HashMap<>();
        // (merchandiser, (collection, (ara.name, area)))
        Map<Merchandiser, Map<String, Map<String, AreaConfiguration>>> areas = new HashMap<>();
        if (config != null && config.areas() != null) {
            config.areas().stream()
                .map(areaConfig -> areaConfig.withTopsortConfiguration(topsortConfigsByArea.get(areaConfig.id())))
                .forEach(areaConfiguration -> {
                    areasById.put(areaConfiguration.id(), areaConfiguration);

                    var mapByCollection = areas.computeIfAbsent(
                        Merchandiser.of(tenantsById.get(areaConfiguration.tenantId()).name()),
                        k -> new HashMap<>()
                    );
                    var mapByAreaName = mapByCollection.computeIfAbsent(
                        projectConfigurationsById.get(areaConfiguration.collectionId()).collection(),
                        k -> new HashMap<>()
                    );
                    mapByAreaName.put(areaConfiguration.name(), areaConfiguration);
                });
        }

        // (configKey, [rules])
        Map<ConfigKey, TreeSet<RuleConfiguration>> rules = new HashMap<>();
        if (config != null && config.rules() != null) {
            config.rules().forEach(rc -> rules.computeIfAbsent(
                    createConfigKey(tenantsById, areasById, rc.areaId()),
                    k -> new TreeSet<>()
                ).add(rc)
            );
        }

        // (configKey, [redirects])
        Map<ConfigKey, TreeSet<RedirectConfiguration>> redirects = new HashMap<>();
        if (config != null && config.redirects() != null) {
            config.redirects().forEach(r -> redirects.computeIfAbsent(
                    createConfigKey(tenantsById, areasById, r.areaId()),
                    k -> new TreeSet<>()
                ).add(r)
            );
        }

        // (configKey, [navigations])
        Map<ConfigKey, TreeSet<NavigationConfiguration>> navigations = new HashMap<>();
        if (config != null && config.navigations() != null) {
            config.navigations().forEach(n -> navigations.computeIfAbsent(
                    createConfigKey(tenantsById, areasById, n.areaId()),
                    k -> new TreeSet<>()
                ).add(n)
            );
        }

        // (configKey, (biasingProfileId, biasingProfile))
        Map<ConfigKey, Map<Integer, BiasingProfileConfiguration>> biasingProfiles = new HashMap<>();
        if (config != null && config.biasingProfiles() != null) {
            config.biasingProfiles().forEach(bp -> biasingProfiles.computeIfAbsent(
                    createConfigKey(tenantsById, areasById, bp.areaId()),
                    k -> new HashMap<>()
                ).put(bp.id(), bp)
            );
        }

        // (configKey, (zoneId, zone))
        Map<ConfigKey, Map<Integer, ZoneConfiguration>> zones = new HashMap<>();
        if (config != null && config.zones() != null) {
            config.zones().forEach(z -> zones.computeIfAbsent(
                    createConfigKey(tenantsById, areasById, z.areaId()),
                    k -> new HashMap<>()
                ).put(z.id(), z)
            );
        }

        // (merchandiser, (collection, (attribute.key, attribute)))
        Map<Merchandiser, Map<String, Map<String, AttributeConfiguration>>> attributes = new HashMap<>();
        if (config != null && config.attributes() != null) {
            config.attributes().forEach(attributeConfiguration -> {
                var pc = projectConfigurationsById.get(attributeConfiguration.collectionId());
                attributes
                    .computeIfAbsent(
                        Merchandiser.of(tenantsById.get(pc.tenantId()).name()),
                        k -> new HashMap<>()
                    ).computeIfAbsent(
                        pc.collection(),
                        k -> new HashMap<>()
                    ).put(attributeConfiguration.key(), attributeConfiguration);
            });
        }

        // (merchandiser, (collectionName (siteFilter.name, siteFilter)))
        Map<Merchandiser, Map<String, Map<String, SiteFilterConfiguration>>> siteFilters = new HashMap<>();
        if (config != null && config.siteFilters() != null) {
            config.siteFilters().forEach(siteFilter -> {
                var pc = projectConfigurationsById.get(siteFilter.collectionId());
                siteFilters
                    .computeIfAbsent(
                        Merchandiser.of(tenantsById.get(pc.tenantId()).name()),
                        k -> new HashMap<>()
                    ).computeIfAbsent(
                        pc.collection(),
                        k -> new HashMap<>()
                    ).put(siteFilter.name(), siteFilter);
            });
        }

        // Resolve feature flags.
        var features = (config == null || config.features() == null)
            ? new Features()
            : config.features();

        return new ConfigurationManager(
            tenants,
            tenantsById,

            projectConfigurations,
            projectConfigurationsById,

            attributes,

            areasById,
            areas,

            rules,
            redirects,
            navigations,
            biasingProfiles,
            zones,
            siteFilters,

            features,
            cbcCipher
        );
    }
}
