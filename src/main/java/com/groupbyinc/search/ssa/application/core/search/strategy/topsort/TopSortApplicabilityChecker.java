package com.groupbyinc.search.ssa.application.core.search.strategy.topsort;

import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.topsort.TopSortService;
import io.micronaut.core.annotation.NonNull;
import lombok.RequiredArgsConstructor;

import javax.annotation.ParametersAreNonnullByDefault;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.core.SearchMode.PRODUCT_SEARCH;
import static com.groupbyinc.search.ssa.topsort.TopSortService.slots;

/**
 * Helper class to check if TopSort strategy should be applied to a search request.
 * Encapsulates common logic for determining TopSort applicability across different versions.
 */
@RequiredArgsConstructor
@ParametersAreNonnullByDefault
public class TopSortApplicabilityChecker {

    private final FeaturesManager featuresManager;
    private final TopSortService topsortService;
    private final String featureFlagName;
    private final boolean enabledManually;

    /**
     * Checks whether TopSort flow should be applied for current search request.
     * Considering multiple parameters such as:
     * <ul>
     *     <li>Search mode</li>
     *     <li>Original request pagination</li>
     *     <li>Original request topsort flag (overrides feature flag)</li>
     *     <li>Feature flag</li>
     *     <li>Is top sort api key enabled or not</li>
     * </ul>
     *
     * @param searchParameters the search parameters used to conduct a search.
     * @return true if TopSort should be applied, false otherwise.
     * <a href="https://groupby.atlassian.net/wiki/spaces/Integrations/pages/3981443077/
     *    Topsort+Search+API#Topsort-logic-invocation-prerequisites">
     *     Logic of invocation docs
     *  </a>
     */
    public boolean isApplicable(@NonNull SearchParameters searchParameters) {
        var context = getRequestContext();
        var pagination = searchParameters.getPagination();
        var mode = searchParameters.getSearchMode();

        if (mode != PRODUCT_SEARCH || (pagination.getOffset() > 0) || (pagination.getSize() <= 0)) {
            return false;
        }

        var featureEnabled = featuresManager.getBooleanFlagConfiguration(
            context.getLdContext(),
            featureFlagName
        );

        // one of these must be true to continue (see docs)
        if (!featureEnabled && !this.enabledManually) {
            return false;
        }

        var sponsoredRequest = searchParameters.getSponsoredRecordsRequest();
        if (sponsoredRequest == null || slots(sponsoredRequest, pagination.getSize()) <= 0) {
            return false;
        }
        return topsortService.getApiKeyForEnabledConfiguration() != null;
    }
}
