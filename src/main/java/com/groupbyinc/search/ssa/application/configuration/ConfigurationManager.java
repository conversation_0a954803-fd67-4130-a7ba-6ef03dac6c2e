package com.groupbyinc.search.ssa.application.configuration;

import com.groupbyinc.proto.commandcenter.config.MessageType;
import com.groupbyinc.search.ssa.core.Config;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.Prioritized;
import com.groupbyinc.search.ssa.core.area.AreaConfiguration;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfileConfiguration;
import com.groupbyinc.search.ssa.core.features.Features;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.project.ProjectConfiguration;
import com.groupbyinc.search.ssa.core.redirect.RedirectConfiguration;
import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;
import com.groupbyinc.search.ssa.core.tenant.TenantConfiguration;
import com.groupbyinc.search.ssa.core.zone.ZoneConfiguration;
import com.groupbyinc.search.ssa.topsort.model.TopsortConfiguration;
import com.groupbyinc.utils.crypto.AesCipherStrategy;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Predicate;

import static com.groupbyinc.search.ssa.application.configuration.ConfigKey.createConfigKey;

@Slf4j
public class ConfigurationManager {

    // (merchandiser, tenant)
    private final Map<Merchandiser, TenantConfiguration> tenants;
    // (tenantId, tenant)
    private final Map<Integer, TenantConfiguration> tenantsById;

    // (merchandiser, (collection, projectConfiguration))
    private final Map<Merchandiser, Map<String, ProjectConfiguration>> projectConfigurations;
    // (projectConfigurationId, projectConfiguration)
    private final Map<Integer, ProjectConfiguration> projectConfigurationsById;

    // (merchandiser, (collection, (attribute.key, attribute)))
    private final Map<Merchandiser, Map<String, Map<String, AttributeConfiguration>>> attributes;

    // (areaId, area)
    private final Map<Integer, AreaConfiguration> areasById;

    // (merchandiser, (collection, (ara.name, area)))
    private final Map<Merchandiser, Map<String, Map<String, AreaConfiguration>>> areas;

    // (configKey, [rules])
    private final Map<ConfigKey, TreeSet<RuleConfiguration>> rules;
    // (configKey, [redirects])
    private final Map<ConfigKey, TreeSet<RedirectConfiguration>> redirects;
    // (configKey, [navigations])
    private final Map<ConfigKey, TreeSet<NavigationConfiguration>> navigations;
    // (configKey, (biasingProfileId, biasingProfile))
    private final Map<ConfigKey, Map<Integer, BiasingProfileConfiguration>> biasingProfiles;
    // (configKey, (zoneId, zone))
    private final Map<ConfigKey, Map<Integer, ZoneConfiguration>> zones;

    // (merchandiser, (collection (siteFilter.name, siteFilter))
    private final Map<Merchandiser, Map<String, Map<String, SiteFilterConfiguration>>> siteFilters;

    // region features
    @Getter
    private final Features features;
    private final AesCipherStrategy cbcCipher;


    public ConfigurationManager(Map<Merchandiser, TenantConfiguration> tenants,
                                Map<Integer, TenantConfiguration> tenantsById,

                                Map<Merchandiser, Map<String, ProjectConfiguration>> projectConfigurations,
                                Map<Integer, ProjectConfiguration> projectConfigurationsById,

                                Map<Merchandiser, Map<String, Map<String, AttributeConfiguration>>> attributes,

                                Map<Integer, AreaConfiguration> areasById,
                                Map<Merchandiser, Map<String, Map<String, AreaConfiguration>>> areas,

                                Map<ConfigKey, TreeSet<RuleConfiguration>> rules,
                                Map<ConfigKey, TreeSet<RedirectConfiguration>> redirects,
                                Map<ConfigKey, TreeSet<NavigationConfiguration>> navigations,
                                Map<ConfigKey, Map<Integer, BiasingProfileConfiguration>> biasingProfiles,
                                Map<ConfigKey, Map<Integer, ZoneConfiguration>> zones,
                                Map<Merchandiser, Map<String, Map<String, SiteFilterConfiguration>>> siteFilters,

                                Features features,
                                AesCipherStrategy cbcCipher) {
        this.tenants = tenants;
        this.tenantsById = tenantsById;

        this.projectConfigurations = projectConfigurations;
        this.projectConfigurationsById = projectConfigurationsById;

        this.attributes = attributes;

        this.areasById = areasById;
        this.areas = areas;

        this.rules = rules;
        this.redirects = redirects;
        this.navigations = navigations;
        this.biasingProfiles = biasingProfiles;
        this.zones = zones;
        this.siteFilters = siteFilters;

        this.features = features;
        this.cbcCipher = cbcCipher;
    }

    //region get config
    public Optional<TenantConfiguration> getTenant(Merchandiser merchandiser) {
        return Optional.ofNullable(tenants.get(merchandiser));
    }

    public Optional<AreaConfiguration> getArea(Merchandiser merchandiser, String collection, String areaName) {
        return Optional.ofNullable(
            areas
                .getOrDefault(merchandiser, new HashMap<>())
                .getOrDefault(collection, new HashMap<>())
                .get(areaName)
        );
    }

    public Optional<TopsortConfiguration> getTopSortConfig(Merchandiser merchandiser,
                                                           String collection,
                                                           String areaName) {
        return getArea(merchandiser, collection, areaName).map(AreaConfiguration::topsortConfiguration);
    }

    public Optional<SiteFilterConfiguration> getSiteFiltersByName(Merchandiser merchandiser,
                                                                  String collection,
                                                                  String name) {
        return Optional.ofNullable(
            siteFilters
                .getOrDefault(merchandiser, new HashMap<>())
                .getOrDefault(collection, new HashMap<>())
                .get(name)
        );
    }

    public Optional<SiteFilterConfiguration> getSiteFiltersById(Merchandiser merchandiser,
                                                                String collection,
                                                                Integer id) {
        return siteFilters
            .getOrDefault(merchandiser, new HashMap<>())
            .getOrDefault(collection, new HashMap<>())
            .values()
            .stream()
            .filter(siteFilterConfiguration -> siteFilterConfiguration.id().equals(id))
            .findFirst();
    }

    public Optional<ProjectConfiguration> getProjectConfiguration(Merchandiser merchandiser, String collection) {
        var projectConfiguration = projectConfigurations
            .getOrDefault(merchandiser, new HashMap<>())
            .get(collection);

        return Optional.ofNullable(projectConfiguration);
    }

    public Set<RuleConfiguration> getRules(Merchandiser merchandiser, Integer areaId) {
        return Collections.unmodifiableSortedSet(
            rules.getOrDefault(
                new ConfigKey(merchandiser, areaId),
                new TreeSet<>()
            )
        );
    }

    public Set<RedirectConfiguration> getRedirects(Merchandiser merchandiser, Integer areaId) {
        return Collections.unmodifiableSortedSet(
            redirects.getOrDefault(
                new ConfigKey(merchandiser, areaId),
                new TreeSet<>()
            )
        );
    }

    public Set<NavigationConfiguration> getNavigations(Merchandiser merchandiser, Integer areaId) {
        return Collections.unmodifiableSortedSet(
            navigations.getOrDefault(
                new ConfigKey(merchandiser, areaId),
                new TreeSet<>()
            )
        );
    }

    public List<BiasingProfileConfiguration> getBiasingProfiles(Merchandiser merchandiser, Integer areaId) {
        return List.copyOf(
            biasingProfiles.getOrDefault(
                new ConfigKey(merchandiser, areaId),
                new HashMap<>()
            ).values()
        );
    }

    public List<ZoneConfiguration> getZones(Merchandiser merchandiser, Integer areaId) {
        return List.copyOf(
            zones.getOrDefault(
                new ConfigKey(merchandiser, areaId),
                new HashMap<>()
            ).values()
        );
    }

    public Map<String, AttributeConfiguration> getAttributeConfiguration(Merchandiser merchandiser,
                                                                         String collection) {
        return attributes
            .getOrDefault(merchandiser, new HashMap<>())
            .getOrDefault(collection, new HashMap<>());
    }
    //endregion

    //region update config
    public void updateTenantConfig(List<TenantConfiguration> configs) {
        configs.forEach(tenant -> {
            var type = tenant.messageType();
            var merchandiser = Merchandiser.of(tenant.name());
            switch (type) {
                case UPDATE, CREATE -> {
                    tenants.put(merchandiser, tenant);
                    tenantsById.put(tenant.id(), tenant);
                }
                case DELETE -> handleTenantDelete(merchandiser, tenant);
                default -> log.warn("Unrecognised message: {}", tenant);
            }
        });
    }

    public void updateProjectConfigurationConfig(List<ProjectConfiguration> configs) {
        configs.forEach(projectConfiguration -> {
            var merchandiser = Merchandiser.of(
                tenantsById.get(projectConfiguration.tenantId()).name()
            );
            switch (projectConfiguration.messageType()) {
                case UPDATE, CREATE -> {
                    projectConfigurations
                        .computeIfAbsent(merchandiser, k -> new HashMap<>())
                        .put(projectConfiguration.collection(), projectConfiguration);
                    projectConfigurationsById.put(projectConfiguration.id(), projectConfiguration);
                }
                case DELETE -> handleProjectConfigurationDelete(merchandiser, projectConfiguration);
                default -> log.warn("Unrecognised message: {}", projectConfiguration);
            }
        });
    }

    public void updateAttributeConfig(List<AttributeConfiguration> configs) {
        configs.forEach(attribute -> {
            var pc = projectConfigurationsById.get(
                attribute.collectionId()
            );
            var merchandiser = Merchandiser.of(
                tenantsById.get(pc.tenantId()).name()
            );

            var attributesByKey = attributes
                .computeIfAbsent(merchandiser, k -> new HashMap<>())
                .computeIfAbsent(pc.collection(), k -> new HashMap<>());

            switch (attribute.messageType()) {
                case UPDATE, CREATE -> attributesByKey.put(attribute.key(), attribute);
                case DELETE -> attributesByKey.remove(attribute.key());
                default -> log.warn("Unrecognised message: {}", attribute);
            }
        });
    }

    public void updateAreaConfig(List<AreaConfiguration> configs) {
        configs.forEach(area -> {
            var merchandiser = Merchandiser.of(
                tenantsById.get(area.tenantId()).name()
            );

            var byCollection = areas.computeIfAbsent(
                merchandiser,
                k -> new HashMap<>()
            );

            var mapByName = byCollection.computeIfAbsent(
                projectConfigurationsById.get(area.collectionId()).collection(),
                k -> new HashMap<>()
            );

            switch (area.messageType()) {
                case UPDATE, CREATE -> {
                    mapByName.put(area.name(), area);
                    areasById.put(area.id(), area);
                }
                case DELETE -> {
                    mapByName.remove(area.name());
                    areasById.remove(area.id());
                    deleteByAreaKeys(new ConfigKey(merchandiser, area.id()));
                }
                default -> log.warn("Unrecognised message: {}", area);
            }
        });
    }

    public void updateTopsortConfig(List<TopsortConfiguration> configs) {
        configs.stream().map(topsortConfig -> {
                try {
                    return topsortConfig.withApiKey(cbcCipher.decrypt(topsortConfig.apiKey()));
                } catch (Exception e) {
                    log.error("Failed to decrypt topsort api key, area: {}, ex: {}",
                        topsortConfig.areaId(), e.getMessage(), e
                    );
                    return topsortConfig.withApiKey(null);
                }
            })
            .filter(config -> config.apiKey() != null || config.messageType() == MessageType.DELETE)
            .forEach(topsortConfig -> {
                var areaConfig = areasById.get(topsortConfig.areaId());
                if (areaConfig == null) {
                    log.error("Cannot find area configuration by id: {}", topsortConfig.areaId());
                    return;
                }

                var merchandiser = Merchandiser.of(tenantsById.get(areaConfig.tenantId()).name());
                var byCollection = areas.computeIfAbsent(merchandiser, k -> new HashMap<>());
                var mapByName = byCollection.computeIfAbsent(
                    projectConfigurationsById.get(areaConfig.collectionId()).collection(),
                    k -> new HashMap<>()
                );

                switch (topsortConfig.messageType()) {
                    case UPDATE, CREATE -> {
                        areaConfig = areaConfig.withTopsortConfiguration(topsortConfig);
                        mapByName.put(areaConfig.name(), areaConfig);
                        areasById.put(areaConfig.id(), areaConfig);
                    }
                    case DELETE -> {
                        areaConfig = areaConfig.withTopsortConfiguration(TopsortConfiguration.DISABLED);
                        mapByName.put(areaConfig.name(), areaConfig);
                        areasById.put(areaConfig.id(), areaConfig);
                    }
                    default -> log.warn("Unrecognised message: {}", topsortConfig);
                }
            });
    }

    public void updateSiteFilterConfig(List<SiteFilterConfiguration> newConfig) {
        newConfig.forEach(filter -> {
            var type = filter.messageType();
            var pc = projectConfigurationsById.get(
                filter.collectionId()
            );

            var merchandiser = Merchandiser.of(
                tenantsById.get(pc.tenantId()).name()
            );

            var mapByCollection = siteFilters.computeIfAbsent(
                merchandiser,
                k -> new HashMap<>()
            );

            var mapByName = mapByCollection.computeIfAbsent(
                pc.collection(),
                k -> new HashMap<>()
            );

            switch (type) {
                case UPDATE, CREATE -> mapByName.put(filter.name(), filter);
                case DELETE -> mapByName.remove(filter.name());
            }
        });
    }

    public void updateBiasingProfileConfig(List<BiasingProfileConfiguration> newConfig) {
        updateConfig(newConfig, tenantsById, areasById, biasingProfiles);
    }

    public void updateZoneConfig(List<ZoneConfiguration> newConfig) {
        updateConfig(newConfig, tenantsById, areasById, zones);
    }

    public void updateRuleConfig(List<RuleConfiguration> newConfig) {
        updatePrioritizedConfig(newConfig, tenantsById, areasById, rules);
    }

    public void updateRedirectConfig(List<RedirectConfiguration> newConfig) {
        updatePrioritizedConfig(newConfig, tenantsById, areasById, redirects);
    }

    public void updateNavigationConfig(List<NavigationConfiguration> newConfig) {
        updatePrioritizedConfig(newConfig, tenantsById, areasById, navigations);
    }
    //endregion

    /**
     * Used to delete Tenant configuration from internal caches based on a passed
     * merchandiser object and configuration. Take note that related configurations
     * (ProjectConfigurations, Attributes, and Areas) also would be removed.
     *
     * @param merchandiser        for which we need to delete project configurations.
     * @param tenantConfiguration tenant configuration to delete.
     */
    private void handleTenantDelete(Merchandiser merchandiser, TenantConfiguration tenantConfiguration) {
        tenants.remove(merchandiser);
        tenantsById.remove(tenantConfiguration.id());

        projectConfigurations.remove(merchandiser);
        projectConfigurationsById
            .values()
            .stream()
            .filter(pc -> pc.tenantId().equals(tenantConfiguration.id()))
            .map(ProjectConfiguration::id)
            .toList()
            .forEach(projectConfigurationsById::remove);

        attributes.remove(merchandiser);

        areas.remove(merchandiser);

        deleteAreasByCondition(
            merchandiser,
            area -> area.tenantId().equals(tenantConfiguration.id())
        );
    }

    /**
     * Used to delete area-based configurations by passed configuration keys.
     *
     * @param keys to remove.
     */
    private void deleteByAreaKeys(ConfigKey... keys) {
        for (ConfigKey key : keys) {
            zones.remove(key);
            rules.remove(key);
            redirects.remove(key);
            navigations.remove(key);
            biasingProfiles.remove(key);
        }
    }

    /**
     * Used to delete ProjectConfiguration from internal caches based on a passed
     * merchandiser object and configuration. Take note that related configurations
     * (Attributes and Areas) also would be removed.
     *
     * @param merchandiser         for which we need to delete project configurations.
     * @param projectConfiguration to configuration to delete.
     */
    private void handleProjectConfigurationDelete(Merchandiser merchandiser,
                                                  ProjectConfiguration projectConfiguration) {
        projectConfigurations
            .getOrDefault(merchandiser, new HashMap<>())
            .remove(projectConfiguration.collection());
        projectConfigurationsById.remove(projectConfiguration.id());

        attributes
            .getOrDefault(merchandiser, new HashMap<>())
            .remove(projectConfiguration.collection());

        areas
            .getOrDefault(merchandiser, new HashMap<>())
            .remove(projectConfiguration.collection());

        deleteAreasByCondition(
            merchandiser,
            area -> area.collectionId().equals(projectConfiguration.id())
        );
    }

    /**
     * Used to delete area from {@link ConfigurationManager#areasById} also delete
     * all configurations related to this area.
     *
     * @param merchandiser Represents a merchandiser (GroupBy inc.'s customer).
     * @param condition    condition by which area has to be deleted.
     */
    private void deleteAreasByCondition(Merchandiser merchandiser,
                                        Predicate<AreaConfiguration> condition) {
        var ids = areasById
            .values()
            .stream()
            .filter(condition)
            .map(AreaConfiguration::id)
            .toList();

        ids.forEach(areasById::remove);

        var keys = ids
            .stream()
            .map(id -> new ConfigKey(merchandiser, id))
            .toArray(ConfigKey[]::new);

        deleteByAreaKeys(keys);
    }

    /**
     * Common method to handle pubsub messages for area-based configurations which
     * are required to be stored according their priority.
     *
     * @param configs     list with area-based configurations.
     * @param tenantsById map with available tenants grouped by theis IDs.
     * @param areasById   map with configured areas grouped by theirs IDs.
     * @param cache       map with configurations which need to be changed.
     *
     * @param <T> type of area-based configuration.
     */
    private static <T extends Prioritized<T>> void updatePrioritizedConfig(
        List<T> configs,
        Map<Integer, TenantConfiguration> tenantsById,
        Map<Integer, AreaConfiguration> areasById,
        Map<ConfigKey, TreeSet<T>> cache
    ) {
        configs.forEach(c -> {
            var configKey = createConfigKey(tenantsById, areasById, c.areaId());
            var set = new TreeSet<>(cache.getOrDefault(configKey, new TreeSet<>()));
            switch (c.messageType()) {
                case UPDATE -> {
                    if (set.stream().anyMatch(t -> t.id().equals(c.id()))) {
                        set.removeIf(t -> t.id().equals(c.id()));
                        set.add(c);
                    } else {
                        log.warn("Update of config with ID#{} is skipped because was not added before.", c.id());
                    }
                }
                case DELETE -> set.removeIf(t -> t.id().equals(c.id()));
                case CREATE -> {
                    set.removeIf(t -> t.id().equals(c.id()));
                    set.add(c);
                }
                default -> log.warn("Unrecognised message: {}", c);
            }
            cache.remove(configKey);
            if (!set.isEmpty()) {
                cache.put(configKey, set);
            }
        });
    }

    /**
     * Common method to handle pubsub messages for area-based configurations.
     *
     * @param configs     list with area-based configurations.
     * @param tenantsById map with available tenants grouped by theis IDs.
     * @param areasById   map with configured areas grouped by theirs IDs.
     * @param cache       map with configurations which need to be changed.
     *
     * @param <T> type of area-based configuration.
     */
    private static <T extends Config> void updateConfig(List<T> configs,
                                                        Map<Integer, TenantConfiguration> tenantsById,
                                                        Map<Integer, AreaConfiguration> areasById,
                                                        Map<ConfigKey, Map<Integer, T>> cache) {
        configs.forEach(c -> {
            var type = c.messageType();
            var configKey = createConfigKey(tenantsById, areasById, c.areaId());
            var map = new HashMap<>(cache.getOrDefault(configKey, new HashMap<>()));
            switch (type) {
                case UPDATE, CREATE -> map.put(c.id(), c);
                case DELETE -> map.remove(c.id());
                default -> log.warn("Unrecognised message: {}", c);
            }
            cache.remove(configKey);
            if (!map.isEmpty()) {
                cache.put(configKey, map);
            }
        });
    }

}
