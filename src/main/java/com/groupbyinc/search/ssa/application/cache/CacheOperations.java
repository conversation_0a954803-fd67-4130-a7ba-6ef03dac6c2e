package com.groupbyinc.search.ssa.application.cache;

import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.features.FeatureFlag;
import com.groupbyinc.search.ssa.redis.CacheResponse;
import com.groupbyinc.search.ssa.redis.CacheSource;

import com.fasterxml.jackson.core.type.TypeReference;

public interface CacheOperations {

    <C extends CacheConfig & FeatureFlag, T> CacheResponse<C, T> getFromCache(Cache cache,
                                                                              CacheKeyGenerator<C> cacheKeyGenerator,
                                                                              SearchParameters searchParameters,
                                                                              TypeReference<T> type,
                                                                              C config,
                                                                              CacheSource source);

    <C extends CacheConfig, T> void saveToCache(CacheResponse<C, T> response, CacheSource source, T data);

}
