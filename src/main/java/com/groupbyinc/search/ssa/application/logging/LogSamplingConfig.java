package com.groupbyinc.search.ssa.application.logging;

import com.groupbyinc.search.ssa.features.FeatureFlag;

import io.micronaut.core.annotation.Nullable;

import java.util.Map;
import java.util.Optional;

/**
 * Configuration for log sampling.
 *
 * @param rates The map of keys to sampling rates.
 */
public record LogSamplingConfig(Map<String, Double> rates) implements FeatureFlag {

    public LogSamplingConfig(@Nullable Map<String, Double> rates) {
        if (rates == null) {
            rates = Map.of();
        }
        this.rates = rates;
    }

    private static final String DEFAULT_RATE_KEY = "default";
    public static final LogSamplingConfig DEFAULT = new LogSamplingConfig(Map.of());

    /**
     * Get the sampling rate for the given key or the default rate if the key is not found.
     *
     * @param key The key to get the rate for.
     * @return The sampling rate for the key, or the default rate if the key is not found.
     */
    public Optional<Double> getRateForKeyOrDefault(String key) {
        return Optional.ofNullable(rates.get(key))
            .or(() -> Optional.ofNullable(rates.get(DEFAULT_RATE_KEY)));
    }
}
