package com.groupbyinc.search.ssa.application.utils;

import com.fasterxml.jackson.databind.util.StdConverter;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

public class LongToLocalDateTimeConverter extends StdConverter<Long, LocalDateTime> {

    public LocalDateTime convert(final Long value) {
        return Instant.ofEpochMilli(value).atZone(ZoneOffset.UTC).toLocalDateTime();
    }
}
