package com.groupbyinc.search.ssa.application.core.search.strategy.base;

import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngineSelector;
import com.groupbyinc.search.ssa.application.core.search.strategy.SearchStrategy;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.productcatalog.ProductCatalogService;
import com.groupbyinc.search.ssa.util.debug.StrategyNameContext;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.propagation.PropagatedContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.ParametersAreNonnullByDefault;

@Slf4j
@RequiredArgsConstructor
@ParametersAreNonnullByDefault
public class DefaultSearchStrategy extends SearchStrategy {

    private final SearchEngineSelector searchEngineSelector;
    private final ProductCatalogService productCatalogService;

    /**
     * The search strategy that has a post-processing using data-catalog-fetch.
     * Typically, im means that we request product IDs from a Google retail and
     * then fill returned products with attributes received from data-catalog-fetch.
     * <p>
     * Flow: google -> data-catalog-fetch -> response
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return {@link SearchResults} result of search call.
     */
    @NonNull
    @Override
    public SearchResults search(SearchParameters searchParameters) {
        log.debug("DefaultSearchStrategy used for search.");

        try (var ignored = PropagatedContext.getOrEmpty().plus(new StrategyNameContext(name())).propagate()) {
            var response = searchEngineSelector.getEngine(searchParameters).search(searchParameters);

            response.getSearchStrategies().add(name());

            return productCatalogService.fillProductsWithMetadata(searchParameters, response);
        }
    }

    /**
     * Define is this search strategy have to be used within the current search request or not.
     * This strategy applied when request it is a product search request, and data-catalog-fetch
     * feature flag enabled.
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return true if this strategy has to be applied, false otherwise.
     */
    @Override
    public boolean isApplicable(SearchParameters searchParameters) {
        return true;
    }

}
