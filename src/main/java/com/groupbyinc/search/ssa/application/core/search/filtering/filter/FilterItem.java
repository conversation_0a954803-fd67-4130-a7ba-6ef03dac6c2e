package com.groupbyinc.search.ssa.application.core.search.filtering.filter;

import com.groupbyinc.search.ssa.core.navigation.Range;
import io.micronaut.core.annotation.Nullable;
import lombok.Builder;
import lombok.Getter;

import java.util.Objects;

import static java.util.Objects.requireNonNullElse;

/**
 * Used to represent one filter which will be converted into expression.
 */
@Getter
public class FilterItem {

    @Nullable
    private final String value;

    @Nullable
    private final Range range;

    /**
     * if true – expression has to be negated.
     */
    private final boolean exclude;

    @Nullable
    private final Double numberValue;

    @Builder
    public FilterItem(@Nullable String value,
                      @Nullable Range range,
                      @Nullable Boolean exclude,
                      @Nullable Double numberValue) {
        this.value = value;
        this.range = range;
        this.exclude = requireNonNullElse(exclude, false);
        this.numberValue = numberValue;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof FilterItem that)) {
            return false;
        }
        return exclude == that.exclude
            && Objects.equals(value, that.value)
            && Objects.equals(range, that.range)
            && Objects.equals(numberValue, that.numberValue);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value, range, exclude, numberValue);
    }

}
