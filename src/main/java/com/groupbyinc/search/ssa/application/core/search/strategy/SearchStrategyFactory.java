package com.groupbyinc.search.ssa.application.core.search.strategy;

import java.util.List;

import com.groupbyinc.search.ssa.application.core.pintotop.PinToTopService;
import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngineSelector;
import com.groupbyinc.search.ssa.application.core.search.strategy.base.DefaultSearchStrategy;
import com.groupbyinc.search.ssa.application.core.search.strategy.fallback.FallbackSearchStrategy;
import com.groupbyinc.search.ssa.application.core.search.strategy.multiproduct.MultiProductSearchService;
import com.groupbyinc.search.ssa.application.core.search.strategy.multiproduct.MultiProductSearchStrategy;
import com.groupbyinc.search.ssa.application.core.search.strategy.partnumber.PartNumberSearchStrategy;
import com.groupbyinc.search.ssa.application.core.search.strategy.pintotop.PinToTopSearchStrategy;
import com.groupbyinc.search.ssa.application.core.search.strategy.topsort.TopSortSearchStrategy;
import com.groupbyinc.search.ssa.application.core.search.strategy.topsort.TopSortSearchStrategyV2;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.partnumber.PartNumberSearchService;
import com.groupbyinc.search.ssa.productcatalog.ProductCatalogService;
import com.groupbyinc.search.ssa.topsort.TopSortService;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.annotation.Order;
import jakarta.inject.Named;

/**
 * A factory to create {@link SearchStrategy} beans.
 * Take note that each bean has an order annotation, so we can control an order
 * how search strategies
 * are applied to the search request.
 * <p>
 * NOTE: make sure that {@code defaultSearchStrategy} is always the last one.
 */
@Factory
public class SearchStrategyFactory {

    @Context
    @Order(1)
    public SearchStrategy fallbackSearchStrategy() {
        return new FallbackSearchStrategy();
    }

    @Context
    @Order(2)
    public SearchStrategy partNumberSearchStrategy(PinToTopService pinToTopServiceForPartNumber,
                                                   PartNumberSearchService partNumberSearchService,
                                                   FeaturesManager featuresManager,
                                                    ProductCatalogService productCatalogService,
                                                   @Value("${mongo.part-number.index-delay-seconds}")
                                                   int partNumberIndexDelaySeconds) {

        pinToTopServiceForPartNumber.setRegularSearchExecutor(partNumberSearchService::search);
        var pinToTopStrategyForPartNumber = new PinToTopSearchStrategy(featuresManager, pinToTopServiceForPartNumber);

        var partNumberSearchStrategy = new PartNumberSearchStrategy(
            partNumberSearchService, productCatalogService, featuresManager, partNumberIndexDelaySeconds
        );
        return partNumberSearchStrategy.link(List.of(pinToTopStrategyForPartNumber));
    }

    @Context
    @Order(3)
    public SearchStrategy multiProductSearchStrategy(MultiProductSearchService multiProductSearchService,
                                                     FeaturesManager featuresManager) {
        return new MultiProductSearchStrategy(featuresManager, multiProductSearchService);
    }

    @Context
    @Order(4)
    public SearchStrategy topSortSearchStrategyV2(@Named("pinToTopSearchStrategy") SearchStrategy pinToTopStrategy,
                                                  @Named("defaultSearchStrategy") SearchStrategy defaultSearchStrategy,
                                                  TopSortService topsortService,
                                                  FeaturesManager featuresManager,
                                                  ProductCatalogService productCatalogService) {
        return new TopSortSearchStrategyV2(topsortService, featuresManager, productCatalogService)
            .link(List.of(pinToTopStrategy, defaultSearchStrategy));
    }

    @Context
    @Order(5)
    public SearchStrategy topSortSearchStrategy(@Named("pinToTopSearchStrategy") SearchStrategy pinToTopStrategy,
                                                @Named("defaultSearchStrategy") SearchStrategy defaultSearchStrategy,
                                                TopSortService topsortService,
                                                FeaturesManager featuresManager,
                                                ProductCatalogService productCatalogService) {
        return new TopSortSearchStrategy(topsortService, featuresManager, productCatalogService)
            .link(List.of(pinToTopStrategy, defaultSearchStrategy));
    }

    @Context
    @Order(6)
    @Named("pinToTopSearchStrategy")
    public SearchStrategy pinToTopSearchStrategy(PinToTopService pinToTopService,
                                                 FeaturesManager featuresManager) {
        return new PinToTopSearchStrategy(featuresManager, pinToTopService);
    }

    @Context
    @Order(7)
    @Named("defaultSearchStrategy")
    public SearchStrategy defaultSearchStrategy(ProductCatalogService productCatalogService,
                                                @Named("default") SearchEngineSelector searchEngineSelector) {
        return new DefaultSearchStrategy(searchEngineSelector, productCatalogService);
    }

    @Context
    SearchStrategyPriorityList searchStrategyPriorityList(List<SearchStrategy> searchStrategies) {
        return new SearchStrategyPriorityList(searchStrategies);
    }

}
