package com.groupbyinc.search.ssa.application.core.search.processor;

import com.groupbyinc.search.ssa.core.SearchParameters;

public interface Processor<T> {

    /**
     * Process request using configuration-based processors.
     *
     * @param searchParameters to modify.
     *
     * @return object representing a result of processing.
     */
    T process(SearchParameters searchParameters);

}
