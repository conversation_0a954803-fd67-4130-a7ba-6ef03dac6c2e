package com.groupbyinc.search.ssa.application.core.search.processor.rule;

import com.groupbyinc.search.ssa.application.Clock;
import com.groupbyinc.search.ssa.application.logging.LoggingContext;
import com.groupbyinc.search.ssa.application.core.search.processor.Processor;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.rule.ExperimentVariant;
import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
import com.groupbyinc.search.ssa.core.rule.RuleTriggerResult;

import com.groupbyinc.search.ssa.util.abtest.ABTestUtils;

import io.micronaut.context.annotation.Context;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.Optional;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.core.rule.RuleTriggerResult.NOT_TRIGGERED;

@Slf4j
@Context
@RequiredArgsConstructor
public class RuleProcessor implements Processor<RuleProcessorResult> {

    private final Clock clock;

    /**
     * Process the search parameters, seeing if any rules trigger and incorporating
     * their configuration where applicable.
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return processing result containing information about processing of the rules and if one triggered.
     */
    public RuleProcessorResult process(SearchParameters searchParameters) {
        var result = preProcess(searchParameters);
        logStatistics(result);
        return result;
    }

    public RuleProcessorResult preProcess(SearchParameters searchParameters) {
        var hasNoBiasingProfiles = searchParameters.getBiasingProfile() == null;
        var hasNoSorts = searchParameters.getSorts().isEmpty();
        var hasNoIncludedNavigations = searchParameters.getIncludedNavigations().isEmpty();
        var hasNoExcludedNavigations = searchParameters.getExcludedNavigations().isEmpty();

        if (hasNoBiasingProfiles || hasNoSorts || (hasNoIncludedNavigations && hasNoExcludedNavigations)) {
            return processRules(searchParameters);
        }

        return RuleProcessorResult.DEFAULT;
    }

    private RuleProcessorResult processRules(SearchParameters searchParameters) {
        var rules = searchParameters.getMerchandisingConfiguration().ruleConfigurations();

        int numRulesProcessed = 0;
        Optional<TriggeredRule> triggeredRule = Optional.empty();

        var startTime = clock.now();
        for (var rule : rules) {
            numRulesProcessed++;

            var ruleTriggerResult = triggerRule(rule, searchParameters);
            if (ruleTriggerResult.triggered()) {
                triggeredRule = Optional.of(
                    new TriggeredRule(rule, ruleTriggerResult.triggerSet(), Optional.empty())
                );
                break;
            }
        }
        var processingDuration = Duration.between(startTime, clock.now());

        var resultBuilder = RuleProcessorResult
            .builder()
            .numTotalRules(rules.size())
            .numRulesProcessed(numRulesProcessed)
            .processingDuration(processingDuration);


        if (triggeredRule.isEmpty()) {
            return RuleProcessorResult.DEFAULT;
        }

        if (triggeredRule.get().rule().isRuleInExperiment()) {
            return processExperimentRule(triggeredRule.get(), searchParameters, resultBuilder);
        }

        return resultBuilder.triggeredRule(triggeredRule).build();
    }

    private RuleTriggerResult triggerRule(RuleConfiguration rule, SearchParameters searchParameters) {
        if (!rule.isActive(clock.now())) {
            return NOT_TRIGGERED;
        }

        return rule.triggersOn(searchParameters);
    }

    private RuleProcessorResult processExperimentRule(TriggeredRule triggeredRule,
                                                      SearchParameters searchParameters,
                                                      RuleProcessorResult.RuleProcessorResultBuilder result) {
            var triggeredVariant = defineTriggeredVariant(triggeredRule.rule(), searchParameters);
            log.info("Triggered Rule Variant '{}'.", triggeredVariant.getName());

            var rule = createRuleFromChosenVariant(
                triggeredRule,
                triggeredVariant
            );

        return result.triggeredRule(Optional.of(rule)).build();
    }

    private ExperimentVariant defineTriggeredVariant(RuleConfiguration rule, SearchParameters searchParameters) {
        var uniqueUserId = getRequestContext().getUniqueUserId();

        if (searchParameters.getMerchandisingConfiguration().features().enableTrafficSplit()) {
            return rule.getTriggeredVariant(uniqueUserId);
        } else {
            @SuppressWarnings("deprecation")
            int variantIndex = ABTestUtils.abRandomizer(uniqueUserId, rule.getVariants().size());
            return rule.getVariants().get(variantIndex);
        }
    }

    private TriggeredRule createRuleFromChosenVariant(TriggeredRule triggeredRule,
                                                      ExperimentVariant experimentVariant) {
        var rule = triggeredRule.rule();
        var variant = experimentVariant.getRuleVariant();

        var newRule = RuleConfiguration.builder()
            .id(rule.id())
            .name(rule.getName())
            .areaId(rule.areaId())
            .priority(rule.getPriority())
            .activeHoursEnabled(rule.activeHoursEnabled())
            .activeFrom(rule.activeFrom())
            .activeTo(rule.activeTo())
            .triggerSets(rule.getTriggerSets())
            .biasingProfileName(variant.biasingProfileName())
            .includedNavigations(variant.includedNavigations())
            .searchFilters(variant.searchFilters())
            .attributeFilters(variant.attributeFilters())
            .productIdFilter(variant.productIdFilter())
            .template(variant.template())
            .boostedProductBuckets(variant.boostedProductBuckets())
            .buriedProductBuckets(variant.buriedProductBuckets())
            .pinnedRefinements(variant.pinnedRefinements())
            .pinnedProducts(variant.pinnedProducts())
            .productVisibilityBias(variant.productVisibilityBias())
            .messageType(rule.messageType())
            .type(rule.getType())
            .variants(rule.getVariants())
            .build();

        return new TriggeredRule(newRule, triggeredRule.triggerSet(), Optional.of(experimentVariant.getName()));
    }

    private void logStatistics(RuleProcessorResult result) {
        log.debug(
            "Processed {} out of {} rules in {}.",
            result.numRulesProcessed(),
            result.numTotalRules(),
            result.processingDuration()
        );

        if (result.triggeredRule().isPresent()) {
            LoggingContext.setRuleContext(result.triggeredRule().get().rule());
            log.debug("Triggered rule '{}'.", result.triggeredRule().get().rule().getName());
        } else {
            log.debug("No rule was triggered.");
        }
    }

}
