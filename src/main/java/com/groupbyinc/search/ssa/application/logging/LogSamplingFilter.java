package com.groupbyinc.search.ssa.application.logging;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.filter.Filter;
import ch.qos.logback.core.spi.FilterReply;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;

import static com.groupbyinc.search.ssa.application.logging.LoggingContext.REQUEST_SAMPLED_MDC_KEY;

public class LogSamplingFilter extends Filter<ILoggingEvent> {

    /**
     * Marker to indicate that a log line will be considered for sampling
     * and should only be logged if the request is sampled.
     */
    public static final Marker SAMPLE_MARKER = MarkerFactory.getMarker("evaluate_sampling");

    @Override
    public FilterReply decide(ILoggingEvent event) {
        var mdc = event.getMDCPropertyMap();

        var requestSampled = mdc.get(REQUEST_SAMPLED_MDC_KEY);
        var acceptLog = requestSampled == null || Boolean.parseBoolean(requestSampled);

        var hasSampleMarker = event.getMarkerList() != null && event.getMarkerList().contains(SAMPLE_MARKER);

        if (hasSampleMarker && !acceptLog) {
            return FilterReply.DENY;
        }

        // allow it to pass
        return FilterReply.NEUTRAL;
    }
}
