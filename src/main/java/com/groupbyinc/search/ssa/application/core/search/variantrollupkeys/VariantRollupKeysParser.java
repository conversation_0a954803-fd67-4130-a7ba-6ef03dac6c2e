package com.groupbyinc.search.ssa.application.core.search.variantrollupkeys;


import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;

import jakarta.inject.Singleton;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.application.core.search.converter.AbstractFacetConverter.INVENTORIES_PREFIX;
import static com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKey.RollupKeyType.FULFILLMENT_INFO;
import static com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKey.RollupKeyType.INVENTORY_ATTRIBUTE;
import static com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupKey.RollupKeyType.VARIANT_ATTRIBUTE;
import static com.groupbyinc.search.ssa.util.StringUtils.removeWhitespaces;

@Singleton
public class VariantRollupKeysParser {

    // Mapping from rollup key prefixes to FulfillmentInfo types
    public static final Map<String, String> FULFILLMENT_KEY_TO_TYPE = Map.of(
        "pickupInStore", "pickup-in-store",
        "shipToStore", "ship-to-store",
        "sameDayDelivery", "same-day-delivery",
        "nextDayDelivery", "next-day-delivery",
        "customFulfillment1", "custom-type-1",
        "customFulfillment2", "custom-type-2",
        "customFulfillment3", "custom-type-3",
        "customFulfillment4", "custom-type-4",
        "customFulfillment5", "custom-type-5"
    );

    // Mapping from allowed variant rollup keys that are not attributes to the Document fields
    public static final Map<String, String> ALLOWED_SIMPLE_KEYS_TO_PATH = Map.of(
        "variantId", "id"
    );
    private static final Pattern INVENTORY_ROLLUP_KEY_PATTERN = Pattern.compile("inventory\\(([^,]+),(.+)\\)");
    private static final String INVENTORY_PREFIX = "inventory(";

    /**
     * Parse a list of variant rollup keys.
     *
     * @param variantRollupKeys List of variant rollup keys strings
     * @param attributes        Attributes configuration
     *
     * @return List of VariantRollupKeys parsed from the input strings
     * @throws IllegalArgumentException if any of the keys are invalid
     */
    public List<VariantRollupKey> parseVariantRollupKeys(List<String> variantRollupKeys,
                                                         Map<String, AttributeConfiguration> attributes) {

        Set<VariantRollupKey> result = new HashSet<>();
        Set<String> invalidKeys = new HashSet<>();

        for (String key : variantRollupKeys) {
            var parsedKey = tryParseKey(removeWhitespaces(key), attributes);
            if (parsedKey.isPresent()) {
                result.add(parsedKey.get());
            } else {
                invalidKeys.add(key);
            }
        }

        if (!invalidKeys.isEmpty()) {
            getRequestContext().addWarning(
                "Unsupported variant rollup keys: '%s'".formatted(String.join("; ", invalidKeys))
            );
        }

        return List.copyOf(result);
    }

    private Optional<VariantRollupKey> tryParseKey(String key,
                                                   Map<String, AttributeConfiguration> attributesConfiguration) {
        if (StringUtils.isBlank(key)) {
            return Optional.empty();
        }

        if (ALLOWED_SIMPLE_KEYS_TO_PATH.containsKey(key)) { // Simple Variant rollup key
            return Optional.of(
                new VariantRollupKey(
                    key,
                    VARIANT_ATTRIBUTE,
                    key,
                    ALLOWED_SIMPLE_KEYS_TO_PATH.get(key),
                    null)
            );

        } else if (key.startsWith(INVENTORY_PREFIX)) { // Inventory attribute key
            return getInventoryRollupKey(key, attributesConfiguration);

        } else if (attributesConfiguration.containsKey(key)) { // Variant attribute key
            return Optional.of(
                new VariantRollupKey(
                    key,
                    VARIANT_ATTRIBUTE,
                    key,
                    attributesConfiguration.get(key).path(),
                    null)
            );

        } else { // FulfillmentInfo key or invalid key
            return getFulfillmentInfoRollupKey(key);
        }
    }

    private Optional<VariantRollupKey> getInventoryRollupKey(String key,
                                                             Map<String, AttributeConfiguration> attributesConfig) {
        var matcher = INVENTORY_ROLLUP_KEY_PATTERN.matcher(key);
        if (matcher.matches()) {
            var placeId = matcher.group(1);
            var attribute = matcher.group(2);
            attribute = attribute.trim();
            if (inventoryAttributeIsValid(attribute, attributesConfig)) {
                var path = attributesConfig
                    .get(INVENTORIES_PREFIX + attribute)
                    .path()
                    .substring(INVENTORIES_PREFIX.length());
                return Optional.of(new VariantRollupKey(key, INVENTORY_ATTRIBUTE, attribute, path, placeId));
            } else {
                // Invalid inventory attribute
                return Optional.empty();
            }
        } else {
            // Invalid inventory key format
            return Optional.empty();
        }
    }

    private Optional<VariantRollupKey> getFulfillmentInfoRollupKey(String key) {
        String[] parts = key.split("\\.");
        if (parts.length == 2) {
            var fulfillment = parts[0];
            var placeId = parts[1];
            var fulfillmentType = FULFILLMENT_KEY_TO_TYPE.get(fulfillment);
            if (fulfillmentType != null) {
                return Optional.of(
                    new VariantRollupKey(key, FULFILLMENT_INFO, fulfillment, fulfillmentType, placeId)
                );
            } else {
                // Invalid fulfillment type
                return Optional.empty();
            }
        } else {
            // Invalid key
            return Optional.empty();
        }
    }

    private boolean inventoryAttributeIsValid(String attribute,
                                              Map<String, AttributeConfiguration> attributesConfig) {
        var attributeKey = INVENTORIES_PREFIX + attribute;
        return attributesConfig.containsKey(attributeKey);
    }

}
