package com.groupbyinc.search.ssa.application.core.search;

import com.groupbyinc.search.ssa.api.builders.SearchResponseBuilder;
import com.groupbyinc.search.ssa.api.dto.FacetSearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.FacetSearchResponseDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SearchResponseDto;
import com.groupbyinc.search.ssa.api.error.ProcessingException;
import com.groupbyinc.search.ssa.api.event.SearchSuccessEvent;
import com.groupbyinc.search.ssa.application.Clock;
import com.groupbyinc.search.ssa.application.builders.SearchParametersBuilder;
import com.groupbyinc.search.ssa.application.core.search.processor.redirect.RedirectProcessor;
import com.groupbyinc.search.ssa.application.core.search.processor.rule.RuleProcessor;
import com.groupbyinc.search.ssa.application.core.search.processor.rule.RuleProcessorResult;
import com.groupbyinc.search.ssa.application.core.search.processor.rule.RuleSearchParametersUpdater;
import com.groupbyinc.search.ssa.application.core.search.productvisibility.ProductVisibilityBooster;
import com.groupbyinc.search.ssa.application.core.search.strategy.SearchStrategyPriorityList;
import com.groupbyinc.search.ssa.application.logging.LoggingContext;
import com.groupbyinc.search.ssa.core.Rewrites;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.navigation.Facet;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.core.rule.RuleTemplate;
import com.groupbyinc.search.ssa.core.rule.RuleTemplateSection;
import com.groupbyinc.search.ssa.core.template.Template;
import com.groupbyinc.search.ssa.core.template.zone.TemplateZoneBase;
import com.groupbyinc.search.ssa.core.template.zone.TemplateZoneContent;
import com.groupbyinc.search.ssa.core.template.zone.TemplateZoneHTML;
import com.groupbyinc.search.ssa.core.template.zone.TemplateZoneRichContent;
import com.groupbyinc.search.ssa.core.trigger.SelectedRefinementTrigger;
import com.groupbyinc.search.ssa.core.trigger.TriggerSet;
import com.groupbyinc.search.ssa.core.zone.ZoneConfiguration;
import com.groupbyinc.search.ssa.metrics.opentelemetry.interceptors.ApiLatencyMetricsCollector;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.micronaut.context.annotation.Context;
import io.micronaut.context.event.ApplicationEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.application.logging.LogSamplingFilter.SAMPLE_MARKER;
import static com.groupbyinc.search.ssa.core.SearchMode.FACETED_SEARCH;
import static com.groupbyinc.search.ssa.core.SearchMode.PRODUCT_SEARCH;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.ContextUtils.attachContextForFacetSearch;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.ContextUtils.attachContextForSearch;
import static com.groupbyinc.search.ssa.util.StringUtils.safeWriteToJsonString;

import static java.util.stream.Collectors.toMap;

/**
 * An application level service/orchestrator for calling a specific search adapter via the search engine interface.
 */
@Slf4j
@Context
@RequiredArgsConstructor
public class SearchService {

    private final Clock clock;
    private final ObjectMapper objectMapper;
    private final RuleProcessor ruleProcessor;
    private final RedirectProcessor redirectProcessor;
    private final SearchStrategyPriorityList searchStrategies;
    private final SearchResponseBuilder searchResponseBuilder;
    private final SearchParametersBuilder searchParametersBuilder;
    private final ProductVisibilityBooster productVisibilityBooster;
    private final RuleSearchParametersUpdater ruleSearchParametersUpdater;
    private final ApplicationEventPublisher<SearchSuccessEvent> applicationEventPublisher;

    @ApiLatencyMetricsCollector
    public SearchResponseDto search(SearchRequestDto request) {
        var context = getRequestContext();
        LoggingContext.set(context, PRODUCT_SEARCH.name());

        log.info(
            SAMPLE_MARKER,
            "Performing search. id: {}, visitorId: {}, options: {}, request: {}",
            context.getRequestId(),
            context.getVisitorId(),
            context.getRequestOptions(),
            safeWriteToJsonString(objectMapper,
                request.toBuilder()
                    .loginId(context.getLoginId())
                    .userAttributes(context.getUserAttributes())
                    .build(),
                log)
        );

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            request,
            PRODUCT_SEARCH,
            Facet.EMPTY
        );

        var searchResults = searchInternal(searchParameters);

        var result = searchResponseBuilder.buildSearchResponseDto(
            searchParameters,
            searchResults,
            request
        );

        if (isDirectSearchBeaconRequired(result)) {
            applicationEventPublisher.publishEventAsync(new SearchSuccessEvent(context, result));
        }

        log.info(
            SAMPLE_MARKER,
            "Search result: {}, cached: {}",
            safeWriteToJsonString(objectMapper, result.log(), log),
            searchResults.getMetadata().isCached()
        );
        attachContextForSearch(context.getMerchandiser(), context.getCollection(), result);

        // hide technical fields explicitly (we cannot use @JsonIgnore, since the same object is serialized in beacon)
        // this was done as quick change with no refactoring to make it safer in short notice
        return result.toBuilder()
            .searchStrategies(null)
            .engineSource(null)
            .build();
    }

    @ApiLatencyMetricsCollector
    public FacetSearchResponseDto facetSearch(FacetSearchRequestDto request) {
        var context = getRequestContext();
        LoggingContext.set(context, FACETED_SEARCH.name());

        var requestForLog = request.originalRequest().toBuilder().loginId(context.getLoginId()).build();

        log.info(
            "Performing facet search. id: {}, visitorId: {}, options: {}, request: {}",
            context.getRequestId(),
            context.getVisitorId(),
            context.getRequestOptions(),
            safeWriteToJsonString(objectMapper, request.toBuilder().originalRequest(requestForLog).build(), log)
        );

        var searchParameters = searchParametersBuilder.buildSearchParameters(
            request.originalRequest(),
            FACETED_SEARCH,
            request.facet()
        );

        var searchResults = searchInternal(searchParameters);

        var result = searchResponseBuilder.buildFacetSearchResponseDto(request, searchResults);

        log.info(
            "Facet search result: {}, cached: {}",
            safeWriteToJsonString(objectMapper, result.toBuilder().originalRequest(requestForLog).build(), log),
            searchResults.getMetadata().isCached()
        );
        attachContextForFacetSearch(context.getMerchandiser(), request.originalRequest().getCollection(), result);

        return result;
    }

    private SearchResults searchInternal(SearchParameters searchParameters) {
        // Start timer for search processing
        var searchStartTime = clock.now();

        // See if a redirect is resolved and return early
        var redirectProcessorResult = redirectProcessor.process(searchParameters);
        if (redirectProcessorResult.isPresent()) {
            return redirectProcessorResult.get();
        }

        var ruleResult = ruleProcessor.process(searchParameters);
        ruleSearchParametersUpdater.updateSearchParametersByRule(searchParameters, ruleResult);

        searchParameters.setSelectedRefinementTriggers(getSelectedRefinementTriggers(ruleResult));

        var searchResults = searchStrategies.getApplicableSearchStrategy(searchParameters)
            .orElseThrow(() -> new ProcessingException("Applicable search strategy not found."))
            .search(searchParameters);

        var boostingResult = productVisibilityBooster.boostProducts(ruleResult, searchResults.getRecords());

        if (boostingResult.reranked()) {
            searchResults.setRecords(boostingResult.rerankedRecords());

            searchResults.getRewrites().add(Rewrites.VISIBILITY.name());

            getRequestContext().getDebugDetails().setVisibilityScore(boostingResult.score());
        }

        addStaticData(searchResults, searchParameters, ruleResult);
        searchResults.getMetadata().setTotalTime(Duration.between(searchStartTime, clock.now()));

        return searchResults;
    }

    private void addStaticData(SearchResults searchResults,
                               SearchParameters searchParameters,
                               RuleProcessorResult ruleResult) {
        // Add area metadata as "siteParams" to the response
        var area = searchParameters.getMerchandisingConfiguration().areaConfiguration();
        if (area != null) {
            searchResults.setSiteParams(area.metadata());
        }

        if (PRODUCT_SEARCH == searchParameters.getSearchMode() && ruleResult.triggeredRule().isPresent()) {
            var rule = ruleResult.triggeredRule().get().rule();

            // Create the template from the triggered rule
            var template = Template.ofRule(rule);

            var shouldResolveZones = shouldResolveTemplateZones(
                rule.getTemplate(),
                searchParameters.getRefinements(),
                ruleResult.triggeredRule().get().triggerSet()
            );

            if (shouldResolveZones) {
                // fill template zones
                resolveZones(
                    template,
                    rule.getTemplate(),
                    getZones(searchParameters)
                );
            }

            template.setTriggerSet(ruleResult.triggeredRule().get().triggerSet());
            searchResults.setTemplate(template);
            searchResults.setRuleVariantName(searchParameters.getRuleVariantName());
        }
    }

    private Map<Integer, ZoneConfiguration> getZones(SearchParameters searchParameters) {
        return searchParameters.getMerchandisingConfiguration()
            .zones()
            .stream()
            .collect(toMap(ZoneConfiguration::id, Function.identity()));
    }

    /**
     * Method used to fill response "template" object with zone content.
     * <p>
     * Inside triggered rule we have only "name - id" pair which is described as
     * a zone object, so when we want to add a zone content to the response, we
     * may want to iterate over all zones in template from triggered rule, and
     * for each of them we want to extract a zone configuration based on ID and
     * then fill the response object with content from this configuration.
     *
     * @param template     template model used to build client response.
     * @param ruleTemplate template object from triggered rule.
     * @param zones        zone configurations for the current area which are stored by their IDs as a keys of map.
     */
    private void resolveZones(Template template, RuleTemplate ruleTemplate, Map<Integer, ZoneConfiguration> zones) {
        Optional.ofNullable(ruleTemplate).ifPresent(
            value -> template.addZones(
                value.sections()
                    .stream()
                    .filter(section -> zones.containsKey(section.zoneId()) || section.zoneContent() != null)
                    .map(section ->
                        createZone(
                            zones.get(section.zoneId()),
                            section.name(),
                            section
                        )
                    )
                    .filter(Objects::nonNull)
                    .toList()
            )
        );
    }

    private TemplateZoneBase createZone(ZoneConfiguration config, String name, RuleTemplateSection section) {
        if (config == null) {
            return switch (section.zoneType()) {
                case CONTENT -> new TemplateZoneContent(name, section.zoneType(), section.zoneContent());
                case RICH_CONTENT -> new TemplateZoneRichContent(name, section.zoneType(), section.zoneContent());
                case HTML -> new TemplateZoneHTML(name, section.zoneType(), section.zoneContent());
                default -> null;
            };
        }

        return switch (config.zoneType()) {
            case CONTENT -> new TemplateZoneContent(name, config.zoneType(), config.value());
            case RICH_CONTENT -> new TemplateZoneRichContent(name, config.zoneType(), config.value());
            case HTML -> new TemplateZoneHTML(name, config.zoneType(), config.value());

            // For new types.
            default -> null;
        };
    }

    /*
     * Returns true when either:
     * 1) Exact matching isn't enabled in RuleTemplate
     * 2) Exact matching is enabled in RuleTemplate and there was an exact search match
     */
    private boolean shouldResolveTemplateZones(RuleTemplate ruleTemplate,
                                               Collection<SelectedRefinement> refinements,
                                               TriggerSet triggerSet) {
        if (!shouldExactMatchTemplate(ruleTemplate)) {
            return true;
        }

        Collection<SelectedRefinementTrigger> refinementTriggers = triggerSet != null
            ? triggerSet.getSelectedRefinementTriggers()
            : List.of(); // case when rule has no trigger sets.

        return isExactMatch(refinements, refinementTriggers);
    }

    private boolean shouldExactMatchTemplate(RuleTemplate template) {
        return template != null && Boolean.TRUE.equals(template.enableExactMatching());
    }

    private boolean isExactMatch(Collection<SelectedRefinement> refinements,
                                 Collection<SelectedRefinementTrigger> refinementTriggers) {
        return TriggerSet.collectionMatches(refinements, refinementTriggers, true);
    }

    private boolean isDirectSearchBeaconRequired(SearchResponseDto response) {
        var request = response.getOriginalRequest();

        // We do not want to make a direct search request when the requested records count is 0
        return (request.getPageSize() != null && request.getPageSize() > 0);
    }

    private List<SelectedRefinementTrigger> getSelectedRefinementTriggers(RuleProcessorResult ruleResult) {
        if(ruleResult.triggeredRule().isEmpty()) {
            return List.of();
        }

        var triggerSet = ruleResult.triggeredRule().get().triggerSet();
        if (triggerSet != null) {
            return triggerSet.getSelectedRefinementTriggers();
        }

        return List.of();
    }
}
