package com.groupbyinc.search.ssa.application.builders;

import com.groupbyinc.search.ssa.api.dto.BiasingProfileDto;
import com.groupbyinc.search.ssa.api.dto.CustomParameterDto;
import com.groupbyinc.search.ssa.api.dto.NavigationTypeDto;
import com.groupbyinc.search.ssa.api.dto.PinnedProductDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SelectedRefinementDto;
import com.groupbyinc.search.ssa.api.dto.SortDto;
import com.groupbyinc.search.ssa.api.dto.tiles.TilesNavigationDto;
import com.groupbyinc.search.ssa.core.CustomParameter;
import com.groupbyinc.search.ssa.core.Pagination;
import com.groupbyinc.search.ssa.core.SearchMode;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.Sort;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfile;
import com.groupbyinc.search.ssa.core.navigation.Facet;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.core.rule.PinnedProduct;
import com.groupbyinc.search.ssa.core.tiles.TilesNavigation;
import com.groupbyinc.search.ssa.retail.util.v2alpha.UserAttributesHelper;
import com.groupbyinc.search.ssa.util.PrefixTreeMap;

import io.micronaut.core.annotation.Nullable;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.ParametersAreNonnullByDefault;
import java.util.List;
import java.util.OptionalInt;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.groupbyinc.search.ssa.api.utils.SearchUtils.getRequestPageCategories;
import static com.groupbyinc.search.ssa.retail.util.ConversationUtils.isFollowupConversation;

@Slf4j
@Singleton
@RequiredArgsConstructor
@ParametersAreNonnullByDefault
public class SearchParametersBuilder {

    private final SearchParametersPreparer searchParametersPreparer;
    private final UserAttributesHelper userAttributesHelper;

    public SearchParameters buildSearchParameters(SearchRequestDto searchRequest, SearchMode mode, Facet facet) {
        var originalPagination = new Pagination(searchRequest.getPageSize(), searchRequest.getSkip());

        var searchParameters = SearchParameters.builder()
            .query(searchRequest.getQuery())
            .pagination(originalPagination)
            .originalPagination(originalPagination)
            .biasingProfileName(searchRequest.getBiasingProfile())
            .biasingProfile(convertBiasingProfileDto(searchRequest.getBiasing()))
            .customParameters(convertCustomParameterDtos(searchRequest.getCustomUrlParams()))
            .sorts(convertSortDtos(searchRequest))
            .includedNavigations(searchRequest.getIncludedNavigations())
            .excludedNavigations(searchRequest.getExcludedNavigations())
            .dynamicFacet(searchRequest.getDynamicFacet())
            .variantRollupKeys(searchRequest.getVariantRollupKeys())
            .preFilter(searchRequest.getPreFilter())
            .siteFilterName(searchRequest.getSite())
            .responseMask(searchRequest.getResponseMask())
            .prefixTreeMap(PrefixTreeMap.fromList(searchRequest.getResponseMask()))
            .spellCorrectionMode(searchRequest.getSpellCorrectionMode())
            .includeExpandedResults(searchRequest.getIncludeExpandedResults())
            .pinUnexpandedResults(searchRequest.getPinUnexpandedResults())
            .searchMode(mode)
            .overwrites(searchRequest.getOverwrites())
            .facetLimit(searchRequest.getFacetLimit())
            .pinnedProducts(convertPinnedProductsDto(searchRequest.getPinnedProducts()))
            .sponsoredRecordsRequest(searchRequest.getSponsoredRecords())
            .topSortEnabled(searchRequest.isEnableTopsort())
            .topSortV2Enabled(searchRequest.isEnableTopsortV2())
            .partNumberSearchEnabled(searchRequest.getEnablePartNumberSearch())
            .partNumberFallbackEnabled(searchRequest.getEnablePartNumberFallback())
            .partNumberExpansionEnabled(searchRequest.getEnablePartNumberExpansion())
            .inventoryStoreId(searchRequest.getInventoryStoreId())
            .facet(facet)
            .tilesNavigation(convertTilesNavigation(searchRequest.getTilesNavigation()))
            .pageCategories(getRequestPageCategories(searchRequest))
            .userAttributes(userAttributesHelper.convertUserAttributes(searchRequest.getUserAttributes()));

        populateRefinementsForConversationalSearch(searchRequest, searchParameters);
        searchParameters.refinements(defineSelectedRefinements(searchRequest, mode, facet));

        // Prepare the search parameters, by loading the configurations from cc-api
        searchParametersPreparer.prepare(searchRequest, searchParameters);

        return searchParameters.build();
    }

    private void populateRefinementsForConversationalSearch(
        SearchRequestDto searchRequest,
        SearchParameters.SearchParametersBuilder searchParameters
    ) {
        if (isFollowupConversation(searchRequest.getConversationalSearchConfig())) {
            searchParameters.conversationalSearchConfig(searchRequest.getConversationalSearchConfig().toDomain());
            if (searchRequest.getConversationalSearchConfig().userAnswer() != null
                && searchRequest.getConversationalSearchConfig().userAnswer().selectedAnswer() != null) {

                var productAttributeValue = searchRequest.getConversationalSearchConfig().userAnswer()
                    .selectedAnswer().productAttributeValue();
                var refinementFromSelectedAnswer = new SelectedRefinementDto(productAttributeValue.name(),
                    NavigationTypeDto.Value, productAttributeValue.value(), true);

                if (searchRequest.getRefinements() == null) {
                    searchRequest.setRefinements(List.of(refinementFromSelectedAnswer));
                } else if (searchRequest.getRefinements().stream()
                    .noneMatch(e -> refinementFromSelectedAnswer.navigationName().equalsIgnoreCase(e.navigationName())
                        && refinementFromSelectedAnswer.value().equalsIgnoreCase(e.value()))) {
                    searchRequest.getRefinements().add(refinementFromSelectedAnswer);
                } else {
                    OptionalInt indexOpt = IntStream.range(0, searchRequest.getRefinements().size())
                        .filter(i -> searchRequest.getRefinements().get(i).navigationName()
                            .equalsIgnoreCase(refinementFromSelectedAnswer.navigationName())
                            && searchRequest.getRefinements().get(i).value()
                            .equalsIgnoreCase(refinementFromSelectedAnswer.value()))
                        .findFirst();
                    indexOpt.ifPresent(index ->
                        searchRequest.getRefinements().set(index, refinementFromSelectedAnswer));
                }
            }
        }
    }

    private static TilesNavigation convertTilesNavigation(TilesNavigationDto tilesNavigationDto) {
        return TilesNavigation.toDomain(tilesNavigationDto);
    }

    private static List<CustomParameter> convertCustomParameterDtos(@Nullable List<CustomParameterDto> parameters) {
        if (parameters == null) {
            return List.of();
        }

        return parameters
            .stream()
            .map(CustomParameterDto::toDomain)
            .collect(Collectors.toList());
    }

    private static BiasingProfile convertBiasingProfileDto(@Nullable BiasingProfileDto biasing) {
        if (biasing == null) {
            return null;
        }

        return biasing.toDomain();
    }

    private List<SelectedRefinement> defineSelectedRefinements(
        SearchRequestDto searchRequest,
        SearchMode mode,
        @Nullable Facet facet
    ) {
        if (searchRequest.getRefinements() == null) {
            return List.of();
        }

        return searchRequest
            .getRefinements()
            .stream()
            .filter(refinement -> {
                if (mode == SearchMode.FACETED_SEARCH) {
                    // If a request selected refinements contains the same refinement as refinement for which
                    // we need to search facets, we need to exclude this selected refinement from a search request
                    // to not filter products by this refinement and make a search by all facets.
                    return facet != null && !refinement.getNavigationName().equals(facet.navigationName());
                }
                return true;
            })
            .map(SelectedRefinementDto::toDomain)
            .toList();
    }

    private static List<Sort> convertSortDtos(SearchRequestDto searchRequest) {

        if (searchRequest.getSorts() == null) {
            return List.of();
        }

        return searchRequest
            .getSorts()
            .stream()
            .map(SortDto::toDomain)
            .collect(Collectors.toList());
    }

    private List<PinnedProduct> convertPinnedProductsDto(@Nullable List<PinnedProductDto> pinnedProducts) {
        return pinnedProducts != null
            ? pinnedProducts.stream().map(PinnedProductDto::toDomain).toList()
            : null;
    }
}
