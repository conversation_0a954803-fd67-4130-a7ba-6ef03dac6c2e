package com.groupbyinc.search.ssa.application.core.search.filtering;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.Expression;
import com.groupbyinc.search.ssa.application.core.search.filtering.expression.NotExpression;
import com.groupbyinc.search.ssa.application.core.search.filtering.expression.numerical.ComparisonExpression;
import com.groupbyinc.search.ssa.application.core.search.filtering.expression.numerical.RangeExpression;
import com.groupbyinc.search.ssa.application.core.search.filtering.expression.text.TextExpression;
import com.groupbyinc.search.ssa.application.core.search.filtering.filter.Filter;
import com.groupbyinc.search.ssa.application.core.search.filtering.filter.FilterItem;
import com.groupbyinc.search.ssa.application.core.search.filtering.filter.FilterItemWrapper;
import com.groupbyinc.search.ssa.application.core.search.filtering.filter.ToFilter;
import com.groupbyinc.search.ssa.application.core.search.filtering.filter.Type;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.filter.AttributeFilter;
import com.groupbyinc.search.ssa.core.filter.ProductIdFilter;
import com.groupbyinc.search.ssa.core.filter.RangeFilter;
import com.groupbyinc.search.ssa.core.filter.ValueFilter;
import com.groupbyinc.search.ssa.core.navigation.Navigation;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.core.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import static com.groupbyinc.search.ssa.core.navigation.NavigationType.RANGE;
import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultList;

import static io.micrometer.core.instrument.util.StringEscapeUtils.escapeJson;
import static io.micronaut.core.util.CollectionUtils.isEmpty;
import static io.micronaut.core.util.CollectionUtils.isNotEmpty;
import static java.util.Objects.requireNonNull;

public abstract class FilterServiceBase<T> implements FilterService<T> {

    /**
     * Converts a collection of filter expressions, grouped by field names, into a single {@link Filter} object.
     * This method iterates over each entry in the provided map, where each key represents a field name and each
     * value is a {@link FilterItemWrapper} that encapsulates filter items for that field. Depending on the type
     * of filter (text or numeric) associated with each field, it delegates to specific methods to add these filters
     * to the {@link Filter}.
     * <p>
     * The conversion process includes:
     * - For text filters: invoking {@link #addTextExpressions(String, FilterItemWrapper, Filter)} to handle
     *   text-based filter expressions.
     * - For numeric filters: invoking {@link #addNumericExpressions(String, FilterItemWrapper, Filter)} to handle
     *   numeric-based filter expressions.
     * <p>
     * This method ensures that all filter expressions are properly integrated into a unified filter object that
     * can be applied to a search operation, facilitating complex filtering logic that may involve multiple fields
     * and types of filter criteria.
     *
     * @param itemsByField map where keys are field names and values are {@link FilterItemWrapper} instances,
     *                     each containing a collection of filter items to be converted into part of the final
     *                     filter object.
     *
     * @return A {@link Filter} object constructed from the aggregated filter items, ready to be applied to a search
     *         operation to refine results based on the specified criteria.
     */
    protected abstract Filter<T> convertFilterItemsToFilter(@NonNull Map<String, FilterItemWrapper> itemsByField);

    /**
     * Combine passed filter expressions with logical 'OR' condition.
     *
     * @param expressions to combine with logical 'OR'.
     *
     * @return filter object where passed filters are combined with logical 'OR'.
     */
    protected abstract Filter<T> combineFiltersAsOrs(List<ToFilter<T>> expressions);

    /**
     * Create a filter object for product IDs which must be included in a search.
     *
     * @param productIdFilter filter with included product IDs.
     *
     * @return filter object with included product IDs.
     */
    protected abstract Filter<T> createIncludedProductIdFilter(@NonNull ProductIdFilter productIdFilter);

    /**
     * Create a filter object for product IDs which must be excluded from a search.
     *
     * @param productIdFilter filter with excluded product IDs.
     *
     * @return filter object with excluded product IDs.
     */
    protected abstract NotExpression<T> createExcludedProductIdsFilter(@NonNull ProductIdFilter productIdFilter);

    /**
     * Build a negotiated expression for a passed expression.
     *
     * @param expression needs to be negotiated.
     *
     * @return negotiated filter expression around passed filter expression.
     */
    protected abstract NotExpression<T> buildNotExpression(Expression<T> expression);

    /**
     * Build a textual expression for the multiple values.
     *
     * @param field  facet(attribute) the expression is for.
     * @param values the field values can be.
     *
     * @return textual filter expression around passed field and values.
     */
    protected abstract TextExpression<T> buildTextExpression(@NonNull String field, @NonNull List<String> values);

    /**
     * Build a textual expression for a single value.
     *
     * @param field facet(attribute) the expression is for.
     * @param value the field value can be.
     *
     * @return textual filter expression around passed field and value.
     */
    @NonNull
    protected abstract TextExpression<T> buildSinglenessTextExpression(@NonNull String field, @NonNull String value);

    /**
     * Used to build numeric expression based on passed range.
     * <p>
     * NOTE: see: {@link RangeExpression}
     *
     * @param field facet(attribute) the expression is for.
     * @param range values the field value can be.
     *
     * @return created numeric expression.
     */
    @NonNull
    protected abstract Expression<T> buildRangeExpression(@NonNull String field, @NonNull Range range);

    /**
     * Used to build numeric expression based on passed value.
     * <p>
     * NOTE: see: {@link ComparisonExpression}
     *
     * @param field facet(attribute) the expression is for.
     * @param value single values the field value can be.
     *
     * @return created numeric expression.
     */
    @NonNull
    protected abstract Expression<T> buildComparisonExpression(@NonNull String field, @Nullable Double value);

    /**
     * Merge pre-filter and site-filter if both are present and not null or return non-null value of two of them.
     * <p>
     * NOTE: site-filter originates from two sources:
     *  #1 User supplied through request.
     *  #2 Customer configuration through ccapi.
     *
     * @param preFilter pre-filters passed to the request.
     * @param site      site-filter passed to the request or configured for area.
     *
     * @return Optional<String> with a filter object or empty.
     */
    @NonNull
    public static Optional<String> mergePreAndSiteFilters(@Nullable String preFilter,
                                                          @Nullable SiteFilterConfiguration site) {
        var preFilterCheck = preFilter != null && !preFilter.isBlank();
        var siteFilterCheck = (site != null && site.rawFilter() != null) && !site.rawFilter().isBlank();

        if (preFilterCheck && site != null) {
            var siteFilter = site.rawFilter();
            if (StringUtils.isEmpty(siteFilter) || siteFilter.isBlank()) {
                return Optional.of(preFilter);
            }
            return Optional.of(AND_JOIN_TEMPLATE.formatted(preFilter, siteFilter));
        }

        if (preFilterCheck) {
            return Optional.of(preFilter);
        }

        if (siteFilterCheck) {
            return Optional.of(site.rawFilter());
        }

        return Optional.empty();
    }

    /**
     * Enrich a passed filter object with filters from passed search parameters.
     *
     * @param filter           to add filters from search parameters.
     * @param fieldToPath      function to transform attribute filed to the path in filter object.
     * @param searchParameters containing attribute filters to be aggregated.
     */
    protected void fillFilterWithFilters(@NonNull Filter<T> filter,
                                         @NonNull Function<String, String> fieldToPath,
                                         @NonNull SearchParameters searchParameters) {
        var filters = aggregateAttributeFilters(fieldToPath, searchParameters);
        addProductIdInclusionFilter(searchParameters, filters);
        filter.addToOrs(filters);
        var exclusionAndRefinementFilters = createExclusionAndRefinementFilters(fieldToPath, searchParameters);
        if (!exclusionAndRefinementFilters.isEmpty()) {
            filter.addToAnds(exclusionAndRefinementFilters);
        }
    }

    /**
     * Aggregates attribute filters from the provided search parameters into a list of {@link ToFilter} objects.
     * This method iterates through each attribute filter in the search parameters, checks if there are any
     * value or range filters defined, and then adds these filters to the list after converting them using
     * the {@code createFilterForAttributeFilter} method. This process ensures that all relevant attribute
     * filters are included in the final filter construction for the search query.
     *
     * @param searchParameters containing attribute filters to be aggregated.
     *
     * @return A list of {@link ToFilter} objects representing the aggregated attribute filters.
     */
    protected List<ToFilter<T>> aggregateAttributeFilters(@NonNull Function<String, String> fieldToPath,
                                                          @NonNull SearchParameters searchParameters) {
        List<ToFilter<T>> filters = new ArrayList<>();
        for (var attributeFilter : searchParameters.getAttributeFilters()) {
            var valueFilters = notNullOrDefaultList(attributeFilter.valueFilters());
            var rangeFilters = notNullOrDefaultList(attributeFilter.rangeFilters());

            if (!valueFilters.isEmpty() || !rangeFilters.isEmpty()) {
                filters.add(createFilterForAttributeFilter(valueFilters, rangeFilters, fieldToPath));
            }
        }
        return filters;
    }

    /**
     * Creates a {@link Filter} object for a given {@link AttributeFilter}. This method processes both range and value
     * filters defined within the {@link AttributeFilter} and consolidates them into a single {@link Filter} object.
     * This consolidation allows for applying multiple filtering criteria to a search query based on the attributes of
     * the items being searched.
     * <p>
     * The method works by first initializing a map to store filter items by their corresponding field names.
     * It then processes any range filters provided in the {@link AttributeFilter}, adding them to the map. Similarly,
     * it processes any value filters, adding them as well. Finally, it converts the accumulated filter items into a
     * {@link Filter} object that represents the combined filtering criteria.
     * <p>
     * This approach enables the application of complex filtering logic, including both range-based and discrete value
     * filters, to the attributes of searchable items, enhancing the flexibility and precision of search operations.
     *
     * @param valueFilters value filters
     * @param rangeFilters range filters
     *
     * @return A {@link Filter} object representing the combined filter criteria derived from the provided
     *         {@link AttributeFilter}.
     * @see AttributeFilter for details on the structure of range and value filters.
     */
    @NonNull
    protected Filter<T> createFilterForAttributeFilter(@NonNull List<ValueFilter> valueFilters,
                                                       @NonNull List<RangeFilter> rangeFilters,
                                                       @NonNull Function<String, String> fieldToPath
    ) {
        Map<String, FilterItemWrapper> filterItemsByField = new HashMap<>();
        addValueFilters(valueFilters, fieldToPath, filterItemsByField);
        addRangeFilters(rangeFilters, fieldToPath, filterItemsByField);
        return convertFilterItemsToFilter(filterItemsByField);
    }

    /**
     * Used to convert value filter from triggered rule to the retail filter object.
     *
     * @param values             list of {@link ValueFilter}.
     * @param filterItemsByField map to accumulate created filters.
     */
    protected void addValueFilters(@NonNull List<ValueFilter> values,
                                   @NonNull Function<String, String> fieldToPath,
                                   @NonNull Map<String, FilterItemWrapper> filterItemsByField) {
        values.forEach(valueFilter -> {
            var wrapper = filterItemsByField.computeIfAbsent(
                fieldToPath.apply(valueFilter.getField()),
                key -> new FilterItemWrapper(
                    getTypeFromValueFilterType(valueFilter.getType())
                )
            );

            wrapper.getFilterItems().add(
                FilterItem
                    .builder()
                    .exclude(valueFilter.isExclude())
                    .value(valueFilter.getValue())
                    .numberValue(valueFilter.getNumberValue())
                    .build()
            );
        });
    }

    /**
     * Used to convert range filter from triggered rule to the filter object.
     *
     * @param ranges             list of {@link RangeFilter}
     * @param filterItemsByField map to accumulate created filters.
     */
    protected void addRangeFilters(@NonNull List<RangeFilter> ranges,
                                   @NonNull Function<String, String> fieldToPath,
                                   @NonNull Map<String, FilterItemWrapper> filterItemsByField) {
        ranges.forEach(rangeFilter -> {
            var wrapper = filterItemsByField.computeIfAbsent(
                fieldToPath.apply(rangeFilter.getField()),
                key -> new FilterItemWrapper(Type.NUMERIC)
            );

            wrapper.getFilterItems().add(
                FilterItem
                    .builder()
                    .range(rangeFilter.getRange())
                    .exclude(rangeFilter.getRange().exclude())
                    .build()
            );
        });
    }

    /**
     * Used to convert selected refinements passed in search request to the filter object.
     *
     * @param selectedRefinements refinements selected by user from search request.
     * @param filterItemsByField  map to accumulate created filters.
     */
    protected void addSelectedRefinementsFilters(@NonNull Function<String, String> fieldToPath,
                                                 @NonNull List<SelectedRefinement> selectedRefinements,
                                                 @NonNull Map<String, FilterItemWrapper> filterItemsByField) {
        selectedRefinements.forEach(refinement -> {
            var wrapper = filterItemsByField.computeIfAbsent(
                fieldToPath.apply(refinement.getField()),
                key -> new FilterItemWrapper(
                    getTypeFromNavigationType(refinement.getType()),
                    refinement.isOr()
                )
            );

            wrapper.getFilterItems().add(
                FilterItem
                    .builder()
                    .value(refinement.getValue())
                    .range(refinement.getRange())
                    .build()
            );
        });
    }

    /**
     * Adds a product ID inclusion filter to the filters if the search parameters specify included product IDs.
     * This method checks if the search parameters contain a product ID filter with non-empty included product IDs.
     * If such product IDs are present, it creates a filter that includes these product IDs in the search results
     * and adds this filter to the provided list of filters. The inclusion filter ensures that only search results
     * containing the specified product IDs are included, allowing for targeted filtering based on product ID.
     *
     * @param searchParameters containing product ID filters to be evaluated for inclusion.
     * @param filters          list of {@link ToFilter} objects to which the product ID inclusion filter is added.
     */
    protected void addProductIdInclusionFilter(@NonNull SearchParameters searchParameters,
                                               @NonNull List<ToFilter<T>> filters) {
        var productIdFilter = searchParameters.getProductIdFilter();
        if (productIdFilter == null || isEmpty(productIdFilter.includedProductIds())) {
            return;
        }
        filters.add(createIncludedProductIdFilter(productIdFilter));
    }

    /**
     * Adds exclusion filters for product IDs and refinement filters to the filter builder.
     * Handles three cases:
     * 1. Exclusion product IDs and selected refinements: combine them using "AND".
     * 2. Only exclusion product IDs: adds a filter to exclude these IDs.
     * 3. Only selected refinements: adds the refinements as filters.
     * <p>
     * This method enables dynamic search filter construction, catering to various filtering
     * needs such as excluding specific products while applying refinement criteria.
     *
     * @param searchParameters with product ID filters and refinements.
     */
    @NonNull
    protected List<ToFilter<T>> createExclusionAndRefinementFilters(@NonNull Function<String, String> fieldToPath,
                                                                    @NonNull SearchParameters searchParameters) {
        List<ToFilter<T>> filters = new ArrayList<>();
        var refinements = createFilterForSelectedRefinements(fieldToPath, searchParameters);
        if (refinements.hasFilters()) {
            filters.add(refinements);
        }

        var productIdFilter = searchParameters.getProductIdFilter();
        if (productIdFilter != null && isNotEmpty(productIdFilter.excludedProductIds())) {
            filters.add(
                createExcludedProductIdsFilter(productIdFilter)
            );
        }

        return filters;
    }

    /**
     * Used to convert text expressions to the filter object.
     *
     * @param field   facet(attribute) the expression is for.
     * @param wrapper object to stores {@link FilterItem} with required meta-information.
     * @param filter  object used to accumulate retail filters.
     */
    protected void addTextExpressions(@NonNull String field,
                                      @NonNull FilterItemWrapper wrapper,
                                      @NonNull Filter<T> filter) {
        if (wrapper.isOr()) {
            addMultivaluedTextExpression(field, wrapper, filter);
        } else {
            // here we assume that we can use only one value, so we get only a first element of a passed list.
            var item = wrapper.getFilterItems().stream().findFirst().orElseThrow();
            addSinglenessTextExpression(field, item, filter);
        }
    }

    /**
     * Used to convert text expressions with more than one value to the filter object.
     * <p>
     * #1 regular text expressions are added to the passed builder using "AND" condition.
     * #2 expressions which are needed to be negated wrapped with logical "NOT" condition before
     *    they're added to the passed builder using "AND" condition.
     *
     * @param field   facet(attribute) the expression is for.
     * @param wrapper object to stores {@link FilterItem} with required meta-information.
     * @param filter  object used to accumulate retail filters.
     *
     * @see Navigation#isMultiSelect()
     */
    protected void addMultivaluedTextExpression(@NonNull String field,
                                                @NonNull FilterItemWrapper wrapper,
                                                @NonNull Filter<T> filter) {
        // Expressions accumulated with "AND" condition.
        var includeFilters = wrapper.getFilterItems()
            .stream()
            .filter(filterItem -> !filterItem.isExclude())
            .map(s -> requireNonNull(escapeJson(s.getValue())))
            .toList();
        if (!includeFilters.isEmpty()) {
            filter.addToAnds(buildTextExpression(field, includeFilters));
        }

        // Expressions which are negated before accumulated with "AND" condition.
        var excludeFilters = wrapper.getFilterItems()
            .stream()
            .filter(FilterItem::isExclude)
            .map(s -> requireNonNull(escapeJson(s.getValue())))
            .toList();
        if (!excludeFilters.isEmpty()) {
            filter.addToAnds(buildNotExpression(buildTextExpression(field, excludeFilters)));
        }
    }

    /**
     * Used to convert text expressions with one value to the filter object.
     * <p>
     * #1 regular text expression is added to the passed builder using "AND" condition.
     * #2 expressions which need to be negated wrapped with logical "NOT" condition before
     *    it is added to the passed builder using "AND" condition.
     *
     * @param field  facet(attribute) the expression is for.
     * @param item   object represents expression.
     * @param filter object used to accumulate retail filters.
     *
     * @see Navigation#isMultiSelect()
     */
    protected void addSinglenessTextExpression(@NonNull String field,
                                               @NonNull FilterItem item,
                                               @NonNull Filter<T> filter) {
        if (item.getValue() == null) {
            return;
        }

        var expression = buildSinglenessTextExpression(field, item.getValue());

        // Define is expression needs to be negated.
        if (item.isExclude()) {
            filter.addToAnds(buildNotExpression(expression));
        } else {
            filter.addToAnds(expression);
        }
    }

    /**
     * Used to convert numeric expressions to the filter object.
     *
     * @param field   facet(attribute) the expression is for.
     * @param wrapper object to stores {@link FilterItem} with required meta-information.
     * @param filter  object used to accumulate retail filters.
     */
    protected void addNumericExpressions(@NonNull String field,
                                         @NonNull FilterItemWrapper wrapper,
                                         @NonNull Filter<T> filter) {
        if (wrapper.isOr()) {
            addMultivaluedNumericExpression(field, wrapper, filter);
        } else {
            // here we assume that we can use only one value, so we get only a first element of a passed list.
            var item = wrapper.getFilterItems().stream().findFirst().orElseThrow();
            addSinglenessNumericExpression(field, item, filter);
        }
    }

    protected void addExpressionsToFilter(@NonNull Filter<T> filter,
                                          @NonNull Map<String, FilterItemWrapper> filterItemsByField) {
        filterItemsByField.forEach((fieldName, wrapperWithMetadata) -> {
            var type = wrapperWithMetadata.getType();
            if (type == Type.TEXT) {
                addTextExpressions(fieldName, wrapperWithMetadata, filter);
            }
            if (type == Type.NUMERIC) {
                addNumericExpressions(fieldName, wrapperWithMetadata, filter);
            }
        });
    }

    /**
     * Used to convert numeric expressions with more than one value to the filter object.
     * <p>
     * #1 regular numeric expressions are added to the passed builder using "OR" condition.
     * #2 expressions which are needs to be negated wrapped with logical "NOT" condition before
     *    they're added to the passed builder using "AND" condition.
     *<p>
     * NOTE: Numeric expression can be:
     *      #1 Range see: {@link RangeExpression}
     *      #2 Logical condition see: {@link ComparisonExpression}
     *
     * @param field   facet(attribute) the expression is for.
     * @param wrapper object to stores {@link FilterItem} with required meta-information.
     * @param filter  object used to accumulate retail filters.
     */
    private void addMultivaluedNumericExpression(@NonNull String field,
                                                 @NonNull FilterItemWrapper wrapper,
                                                 @NonNull Filter<T> filter) {
        var expressions = new ArrayList<ToFilter<T>>();

        // Not negated
        wrapper
            .getFilterItems()
            .stream()
            .filter(filterItem -> !filterItem.isExclude())
            .map(item -> buildNumericExpression(field, item.getRange(), item.getNumberValue()))
            .forEach(expressions::add);

        if (!expressions.isEmpty()) {
            filter.addToAnds(combineFiltersAsOrs(expressions));
        }

        // negated
        wrapper
            .getFilterItems()
            .stream()
            .filter(FilterItem::isExclude)
            .forEach(item -> filter.addToAnds(
                buildNotExpression(
                    buildNumericExpression(field, item.getRange(), item.getNumberValue())
                )
            ));
    }

    /**
     * Used to convert numeric expressions with one value to the filter object.
     * <p>
     * #1 regular range expression is added to the passed builder using "AND" condition.
     * #2 expressions which need to be negated wrapped with logical "NOT" condition before
     *    it is added to the passed builder using "AND" condition.
     *<p>
     * NOTE: Numeric expression can be:
     *      #1 Range see: {@link RangeExpression}
     *      #2 Logical condition see: {@link ComparisonExpression}
     *
     * @param field  facet(attribute) the expression is for.
     * @param item   object represents expression.
     * @param filter object used to accumulate retail filters.
     */
    private void addSinglenessNumericExpression(@NonNull String field,
                                                @NonNull FilterItem item,
                                                @NonNull Filter<T> filter) {
        var expression = buildNumericExpression(field, item.getRange(), item.getNumberValue());

        // Define is expression needs to be negated.
        if (item.isExclude()) {
            filter.addToAnds(buildNotExpression(expression));
        } else {
            filter.addToAnds(expression);
        }
    }

    /**
     * Used to build numeric expression based on passed range or value.
     * <p>
     * NOTE: Numeric expression can be:
     *      #1 Range (if passed range is not null) see: {@link RangeExpression}
     *      #2 Logical condition (if passed range is null and value is not null) see: {@link ComparisonExpression}
     *
     * @param field facet(attribute) the expression is for.
     * @param range values the field value can be.
     * @param value single values the field value can be.
     *
     * @return created numeric expression.
     */
    @NonNull
    private Expression<T> buildNumericExpression(@NonNull String field, @Nullable Range range, @Nullable Double value) {
        if (range != null) {
            return buildRangeExpression(field, range);
        }

        return buildComparisonExpression(field, value);
    }


    /**
     * Constructs a {@link Filter} object from selected refinements specified in {@link SearchParameters}.
     * <p>
     * Processes the selected refinements (filters chosen by the user) and organizes
     * them into a coherent {@link Filter} object that can be applied to a search query.
     * <p>
     * The purpose is to refine search results based on specific criteria selected by the user,
     * such as filtering products by color, size, brand, and same.
     * <p>
     * The method operates by:
     * 1. Initializing a map to store filter items, with each key representing a field name and each value being a
     *    {@link FilterItemWrapper} that encapsulates the filter criteria for that field.
     * 2. Processing the selected refinements provided in {@link SearchParameters}, adding them to the map.
     * 3. Converting the map of filter items into a single {@link Filter} object that encapsulates all the selected
     *    refinement criteria.
     * <p>
     * This structured approach enables the application of multiple selected refinements to a search query,
     * thereby allowing users to narrow down their search results based on a combination of attributes.
     *
     * @param searchParameters containing the list of refinements(selected filters) to be applied to the search query.
     *
     * @return A {@link Filter} object that represents the combined filter criteria based on the selected refinements
     *         provided in the {@link SearchParameters}.
     */
    @NonNull
    private Filter<T> createFilterForSelectedRefinements(@NonNull Function<String, String> fieldToPath,
                                                         @NonNull SearchParameters searchParameters) {
        Map<String, FilterItemWrapper> filterItemsByField = new HashMap<>();
        addSelectedRefinementsFilters(fieldToPath, searchParameters.getRefinements(), filterItemsByField);
        return convertFilterItemsToFilter(filterItemsByField);
    }

    /**
     * Used to define a filter type based on a passed value filter type.
     *
     * @param type value filter type used to define {@link Type}
     *
     * @return {@link Type} value defined based on passed value filter type.
     */
    @NonNull
    private Type getTypeFromValueFilterType(@NonNull ValueFilter.ValueFilterType type) {
        if (type == ValueFilter.ValueFilterType.NUMERIC) {
            return Type.NUMERIC;
        }
        return Type.TEXT;
    }


    /**
     * Used to define a filter type based on a passed navigation type.
     *
     * @param type navigation type used to define {@link Type}
     *
     * @return {@link Type} value defined based on passed navigation type.
     */
    @NonNull
    private Type getTypeFromNavigationType(@NonNull NavigationType type) {
        if (type == RANGE) {
            return Type.NUMERIC;
        }
        return Type.TEXT;
    }

}
