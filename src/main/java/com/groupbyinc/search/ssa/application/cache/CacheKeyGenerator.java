package com.groupbyinc.search.ssa.application.cache;

import com.groupbyinc.search.ssa.core.SearchParameters;

/**
 * Define a way how key which is stored in a cache have to be generated.
 */
public interface CacheKeyGenerator<T extends CacheConfig> {

    /**
     * Generate a key which may be used to get or safe values from a cache.
     * UserAttributes as personal information will be added into the key while generate for search and browse, not for mongo.
     *
     * @param searchParameters object with request related configurations.
     * @param config           cache configuration.
     *
     * @return a string which was build based on a passed object.
     */
    String generateCacheKey(SearchParameters searchParameters, T config);

}
