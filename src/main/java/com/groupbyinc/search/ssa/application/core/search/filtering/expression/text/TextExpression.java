package com.groupbyinc.search.ssa.application.core.search.filtering.expression.text;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.Expression;
import io.micronaut.core.annotation.NonNull;

import java.util.List;

/**
 * Expression for text facets(attributes).
 *
 * @param <T> type of filter object which will be created from this expression.
 */
public abstract class TextExpression<T> extends Expression<T> {

    /**
     * Attribute values, one of them product should contain in specific textual attribute.
     */
    @NonNull
    protected final List<String> refinements;

    public TextExpression(@NonNull String field, @NonNull List<String> refinements) {
        super(field);
        this.refinements = refinements;
    }

}
