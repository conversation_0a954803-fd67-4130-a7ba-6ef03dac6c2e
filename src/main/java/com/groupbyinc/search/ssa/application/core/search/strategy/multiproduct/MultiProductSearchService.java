package com.groupbyinc.search.ssa.application.core.search.strategy.multiproduct;

import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngine;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.SearchMetadata;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.core.request.RequestServed;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.productcatalog.ProductCatalogService;
import com.groupbyinc.search.ssa.retail.filtering.RetailFilterService;
import com.groupbyinc.search.ssa.retail.filteringold.RetailFilterServiceOld;

import io.micronaut.context.annotation.Context;
import io.micronaut.core.annotation.NonNull;
import jakarta.inject.Named;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.ParametersAreNonnullByDefault;
import java.time.Duration;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.USE_NEW_RETAIL_FILTERING;

/**
 * This is a service to handle multi-product search, it is a search when we receive
 * a request where we request specific products by passing selected navigations with
 * product IDs.
 */
@Slf4j
@Context
@ParametersAreNonnullByDefault
public class MultiProductSearchService {

    public static final String PRODUCT_ID_REFINEMENT_FIELD = "productId";
    public static final Predicate<SelectedRefinement> NOT_PRODUCT_ID_REFINEMENT = refinement ->
        !PRODUCT_ID_REFINEMENT_FIELD.equals(refinement.getField());

    private static final String ERROR_MESSAGE_FORMAT = "Error while searching for products: %s";

    private final SearchEngine googleSearchEngine;
    private final RetailFilterServiceOld retailFilterServiceOld;
    private final RetailFilterService retailFilterService;
    private final FeaturesManager featuresManager;
    private final ProductCatalogService productCatalogService;

    public MultiProductSearchService(RetailFilterServiceOld retailFilterServiceOld,
                                     RetailFilterService retailFilterService,
                                     FeaturesManager featuresManager,
                                     ProductCatalogService productCatalogService,
                                     @Named("googleSearchEngine") SearchEngine googleSearchEngine) {
        this.googleSearchEngine = googleSearchEngine;
        this.retailFilterServiceOld = retailFilterServiceOld;
        this.retailFilterService = retailFilterService;
        this.productCatalogService = productCatalogService;
        this.featuresManager = featuresManager;
    }

    @NonNull
    public SearchResults search(@NonNull SearchParameters searchParameters) {
        var productIds = searchParameters
            .getRefinements()
            .stream()
            .filter(ref -> PRODUCT_ID_REFINEMENT_FIELD.equals(ref.getField()))
            .map(SelectedRefinement::getValue)
            .collect(Collectors.toSet());

        return getProducts(searchParameters, productIds);
    }

    private SearchResults getProducts(SearchParameters searchParameters, Set<String> productIds) {
        try {
            var records = productCatalogService.getProductsDetails(searchParameters, productIds, true);
            return buildSearchResults(searchParameters, records.records());
        } catch (Exception e) {
            var errorMessage = ERROR_MESSAGE_FORMAT.formatted(e.getMessage());
            log.error("Failing back to SearchGoogle, Product Catalog error: {}", errorMessage);
            getRequestContext().addWarning(errorMessage);
            // Fallback to google search in case of invocation error.
            return googleSearchEngine.search(searchParameters);
        }
    }

    private SearchResults buildSearchResults(SearchParameters searchParameters, List<Record> records) {
        var facetConverter = searchParameters.getRetailFacetConverter();

        var metadata = SearchMetadata.builder()
            .attributionToken(UUID.randomUUID().toString())
            .retailTime(Duration.ZERO)
            .cached(false)
            .build();

        var useNewRetailFiltering = featuresManager.getBooleanFlagConfiguration(
            getRequestContext().getLdContext(),
            USE_NEW_RETAIL_FILTERING
        );

        var filter = useNewRetailFiltering
            ? retailFilterService.createFilter(searchParameters)
            : retailFilterServiceOld.createFilter(searchParameters);

        return SearchResults
            .builder()
            .numTotalRecords(records.size())
            .records(records)
            .selectedNavigations(
                facetConverter.convertSelectedRefinementsToNavigations(
                    searchParameters.getRefinements()
                )
            )
            .filter(filter)
            .siteFilter(searchParameters.getMerchandisingConfiguration().siteFilter())
            .metadata(metadata)
            .pageCategories(searchParameters.getPageCategories())
            .requestServed(RequestServed.FETCH)
            .facetLimit(facetConverter.getFacetLimit())
            .build();
    }

}
