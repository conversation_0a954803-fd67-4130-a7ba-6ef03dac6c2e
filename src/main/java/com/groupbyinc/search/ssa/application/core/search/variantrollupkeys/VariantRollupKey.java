package com.groupbyinc.search.ssa.application.core.search.variantrollupkeys;

import io.micronaut.core.annotation.Nullable;

import java.util.List;

public record VariantRollupKey(String key,
                               RollupKeyType type,
                               String attribute,
                               String path,
                               @Nullable String placeId) {

    public VariantRollupKey(String key, RollupKeyType type, String attribute, String path) {
        this(key, type, attribute, path, null);
    }

    public List<String> getPathAsList() {
        return List.of(path.split("\\."));
    }

    public enum RollupKeyType {
        VARIANT_ATTRIBUTE,
        INVENTORY_ATTRIBUTE,
        FULFILLMENT_INFO,
    }
}
