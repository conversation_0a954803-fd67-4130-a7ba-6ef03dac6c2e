package com.groupbyinc.search.ssa.application.core.search.processor.redirect;

import com.groupbyinc.search.ssa.application.Clock;
import com.groupbyinc.search.ssa.application.core.search.processor.Processor;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.redirect.RedirectConfiguration;

import io.micronaut.context.annotation.Context;
import lombok.RequiredArgsConstructor;

import java.util.Optional;

@Context
@RequiredArgsConstructor
public class RedirectProcessor implements Processor<Optional<SearchResults>> {

    private final Clock clock;

    /**
     * Process incoming request using configured redirects and apply redirect
     * configuration if suitable.
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return {@link SearchResults} result of search call if redirect applied,
     *         otherwise empty optional object.
     */
    @Override
    public Optional<SearchResults> process(SearchParameters searchParameters) {
        return resolveRedirect(searchParameters).map(this::buildSearchResults);
    }

    private Optional<RedirectConfiguration> resolveRedirect(SearchParameters searchParameters) {
        if (searchParameters.getMerchandisingConfiguration().redirectConfigurations().isEmpty()) {
            return Optional.empty();
        }

        return searchParameters
            .getMerchandisingConfiguration()
            .redirectConfigurations()
            .stream()
            .filter(redirectConfiguration -> redirectConfiguration.isActive(clock.now()))
            .filter(triggered -> triggered.trigger(searchParameters))
            .findFirst();
    }

    private SearchResults buildSearchResults(RedirectConfiguration redirectConfiguration) {
        return SearchResults
            .builder()
            .redirectUrl(redirectConfiguration.url())
            .redirectMetadata(redirectConfiguration.metadata())
            .build();
    }

}
