package com.groupbyinc.search.ssa.application.core.search.strategy.pintotop;

import com.groupbyinc.search.ssa.application.core.pintotop.PinToTopService;
import com.groupbyinc.search.ssa.application.core.search.strategy.SearchStrategy;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.util.debug.StrategyNameContext;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.propagation.PropagatedContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.ParametersAreNonnullByDefault;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.core.SearchMode.PRODUCT_SEARCH;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_PIN_TO_TOP;

@Slf4j
@RequiredArgsConstructor
@ParametersAreNonnullByDefault
public class PinToTopSearchStrategy extends SearchStrategy {

    private final FeaturesManager featuresManager;
    private final PinToTopService pinToTopService;

    /**
     * The search strategy that requests searched products from Google and asynchronously
     * requests a pinned product from a Google or data-catalog-fetch based on enabled
     * data-catalog-fetch or not.
     * After both requests are completed, they results are merged.
     * After result merging the post-processing using data-catalog-fetch applied to
     * fill products from Google with attributes.
     * <p>
     * Flow: [google -> (google | productCatalog)] -> productCatalog -> response
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return {@link SearchResults} result of search call.
     */
    @NonNull
    @Override
    public SearchResults search(SearchParameters searchParameters) {
        log.debug("PinToTopSearchStrategy used for search.");

        try (var ignored = PropagatedContext.getOrEmpty().plus(new StrategyNameContext(name())).propagate()) {
            var response = pinToTopService.search(searchParameters);
            response.getSearchStrategies().add(name());
            return response;
        }
    }

    /**
     * Define is this search strategy have to be used within the current search request or not.
     * This strategy applied when request it is a product search request, and pin to top feature
     * flag enabled, also request itself must contain pinned products.
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return true if this strategy has to be applied, false otherwise.
     */
    @Override
    public boolean isApplicable(SearchParameters searchParameters) {
        var context = getRequestContext().getLdContext();

        return searchParameters.getSearchMode() == PRODUCT_SEARCH
            && !searchParameters.getPinnedProducts().isEmpty()
            && featuresManager.getBooleanFlagConfiguration(context, ENABLE_PIN_TO_TOP);
    }

}
