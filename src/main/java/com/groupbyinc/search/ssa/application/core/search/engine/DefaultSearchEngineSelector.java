package com.groupbyinc.search.ssa.application.core.search.engine;

import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.mongo.settings.MongoFeatureSettings;
import com.groupbyinc.search.ssa.util.abtest.VariantSelector;

import io.micronaut.context.annotation.Context;
import jakarta.inject.Named;
import lombok.extern.slf4j.Slf4j;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.core.SearchMode.PRODUCT_SEARCH;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_CRM_PERSONALIZATION;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_MONGO_BROWSE_ENGINE;

import static io.micronaut.core.util.StringUtils.isEmpty;

@Slf4j
@Context
public class DefaultSearchEngineSelector implements SearchEngineSelector {

    private final SearchEngine mongoSearchEngine;
    private final SearchEngine googleSearchEngine;
    private final SearchEngine googleV2AlphaSearchEngine;
    private final FeaturesManager featuresManager;

    public DefaultSearchEngineSelector(FeaturesManager featuresManager,
                                       @Named("mongoSearchEngine") SearchEngine mongoSearchEngine,
                                       @Named("googleSearchEngine") SearchEngine googleSearchEngine,
                                       @Named("googleV2AlphaSearchEngine") SearchEngine googleV2AlphaSearchEngine) {
        this.featuresManager = featuresManager;
        this.mongoSearchEngine = mongoSearchEngine;
        this.googleSearchEngine = googleSearchEngine;
        this.googleV2AlphaSearchEngine = googleV2AlphaSearchEngine;
    }

    @Override
    public SearchEngine getEngine(SearchParameters searchParameters) {
        if (isMongoEngineUsed(searchParameters)) {
            return mongoSearchEngine;
        }
        if(isCRMPersonalizationApplicable(searchParameters)){
            return googleV2AlphaSearchEngine;
        }
        return googleSearchEngine;
    }

    private boolean isMongoEngineUsed(SearchParameters searchParameters) {
        var context = getRequestContext();
        var isBrowse = isEmpty(searchParameters.getQuery());

        if (!isBrowse || searchParameters.getSearchMode() != PRODUCT_SEARCH) {
            return false;
        }

        var mongoSettings = featuresManager.getObjectFlagConfiguration(
            context.getLdContext(),
            ENABLE_MONGO_BROWSE_ENGINE,
            MongoFeatureSettings.class,
            MongoFeatureSettings.DEFAULT
        );

        if (!mongoSettings.enabled()) {
            return false;
        }

        if (mongoSettings.abTesting().enabled()) {
            return isMongoUsedForABTesting(mongoSettings, context.getUniqueUserId());
        }

        return true;
    }

    private boolean isMongoUsedForABTesting(MongoFeatureSettings settings, String userId) {
        var configuredPercentage = settings.abTesting().rolloutPercentage();

        if (configuredPercentage <= 0 || configuredPercentage > 1) {
            log.warn(
                "Incorrect value '{}' enableMongoBrowseEngine#abTesting#rolloutPercentage.",
                configuredPercentage
            );
            return false;
        }

        int percentage = (int) (configuredPercentage * 100);
        int[] buckets = {
            percentage,      // mongo
            100 - percentage // google
        };

        var selector = new VariantSelector(buckets);

        var variantId = selector.getRangeId(userId);

        // 0 – mongo 1 – google
        return variantId == 0;
    }

    private boolean isCRMPersonalizationApplicable(SearchParameters searchParameters) {
        var context = getRequestContext();
        var enableCRMPersonalization = featuresManager.getBooleanFlagConfiguration(
            context.getLdContext(),
            ENABLE_CRM_PERSONALIZATION
        );
        return enableCRMPersonalization
            && searchParameters.getUserAttributes() != null
            && !searchParameters.getUserAttributes().isEmpty();
    }
}
