package com.groupbyinc.search.ssa.application.cache;

import io.micronaut.core.annotation.NonNull;

import javax.annotation.ParametersAreNonnullByDefault;

/**
 * Define a list of operations which are supported by cache
 */
@ParametersAreNonnullByDefault
public interface Cache {

    /**
     * Save passed key-value pair to the cache.
     *
     * @param key    by which value will be saved.
     * @param value  a string representation of saved value.
     * @param config object with any cache related configuration, such as TTL or etc.
     */
    void save(String key, String value, CacheConfig config);

    /**
     * Retrieve a value associated with the given key.
     *
     * @param key associated with which value we need to retrieve.
     *
     * @return a value or empty string if the given key not found in a cache.
     */
    @NonNull
    String get(String key);

    /**
     * Invalidate all values in cache based on passed tenant and collection.
     * Technically it build a key prefix: "<tenant><collection>_"
     * and calls: KEYS "prefix:*" | xargs redis-cli DEL
     *
     * @param tenant     to invalidate keys
     * @param collection to invalidate keys
     *
     */
    void invalidateByTenantAndCollection(String tenant, String collection);

}
