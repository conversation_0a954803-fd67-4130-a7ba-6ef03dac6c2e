package com.groupbyinc.search.ssa.application.core.search.filtering.expression;

import com.groupbyinc.search.ssa.application.core.search.filtering.filter.ToFilter;
import io.micronaut.core.annotation.NonNull;

/**
 * Represents a negation expression.
 *
 * @param <T> type of filter object which will be created from this expression.
 */
public abstract class NotExpression<T> implements ToFilter<T> {

    /**
     * Expression which is needs to be negotiated.
     */
    @NonNull
    protected final Expression<T> expression;

    public NotExpression(@NonNull Expression<T> expression) {
        this.expression = expression;
    }

}
