package com.groupbyinc.search.ssa.application.core.search.converter;

import com.groupbyinc.search.ssa.core.Metadata;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.navigation.Navigation;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.navigation.NavigationRefinement;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.core.rule.PinnedRefinement;
import com.groupbyinc.search.ssa.core.rule.Refinement;
import com.groupbyinc.search.ssa.util.AttributeUtils;

import io.micronaut.core.annotation.NonNull;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.application.core.search.strategy.topsort.TopSortSearchStrategyV2.TOPSORT_SPONSORED_RECORD_ID_NAVIGATION;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.VALUE;
import static com.groupbyinc.search.ssa.util.AttributeUtils.INVENTORIES_NAVIGATION_PATTERN_SECOND;
import static com.groupbyinc.search.ssa.util.AttributeUtils.INVENTORY_NAVIGATION_TEMPLATE;
import static com.groupbyinc.search.ssa.util.StringUtils.SOURCE_DYNAMIC;
import static com.groupbyinc.search.ssa.util.StringUtils.SPACE;
import static com.groupbyinc.search.ssa.util.StringUtils.UNDERSCORE;

import static io.micronaut.core.util.CollectionUtils.isNotEmpty;
import static java.lang.Integer.compare;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

@Slf4j
public abstract class AbstractFacetConverter {

    protected static final String NULL_VALUE_LOG_MESSAGE_TEMPLATE = "Value null for refinement: {}";

    // Maximum of available navigations that can be returned.
    protected static final int FACET_LIMIT = 300;

    // A default count of facets inside returned available navigations.
    protected static final int SPEC_LIMIT = 100;

    public static final String INVENTORIES_PREFIX = "inventories.";
    private static final String INVENTORIES_NAVIGATION_REGEX = "^inventories\\.(.*)$";
    private static final Pattern INVENTORIES_NAVIGATION_PATTERN = Pattern.compile(INVENTORIES_NAVIGATION_REGEX);

    // Used to compare navigation objects based on the priority of them in CC configurations.
    @Getter
    protected final Comparator<Navigation> navigationComparator = this::compareNavigations;

    @Getter
    protected final Integer facetLimit;
    protected final Set<String> excludedNavigations;
    protected final List<PinnedRefinement> pinnedRefinements;
    protected final Map<String, AttributeConfiguration> attributeConfiguration;
    protected final Map<String, NavigationConfiguration> navigationConfigurations;

    private final String inventoryStoreId;
    private final List<String> includedNavigations;
    private final Map<String, Integer> priorityByIncludedNavigationName = new HashMap<>();

    public AbstractFacetConverter(@NonNull SearchParameters searchParameters) {
        this.pinnedRefinements = searchParameters.getPinnedRefinements();
        this.includedNavigations = searchParameters.getIncludedNavigations();
        this.excludedNavigations = searchParameters.getExcludedNavigations();
        this.facetLimit = searchParameters.getFacetLimit() == null
            ? FACET_LIMIT
            : searchParameters.getFacetLimit();
        this.inventoryStoreId = searchParameters.getInventoryStoreId();

        var merchandisingConfiguration = searchParameters.getMerchandisingConfiguration();
        this.attributeConfiguration = merchandisingConfiguration.attributeConfigurations();
        this.navigationConfigurations = merchandisingConfiguration
            .navigationConfigurations()
            .stream()
            .collect(toMap(NavigationConfiguration::field, identity()));

        IntStream
            .range(0, this.includedNavigations.size())
            .forEach(i -> this.priorityByIncludedNavigationName.put(this.includedNavigations.get(i), i + 1));
    }

    public Navigation convertFacetToNavigation(String facetKey,
                                               NavigationType navigationType,
                                               List<NavigationRefinement> refinements) {
        var m = INVENTORIES_NAVIGATION_PATTERN_SECOND.matcher(facetKey);
        if (m.find()) {
            var placeId = m.group(1);
            var key = INVENTORIES_PREFIX + m.group(2);
            var name = getDisplayName(key);

            var navigationConfiguration = navigationConfigurations.get(key);
            var metadata = getMetadata(facetKey, navigationConfiguration);

            if (navigationConfiguration == null) {
                return Navigation.builder()
                    .name(name)
                    .field(key)
                    .placeId(placeId)
                    .type(navigationType)
                    .refinements(refinements)
                    .metadata(metadata)
                    .pinned(false)
                    .build();
            }

            return Navigation.builder()
                .name(name)
                .field(navigationConfiguration.field())
                .type(navigationType)
                .placeId(placeId)
                .refinements(refinements)
                .multiSelect(navigationConfiguration.multiSelect())
                .metadata(metadata)
                .pinned(navigationType == VALUE && includedNavigations.contains(key))
                .sort(navigationConfiguration.sort())
                .build();
        }

        var name = getDisplayName(facetKey);

        var navigationConfiguration = navigationConfigurations.get(facetKey);
        var metadata = getMetadata(facetKey, navigationConfiguration);

        if (navigationConfiguration == null) {
            return Navigation.builder() // for Dynamic facets
                .name(name)
                .field(facetKey)
                .type(navigationType)
                .refinements(refinements)
                .source(SOURCE_DYNAMIC)
                .multiSelect(true)
                .metadata(metadata)
                .pinned(includedNavigations.contains(facetKey))
                .build();
        }

        return Navigation.builder()
            .name(navigationConfiguration.name())
            .field(navigationConfiguration.field())
            .type(navigationType)
            .refinements(refinements)
            .multiSelect(navigationConfiguration.multiSelect())
            .metadata(metadata)
            .pinned(navigationType == VALUE && includedNavigations.contains(facetKey))
            .sort(navigationConfiguration.sort())
            .build();
    }

    public List<Navigation> convertSelectedRefinementsToNavigations(List<SelectedRefinement> selectedRefinements) {
        return selectedRefinements.stream().map(fieldRefinementEntry -> {
            var field = fieldRefinementEntry.getField();

            var refinements = switch (fieldRefinementEntry.getType()) {
                case VALUE -> NavigationRefinement.valueRefinement(fieldRefinementEntry.getValue());
                case RANGE -> NavigationRefinement.rangeRefinement(fieldRefinementEntry.getRange());
            };

            return Navigation.builder()
                .field(field)
                .name(getDisplayName(field))
                .type(fieldRefinementEntry.getType())
                .refinements(List.of(refinements))
                .multiSelect(fieldRefinementEntry.isOr())
                .build();
        }).toList();
    }

    /**
     * Take a note that from "includedNavigations" array this method will resolve only non-dynamic navigations.
     * Dynamic navigations are always returned from the search engine, so we don't need to send them as facets.
     */
    protected List<ResolvedNavigationConfiguration> resolveNavigations() {
        var containsTopsortSponsorRecordId = includedNavigations.contains(TOPSORT_SPONSORED_RECORD_ID_NAVIGATION);
        var onlyTopsortSponsorRecordId = containsTopsortSponsorRecordId && includedNavigations.size() == 1;
        if (!includedNavigations.isEmpty() && !onlyTopsortSponsorRecordId) {
            List<ResolvedNavigationConfiguration> resolved = new ArrayList<>();
            includedNavigations.forEach(s -> {
                var m = AttributeUtils.INVENTORIES_NAVIGATION_PATTERN.matcher(s);
                var m2 = INVENTORIES_NAVIGATION_PATTERN_SECOND.matcher(s);

                String field;
                String key;
                if (m.find()) {
                    field = INVENTORY_NAVIGATION_TEMPLATE.formatted(m.group(1), m.group(2));
                    key = INVENTORIES_PREFIX + m.group(2);
                } else if (m2.find()) {
                    field = s;
                    key = INVENTORIES_PREFIX + m2.group(2);
                } else {
                    field = s;
                    key = s;
                }

                var nc = navigationConfigurations.get(key);
                if (nc != null) {
                    resolved.add(new ResolvedNavigationConfiguration(
                        nc.name(),
                        field,
                        nc.type(),
                        nc.ranges(),
                        nc.sort(),
                        nc.multiSelect()
                    ));
                } else {
                    var attribute = attributeConfiguration.get(key);
                    if (attribute == null) {
                        log.warn("Not able to resolve navigation for: {}", s);
                    } else {
                        // Case when no global navigation created but attribute used in the rule as pinned navigation
                        resolved.add(ResolvedNavigationConfiguration.fromAttribute(attribute));
                    }
                }
            });
            return resolved;
        }

        var resolved = getResolvedNavigationConfigurationStream();

        if (!excludedNavigations.isEmpty()) {
            resolved = resolved
                .filter(navigation -> !excludedNavigations.contains(navigation.field()));
        }

        if (containsTopsortSponsorRecordId) {
            resolved = Stream.concat(
                resolved,
                Stream.of(createTopsortSponsoredRecordIdNavigation())
            );
        }

        return resolved.toList();
    }

    protected boolean isPinnedRefinement(@NonNull String facetKey, @NonNull String facetValue) {
        if (pinnedRefinements == null) {
            return false;
        }

        return pinnedRefinements
            .stream()
            .filter(refinement -> facetKey.equals(refinement.navigation()))
            .filter(refinement -> isNotEmpty(refinement.refinements()))
            .flatMap(pinnedRefinement -> pinnedRefinement.refinements().stream())
            .map(Refinement::value)
            .anyMatch(value -> value.equals(facetValue));
    }

    private String getDisplayName(String facetKey) {
        if (attributeConfiguration != null) {
            var attribute = attributeConfiguration.get(facetKey);
            if (attribute != null) {
                return attribute.displayName();
            }
        }

        // pick word after dot separator and replace "_" with spaces to be user-friendly.
        return facetKey
            .substring(facetKey.indexOf('.') + 1)
            .replace(UNDERSCORE, SPACE);
    }

    private List<Metadata> getMetadata(String key, NavigationConfiguration navigationConfiguration) {
        List<Metadata> metadata = new ArrayList<>();
        if (navigationConfiguration != null && navigationConfiguration.metadata() != null) {
            metadata.addAll(navigationConfiguration.metadata());
        }
        if (attributeConfiguration != null) {
            var attribute = attributeConfiguration.get(key);
            if (attribute != null && attribute.metadata() != null) {
                metadata.addAll(attribute.metadata());
            }
        }
        return metadata.isEmpty() ? null : metadata;
    }

    private Stream<ResolvedNavigationConfiguration> getResolvedNavigationConfigurationStream() {

        return navigationConfigurations
            .values()
            .stream()
            .map(n -> {
                var field = n.field();
                var m = INVENTORIES_NAVIGATION_PATTERN.matcher(field);

                // For all "inventories" navigations from CC-API, we want
                // to inject inventoryStoreId if it is passed in the request
                if (!inventoryStoreId.isEmpty() && m.find()) {
                    var fieldUpdated = INVENTORY_NAVIGATION_TEMPLATE.formatted(inventoryStoreId, m.group(1));

                    return new ResolvedNavigationConfiguration(
                        n.name(),
                        fieldUpdated,
                        n.type(),
                        n.ranges(),
                        n.sort(),
                        n.multiSelect()
                    );
                } else {
                    return ResolvedNavigationConfiguration.fromNavigationConfiguration(n);
                }
            })
            // If we still have "inventories" navigations without injected inventoryStoreId, we want to filter them out
            .filter(n -> !n.field().matches(INVENTORIES_NAVIGATION_REGEX));
    }

    /**
     * Used to resolve a result of navigation priorities comparing.
     * <p>
     * The order of navigations at the highest level:
     * Rule pinned navigations ordering > Navigation configuration prioritizing > Dynamic Navigation ordering
     * </p>
     */
    private int compareNavigations (Navigation n1, Navigation n2){
        // Check if navigations are in rule pinned navigations (included navigations)
        boolean n1InIncludedNavigations = priorityByIncludedNavigationName.containsKey(n1.getField());
        boolean n2InIncludedNavigations = priorityByIncludedNavigationName.containsKey(n2.getField());

        // If one navigation is in included navigations and the other is not
        // Put included navigations ahead of non-included ones
        if (Boolean.compare(n1InIncludedNavigations, n2InIncludedNavigations) != 0){
            return n1InIncludedNavigations ? -1 : 1;
        }

        // If both are included, compare by their priority in included navigations
        if (n1InIncludedNavigations)
            return compare(
                priorityByIncludedNavigationName.get(n1.getField()),
                priorityByIncludedNavigationName.get(n2.getField())
            );

        // Check if navigations are in area navigation configurations
        boolean n1InNavConf = navigationConfigurations.containsKey(n1.getField());
        boolean n2InNavConf = navigationConfigurations.containsKey(n2.getField());

        // If one navigation is included in area navigation config and the other is not
        // Put included navigations ahead of non-included ones
        if (Boolean.compare(n1InNavConf, n2InNavConf) != 0){
            return n1InNavConf ? -1 : 1;
        }

        // If both are from area navigation config, compare by their priority
        if (n1InNavConf)
            return compare(
                navigationConfigurations.get(n1.getField()).priority(),
                navigationConfigurations.get(n2.getField()).priority()
            );

        return 0;
    }

    private ResolvedNavigationConfiguration createTopsortSponsoredRecordIdNavigation() {
        return new ResolvedNavigationConfiguration(
            "Topsort Sponsored Record ID",
            TOPSORT_SPONSORED_RECORD_ID_NAVIGATION,
            NavigationType.VALUE,
            null,
            null,
            false
        );
    }

}
