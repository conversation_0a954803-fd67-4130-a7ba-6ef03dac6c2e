package com.groupbyinc.search.ssa.application.core.search.filtering;

import com.groupbyinc.search.ssa.core.SearchParameters;

import io.micronaut.core.annotation.NonNull;

/**
 * Define a contract how search filter must be created.
 *
 * @param <T> type of filter object which will be created from this expression.
 */
public interface FilterService<T> {

    /**
     * Template used to join two raw filters with logical "AND" condition.
     * For example, Join pre-filter and site-filter.
     */
    String AND_JOIN_TEMPLATE = "(%s) AND (%s)";

    /**
     * String representation of logical "AND" condition.
     */
    String LOGICAL_AND = "AND";

    /**
     * Used to create a filter for the search request.
     *
     * @param searchParameters object which is representing a search request.
     *
     * @return filter which needs to be applied to the search request.
     */
    @NonNull
    T createFilter(@NonNull SearchParameters searchParameters);

}
