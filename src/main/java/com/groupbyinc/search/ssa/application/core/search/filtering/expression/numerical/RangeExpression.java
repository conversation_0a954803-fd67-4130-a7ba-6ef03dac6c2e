package com.groupbyinc.search.ssa.application.core.search.filtering.expression.numerical;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.Expression;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;

/**
 * Expression for a range attributes.
 *
 * @param <T> type of filter object which will be created from this expression.
 */
public abstract class RangeExpression<T> extends Expression<T> {

    /**
     * Inclusive lower bounds of the range.
     */
    @Nullable
    protected final Number lower;

    /**
     * Exclusive upper bounds of the range.
     */
    @Nullable
    protected final Number upper;

    public RangeExpression(@NonNull String field, @Nullable Number lower, @Nullable Number upper) {
        super(field);
        this.lower = lower;
        this.upper = upper;
    }

}
