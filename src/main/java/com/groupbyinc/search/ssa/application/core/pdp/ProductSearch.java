package com.groupbyinc.search.ssa.application.core.pdp;

import com.groupbyinc.search.ssa.core.product.Product;
import com.groupbyinc.search.ssa.metrics.opentelemetry.interceptors.ApiLatencyMetricsCollector;

import java.util.List;
import java.util.Optional;

public interface ProductSearch {

    /**
     * Makes a single product search call to product catalog, returns all available fields, including variants and local
     * inventory. Prioritizes variants (if parameter is provided) within a collection of found variants.
     *
     * @param productId             product id to search for
     * @param prioritizedVariantIds variant Ids to be prioritized (i.e., pushed to the start of variant collection)
     *
     * @return nullable {@link Optional} that contains the product
     */
    @ApiLatencyMetricsCollector
    Optional<Product> getProductDetails(String productId, List<String> prioritizedVariantIds);

}
