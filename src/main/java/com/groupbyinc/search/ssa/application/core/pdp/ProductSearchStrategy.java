package com.groupbyinc.search.ssa.application.core.pdp;

import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.core.product.Product;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.productcatalog.ProductCatalogService;
import com.groupbyinc.search.ssa.retail.ProductGoogleSearcher;

import com.launchdarkly.sdk.LDContext;
import io.micronaut.context.annotation.Context;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_PDP_FETCH;

@Slf4j
@Context
@AllArgsConstructor
public class ProductSearchStrategy {
    private final FeaturesManager featuresManager;
    private final ProductCatalogService productCatalogService;
    private final ProductGoogleSearcher productGoogleSearcher;

    public ProductSearch getStrategyFor(RequestContext context) {
        return isFetchPdpEnabled(context.getLdContext())
            ? new StrategyWithFallback(productCatalogService, productGoogleSearcher)
            : productGoogleSearcher;
    }

    private boolean isFetchPdpEnabled(LDContext context) {
        return featuresManager.getBooleanFlagConfiguration(context, ENABLE_DATA_CATALOG_PDP_FETCH);
    }

    @AllArgsConstructor
    static class StrategyWithFallback implements ProductSearch {
        ProductSearch strategy;
        ProductSearch fallbackStrategy;

        private Optional<ProductSearch> fallback() {
            return fallbackStrategy != null && fallbackStrategy != strategy
                ? Optional.of(fallbackStrategy)
                : Optional.empty();
        }

        @Override
        public Optional<Product> getProductDetails(String productId, List<String> prioritizedVariantIds) {
            var product = strategy.getProductDetails(productId, prioritizedVariantIds);
            if (product.isEmpty() && fallback().isPresent()) {
                log.debug("Fallback strategy used for PDP [{}]", productId);
                product = fallback().get().getProductDetails(productId, prioritizedVariantIds);
                if (product.isPresent()) {
                    log.warn("Fallback strategy returned product PDP [{}]", productId);
                }
            }
            return product;
        }
    }

}
