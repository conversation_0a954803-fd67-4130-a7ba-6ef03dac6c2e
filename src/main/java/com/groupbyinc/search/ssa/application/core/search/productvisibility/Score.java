package com.groupbyinc.search.ssa.application.core.search.productvisibility;

import com.groupbyinc.search.ssa.core.rule.ProductVisibilityBias;

import lombok.Getter;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static java.math.BigDecimal.valueOf;

@Getter
public class Score {
    private static final Double DEFAULT_SCORE_CAP = 1.;

    final double totalScore;
    final double score;
    final double notMatchingScore;

    final double appliedMultiplier;
    final double visibilityCap;
    final double maxPossibleScore;
    final double maxAllowedScore;

    final double targetScore;
    double newScore;
    double newNotMatchingScore;

    Score(double totalScore,
          double score,
          double notMatchedScore,
          double appliedMultiplier,
          double percentageCap,
          double maxPossibleScore,
          double maxAllowedScore,
          double targetScore) {

        this.totalScore = totalScore;
        this.score = score;
        this.notMatchingScore = notMatchedScore;
        this.appliedMultiplier = appliedMultiplier;
        this.visibilityCap = percentageCap;
        this.maxPossibleScore = maxPossibleScore;
        this.maxAllowedScore = maxAllowedScore;
        this.targetScore = targetScore;
        this.newScore = score;
        this.newNotMatchingScore = notMatchedScore;
    }

    static Score calculate(List<ProductVisibility> visibilities, ProductVisibilityBias bias) {
        var matchingScore = BigDecimal.ZERO;
        var notMatchingScore = BigDecimal.ZERO;
        var size = 0;

        for (int i = 0; i < visibilities.size(); i++) {
            var score = valueOf(Tier.getScore(i));
            if (visibilities.get(i).matching()) {
                size++;
                matchingScore = matchingScore.add(score);
            } else {
                notMatchingScore = notMatchingScore.add(score);
            }
        }

        var totalScore = matchingScore.add(notMatchingScore);
        var multiplier = valueOf(bias.multiplier());
        var percentageOffset = valueOf(bias.percentageOffset());
        var appliedMultiplier = multiplier.add(multiplier.multiply(percentageOffset));

        var percentageCap = bias.visibilityPercentageCap() != null
            ? valueOf(bias.visibilityPercentageCap())
            : valueOf(DEFAULT_SCORE_CAP);

        var maxAllowedScore = totalScore.multiply(percentageCap);
        var maxPossibleScore = maxPossibleScore(visibilities, size);

        var targetScore = matchingScore.multiply(appliedMultiplier)
            .min(maxAllowedScore)
            .min(maxPossibleScore);

        return new Score(
            totalScore.doubleValue(),
            matchingScore.doubleValue(),
            notMatchingScore.doubleValue(),

            appliedMultiplier.doubleValue(),
            percentageCap.doubleValue(),
            maxPossibleScore.doubleValue(),
            maxAllowedScore.doubleValue(),

            targetScore.doubleValue()
        );
    }

    private static BigDecimal maxPossibleScore(List<ProductVisibility> visibilities, int matchingSize) {
        var counter = 0;
        var position = 0;
        var maxPossibleScore = BigDecimal.ZERO;
        while (counter < matchingSize && position < visibilities.size()) {
            var v = visibilities.get(position);
            if (v.matching() || !v.locked()) {
                maxPossibleScore = maxPossibleScore.add(valueOf(Tier.getScore(position)));
                counter++;
            }
            position++;
        }
        return maxPossibleScore;
    }

    enum Tier {
        TIER_1(100, 7, null),
        TIER_2(67, 15, TIER_1),
        TIER_3(40, 23, TIER_2),
        TIER_4(25, 31, TIER_3),
        TIER_5(17, 39, TIER_4),
        TIER_6(12, 55, TIER_5),
        TIER_7(9, 71, TIER_6),
        TIER_8(7, 87, TIER_7),
        TIER_9(5, 103, TIER_8),
        TIER_10(4, Integer.MAX_VALUE, TIER_9);

        final int score;
        final int left;
        final int right;
        final int size;
        final Tier previousTier;

        Tier(int score, int right, Tier previousTier) {
            this.score = score;
            this.previousTier = previousTier;
            this.left = previousTier != null ? previousTier.right + 1 : 0;
            this.right = right;
            this.size = this.right - this.left + 1;
        }

        static Tier getTier(int index) {
            return Arrays.stream(values())
                .filter(tier -> index <= tier.right)
                .findFirst()
                .orElse(TIER_10);
        }

        static int minPossibleGain(int maxIndex) {
            var t = getTier(maxIndex);
            if (t.previousTier != null) {
                return t.previousTier.score - t.score;
            }
            return 0;
        }

        static Tier getNextTier(Tier currentTier) {
            return Arrays.stream(values())
                .filter(t -> t.left == currentTier.right + 1)
                .findFirst()
                .orElse(null);
        }

        static int getNextTierScoreGain(Tier currentTier) {
            return currentTier.score - Tier.getNextTier(currentTier).score;
        }

        static int getScore(int index) {
            return getTier(index).score;
        }

        static int getTotalScoreUpTo(int index) {
            var t = getTier(index);
            //current partial tier score
            var totalScore = (index - t.left + 1) * t.score;
            while (t.previousTier != null) {
                t = t.previousTier;
                totalScore += t.size * t.score;
            }
            return totalScore;
        }
    }
}
