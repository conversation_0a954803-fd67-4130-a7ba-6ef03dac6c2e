package com.groupbyinc.search.ssa.application.core.search.variantrollupkeys;

import com.google.gson.JsonElement;
import com.google.protobuf.Value;
import io.micronaut.core.annotation.Nullable;
import jakarta.inject.Singleton;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import static com.groupbyinc.search.ssa.util.Constants.COUNT;
import static com.groupbyinc.search.ssa.util.Constants.KEY;
import static com.groupbyinc.search.ssa.util.Constants.VALUE;
import static com.groupbyinc.search.ssa.util.JSONUtils.toElementsStream;
import static com.groupbyinc.search.ssa.util.JSONUtils.toJsonObject;

@Singleton
public class VariantRollupValuesConverter {

    /**
     * Converts the variant rollup values from the Google protobuf format.
     *
     * @param variantsRollUpValues the variant rollup values in protobuf format
     * @return the variant rollup values in a list of maps
     */
    public List<Map<String, Object>> convertVariantRollupValues(Map<String, Value> variantsRollUpValues) {
        return variantsRollUpValues.entrySet().stream()
            // with this filter we suppress FulfillmentInfo variant rollup values, which has a different format,
            // as they are not used by customers
            .filter(entry -> entry.getValue().hasListValue())
            .map(this::convertListValue)
            .toList();
    }

    private Map<String, Object> convertListValue(Entry<String, Value> entry) {
        var protoList = entry.getValue().getListValue();
        var values = protoList.getValuesList().stream()
            .map(this::convertSingleValue)
            .toList();
        return Map.of(
            KEY, entry.getKey(),
            COUNT, values.size(),
            VALUE, values
        );
    }

    private Object convertSingleValue(Value protoValue) {
        return switch (protoValue.getKindCase()) {
            case STRING_VALUE -> protoValue.getStringValue();
            case NUMBER_VALUE -> protoValue.getNumberValue();
            case BOOL_VALUE -> protoValue.getBoolValue();
            default -> protoValue.getNullValue();
        };
    }

    /**
     * Converts the variant rollup values from the Object Map.
     *
     * @param variantsRollUpValues the variant rollup values in the Map format
     * @return the variant rollup values in a list of maps
     */
    public List<Map<String, Object>> convertVariantRollupObjects(Map<String, Object> variantsRollUpValues) {
        var rollupValues = toJsonObject(variantsRollUpValues);
        return rollupValues.entrySet().stream()
            // with this filter we suppress FulfillmentInfo variant rollup values, which has a different format,
            // as they are not used by customers
            .filter(entry -> entry.getValue().isJsonArray())
            .map(this::convertArrayValue)
            .toList();

    }

    private Map<String, Object> convertArrayValue(Entry<String, JsonElement> entry) {
        var values = toElementsStream(entry.getValue().getAsJsonArray())
            .map(this::convertSingleValue)
            .toList();
        return Map.of(
            KEY, entry.getKey(),
            COUNT, values.size(),
            VALUE, values
        );

    }

    private Object convertSingleValue(@Nullable JsonElement jsonElement) {
        if (jsonElement == null || jsonElement.isJsonNull()) {
            return null;

        } else if (jsonElement.isJsonPrimitive()) {
            var jsonjsonPrimitive = jsonElement.getAsJsonPrimitive();

            if (jsonjsonPrimitive.isBoolean()) {
                return jsonjsonPrimitive.getAsBoolean();

            } else if (jsonjsonPrimitive.isNumber()) {
                return jsonjsonPrimitive.getAsDouble();

            } else {
                return jsonjsonPrimitive.getAsString();
            }

        } else {
            return jsonElement.toString();
        }
    }
}
