package com.groupbyinc.search.ssa.application.core.search.filtering.filter;

import io.micronaut.core.annotation.NonNull;

/**
 * Interface for defining something that can be converted to a search filter.
 *
 * @param <T> type of filter object.
 */
@FunctionalInterface
public interface ToFilter<T> {

    /**
     * Convert the object into a search filter.
     *
     * @return a custom filter object
     */
    @NonNull
    T toFilter();

}
