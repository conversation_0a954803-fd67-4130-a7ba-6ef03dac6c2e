package com.groupbyinc.search.ssa.application.core.search.strategy;

import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;

import io.micronaut.core.annotation.NonNull;

import java.util.List;
import java.util.Optional;


/**
 * A search strategy is a way to apply a specific search logic to the search request.
 * <p>
 * Multiple {@code SearchStrategy} can be applied to a single search request, where each strategy
 * can decide whether to proceed with the next applicable strategy in the linked chain or not.
 */
public abstract class SearchStrategy {

    /**
     * The list of linked search strategies to be applied to the request.
     */
    private SearchStrategyPriorityList linkedSearchStrategies;

    /**
     * Links the current search strategy with the list of search strategies to be conditionally applied.
     *
     * @param searchStrategies the list of search strategies to be conditionally applied.
     *                         The order of the list is important as it defines the order of the search strategies to be applied.
     *
     * @return the current search strategy.
     */
    public SearchStrategy link(@NonNull List<SearchStrategy> searchStrategies) {
        this.linkedSearchStrategies = new SearchStrategyPriorityList(searchStrategies);
        return this;
    }

    /**
     * Get the first applicable search strategy in the linked chain.
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return the first applicable search strategy in the linked chain.
     */
    public Optional<SearchStrategy> getApplicableLinkedSearchStrategy(SearchParameters searchParameters) {
        return linkedSearchStrategies != null
            ? linkedSearchStrategies.getApplicableSearchStrategy(searchParameters)
            : Optional.empty();
    }

    /**
     * Run a search against the engine for given parameters.
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return {@link SearchResults} result of search call.
     */
    @NonNull
    public abstract SearchResults search(SearchParameters searchParameters);

    /**
     * Define if the search strategy has to be used within the current search request or not.
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return true if this strategy has to be applied, false otherwise.
     */
    public abstract boolean isApplicable(SearchParameters searchParameters);

    /**
     * Name of the strategy.
     */
    protected String name() {
        return this.getClass().getSimpleName();
    }

}
