package com.groupbyinc.search.ssa.application.core.search.strategy.fallback;

import com.groupbyinc.search.ssa.application.core.search.strategy.SearchStrategy;
import com.groupbyinc.search.ssa.core.Pagination;
import com.groupbyinc.search.ssa.core.SearchMetadata;
import com.groupbyinc.search.ssa.core.SearchMode;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.request.RequestServed;

import io.micronaut.core.annotation.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.ParametersAreNonnullByDefault;
import java.util.List;

/**
 * Used where we do not need to make external calls to search engines and would
 * rather return static contents to end users.
 */
@Slf4j
@RequiredArgsConstructor
@ParametersAreNonnullByDefault
public class FallbackSearchStrategy extends SearchStrategy {

    /**
     * There are search use-cases where we do not need to make external calls to
     * search engines and would rather return static contents to end users.
     * <p>
     * One such use case is when {@link Pagination#getSize() pageSize}=0 for the
     * Google search calls - quotas for clients are impacted even though no results
     * were requested.
     * For this reason, when a search call requests {@link Pagination#getSize() pageSize}=0
     * we do not send request to Google and only reply with the applicable rules
     * on our side of Command Center.
     * <p>
     * Flow: request -> rule processing -> response
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return result of search with static content only.
     */
    @NonNull
    @Override
    public SearchResults search(SearchParameters searchParameters) {
        log.debug("FallbackSearchStrategy used for search.");
        return SearchResults
            .builder()
            .numTotalRecords(0)
            .query(searchParameters.getQuery())
            .requestServed(RequestServed.STATIC)
            .metadata(SearchMetadata.builder().build())
            .correctedQuery(searchParameters.getQuery())
            .biasingProfile(searchParameters.getBiasingProfile())
            .pageCategories(searchParameters.getPageCategories())
            .searchStrategies(List.of(name()))
            .includeExpandedResults(searchParameters.getIncludeExpandedResults())
            .build();
    }

    /**
     * Define is this search strategy have to be used within the current search request or not.
     * This strategy applied when request page size is 0, and it is a product search request.
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return true if this strategy has to be applied, false otherwise.
     */
    @Override
    public boolean isApplicable(SearchParameters searchParameters) {
        return SearchMode.PRODUCT_SEARCH == searchParameters.getSearchMode()
            && searchParameters.getPagination().getSize() == 0;
    }

}
