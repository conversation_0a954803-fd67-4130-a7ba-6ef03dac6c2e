package com.groupbyinc.search.ssa.application.core.search.strategy;

import com.groupbyinc.search.ssa.core.SearchParameters;

import java.util.List;
import java.util.Optional;

/**
 * A list of search strategies ordered by priority.
 * The order of the search strategies is important as the first applicable search strategy should be applied.
 */
public record SearchStrategyPriorityList(List<SearchStrategy> searchStrategies) {

    public Optional<SearchStrategy> getApplicableSearchStrategy(SearchParameters searchParameters) {
        return searchStrategies.stream()
            .filter(strategy -> strategy.isApplicable(searchParameters))
            .findFirst();
    }

}
