package com.groupbyinc.search.ssa.application.builders;

import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.application.Clock;
import com.groupbyinc.search.ssa.application.logging.LoggingContext;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.area.AreaConfiguration;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.rule.Overwrites;
import com.groupbyinc.search.ssa.core.rule.PinnedRefinement;
import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;

import io.micronaut.context.annotation.Context;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.core.util.CollectionUtils;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

@Context
@RequiredArgsConstructor
public class SearchParametersPreparer {

    private final Clock clock;
    private final ConfigurationManager configurationManager;

    public void prepare(SearchRequestDto searchRequest,
                        SearchParameters.SearchParametersBuilder builder) {
        var context = getRequestContext();
        var merchandiser = context.getMerchandiser();
        var collection = context.getCollection();

        var area = getArea(merchandiser, collection, context.getArea());
        var navigationConfigs = configurationManager.getNavigations(merchandiser, area.id());

        var merchandisingConfiguration = createMerchandisingConfiguration(
            merchandiser,
            collection,
            area,
            searchRequest.getSite(),
            searchRequest.getOverwrites(),
            navigationConfigs
        );

        builder
            .pinnedRefinements(navigationsToPinRefinements(navigationConfigs))
            .merchandisingConfiguration(merchandisingConfiguration);

        var tenantSettings = configurationManager
            .getTenant(merchandiser)
            .orElseThrow()
            .settings();

        if (searchRequest.getIncludeExpandedResults() == null) {
            var includeExpandedResults = tenantSettings == null || tenantSettings.includeExpandedResults();
            builder.includeExpandedResults(includeExpandedResults);
        }

        if (searchRequest.getFacetLimit() == null) {
            var facetLimit = tenantSettings == null
                ? null
                : tenantSettings.facetLimit();
            builder.facetLimit(facetLimit);
        }
    }

    private AreaConfiguration getArea(Merchandiser merchandiser, String collection, String areaName) {
        return configurationManager
            .getArea(merchandiser, collection, areaName)
            .orElseThrow(() -> new NoSuchElementException("No area with name: " + areaName));
    }

    private MerchandisingConfiguration createMerchandisingConfiguration(Merchandiser merchandiser,
                                                                        String collection,
                                                                        AreaConfiguration area,
                                                                        String siteFilterName,
                                                                        @Nullable Overwrites overwrites,
                                                                        Set<NavigationConfiguration> navigations) {
        var configuration = MerchandisingConfiguration
            .builder()
            .areaConfiguration(area)
            .instantSupplier(clock::now)
            .navigationConfigurations(navigations)
            .features(configurationManager.getFeatures())
            .zones(configurationManager.getZones(merchandiser, area.id()))
            .redirectConfigurations(configurationManager.getRedirects(merchandiser, area.id()))
            .ruleConfigurations(getRuleForContextOrOverwrite(merchandiser, overwrites, area.id()))
            .attributeConfigurations(configurationManager.getAttributeConfiguration(merchandiser, collection))
            .biasingProfileConfigurations(configurationManager.getBiasingProfiles(merchandiser, area.id()));

        // resolve siteFilter
        var siteFilterConfiguration = getSiteFilter(merchandiser, collection, siteFilterName, area);
        if (siteFilterConfiguration.isPresent()) {
            var siteFilter = siteFilterConfiguration.get();
            LoggingContext.setSiteFilter(siteFilter);
            configuration.siteFilter(siteFilter);
        }

        return configuration.build();
    }

    /**
     * It gets rules from config and overwrites them if rules for overwritten were passed.
     *
     * @return overwritten rules or rules from config as is in case overwrites array is empty or null.
     */
    private Set<RuleConfiguration> getRuleForContextOrOverwrite(Merchandiser merchandiser,
                                                                @Nullable Overwrites overwrites,
                                                                Integer areaId) {
        var rules = configurationManager.getRules(merchandiser, areaId);

        if (overwrites != null && CollectionUtils.isNotEmpty(overwrites.rules())) {
            var rulesMappedById = rules.stream().collect(toMap(RuleConfiguration::id, identity()));

            for (var rule : overwrites.rules()) {
                if (rule.id() == -1) {
                    rulesMappedById.put(rule.id(), rule.copyRuleForOverwrite());
                } else {
                    rulesMappedById.put(rule.id(), rule);
                }
            }

            return new TreeSet<>(rulesMappedById.values());

        } else {
            return rules;
        }
    }

    private Optional<SiteFilterConfiguration> getSiteFilter(Merchandiser merchandiser,
                                                            String collection,
                                                            String siteFilterName,
                                                            AreaConfiguration area) {
        if (siteFilterName != null) {
            var filter = configurationManager.getSiteFiltersByName(
                merchandiser,
                collection,
                siteFilterName
            );
            if (filter.isEmpty()) {
                getRequestContext()
                    .addWarning("Not able to find site filter by name: [%s]".formatted(siteFilterName));
            }
            return filter;
        }
        if (area.siteFilterId() != null) {
            return configurationManager.getSiteFiltersById(
                merchandiser,
                collection,
                area.siteFilterId()
            );
        }
        return Optional.empty();
    }

    private List<PinnedRefinement> navigationsToPinRefinements(Set<NavigationConfiguration> navigations) {
        return navigations
            .stream()
            .filter(n -> CollectionUtils.isNotEmpty(n.pinnedRefinements()))
            .map(n -> new PinnedRefinement(n.field(), n.pinnedRefinements()))
            .toList();
    }

}
