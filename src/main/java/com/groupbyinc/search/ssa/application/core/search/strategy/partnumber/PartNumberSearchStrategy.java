package com.groupbyinc.search.ssa.application.core.search.strategy.partnumber;

import com.groupbyinc.search.ssa.application.core.search.strategy.SearchStrategy;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.partnumber.PartNumberSearchService;
import com.groupbyinc.search.ssa.productcatalog.ProductCatalogService;
import com.groupbyinc.search.ssa.util.debug.StrategyNameContext;

import io.micronaut.core.propagation.PropagatedContext;
import io.micronaut.core.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nonnull;
import javax.annotation.ParametersAreNonnullByDefault;
import java.time.LocalDateTime;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.core.Rewrites.PART_NUMBER_EXPANDED;
import static com.groupbyinc.search.ssa.core.Rewrites.PART_NUMBER_NO_REWRITE;
import static com.groupbyinc.search.ssa.core.Rewrites.PART_NUMBER_REWRITE;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_PART_NUMBER_SEARCH;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.PART_NUMBER_INDEX_DELAY_SECONDS;
import static com.groupbyinc.search.ssa.util.Constants.PART_NUMBER_SEARCHABLE;

import static java.time.ZoneOffset.UTC;

@Slf4j
@RequiredArgsConstructor
@ParametersAreNonnullByDefault
public class PartNumberSearchStrategy extends SearchStrategy {

    public static final String NO_PN_ATTRIBUTES_WARNING =
        "Part number search is enabled, but no 'partNumberSearchable' attributes are found. Please check the configuration of the catalog attributes.";
    public static final String PN_DISABLED_BY_FF_WARNING =
        "Part number search is disabled by the external configuration. The request parameter 'enablePartNumberSearch' is ignored.";

    private final PartNumberSearchService partNumberSearchService;
    private final ProductCatalogService productCatalogService;
    private final FeaturesManager featuresManager;
    private final int partNumberIndexDelaySeconds;

    /**
     * @param searchParameters the search parameters used to conduct a search.
     * @return {@link SearchResults} result of search call.
     */
    @Nonnull
    @Override
    public SearchResults search(SearchParameters searchParameters) {
        log.debug("PartNumberSearchStrategy is used for search.");

        try (var ignored = PropagatedContext.getOrEmpty().plus(new StrategyNameContext(name())).propagate()) {
            searchParameters.setPartNumberSearchEnabled(true);

            SearchResults response = getApplicableLinkedSearchStrategy(searchParameters)
                .map(searchStrategy -> searchStrategy.search(searchParameters))
                .orElseGet(() -> {
                    var searchResults = partNumberSearchService.search(searchParameters);
                    return productCatalogService.fillProductsWithMetadata(searchParameters, searchResults);
                });

            response.getSearchStrategies().add(name());

            if (searchParameters.isPartNumberExpansionEnabled()) {
                response.getRewrites().add(PART_NUMBER_EXPANDED.name());
                return response;
            }

            if (response.isFallbackSearchEngineUsed()) {
                response.getRewrites().add(PART_NUMBER_NO_REWRITE.name());
            } else {
                response.getRewrites().add(PART_NUMBER_REWRITE.name());
                partNumberSearchService.addPartNumberLabelToRecords(response);
            }

            return response;
        }
    }

    /**
     * Checks the following conditions to determine if this strategy has to be applied:
     * <ul>
     *     <li>Feature flag {@link FeaturesManager.FeatureFlagNames#ENABLE_PART_NUMBER_SEARCH} must be enabled</li>
     *     <li>If the feature flag is enabled, then optional Search Request parameter {@code enablePartNumberSearch} is considered:</li>
     *     <ul>
     *         <li>{@code true} or not set - perform a part number search based on the partNumberSearchable attributes
     *         <li>{@code false} - do not perform a part number search, regardless of attributes' state</li>
     *     </ul>
     *     <li>The search query is not empty</li>
     *     <li>At least one catalog attribute has {@code partNumberSearchable} flags set to {@code true}</li>
     * </ul>
     *
     * @param searchParameters the search parameters used to conduct a search.
     * @return true if this strategy has to be applied, false otherwise.
     */
    @Override
    public boolean isApplicable(SearchParameters searchParameters) {
        var context = getRequestContext();

        var partNumberSearchEnabled = isPartNumberSearchEnabled(searchParameters);
        var searchQueryExists = StringUtils.isNotEmpty(searchParameters.getQuery());

        var partNumberIndexingDelay = featuresManager.getNumberFeatureFlagConfiguration(
            context.getLdContext(),
            PART_NUMBER_INDEX_DELAY_SECONDS,
            partNumberIndexDelaySeconds
        );

        var partNumberSearchableAttributes = searchParameters.getMerchandisingConfiguration()
            .getPartNumberSearchableAttributes()
            .filter(attribute -> !PART_NUMBER_SEARCHABLE.equals(attribute.lastModifiedField())
                    || checkForIndexingDelay(attribute, partNumberIndexingDelay)
            ).toList();
        searchParameters.getPartNumberSearchableAttributes().addAll(partNumberSearchableAttributes);

        var partNumberSearchApplicable = partNumberSearchEnabled && searchQueryExists;

        if (partNumberSearchApplicable && partNumberSearchableAttributes.isEmpty()) {
            context.addWarning(NO_PN_ATTRIBUTES_WARNING);
        }

        return partNumberSearchApplicable && !partNumberSearchableAttributes.isEmpty();
    }

    private boolean isPartNumberSearchEnabled(SearchParameters searchParameters) {
        var context = getRequestContext();

        var requestFlag = searchParameters.getPartNumberSearchEnabled();
        var featureFlagEnabled = featuresManager
            .getBooleanFlagConfiguration(context.getLdContext(), ENABLE_PART_NUMBER_SEARCH);

        if (!featureFlagEnabled) {
            if (Boolean.TRUE.equals(requestFlag)) {
                context.addWarning(PN_DISABLED_BY_FF_WARNING);
            }

            return false;
        }

        // If the feature flag is enabled, honor the request parameter if provided
        return requestFlag == null || requestFlag;
    }

    private boolean checkForIndexingDelay(AttributeConfiguration attribute, int partNumberIndexingDelay) {
        return attribute.lastModifiedDate() == null
            || LocalDateTime.now(UTC).isAfter(attribute.lastModifiedDate().plusSeconds(partNumberIndexingDelay));
    }

}
