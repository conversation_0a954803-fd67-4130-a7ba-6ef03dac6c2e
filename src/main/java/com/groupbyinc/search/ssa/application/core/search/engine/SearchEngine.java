package com.groupbyinc.search.ssa.application.core.search.engine;

import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.metrics.opentelemetry.interceptors.ApiLatencyMetricsCollector;

import javax.annotation.Nonnull;

/**
 * An interface for the main functionality of a search engine that adapters should implement.
 */
public interface SearchEngine {

    /**
     * Run a search against the engine for given parameters.
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return {@link SearchResults} result of search call.
     */
    @Nonnull
    SearchResults search(@Nonnull SearchParameters searchParameters);

}
