package com.groupbyinc.search.ssa.application.core.search.productvisibility;

import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.application.core.search.processor.rule.RuleProcessorResult;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.RecordLabel;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.rule.ProductVisibilityBias;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.metrics.opentelemetry.interceptors.ApiLatencyMetricsCollector;
import com.groupbyinc.search.ssa.util.AttributeUtils;

import io.micronaut.core.util.CollectionUtils;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.groupbyinc.proto.commandcenter.config.AttributeMessage.AttributeType.NUMERICAL;
import static com.groupbyinc.proto.commandcenter.config.AttributeMessage.AttributeType.TEXTUAL;
import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.Score.Tier;
import static com.groupbyinc.search.ssa.application.core.search.productvisibility.Score.calculate;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_PRODUCT_VISIBILITY_BIAS;

import static java.util.Collections.swap;

@Slf4j
@Singleton
@RequiredArgsConstructor
public class ProductVisibilityBooster {
    private final FeaturesManager featuresManager;
    private final ConfigurationManager configurationManager;

    public record BoostingResult(List<Record> rerankedRecords, boolean reranked, Score score) {

        static BoostingResult notApplicable() {
            return new BoostingResult(List.of(), false, null);
        }
    }

    /**
     * Tries to boost 1st party products by assigning each product group a score and reordering products until gets to
     * target score.
     *
     * @param ruleProcessorResult Triggered rule, which expected to have Visibility Boost bias with configuration
     * @param records             List of records to reorder
     *
     * @return {@link BoostingResult} with boosting results
     */
    @ApiLatencyMetricsCollector
    public BoostingResult boostProducts(RuleProcessorResult ruleProcessorResult, List<Record> records) {
        var context = getRequestContext();

        try {
            var rule = ruleProcessorResult.triggeredRule().orElse(null);
            if (rule == null) {
                return BoostingResult.notApplicable();
            }

            var bias = rule.rule().getProductVisibilityBias();
            if (bias == null || !bias.isValid()) {
                return BoostingResult.notApplicable();
            }

            var enableRuleProductVisibilityBias = featuresManager.getBooleanFlagConfiguration(
                context.getLdContext(),
                ENABLE_PRODUCT_VISIBILITY_BIAS
            );
            if (!enableRuleProductVisibilityBias) {
                return BoostingResult.notApplicable();
            }

            var attribute = configurationManager
                .getAttributeConfiguration(context.getMerchandiser(), context.getCollection())
                .get(bias.attributeName());
            if (attribute == null) {
                log.warn("No attribute configuration found for name [{}]", bias.attributeName());
                return BoostingResult.notApplicable();
            }


            if (attribute.type() == NUMERICAL && CollectionUtils.isEmpty(bias.attributeNumberValues())) {
                log.warn("No values provided for numerical attribute [{}]", bias.attributeName());
                return BoostingResult.notApplicable();
            }
            if (attribute.type() == TEXTUAL && CollectionUtils.isEmpty(bias.attributeValues())) {
                log.warn("No values provided for textual attribute [{}]", bias.attributeName());
                return BoostingResult.notApplicable();
            }

            return boostAndReorder(bias, attribute, records);
        } catch (Exception e) {
            log.error("Error during visibility boost: {}", e.getMessage(), e);
            return BoostingResult.notApplicable();
        }
    }


    private static BoostingResult boostAndReorder(ProductVisibilityBias bias,
                                                  AttributeConfiguration attribute,
                                                  List<Record> records) {
        var visibilities = getProductVisibilities(bias, attribute, records);
        var score = calculate(visibilities, bias);

        if (score.score >= score.targetScore || score.maxPossibleScore == 0) {
            return BoostingResult.notApplicable();
        }

        boostVisibility(visibilities, score);

        if (score.newScore <= score.score) {
            log.info("Visibility boost didn't yield any score improvements");
            return BoostingResult.notApplicable();
        }

        var recordsById = records.stream().collect(Collectors.toMap(Record::getProductId, Function.identity()));
        var reordered = new ArrayList<Record>(records.size());
        visibilities.forEach(v -> reordered.add(recordsById.get(v.recordId())));
        if (reordered.size() != records.size()) {
            log.error(
                "Reordered records did not match expected size, original: {}, reordered: {}",
                records.size(), reordered.size()
            );
            return BoostingResult.notApplicable();
        }

        return new BoostingResult(reordered, true, score);
    }

    /**
     * Attempts to boost the matched products score by reordering the provided list of product visibilities, while
     * ensuring that the score does not exceed the specified target.
     * <p>
     * The method works by performing a series of swaps on the {@code visibilities} list, which is assumed to be ordered
     * by tiers that determine the score contributions of each element. Two main phases are:
     * </p>
     * <ol>
     *   <li>
     *     <strong>Matched product candidate Swaps with guarded start of collection:</strong>
     *     <p>
     *     The method searches for matched candidate positions from which a swap with the preceding element
     *     (if that element is a not matched product) can happen. Swap may or may not yield immediate score increase.
     *     Swaps without scoring is still beneficial because it gives future swaps a space within tiers.
     *     If the score gain from swapping does not exceed the deficit, and the scores are updated accordingly.
     *     </p>
     *   </li>
     *   <li>
     *     <strong>Not matching candidate Swaps:</strong>
     *     <p>
     *     If after the first phase there is still room to boost the score, the method then examines additional swap
     *     opportunities using a similar process for guarded positions. These swaps attempt to move not matching
     *     products out for further score increase.
     *     </p>
     * </ol>
     * <p>
     * The {@code visibilities} and {@code score} are modified in-place as elements are swapped.
     * </p>
     *
     * @param visibilities the list of {@link ProductVisibility} objects representing product visibilities.
     * @param score        {@link Score} object containing original and later new calculated scores.
     */
    private static void boostVisibility(List<ProductVisibility> visibilities, Score score) {
        var minPossibleGain = Tier.minPossibleGain(visibilities.size() - 1);
        var deficit = score.targetScore - score.score;
        var guardUpTo = 5;

        var matchingPosition = getNextMatchingCandidate(visibilities, guardUpTo);
        var matchingStartedFrom = matchingPosition;

        while (matchingPosition >= guardUpTo) {

            // here you might think we can check
            // - if swapping yields any score
            // - if next tier swapping overdo on target score
            // but this loop has two purposes:
            // - gain score if possible but not exceed deficit
            // - move elements with to give next swaps a space with smaller gains that still can be fit into deficit

            var prevPosition = matchingPosition - 1;
            var prevVisibility = visibilities.get(prevPosition);
            if (!prevVisibility.matching() && !prevVisibility.locked()) {
                var gain = Tier.getTier(prevPosition).score - Tier.getTier(matchingPosition).score;
                if (score.newScore + gain <= score.targetScore) {
                    swap(visibilities, matchingPosition, prevPosition);
                    if (gain > 0) {
                        score.newScore += gain;
                        score.newNotMatchingScore -= gain;
                        deficit -= gain;
                    }
                    matchingPosition = prevPosition;
                    if (matchingPosition == guardUpTo) {
                        matchingPosition = getNextMatchingCandidate(visibilities, matchingStartedFrom);
                        matchingStartedFrom = matchingPosition;
                    }
                } else {
                    // current swap will overdue on target score, but check if smaller gain is possible going further
                    if (deficit > minPossibleGain) {
                        matchingPosition = getNextMatchingCandidate(visibilities, matchingStartedFrom);
                        matchingStartedFrom = matchingPosition;
                        continue;
                    }
                    // already at max possible score
                    return;
                }
            } else {
                matchingPosition = getNextMatchingCandidate(visibilities, matchingStartedFrom);
                matchingStartedFrom = matchingPosition;
            }
        }

        // if there's still room for score, but 1st loop exhausted all options - look into guarded positions
        if (deficit > minPossibleGain) {
            var notMatchingPosition = getNextNotMatchingCandidate(visibilities, guardUpTo);
            var notMatchingStartedFrom = notMatchingPosition;

            // no point to get last spot, since there won't be any other to swap
            while (notMatchingPosition >= 0 && notMatchingPosition < visibilities.size() - 2) {
                var nextPosition = notMatchingPosition + 1;
                var nextVisibility = visibilities.get(nextPosition);

                var currentTier = Tier.getTier(notMatchingPosition);

                // this is a dead end, swapping here will overdue target score,
                // but we can't look for next because at this point rest of them are all pushed to the right
                // i.e. no way to get smaller score gain
                if (Tier.getNextTierScoreGain(currentTier) > deficit) {
                    return;
                }

                if (nextVisibility.matching() && !nextVisibility.locked()) {
                    var gain = currentTier.score - Tier.getTier(nextPosition).score;
                    if (score.newScore + gain <= score.targetScore) {
                        swap(visibilities, nextPosition, notMatchingPosition);
                        if (gain > 0) {
                            score.newScore += gain;
                            score.newNotMatchingScore -= gain;
                            deficit -= gain;
                        }
                        notMatchingPosition = nextPosition;
                    } else {
                        if (deficit > minPossibleGain) {
                            notMatchingPosition = getNextNotMatchingCandidate(visibilities, notMatchingStartedFrom);
                            notMatchingStartedFrom = notMatchingPosition;
                            continue;
                        }
                        // already at max possible score
                        return;
                    }
                } else {
                    notMatchingPosition = getNextNotMatchingCandidate(visibilities, notMatchingStartedFrom);
                    notMatchingStartedFrom = notMatchingPosition;
                }
            }
        }
    }

    /**
     * finds most left matching and not locked product
     */
    private static int getNextMatchingCandidate(List<ProductVisibility> visibilities, int startAt) {
        for (int i = startAt + 1; i < visibilities.size(); i++) {
            var v = visibilities.get(i);
            if (v.matching() && !v.locked()) {
                return i;
            }
        }
        return -1;
    }

    /**
     * finds most right not matching and not locked product
     */
    private static int getNextNotMatchingCandidate(List<ProductVisibility> visibilities, int startAt) {
        for (int i = startAt-1; i >= 0; i--) {
            var v = visibilities.get(i);
            if (!v.matching() && !v.locked()) {
                return i;
            }
        }
        return -1;
    }

    private static List<ProductVisibility> getProductVisibilities(ProductVisibilityBias bias,
                                                                  AttributeConfiguration attribute,
                                                                  List<Record> records) {
        return records.stream().map(item -> {
            var matched = hasMatch(
                attribute,
                bias.attributeValues(),
                bias.attributeNumberValues(),
                AttributeUtils.getAttributeValueFromRecord(item, attribute)
            );

            return ProductVisibility.of(matched, item.getLabel() == RecordLabel.PINNED, item.getProductId());
        }).collect(Collectors.toList());
    }

    private static boolean hasMatch(AttributeConfiguration attribute,
                                    List<String> attributes,
                                    List<Double> attributesNums,
                                    List<Object> recordAttrValList) {
        if (CollectionUtils.isEmpty(recordAttrValList)) {
            return false;
        }

        return attribute.type() == NUMERICAL
            ? recordAttrValList.stream().anyMatch(v -> attributesNums.contains(((Number) v).doubleValue()))
            : recordAttrValList.stream().anyMatch(v -> attributes.contains((String) v));
    }

}
