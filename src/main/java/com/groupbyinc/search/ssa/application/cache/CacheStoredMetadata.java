package com.groupbyinc.search.ssa.application.cache;

import com.groupbyinc.search.ssa.util.debug.MongoDebugInfo;
import com.groupbyinc.search.ssa.util.debug.RetailDebugInfo;

public record CacheStoredMetadata(RetailDebugInfo retailDebugInfo, MongoDebugInfo mongoDebugInfo) {

    public static CacheStoredMetadata retail(RetailDebugInfo retailDebugInfo) {
        return new CacheStoredMetadata(retailDebugInfo, null);
    }

    public static CacheStoredMetadata mongo(MongoDebugInfo mongoDebugInfo) {
        return new CacheStoredMetadata(null, mongoDebugInfo);
    }

}
