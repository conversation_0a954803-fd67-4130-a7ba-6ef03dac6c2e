package com.groupbyinc.search.ssa.application.configuration;

import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.area.AreaConfiguration;
import com.groupbyinc.search.ssa.core.tenant.TenantConfiguration;

import java.util.Map;

/**
 * Key that combines a merchandiser and an area id.
 */
public record ConfigKey(Merchandiser merchandiser, Integer areaId) {

    static ConfigKey createConfigKey(Map<Integer, TenantConfiguration> tenantsCacheById,
                                     Map<Integer, AreaConfiguration> areasCacheById,
                                     Integer areaId) {
        var area = areasCacheById.get(areaId);
        return new ConfigKey(
            Merchandiser.of(
                tenantsCacheById.get(area.tenantId()).name()
            ),
            area.id()
        );
    }

    static ConfigKey createConfigKey(Map<Integer, TenantConfiguration> tenantsCacheById,
                                     AreaConfiguration area) {
        return new ConfigKey(
            Merchandiser.of(
                tenantsCacheById.get(area.tenantId()).name()
            ),
            area.id()
        );
    }

}
