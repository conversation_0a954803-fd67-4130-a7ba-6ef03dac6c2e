package com.groupbyinc.search.ssa.application.core.search.filtering.expression.numerical;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.Expression;
import io.micronaut.core.annotation.NonNull;
import lombok.Getter;

/**
 * Expression for logical comparisons.
 * Used to filter products by specific numeric value in some facet(attribute).
 */
@Getter
public abstract class ComparisonExpression<T> extends Expression<T> {

    public static final String EQUAL = "=";
    public static final String NOT_EQUAL = "!=";
    public static final String GREATER_THAN = ">";
    public static final String GREATER_THAN_OR_EQUAL = ">=";
    public static final String LESS_THAN = "<";
    public static final String LESS_THAN_OR_EQUAL = "<=";

    /**
     * value which product should have in specific attribute.
     */
    @NonNull
    protected final Number value;

    /**
     * Operator to define a way to compare.
     */
    @NonNull
    protected final String operator;

    public ComparisonExpression(@NonNull String field, @NonNull String operator, @NonNull Number value) {
        super(field);
        this.value = value;
        this.operator = operator;
    }

}
