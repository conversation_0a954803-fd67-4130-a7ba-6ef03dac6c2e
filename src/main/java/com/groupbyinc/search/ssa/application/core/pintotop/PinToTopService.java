package com.groupbyinc.search.ssa.application.core.pintotop;

import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngine;
import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngineSelector;
import com.groupbyinc.search.ssa.core.Pagination;
import com.groupbyinc.search.ssa.core.ProductSearchResultWrapper;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.RecordLabel;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.rule.PinnedProduct;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.metrics.opentelemetry.MeterRegistry;
import com.groupbyinc.search.ssa.productcatalog.ProductCatalogService;
import com.groupbyinc.search.ssa.retail.GoogleSearchEngine;
import com.groupbyinc.search.ssa.retail.filtering.RetailFilterService;
import com.groupbyinc.search.ssa.retail.filteringold.RetailFilterServiceOld;
import com.groupbyinc.search.ssa.util.debug.StepNameContext;

import com.google.common.annotations.VisibleForTesting;
import io.micronaut.context.annotation.Prototype;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.propagation.PropagatedContext;
import io.micronaut.core.util.StringUtils;
import jakarta.inject.Named;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.ParametersAreNonnullByDefault;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.core.Record.addLabel;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_FETCH;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_PIN_TO_TOP;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_PINNED_PRODUCTS_FILTERING;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.USE_NEW_RETAIL_FILTERING;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.ContextUtils.attachContextForPinToTopSearch;
import static com.groupbyinc.search.ssa.retail.filteringold.RetailFilterServiceOld.AND_JOIN_TEMPLATE;
import static com.groupbyinc.search.ssa.retail.filteringold.RetailFilterServiceOld.PRODUCT_ID_FIELD;

import static java.lang.Math.max;
import static java.lang.Math.min;
import static java.util.concurrent.CompletableFuture.supplyAsync;
import static java.util.function.UnaryOperator.identity;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

@Slf4j
@Prototype
@ParametersAreNonnullByDefault
public class PinToTopService {

    public static final int PIN_TO_TOP_MAX_POSITION = 24;
    public static final String AVAILABILITY = "availability";
    public static final String OUT_OF_STOCK = "OUT_OF_STOCK";
    public static final String PINNED_SEARCH_ERROR_FORMAT = "Error while searching for pinned products: %s";
    public static final String PINNED_SEARCH_MISSING_RECORDS_FORMAT = "Cannot find following pinned records: %s";

    private final ExecutorService executor;
    private final MeterRegistry meterRegistry;
    private final SearchEngine googleSearchEngine;
    private final FeaturesManager featuresManager;
    private final RetailFilterService retailFilterService;
    private final RetailFilterServiceOld filterServiceOld;
    private final ProductCatalogService productCatalogService;

    @Setter
    private Function<SearchParameters, SearchResults> regularSearchExecutor;

    @SuppressWarnings("all")
    public PinToTopService(MeterRegistry meterRegistry,
                           FeaturesManager featuresManager,
                           RetailFilterServiceOld filterServiceOld,
                           RetailFilterService retailFilterService,
                           ProductCatalogService productCatalogService,
                           @Named("googleSearchEngine") SearchEngine googleSearchEngine,
                           @Named("default") SearchEngineSelector searchEngineSelector) {
        this.executor = Executors.newVirtualThreadPerTaskExecutor();
        this.filterServiceOld = filterServiceOld;
        this.meterRegistry = meterRegistry;
        this.featuresManager = featuresManager;
        this.googleSearchEngine = googleSearchEngine;
        this.retailFilterService = retailFilterService;
        this.productCatalogService = productCatalogService;
        this.regularSearchExecutor = (params) -> searchEngineSelector.getEngine(params).search(params);
    }

    /**
     * Makes two calls to Search API to get pinned products and regular search results, and then merge them.
     *
     * @param searchParameters the search parameters containing the pagination details and pinned products.
     *
     * @return the merged search results.
     */
    @NonNull
    public SearchResults search(SearchParameters searchParameters) {
        var pinnedProducts = getPinnedProductIds(searchParameters);

        // searchParameters.originalPagination is client request pagination
        // PinToTop strategy can be chained after another strategy, so it can't use searchParameters.originalPagination
        // to trim result set because it might break higher level strategy that also tinkering with pagination (Topsort)
        // So for PinToTop the original pagination is searchParameters.pagination
        var pinToTopOriginalPagination = searchParameters.getPagination();
        overrideSearchParametersForPinToTop(
            searchParameters,
            pinnedProducts,
            rewritePreFilter(searchParameters, pinnedProducts)
        );

        Supplier<SearchResults> regularSearchTask = () -> {
            var context = PropagatedContext.getOrEmpty().plus(new StepNameContext("regularSearchTask"));
            try (var ignored = context.propagate()) {
                var searchResults = regularSearchExecutor.apply(searchParameters);
                return productCatalogService.fillProductsWithMetadata(searchParameters, searchResults);
            }
        };
        var regularSearchFuture = supplyAsync(
            PropagatedContext.wrapCurrent(regularSearchTask),
            executor
        );

        var useNewRetailFiltering = featuresManager.getBooleanFlagConfiguration(
            getRequestContext().getLdContext(),
            USE_NEW_RETAIL_FILTERING
        );

        var filter = useNewRetailFiltering
            ? retailFilterService.createTextFieldFilter(PRODUCT_ID_FIELD, pinnedProducts)
            : filterServiceOld.createTextFieldFilter(PRODUCT_ID_FIELD, pinnedProducts);

        var searchParametersForPinned = getSearchParametersForPinnedProducts(
            searchParameters,
            filter,
            new Pagination(PIN_TO_TOP_MAX_POSITION, 0L)
        );

        Supplier<ProductSearchResultWrapper> pinnedSearchProductCatalogTask = () -> {
            var context = PropagatedContext
                .getOrEmpty()
                .plus(new StepNameContext("pinnedSearchProductCatalogTask"));
            try (var ignored = context.propagate()) {
                return pinnedSearchProductCatalog(
                    searchParametersForPinned,
                    pinnedProducts
                );
            }
        };
        Supplier<ProductSearchResultWrapper> pinnedSearchGoogleTask = () -> {
            var context = PropagatedContext
                .getOrEmpty()
                .plus(new StepNameContext("pinnedSearchGoogleTask"));
            try (var ignored = context.propagate()) {
                return pinnedSearchGoogle(
                    searchParametersForPinned,
                    pinnedProducts
                );
            }
        };

        var pinnedProductsFuture = isProductCatalogSearchUsed()
            ? supplyAsync(PropagatedContext.wrapCurrent(pinnedSearchProductCatalogTask), executor)
            : supplyAsync(PropagatedContext.wrapCurrent(pinnedSearchGoogleTask), executor);

        return meterRegistry.act(() -> {
            var pinToTopResults = new AtomicReference<ProductSearchResultWrapper>();
            var context = PropagatedContext.getOrEmpty(); // add context to the scope of lambda expression.
            var result = CompletableFuture.allOf(regularSearchFuture, pinnedProductsFuture).thenApplyAsync(
                aVoid -> {
                    try (var ignore = context.propagate()) {
                        var pinnedSearchResult = pinnedProductsFuture.join();
                        pinToTopResults.set(pinnedSearchResult);
                        return mergeResults(
                            regularSearchFuture.join(),
                            pinnedSearchResult.records(),
                            searchParameters,
                            pinToTopOriginalPagination
                        );
                    }
                },
                executor
            ).join();
            attachContextForPinToTopSearch(pinToTopResults.get());
            return result;
        });
    }

    @VisibleForTesting
    void overrideSearchParametersForPinToTop(SearchParameters searchParameters,
                                             List<String> pinnedProductIds,
                                             String preFilter) {
        var customerOffset = searchParameters.getPagination().getOffset();
        var adjustedOffset = max(customerOffset - pinnedProductIds.size(), 0);
        var adjustedPageSize = searchParameters.getPagination().getSize() + pinnedProductIds.size();

        searchParameters.setPreFilter(preFilter);
        searchParameters.setPagination(new Pagination(adjustedPageSize, adjustedOffset));

        var facetConverter = searchParameters.getRetailFacetConverter();
        searchParameters.setVariantRollupKeys(
            facetConverter.buildVariantRollupKeys(searchParameters.getVariantRollupKeys())
        );
    }

    @VisibleForTesting
    SearchParameters getSearchParametersForPinnedProducts(SearchParameters searchParameters,
                                                          String preFilter,
                                                          Pagination pagination) {
        return searchParameters
            .toBuilder()
            .preFilter(preFilter)
            .pagination(pagination)
            .originalPagination(pagination)
            .build();
    }

    private long calculateOffsetDelta(SearchParameters searchParameters) {
        return searchParameters.getOriginalPagination().getOffset() - searchParameters.getPagination().getOffset();
    }

    private boolean isProductCatalogSearchUsed() {
        var context = getRequestContext().getLdContext();

        return featuresManager.getBooleanFlagConfiguration(context, ENABLE_DATA_CATALOG_FETCH)
            && featuresManager.getBooleanFlagConfiguration(context, ENABLE_DATA_CATALOG_PIN_TO_TOP);
    }

    private String rewritePreFilter(SearchParameters searchParameters, List<String> pinnedProductIds) {
        // exclude pinned products from the regular search results to avoid duplication
        var useNewRetailFiltering = featuresManager.getBooleanFlagConfiguration(
            getRequestContext().getLdContext(),
            USE_NEW_RETAIL_FILTERING
        );

        var filter = useNewRetailFiltering
            ? retailFilterService.createTextFieldFilter(PRODUCT_ID_FIELD, pinnedProductIds, true)
            : filterServiceOld.createTextFieldFilter(PRODUCT_ID_FIELD, pinnedProductIds, true);

        return StringUtils.isEmpty(searchParameters.getPreFilter())
            ? filter
            : AND_JOIN_TEMPLATE.formatted(searchParameters.getPreFilter(), filter);
    }

    private ProductSearchResultWrapper pinnedSearchProductCatalog(SearchParameters searchParameters,
                                                                  List<String> pinnedProductIds) {
        try {
            var pinnedProductsFilteringEnabled = pinnedProductsFilteringEnabled();
            return productCatalogService.getPinnedProducts(
                searchParameters,
                new HashSet<>(pinnedProductIds),
                pinnedProductsFilteringEnabled
            );
        } catch (Exception e) {
            var errorMessage = PINNED_SEARCH_ERROR_FORMAT.formatted(e.getMessage());
            log.error("Failing back to SearchGoogle, Product Catalog error: {}", errorMessage);

            getRequestContext().addWarning(errorMessage);

            var context = PropagatedContext.getOrEmpty().plus(new StepNameContext("retailFallback"));
            try (var ignored = context.propagate()) {
                return pinnedSearchGoogle(searchParameters, pinnedProductIds);
            }
        }
    }

    private ProductSearchResultWrapper pinnedSearchGoogle(SearchParameters searchParameters,
                                                          List<String> pinnedProductIds) {
        try {
            var pinnedRecords = ((GoogleSearchEngine) googleSearchEngine).searchForProducts(searchParameters);

            if (pinnedProductIds.size() > pinnedRecords.records().size()) {
                var returned = pinnedRecords.records().stream().map(Record::getProductId).collect(toSet());

                var missingIds = pinnedProductIds
                    .stream()
                    .filter(id -> !returned.contains(id))
                    .toList();

                var missingRecordsMsg = PINNED_SEARCH_MISSING_RECORDS_FORMAT.formatted(missingIds);
                log.warn("Google Cloud Retail. {}", missingRecordsMsg);

                getRequestContext().addWarning(missingRecordsMsg);
            }

            if (!pinnedProductsFilteringEnabled()) {
                return pinnedRecords;
            }

            var relatedPinnedRecords = productCatalogService.getPinnedProducts(
                searchParameters,
                pinnedRecords.records().stream().map(Record::getProductId).collect(toSet()),
                true
            );

            return new ProductSearchResultWrapper(relatedPinnedRecords.records(), pinnedRecords.servedFrom());
        } catch (Exception e) {
            var errorMessage = PINNED_SEARCH_ERROR_FORMAT.formatted(e.getMessage());
            log.warn("Google Cloud Retail: {}", errorMessage);

            getRequestContext().addWarning(errorMessage);

            return ProductSearchResultWrapper.builder().records(List.of()).build();
        }
    }

    private List<String> getPinnedProductIds(SearchParameters searchParameters) {
        return searchParameters.getPinnedProducts().stream()
            .map(PinnedProduct::productId)
            .toList();
    }

    /**
     * Merges the regular search results with the pinned products search results.
     * <p/>
     * <img src="doc-files/Pinned_Products.png" alt="Pinned products search results merging logic">
     * <p/>
     *
     * @param regularSearchResults       the regular search results.
     * @param pinnedProductsSearchResult the pinned products search results.
     * @param searchParameters           original search parameters containing the pagination details and pinned
     *                                   products positions.
     *
     * @return the merged search results containing pinned products at the appropriate positions.
     */
    private SearchResults mergeResults(SearchResults regularSearchResults,
                                       List<Record> pinnedProductsSearchResult,
                                       SearchParameters searchParameters,
                                       Pagination pinToTopOriginalPagination) {

        var customerOffset = (int) pinToTopOriginalPagination.getOffset();
        var customerPageSize = pinToTopOriginalPagination.getSize();

        var pinnedPositionsByProductId = searchParameters.getPinnedProducts()
            .stream()
            .collect(toMap(PinnedProduct::productId, PinnedProduct::position));

        var includeOutOfStockProductIds = searchParameters.getPinnedProducts()
            .stream()
            .filter(PinnedProduct::includeOutOfStock)
            .map(PinnedProduct::productId)
            .collect(toSet());

        var pinnedProductsInStock = pinnedProductsSearchResult.stream()
            .filter(p -> includeOutOfStock(p, includeOutOfStockProductIds))
            .map(record -> record
                .withLabel(RecordLabel.PINNED)
                .withLabels(addLabel(record, RecordLabel.PINNED)))
            .toList();

        var numberOfPinnedProductsBeforeOffset = pinnedProductsInStock.stream()
            .filter(record -> {
                var position = pinnedPositionsByProductId.get(record.getProductId());
                return position <= customerOffset;
            })
            .count();

        // pinned products by position map that are in the customer's page range
        var pinnedProductsInPageByPosition = getFilteredPinnedProductsByPositionInStock(
            pinnedPositionsByProductId,
            entry -> entry.getValue() > customerOffset && entry.getValue() <= (customerOffset + customerPageSize),
            pinnedProductsInStock
        );

        // Customer offset was shifted to the left by the 'offset delta' value based on the potential pinned products
        // number. We need to adjust it depending on the number of found pinned products before the original offset.
        var offsetDelta = calculateOffsetDelta(searchParameters);
        var offsetDeltaAdjusted = (int) max(offsetDelta - numberOfPinnedProductsBeforeOffset, 0);

        // make it mutable
        var recordsFromSearch = new ArrayList<>(regularSearchResults.getRecords());

        List<Record> resultPage;

        if (!recordsFromSearch.isEmpty()) {
            // create a sublist of the regular search results that will be filled with the pinned products
            resultPage = recordsFromSearch.subList(
                offsetDeltaAdjusted,
                min(
                    offsetDeltaAdjusted + customerPageSize - pinnedProductsInPageByPosition.size(),
                    recordsFromSearch.size()
                )
            );

            new TreeMap<>(pinnedProductsInPageByPosition).forEach(
                (position, record) -> {
                    // normalize position to be zero-based and relative to the customer's page
                    var normalizedPosition = position - 1 - customerOffset;

                    if (normalizedPosition < resultPage.size()) {
                        resultPage.add(normalizedPosition, record);
                    }
                }
            );
        } else {
            resultPage = recordsFromSearch;
        }

        // if the regular search results page is not full, append the following pinned products to the end
        if (resultPage.size() < customerPageSize) {
            // pinned products by position map that are beyond the customer's page range
            var pinnedProductsByPositionToAppend = getFilteredPinnedProductsByPositionInStock(
                pinnedPositionsByProductId,
                entry -> entry.getValue() > (customerOffset + resultPage.size()),
                pinnedProductsInStock
            );

            appendPinnedProductsToTheEnd(resultPage, pinnedProductsByPositionToAppend, customerPageSize);
        }

        return regularSearchResults
            .toBuilder()
            .records(resultPage)
            .numTotalRecords(regularSearchResults.getNumTotalRecords() + pinnedProductsInStock.size())
            .build();
    }

    private Map<Integer, Record> getFilteredPinnedProductsByPositionInStock(
        Map<String, Integer> pinnedPositionsByProductId,
        Predicate<Entry<String, Integer>> predicate,
        List<Record> pinnedProductsInStock
    ) {
        var pinnedPositionsByProductIdFiltered = pinnedPositionsByProductId.entrySet()
            .stream()
            .filter(predicate)
            .collect(toMap(Entry::getKey, Entry::getValue));

        return pinnedProductsInStock.stream()
            .filter(record -> pinnedPositionsByProductIdFiltered.containsKey(record.getProductId()))
            .collect(toMap(record -> pinnedPositionsByProductIdFiltered.get(record.getProductId()), identity()));
    }

    private void appendPinnedProductsToTheEnd(List<Record> resultPage,
                                              Map<Integer, Record> pinnedPositionsByProductId,
                                              int customerPageSize) {
        new TreeMap<>(pinnedPositionsByProductId).forEach(
            (position, record) -> {
                if (resultPage.size() >= customerPageSize) {
                    return;
                }
                resultPage.add(record);
            }
        );
    }

    private boolean includeOutOfStock(Record record, Set<String> includeOutOfStockProductIds) {
        if (pinnedProductsFilteringEnabled()
            && includeOutOfStockProductIds.contains(record.getProductId())) {
            return true;
        }
        return record.getMetadata().get(AVAILABILITY) == null
            || !record.getMetadata().get(AVAILABILITY).equals(OUT_OF_STOCK);
    }

    private boolean pinnedProductsFilteringEnabled() {
        return featuresManager.getBooleanFlagConfiguration(
            getRequestContext().getLdContext(),
            ENABLE_PINNED_PRODUCTS_FILTERING
        );
    }

}
