package com.groupbyinc.search.ssa.application.logging;

import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;

import io.micronaut.context.propagation.slf4j.MdcPropagationContext;
import io.micronaut.core.propagation.MutablePropagatedContext;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.util.Collections;

public class LoggingContext {

    public static final String CUSTOMER_MDC_KEY = "customer";
    public static final String AREA_MDC_KEY = "area";
    public static final String COLLECTION_MDC_KEY = "collection";
    public static final String VISITOR_ID_MDC_KEY = "visitorId";
    public static final String REQUEST_ID = "requestId";

    public static final String SITE_FILTER_MDC_KEY = "siteFilter";
    public static final String RULE_CONTEXT_MDC_KEY = "ruleContext";
    public static final String REQUEST_TYPE_MDC_KEY = "requestType";
    public static final String REQUEST_SAMPLED_MDC_KEY = "requestSampled";
    
    public static final String EVENT_TYPE_MDC_KEY = "eventType";
    public static final String EVENT_SUBTYPE_MDC_KEY = "eventSubtype";
    public static final String APP_NAME_MDC_KEY = "applicationName";
    public static final String SERVER_TIME_MDC_KEY = "serverTime";

    private LoggingContext() {
    }

    public static void set(RequestContext context) {
        var mdcCopy = MDC.getCopyOfContextMap() != null ? MDC.getCopyOfContextMap() : Collections.emptyMap();
        if (!mdcCopy.containsKey(CUSTOMER_MDC_KEY)) {
            MDC.put(CUSTOMER_MDC_KEY, context.getMerchandiser().merchandiserId());
        }
        if (!mdcCopy.containsKey(COLLECTION_MDC_KEY)) {
            MDC.put(COLLECTION_MDC_KEY, context.getCollection());
        }
        if (!mdcCopy.containsKey(AREA_MDC_KEY)) {
            MDC.put(AREA_MDC_KEY, String.valueOf(context.getArea()));
        }
        if (!mdcCopy.containsKey(REQUEST_ID)) {
            MDC.put(REQUEST_ID, context.getRequestId());
        }
        if (!mdcCopy.containsKey(VISITOR_ID_MDC_KEY)) {
            MDC.put(VISITOR_ID_MDC_KEY, context.getVisitorId());
        }
    }

    public static void set(RequestContext context, String requestType) {
        set(context);

        if (StringUtils.isNotBlank(requestType)) {
            MDC.put(REQUEST_TYPE_MDC_KEY, requestType);
        }
    }

    public static void setSiteFilter(SiteFilterConfiguration siteFilter) {
        MDC.put(LoggingContext.SITE_FILTER_MDC_KEY, "%s:%s".formatted(siteFilter.id(), siteFilter.name()));
    }

    public static void setRuleContext(RuleConfiguration rule) {
        MDC.put(LoggingContext.RULE_CONTEXT_MDC_KEY, "Rule: name='%s', id=%d".formatted(rule.getName(), rule.id()));
    }

    public static void propagateMDC(MutablePropagatedContext propagatedContext) {
        var ctx = propagatedContext.getContext().find(MdcPropagationContext.class);
        if (ctx.isPresent()) {
            propagatedContext.replace(ctx.get(), new MdcPropagationContext());
        } else {
            propagatedContext.add(new MdcPropagationContext());
        }
    }

}
