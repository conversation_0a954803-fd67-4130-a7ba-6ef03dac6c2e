package com.groupbyinc.search.ssa.application.core.search.strategy.topsort;

import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_TOP_SORT;

import javax.annotation.ParametersAreNonnullByDefault;

import com.groupbyinc.search.ssa.api.error.ProcessingException;
import com.groupbyinc.search.ssa.application.core.search.strategy.SearchStrategy;
import com.groupbyinc.search.ssa.core.Pagination;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.productcatalog.ProductCatalogService;
import com.groupbyinc.search.ssa.topsort.TopSortService;
import com.groupbyinc.search.ssa.util.debug.StrategyNameContext;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.propagation.PropagatedContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
@ParametersAreNonnullByDefault
public class TopSortSearchStrategy extends SearchStrategy {

    private final TopSortService topsortService;
    private final FeaturesManager featuresManager;
    private final ProductCatalogService productCatalogService;

    /**
     * Applies TopSort modifications on top of any other search request.
     * Flow:
     * [(google | (google → (google | data-catalog-fetch)))] → topSort → data-catalog-fetch → response
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return {@link SearchResults} result of search call.
     */
    @NonNull
    @Override
    public SearchResults search(SearchParameters searchParameters) {
        log.debug("TopSortSearchStrategy used for search.");

        try (var ignored = PropagatedContext.getOrEmpty().plus(new StrategyNameContext(name())).propagate()) {
            overrideSearchParametersForTopSort(
                searchParameters,
                paginationOverride(searchParameters.getOriginalPagination())
            );

            var searchResults = getApplicableLinkedSearchStrategy(searchParameters)
                .orElseThrow(() -> new ProcessingException("Applicable search strategy not found."))
                .search(searchParameters);

            var auctionIds = searchResults.getRecords().stream()
                .map(Record::getPrimaryProductId)
                .toList();

            var response = topsortService.fillWithSponsoredProducts(searchParameters, searchResults, auctionIds, false);
            response.getSearchStrategies().add(name());

            // now we want to request data from fetch.
            searchParameters.setSkipProductCatalog(false);

            return productCatalogService.fillProductsWithMetadata(searchParameters, response);
        }
    }

    /**
     * Checks whether we need to apply Topsort flow for current search request.
     * Considering multiple parameters such as:
     * <ul>
     *     <li>Search mode</li>
     *     <li>Original request pagination</li>
     *     <li>Original request topsort flag (overrides feature flag)</li>
     *     <li>Feature flag {@link FeaturesManager.FeatureFlagNames#ENABLE_TOP_SORT}</li>
     *     <li>Is top sort api key enabled or not</li>
     * </ul>
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return true if this strategy has to be applied, false otherwise.
     */
    @Override
    public boolean isApplicable(SearchParameters searchParameters) {
        var checker = new TopSortApplicabilityChecker(
            featuresManager,
            topsortService,
            ENABLE_TOP_SORT,
            Boolean.TRUE.equals(searchParameters.getTopSortEnabled())
        );
        return checker.isApplicable(searchParameters);
    }

    private Pagination paginationOverride(Pagination originalPagination) {
        if (topsortService.getMinRequestPageSize() <= originalPagination.getSize()) {
            return originalPagination;
        }

        return new Pagination(
            topsortService.getMinRequestPageSize(),
            originalPagination.getOffset()
        );
    }

    private void overrideSearchParametersForTopSort(SearchParameters searchParameters, Pagination pagination) {
        // Do not request fetch service if it is a request with "topsort" data.
        // Fetch service will be requested after the "topsort" call.
        searchParameters.setSkipProductCatalog(true);
        searchParameters.setPagination(pagination);
    }

}
