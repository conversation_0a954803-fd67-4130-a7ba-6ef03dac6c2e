package com.groupbyinc.search.ssa.application.core.search.filtering.filter;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import lombok.Singular;

import java.util.List;

import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultList;

/**
 * Accumulate logical filters.
 * Actually, any final filter was created from this object.
 */
public abstract class Filter<T> implements ToFilter<T> {

    /**
     * Stores expressions which are need to be joined as logical "AND".
     */
    @NonNull
    protected final List<ToFilter<T>> ands;

    /**
     * Stores expressions which are need to be joined using logical "OR".
     */
    @NonNull
    protected final List<ToFilter<T>> ors;

    public Filter(@Nullable @Singular List<ToFilter<T>> ands, @Nullable @Singular List<ToFilter<T>> ors) {
        this.ands = notNullOrDefaultList(ands);
        this.ors = notNullOrDefaultList(ors);
    }

    public void addToAnds(ToFilter<T> filter) {
        this.ands.add(filter);
    }

    public void addToAnds(List<ToFilter<T>> filter) {
        this.ands.addAll(filter);
    }

    public void addToOrs(ToFilter<T> filter) {
        this.ors.add(filter);
    }

    public void addToOrs(List<ToFilter<T>> filter) {
        this.ors.addAll(filter);
    }

    /**
     * Check is any filters are applied or not.
     *
     * @return true if this object contains at least one filter, false otherwise.
     */
    public boolean hasFilters() {
        return !ands.isEmpty() || !ors.isEmpty();
    }

}
