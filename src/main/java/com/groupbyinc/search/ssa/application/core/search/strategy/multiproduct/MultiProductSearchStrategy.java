package com.groupbyinc.search.ssa.application.core.search.strategy.multiproduct;

import com.groupbyinc.search.ssa.application.core.search.strategy.SearchStrategy;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.util.debug.StrategyNameContext;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.propagation.PropagatedContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.ParametersAreNonnullByDefault;
import java.util.function.Predicate;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.application.core.search.strategy.multiproduct.MultiProductSearchService.PRODUCT_ID_REFINEMENT_FIELD;
import static com.groupbyinc.search.ssa.core.SearchMode.PRODUCT_SEARCH;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_MULTI_PRODUCT_SEARCH;

@Slf4j
@RequiredArgsConstructor
@ParametersAreNonnullByDefault
public class MultiProductSearchStrategy extends SearchStrategy {

    private static final Predicate<SelectedRefinement> CONTAINS_PRODUCT_ID_REFINEMENTS = refinement ->
        PRODUCT_ID_REFINEMENT_FIELD.equals(refinement.getField());

    private final FeaturesManager featuresManager;
    private final MultiProductSearchService multiProductSearchService;

    /**
     * This is a search strategy applied when we receive a request where we request
     * specific products by passing selected navigations.
     * Flow: request → data-catalog (fallback to google if error) → response
     *
     * @param searchParameters the search parameters used to conduct a search.
     *
     * @return {@link SearchResults} result of search call.
     */
    @NonNull
    @Override
    public SearchResults search(SearchParameters searchParameters) {
        log.debug("MultiProductSearchStrategy used for search.");

        try (var ignored = PropagatedContext.getOrEmpty().plus(new StrategyNameContext(name())).propagate()) {
            var response = multiProductSearchService.search(searchParameters);
            response.getSearchStrategies().add(name());
            return response;
        }
    }

    @Override
    public boolean isApplicable(SearchParameters searchParameters) {
        var context = getRequestContext().getLdContext();

        return searchParameters.getSearchMode() == PRODUCT_SEARCH
            && searchParameters.getQuery().isEmpty()
            && searchParameters.getRefinements().stream().anyMatch(CONTAINS_PRODUCT_ID_REFINEMENTS)
            && featuresManager.getBooleanFlagConfiguration(context, ENABLE_MULTI_PRODUCT_SEARCH);
    }

}
