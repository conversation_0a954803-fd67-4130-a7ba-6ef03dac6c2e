package com.groupbyinc.search.ssa.application.core.search.processor.rule;

import lombok.Builder;

import java.time.Duration;
import java.util.Optional;

@Builder
public record RuleProcessorResult (Integer numTotalRules,
                                   Integer numRulesProcessed,
                                   Duration processingDuration,
                                   Optional<TriggeredRule> triggeredRule) {

    public static RuleProcessorResult DEFAULT = new RuleProcessorResult(0, 0, Duration.ofNanos(0), Optional.empty());

}
