package com.groupbyinc.search.ssa.util.abtest;

/**
 * A pure Java implementation of the Murmur 3 hashing algorithm as presented at
 * <a href="https://sites.google.com/site/murmurhash/">Murmur Project</a>
 * Code is ported from the original C++ source at
 * <a href="https://code.google.com/p/smhasher/source/browse/trunk/MurmurHash3.cpp">MurmurHash3.cpp</a>
 *
 * It is a short implementation of
 * <a href="https://mvnrepository.com/artifact/com.sangupta/murmur/1.0.0">com.sangupta</a>
 * we need to implement it because this library has some Vulnerabilities we need
 * only 'hash_x86_32' rerlated methods.
 */
@SuppressWarnings("all")
public class Murmur {

    /**
     * The maximum value of hash which may be produced by this algorithm.
     */
    public static final long MAX_HASH_VALUE = (long) Math.floor(Math.pow(2, 32));

    /**
     * Seed used in hash function.
     */
    private static final long SEED = 2321168210L;

    /**
     * Helps convert a byte into its unsigned value
     */
    public static final int UNSIGNED_MASK = 0xff;

    /**
     * Helps convert integer to its unsigned value
     */
    public static final long UINT_MASK = 0xFFFFFFFFL;

    private static final int X86_32_C1 = 0xcc9e2d51;

    private static final int X86_32_C2 = 0x1b873593;

    /**
     * Compute the Murmur3 hash as described in the original source code.
     *
     * @param data the data that needs to be hashed
     * @param length the length of the data that needs to be hashed
     *
     * @return the computed hash value
     */
    public static long hash_x86_32(final byte[] data, int length) {
        final int nblocks = length >> 2;
        long hash = SEED;

        // Body ----------
        for(int i = 0; i < nblocks; i++) {
            final int i4 = i << 2;

            long k1 = (data[i4] & UNSIGNED_MASK);
            k1 |= (data[i4 + 1] & UNSIGNED_MASK) << 8;
            k1 |= (data[i4 + 2] & UNSIGNED_MASK) << 16;
            k1 |= (data[i4 + 3] & UNSIGNED_MASK) << 24;

            k1 = (k1 * X86_32_C1) & UINT_MASK;
            k1 = rotl32(k1, 15);
            k1 = (k1 * X86_32_C2) & UINT_MASK;

            hash ^= k1;
            hash = rotl32(hash,13);
            hash = (((hash * 5) & UINT_MASK) + 0xe6546b64L) & UINT_MASK;
        }

        // Tail ----------

        // Advance offset to the unprocessed tail of the data.
        int offset = (nblocks << 2); // nblocks * 2;
        long k1 = 0;

        switch (length & 3) {
            case 3:
                k1 ^= (data[offset + 2] << 16) & UINT_MASK;
            case 2:
                k1 ^= (data[offset + 1] << 8) & UINT_MASK;
            case 1:
                k1 ^= data[offset];
                k1 = (k1 * X86_32_C1) & UINT_MASK;
                k1 = rotl32(k1, 15);
                k1 = (k1 * X86_32_C2) & UINT_MASK;
                hash ^= k1;
        }

        // Finalization ----------

        hash ^= length;
        hash = fmix32(hash);

        return hash;
    }

    /**
     * Rotate left for 32 bits.
     */
    private static long rotl32(long original, int shift) {
        return ((original << shift) & UINT_MASK) | ((original >>> (32 - shift)) & UINT_MASK);
    }

    /**
     * fmix function for 32 bits.
     */
    private static long fmix32(long h) {
        h ^= (h >> 16) & UINT_MASK;
        h = (h * 0x85ebca6bL) & UINT_MASK;
        h ^= (h >> 13) & UINT_MASK;
        h = (h * 0xc2b2ae35L) & UINT_MASK;
        h ^= (h >> 16) & UINT_MASK;

        return h;
    }

}
