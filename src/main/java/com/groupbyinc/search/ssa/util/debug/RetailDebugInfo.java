package com.groupbyinc.search.ssa.util.debug;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import io.micronaut.core.annotation.Nullable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RetailDebugInfo implements DebugInfo {

    public static final String SEARCH_CALL_SUFFIX = "search";
    public static final String PIN_PRODUCTS_CALL_SUFFIX = "pinProducts";

    @Nullable
    private JsonNode retailSearchRequest;

    @Nullable
    private JsonNode retailSearchResponse;

    @JsonCreator
    public RetailDebugInfo(@Nullable @JsonProperty("rawSearchRequest") JsonNode retailSearchRequest,
                           @Nullable @JsonProperty("rawSearchResponse") JsonNode retailSearchResponse) {
        this.retailSearchRequest = retailSearchRequest;
        this.retailSearchResponse = retailSearchResponse;
    }

}