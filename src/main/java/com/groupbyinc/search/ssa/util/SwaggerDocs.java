package com.groupbyinc.search.ssa.util;

public interface SwaggerDocs {

    // SearchApi section START
    String SEARCH_TAG = "Search";

    String SEARCH_SUMMARY = "Provided search functionality";

    String SEARCH_OPERATION_DESCRIPTION = "Perform a search against the GroupBy Retail Search API.";
    String FACET_SEARCH_OPERATION_DESCRIPTION = "Perform a facet search against the GroupBy Retail Search API.";

    String FACET_SEARCH_ACCEPTED_DESCRIPTION = "Successful facet search response. Contains original request and navigation with relevant facet keys.";

    String SEARCH_REQUEST_DESCRIPTION = "Request that should be populated to configure a search API call, made by the client on behalf of a shopper.";
    String FACET_SEARCH_REQUEST_DESCRIPTION = "Request that should be populated to configure a search API call, made by the client on behalf of a shopper." +
        " Contains original request and information about facet for which extra keys requested.";

    String SEARCH_ACCEPTED_DESCRIPTION = "Search was accepted and a normal response could be generated.";
    // SearchApi section END

    // ProductApi section START
    String PRODUCT_TAG = "Product";

    String PRODUCT_SEARCH_SUMMARY = "Provided product search functionality";

    String PRODUCT_SEARCH_OPERATION_DESCRIPTION = "Perform a product search against the GroupBy Retail Search API.";

    String COLLECTION_PARAMETER_NAME = "collection";

    String COLLECTION_PARAMETER_DESCRIPTION = "Collection name";

    String PRODUCT_ID_PARAMETER_NAME = "productId";

    String PRODUCT_ID_PARAMETER_DESCRIPTION = "Product ID which need to be search";

    String PRODUCT_FOUND_DESCRIPTION = "Product is found.";

    String PRODUCT_DETAILS_SKIP_CACHE_NAME = "X-Groupby-Skip-Cache";
    String PRODUCT_DETAILS_SKIP_CACHE_DESCRIPTION = "Avoid reading cache results for underlying product details. Has no effect on search results.";
    // ProductApi section END

    // common error descriptions section START
    String BAD_REQUEST_DESCRIPTION = "Client has made a bad request, usually a validation constraint has been violated. See the message for further information.";

    String AUTHORIZATION_ERROR_DESCRIPTION = "Client is not authorized to perform the requested operation.";

    String INTERNAL_ERROR_DESCRIPTION = "There was an internal error processing the search request.";
    // common error descriptions section END

    // security area
    String CLIENT_KEY_SECURITY_REQUIREMENT = "ClientKey";
    String GROUP_BY_INC_EMPLOYEE_SECURITY_REQUIREMENT = "GroupByIncEmployee";

    // http status codes
    String CODE_200 = "200";
    String CODE_400 = "400";
    String CODE_403 = "403";
    String CODE_500 = "500";
    String CODE_504 = "504";

    // http methods
    String METHOD_POST = "POST";
    String METHOD_GET = "GET";

    // headers section START
    String GROUP_BY_HEADER_NAME = "X-Groupby-Customer-Id";
    String GROUP_BY_HEADER_DESCRIPTION = "Custom HTTP header which may contain tenant name. Used to define a client by its name.";

    String CONTENT_TYPE_HEADER_DESCRIPTION = "In responses, a Content-Type header provides the client with the actual content type of the returned content.";

    String DATE_HEADER_DESCRIPTION = "The Date general HTTP header contains the date and time at which the message was originated.";

    String CONTENT_LENGTH_HEADER_DESCRIPTION = "The Content-Length header indicates the size of the message body, in bytes, sent"
        + " to the recipient.";

    String CONNECTION_HEADER_DESCRIPTION = "The Connection general header controls whether the network connection stays open"
        + " after the current transaction finishes. If the value sent is keep-alive, the connection is persistent and not"
        + " closed, allowing for subsequent requests to the same server to be done.";
    // headers section END

    // ProductDto section START
    String PRODUCT_TITLE = "Product";
    String PRODUCT_DESCRIPTION = "Product representation.";

    String PRODUCT_NAME_FIELD_DESCRIPTION = "Relative path to product in Google Retail system.";
    String PRODUCT_NAME_FIELD_EXAMPLE = "projects/123456789012/locations/global/catalogs/default_catalog/branches/1/products/12345678901";

    String PRODUCT_ID_FIELD_DESCRIPTION = "Product id in Google Retail system.";
    String PRODUCT_ID_FIELD_EXAMPLE = "12345678901";

    String PRODUCT_TYPE_FIELD_DESCRIPTION = "Product type. Possible values: PRIMARY, VARIANT. If the product has variant list and the request specifies the variantIds," +
        " requested variants will be the first in the response.";
    String PRODUCT_TYPE_FIELD_EXAMPLE = "VARIANT, PRIMARY";

    String PRIMARY_PRODUCT_ID_FIELD_DESCRIPTION = "Product ID that is primary in relation to the current one";
    String PRIMARY_PRODUCT_ID_FIELD_EXAMPLE = "1234567";

    String PRODUCT_COLLECTION_MEMBER_IDS_FIELD_DESCRIPTION = "The of the collection members when product type is COLLECTION";
    String PRODUCT_COLLECTION_MEMBER_IDS_FIELD_EXAMPLE = "5";

    String PRODUCT_GTIN_FIELD_DESCRIPTION = "Global Trade Item Number can be used by a company to uniquely identify all of its trade items.GTIN " +
        "defines trade items as products or services that are priced, ordered or invoiced at any point in the supply chain.";
    String PRODUCT_GTIN_FIELD_EXAMPLE = "10614141999993";

    String PRODUCT_CATEGORIES_FIELD_DESCRIPTION = "Product categories (array).";
    String PRODUCT_CATEGORIES_FIELD_EXAMPLE = "[\"Men\", \"Men > Shoes\"]";

    String PRODUCT_TITLE_FIELD_DESCRIPTION = "Product title.";
    String PRODUCT_TITLE_FIELD_EXAMPLE = "Eastland Shoe Men's Yarmouth Boat Shoes";

    String PRODUCT_BRANDS_FIELD_DESCRIPTION = "Product brands.";
    String PRODUCT_BRANDS_FIELD_EXAMPLE = "[\"Eastland Shoe\"]";

    String PRODUCT_DESCRIPTION_FIELD = "Product description.";
    String PRODUCT_DESCRIPTION_FIELD_EXAMPLE = "Eastland Shoe recalls an age of timeless casual fashion with these leather boat shoes.";

    String PRODUCT_LANGUAGE_CODE_FIELD_DESCRIPTION = "Language of the title/description and other string attributes. " +
        "Use language tags defined by [BCP 47][https://www.rfc-editor.org/rfc/bcp/bcp47.txt]. For product search this " +
        "field is in use. It defaults to 'en-US' if unset.";
    String PRODUCT_LANGUAGE_CODE_FIELD_EXAMPLE = "en-US";

    String PRODUCT_ATTRIBUTES_FIELD_DESCRIPTION = "Highly encouraged. Extra product attributes to be included. For example, " +
        "for products, this could include the store name, vendor, style, color, etc. These are very strong signals for " +
        "recommendation model, thus we highly recommend providing the attributes here. Features that can take on one of " +
        "a limited number of possible values. Two types of features can be set are: Textual features. some examples would " +
        "be the brand/maker of a product, or country of a customer. Numerical features. Some examples would be the " +
        "height/weight of a product, or age of a customer.  Max entries count: 200. Length limit of 128 characters.";
    String PRODUCT_ATTRIBUTES_FIELD_EXAMPLE = "{ 'vendor': {'text': ['vendor123', 'vendor456']}, 'lengths_cm': {'numbers'" +
        ":[2.3, 15.4]}, 'heights_cm': {'numbers':[8.1, 6.4]}}";

    String PRODUCT_TAGS_FIELD_DESCRIPTION = "Product tags (array).";
    String PRODUCT_TAGS_FIELD_EXAMPLE = "[\"Any string\"]";

    String PRODUCT_PRICE_INFO_FIELD_DESCRIPTION = "Product price and cost information.";

    String PRODUCT_AVAILABLE_TIME_FIELD_DESCRIPTION = "The timestamp when this becomes available for search.";

    String PRODUCT_AVAILABILITY_FIELD_DESCRIPTION = "The online availability of the product. Default to IN_STOCK";
    String PRODUCT_AVAILABILITY_FIELD_EXAMPLE = "IN_STOCK";

    String PRODUCT_QUANTITY_FIELD_DESCRIPTION = "The available quantity of the item.";
    String PRODUCT_QUANTITY_FIELD_EXAMPLE = "10";

    String PRODUCT_URI_FIELD_DESCRIPTION = "Link to the appropriate product.";
    String PRODUCT_URI_FIELD_EXAMPLE = "https://s4r-apparel.groupby.cloud/product/Eastland-Shoe-Men's-Yarmouth-Boat-Shoes/2725066/94352309386";

    String PRODUCT_IMAGES_FIELD_DESCRIPTION = "Product Image.";

    String PRODUCT_COLOR_INFO_FIELD_DESCRIPTION = "Product color info.";

    String PRODUCT_SIZES_FIELD_DESCRIPTION = "Product sizes (array).";
    String PRODUCT_SIZES_FIELD_EXAMPLE = "8.5";

    String PRODUCT_MATERIALS_FIELD_DESCRIPTION = "The material of the product. For example, 'leather', 'wooden'. " +
        "A maximum of 20 values are allowed. Length limit of 128 characters";
    String PRODUCT_MATERIALS_FIELD_EXAMPLE = "[\"leather\"]";

    String PRODUCT_PATTERNS_FIELD_DESCRIPTION = "The pattern or graphic print of the product. For example, 'striped', " +
        "'polka dot', 'paisley'. A maximum of 20 values are allowed per product. Length limit of 128 characters.";
    String PRODUCT_PATTERNS_FIELD_EXAMPLE = "[\"paisley\"]";

    String PRODUCT_CONDITION_FIELD_DESCRIPTION = "The condition of the product. Strongly encouraged to use the standard" +
        "values: 'new', 'refurbished', 'used'. A maximum of 5 values are allowed per product. Length limit of 128 characters.";
    String PRODUCT_CONDITION_FIELD_EXAMPLE = "[\"new\"]";

    String PRODUCT_PUBLISH_TIME_FIELD_DESCRIPTION = "Time when the product was published";

    String PRODUCT_RETRIEVABLE_FIELDS_DESCRIPTION = "Field mask for retrievable fields.";

    String PRODUCT_VARIANTS_FIELD_DESCRIPTION = "If the product has variant list and the request specifies the variantIds, " +
        "requested variants will be the first in the response.";

    String PRODUCT_LOCAL_INVENTORIES_DESCRIPTION = "A list of local inventories specific to different places.";
    // ProductDto section END

    // ProductCustomAttribute section START
    String PRODUCT_CUSTOM_ATTRIBUTE_TITLE = "ProductCustomAttribute";
    String PRODUCT_CUSTOM_ATTRIBUTE_DESCRIPTION = "A custom attribute that is not explicitly modeled in product.";

    String PRODUCT_CUSTOM_ATTRIBUTE_TEXT_FIELD_DESCRIPTION = "The textual values of this custom attribute. At most 400 " +
        "values are allowed. Empty values are not allowed. Length limit of 256 characters. Exactly one of text or numbers" +
        " should be set.";
    String PRODUCT_CUSTOM_ATTRIBUTE_TEXT_FIELD_EXAMPLE = "[\"yellow\", \"green\"]";

    String PRODUCT_CUSTOM_ATTRIBUTE_NUMBERS_FIELD_DESCRIPTION = "The numerical values of this custom attribute. At most " +
        "400 values are allowed. Exactly one of text or numbers should be set.";
    String PRODUCT_CUSTOM_ATTRIBUTE_NUMBERS_FIELD_EXAMPLE = "[2.3, 15.4] when the key is 'lengths_cm'";
    // ProductCustomAttribute section END

    // PriceInfo section START
    String PRICE_INFO_TITLE = "PriceInfo";
    String PRICE_INFO_DESCRIPTION = "Price info representation.";

    String PRICE_INFO_CURRENCY_CODE_FIELD_DESCRIPTION = "Currency code.";
    String PRICE_INFO_CURRENCY_CODE_FIELD_EXAMPLE = "CAD";

    String PRICE_INFO_PRICE_FIELD_DESCRIPTION = "Price value.";
    String PRICE_INFO_PRICE_FIELD_EXAMPLE = "1.0";

    String PRICE_INFO_ORIGINAL_PRICE_FIELD_DESCRIPTION = "Original price value.";
    String PRICE_INFO_ORIGINAL_PRICE_FIELD_EXAMPLE = "1.0";

    String PRICE_INFO_COST_FIELD_DESCRIPTION = "Cost";
    String PRICE_INFO_COST_FIELD_EXAMPLE = "1.0";

    String PRICE_INFO_PRICE_EFFECTIVE_TIME_FIELD_DESCRIPTION = "The timestamp when the price starts to be effective. This can be set as a future timestamp, and the price is only used for " +
        "search after price effective time. If so, the original price must be set and original price is used before price effective time. Do " +
        "not set if price is always effective because it will cause additional latency during search.";

    String PRICE_INFO_PRICE_EXPIRE_TIME_FIELD_DESCRIPTION = "The timestamp when the price stops to be effective. The price is used for search before price expire time. If this field is " +
        "set, the original price must be set and original price is used after price expire time. Do not set if price is always effective because it will cause additional latency during search.";

    // PriceInfo section END

    // PriceRange section START
    String PRICE_RANGE_TITLE = "PriceRange";
    String PRICE_RANGE_DESCRIPTION = "The price range of all variant of product having the same primaryProductId";

    String PRICE_RANGE_PRICE_FIELD_DESCRIPTION = "The inclusive price interval of all variant of the product having the same primaryProductId.";

    String PRICE_RANGE_ORIGINAL_PRICE_FIELD_DESCRIPTION = "The inclusive original price internal of all variant of product having the same primaryProductId.";
    // PriceRange section END

    // Interval section START
    String INTERVAL_TITLE = "Interval";
    String INTERVAL_DESCRIPTION = "A floating point interval.";

    String INTERVAL_MINIMUM_FIELD_DESCRIPTION = "Inclusive lower bound. The lower bound of the interval. If neither of the min fields (minimum or exclusiveMinimum) are set, " +
        "then the lower bound is negative infinity. This field must be not larger than maximum or exclusiveMaximum.";
    String INTERVAL_MINIMUM_FIELD_EXAMPLE = "1.0";

    String INTERVAL_EXCLUSIVE_MINIMUM_FIELD_DESCRIPTION = "Exclusive lower bound. The lower bound of the interval. If neither of the min fields " +
        "(minimum or exclusiveMinimum) are set, " +
        "then the lower bound is negative infinity. This field must be not larger than maximum or exclusiveMaximum.";
    String INTERVAL_EXCLUSIVE_MINIMUM_FIELD_EXAMPLE = "1.0";

    String INTERVAL_MAXIMUM_FIELD_DESCRIPTION = "Inclusive upper bound. The upper bound of the interval. If neither of the max fields are set, then the upper bound is " +
        "positive infinity. This field must be not smaller than minimum or exclusiveMinimum.";
    String INTERVAL_MAXIMUM_FIELD_EXAMPLE = "1.0";

    String INTERVAL_EXCLUSIVE_MAXIMUM_FIELD_DESCRIPTION = "Exclusive upper bound. The upper bound of the interval. If neither of the max fields are set, then the upper bound is " +
        "positive infinity. This field must be not smaller than minimum or exclusiveMinimum.";
    String INTERVAL_EXCLUSIVE_MAXIMUM_FIELD_EXAMPLE = "1.0";
    // Interval section END

    // Rating section START
    String RATING_TITLE = "Rating";
    String RATING_DESCRIPTION = "The rating of this product.";

    String RATING_COUNT_FIELD_DESCRIPTION = "The total number of ratings. This value is independent of the value of histogram." +
        "This value must be nonnegative.";
    String RATING_COUNT_FIELD_EXAMPLE = "5";

    String RATING_AVERAGE_FIELD_DESCRIPTION = "The average rating of the product. The rating is scaled at 1-5.";
    String RATING_AVERAGE_FIELD_EXAMPLE = "4.5";

    String RATING_HISTOGRAM_FIELD_DESCRIPTION = "List of rating counts per rating value (index = rating - 1). The list is" +
        " empty if there is no rating. If the list is non-empty, its size is always 5. For example, [41, 14, 13, 47, 303]. " +
        "It means that the product got 41 ratings with 1 star, 14 ratings with 2 star, and so on.";
    String RATING_HISTOGRAM_FIELD_EXAMPLE = "[41, 14, 13, 47, 303]";
    // Rating section END

    // Timestamp section START
    String TIMESTAMP_TITLE = "Timestamp";
    String TIMESTAMP_DESCRIPTION = "Timestamp info with seconds and nanos.";

    String TIMESTAMP_SECONDS_FIELD_DESCRIPTION = "Timestamp seconds.";
    String TIMESTAMP_SECONDS_FIELD_EXAMPLE = "100";

    String TIMESTAMP_NANOS_FIELD_DESCRIPTION = "Timestamp nanos.";
    String TIMESTAMP_NANOS_FIELD_EXAMPLE = "100";
    // Timestamp section END

    // FulfillmentInfo section START
    String FULFILMENT_INFO_TITLE = "FulfillmentInfo";
    String FULFILMENT_INFO_DESCRIPTION = "Fulfillment information, such as the store IDs for in-store pickup or region " +
        "IDs for different shipping methods.";

    String FULFILMENT_INFO_TYPE_FIELD_DESCRIPTION = "Fulfillment type. Place where product fulfilled.";
    String FULFILMENT_INFO_TYPE_FIELD_EXAMPLE = "pickup-in-store";

    String FULFILMENT_INFO_PLACE_IDS_FIELD_DESCRIPTION = "Place ids where product fulfilled (array).";
    String FULFILMENT_INFO_PLACE_IDS_FIELD_EXAMPLE = "[6, 4, 8]";
    // FulfillmentInfo section END

    // Image section START
    String IMAGE_TITLE = "Image";
    String IMAGE_DESCRIPTION = "Product image";

    String IMAGE_URI_FIELD_DESCRIPTION = "Absolute path to product image.";
    String IMAGE_URI_FIELD_EXAMPLE = "https://storage.googleapis.com/poc_apparel/images/1/optimized/3412471_fpx.tif";

    String IMAGE_HEIGHT_FIELD_DESCRIPTION = "Height in pixels";
    String IMAGE_HEIGHT_FIELD_EXAMPLE = "150";

    String IMAGE_WIDTH_FIELD_DESCRIPTION = "Width in pixels";
    String IMAGE_WIDTH_FIELD_EXAMPLE = "150";
    // Image section END

    // Audience section START
    String AUDIENCE_TITLE = "Audience";
    String AUDIENCE_DESCRIPTION = "The target group associated with a given audience (e.g. male, veterans," +
        " car owners, musicians, etc.) of the product.";

    String AUDIENCE_GENDERS_FIELD_DESCRIPTION = "The genders of the audience. Strongly encouraged to use the standard " +
        "values: 'male', 'female', 'unisex'. At most 5 values are allowed. Length limit of 128 characters.";
    String AUDIENCE_GENDERS_FIELD_EXAMPLE = "[\"unisex\"]";

    String AUDIENCE_AGE_GROUPS_FIELD_DESCRIPTION = "The age groups of the audience. Strongly encouraged to use the standard " +
        "values: 'newborn' (up to 3 months old), 'infant' (3-12 months old), 'toddler' (1-5 years old), 'kids' (5-13 years old), " +
        "'adult' (typically teens or older). At most 5 values are allowed. Length limit of 128 characters.";
    String AUDIENCE_AGE_GROUPS_FIELD_EXAMPLE = "[\"newborn\"]";
    // Audience section END

    // ColorInfo section START
    String COLOR_INFO_TITLE = "ColorInfo";
    String COLOR_INFO_DESCRIPTION = "Product color info.";

    String COLOR_INFO_COLOR_FAMILIES_FIELD_DESCRIPTION = "Product color families (array).";
    String COLOR_INFO_COLOR_FAMILIES_FIELD_EXAMPLE = "[\"Blue\"]";

    String COLOR_INFO_COLORS_FIELD_DESCRIPTION = "Product color (array).";
    String COLOR_INFO_COLORS_FIELD_EXAMPLE = "[\"Navy\"]";
    // ColorInfo section END

    // FieldMask section START
    String FIELD_MASK_TITLE = "FieldMask";
    String FIELD_MASK_DESCRIPTION = "Retrievable fields.";

    String FIELD_MASK_PATHS_FIELD_DESCRIPTION = "Paths for retrievable fields (array). "
        + "When list is empty or \"*\" string exist in list -api will return all fields.";
    String FIELD_MASK_PATHS_FIELD_EXAMPLE = "[\"color_info\", \"price_info\", \"audience\", \"images\", \"sizes\","
        + " \"materials\", \"name\", \"availability\", \"title\", \"uri\"]";
    // FieldMask section END

    // Promotion section START
    String PROMOTION_TITLE = "Promotion";
    String PROMOTION_DESCRIPTION = "The promotions applied to the product. A maximum of 10 values are allowed per product.";

    String PROMOTION_ID_FIELD_DESCRIPTION = "ID of the promotion. For example, 'free gift'. Length limit of 128 characters.";
    String PROMOTION_ID_FIELD_EXAMPLE = "id0LikeThis";
    // Promotion section END

    // SearchRequestDto section START
    String SEARCH_REQUEST_TITLE = "Search Request";

    String SEARCH_REQUEST_QUERY_FIELD_DESCRIPTION = "Base textual search query.";
    String SEARCH_REQUEST_QUERY_FIELD_EXAMPLE = "blue sweater";

    String SEARCH_REQUEST_AREA_FIELD_DESCRIPTION = "Area name the search is being performed in.";
    String SEARCH_REQUEST_AREA_FIELD_DEFAULT_VALUE = "Production";
    String SEARCH_REQUEST_AREA_FIELD_EXAMPLE = "Production";

    String SEARCH_REQUEST_COLLECTION_FIELD_DESCRIPTION = "Name of collection in project configuration setting which is " +
        "mapped to the google retail backend.";
    String SEARCH_REQUEST_COLLECTION_FIELD_DEFAULT_VALUE = "default";
    String SEARCH_REQUEST_COLLECTION_FIELD_EXAMPLE = "productsClothing";

    String SEARCH_REQUEST_VISITOR_ID_FIELD_DESCRIPTION = "Unique identifier identifying the shopper. Will be " +
        "autogenerated if not provided.";
    String SEARCH_REQUEST_VISITOR_ID_FIELD_EXAMPLE = "38400000-8cf0-11bd-b23e-10b96e40000d";

    String SEARCH_REQUEST_PAGE_SIZE_FIELD_DESCRIPTION = "The number of products to be returned on each page.";
    String SEARCH_REQUEST_PAGE_SIZE_FIELD_DEFAULT_VALUE = "10";
    String SEARCH_REQUEST_PAGE_SIZE_FIELD_MIN_VALUE = "0";
    String SEARCH_REQUEST_PAGE_SIZE_FIELD_EXAMPLE = "25";

    String SEARCH_REQUEST_SKIP_FIELD_DESCRIPTION = "Where in the list of products to begin the page.";
    String SEARCH_REQUEST_SKIP_FIELD_DEFAULT_VALUE = "0";
    String SEARCH_REQUEST_SKIP_FIELD_MIN_VALUE = "0";
    String SEARCH_REQUEST_SKIP_FIELD_EXAMPLE = "50";

    String SEARCH_REQUEST_BIASING_PROFILE_FIELD_DESCRIPTION = "Name of a biasing profile which should be applied to the search. Takes priority over area default.";

    String SEARCH_REQUEST_INCLUDED_NAVIGATIONS_FIELD_DESCRIPTION = "Set of navigation fields to include in the search result. Cannot be set if 'excludedNavigations' is set.";

    String SEARCH_REQUEST_EXCLUDED_NAVIGATIONS_FIELD_DESCRIPTION = "Set of navigation fields to exclude in the search result. Cannot be set if 'includedNavigations' is set.";

    String SEARCH_REQUEST_DYNAMIC_FACET_FIELD_DESCRIPTION = "Set the specifications of dynamically generated facets.";

    String SEARCH_REQUEST_VARIANT_ROLLUP_KEYS_FIELD_DESCRIPTION = "Set the variant rollup keys.";
    String SEARCH_REQUEST_VARIANT_ROLLUP_KEYS_FIELD_EXTERNAL_DOCS_DESCRIPTION = "Google reference";
    String SEARCH_REQUEST_VARIANT_ROLLUP_KEYS_FIELD_EXTERNAL_DOCS_URL = "https://cloud.google.com/retail/docs/reference/rest/v2/projects.locations.catalogs.placements/search#body.request_body.FIELDS.variant_rollup_keys";

    String SEARCH_REQUEST_PRE_FILTER_FIELD_DESCRIPTION = "Set of the prefilter specifications value.";
    String SEARCH_REQUEST_PRE_FILTER_FIELD_EXAMPLE = "(categories:ANY(\"Men\")) AND (ageGroups:ANY(\"adult\")) AND (price: IN(150, 200))";
    String SEARCH_REQUEST_PRE_FILTER_FIELD_EXTERNAL_DOCS_DESCRIPTION = "Google reference";
    String SEARCH_REQUEST_PRE_FILTER_FIELD_EXTERNAL_DOCS_URL = "https://cloud.google.com/retail/docs/filter-and-order#filter";

    String SEARCH_REQUEST_SITE_FILTER_NAME_FIELD_DESCRIPTION = "Name of site filter. If not specified, the specified area's default site will be applied if configured in Command Center. To not use default specify empty value i.e.\"\".  If the site doesn't exist then the search will execute without the site filter and a warning will be provided.";

    String SEARCH_REQUEST_RESPONSE_MASK_FIELD_DESCRIPTION = "List with fields which should be included in metadata object associated with each record in response.";
    String SEARCH_REQUEST_RESPONSE_MASK_FIELD_EXAMPLE = "[\"key.innerKey.value\", \"key2\", \"key.innerKey2.value2\"]";

    String SEARCH_REQUEST_PAGE_CATEGORIES_FIELD_DESCRIPTION = "The categories associated with a category page. " +
        "Required for category navigation queries to achieve good search quality. " +
        "To represent full path of category, use '>' sign to separate different hierarchies. " +
        "If '>' is part of the category name, please replace it with other character(s)." +
        "Max item length = 1.";
    String SEARCH_REQUEST_PAGE_CATEGORIES_FIELD_EXAMPLE = "[\"Sales > 2017 Black Friday Deals\"]";

    String SPELL_CORRECTION_SPEC_DESCRIPTION = "The specification for query spell correction. This defaults to AUTO. "
        + "AUTO - Automatic spell correction built by Google Retail placements.search. placements.search will be based "
        + "on the corrected query if found. SUGGESTION_ONLY - Google Retail placements.search will try to find a spell "
        + "suggestion if there is any and put in the SearchResponse.corrected_query. The spell suggestion will not be "
        + "used as the search query.";

    String INCLUDE_EXPANDED_RESULTS_DESCRIPTION = "When a shopper uses an ambiguous or a multi-word search phrase, they can "
        + "get an empty response. After turning on include expanded results, Retail Search analyzes the request and "
        + "returns the expanded list of products based on the parsed search query. For example, if you search \"Google Pixel 5\" "
        + "without query expansion, you might only get \"google_pixel_5\" in the result. With query expansion, you might get "
        + "\"google_pixel_4a_with_5g\", \"google_pixel_4a\" and \"google_pixel_5_case\" as well."
        + "The default value is configured in the tenant settings or true if there is no such setting";

    String PIN_UNEXPANDED_RESULTS_DESCRIPTION = "This configuration depends on include expanded results settings. "
        + "If this field is set to true,unexpanded products are always at the top of the search results, followed "
        + " by the expanded results. Default value: true";

    String SEARCH_REQUEST_LOGIN_ID_FIELD_DESCRIPTION = """
        Highly recommended for logged-in users. Unique identifier for logged-in user,
        such as a user name. Don't set for anonymous users.

        Don't set the field to the same fixed ID for different users. This mixes
        the event history of those users together, which results in degraded
        model quality.

        The field must be a UTF-8 encoded string with a length limit of 128
        characters.
        """;

    String SEARCH_REQUEST_INVENTORY_STORE_ID_FIELD_DESCRIPTION = """
        Store identifier which is used to distinguish inventory navigations when the API request sent to Google search
        (Retail API).
        """;

    String SEARCH_REQUEST_INCLUDE_OUT_OF_STOCK_FIELD_DESCRIPTION = """
        Include Out Of Stock products in pinned products responses when true, or skip pinned products when false.
        """;

    String SEARCH_REQUEST_DEBUG_FIELD_DESCRIPTION = """
        Enable additional debug info in response.

        Note: attaching debug info significantly affects performance.
        Is not supposed to be used for large requests.

        """;

    String SEARCH_REQUEST_OVERWRITES_FIELD_DESCRIPTION = "Overwrites field encapsulate entities that can overwrite already existing ones, e.g " +
        "Rules that need to be overwrites for preview functionality.";

    String SEARCH_REWRITES_DESCRIPTION =
        """
            Array represents the intent to rewrite the original query indicating the processing of Fitment
            (or other service) and which query rewriting it performed (if any), allowing for analytics on this data later.
            Those values should be appended to a `rewrites` array in the beacon in case there are already
            any rewrites (eg. other layers in front of S4R, such as part number search identification)
            """;
    String SEARCH_REWRITES_EXAMPLE = "[\"service_enabled\",\"service_client_rewrite\" ]";

    String FACET_LIMIT = """
        Maximum of facet values that should be returned for this facet. If not specified, defaults to 20.
        The maximum allowed value is 300. Values above 300 will be coerced to 300.

        If this field is negative, an INVALID_ARGUMENT is returned.

        This limit (300) is configured on Google side, but Google have an ability to change it for specific project.
        """;

    String SEARCH_REQUEST_SPONSORED_RECORDS_FIELD_DESCRIPTION = "Encapsulate arguments that control sponsored records behaviour.";

    String SEARCH_REQUEST_SPONSORED_RECORDS_COUNT_FIELD_DESCRIPTION = "Controls total count of sponsored records to be requested.";
    String SEARCH_REQUEST_SPONSORED_RECORDS_COUNT_FIELD_EXAMPLE = "3";

    String SEARCH_REQUEST_SPONSORED_RECORDS_POSITIONS_FIELD_DESCRIPTION = "Controls index positions of sponsored products within $.records field in the analytics search event.";
    String SEARCH_REQUEST_SPONSORED_RECORDS_POSITIONS_FIELD_EXAMPLE = "[0, 1, 4]";

    String SEARCH_REQUEST_ENABLE_TOPSORT_V2_FIELD_DESCRIPTION = "Enable TopSort V2 service for this request.";

    String SEARCH_REQUEST_ENABLE_TOPSORT_FIELD_DESCRIPTION = "Enable Topsort sponsored products in the response.";

    String SEARCH_REQUEST_ENABLE_PART_NUMBER_SEARCH_DESCRIPTION = """
        Enable part number search for the request:
        true - perform a part number search, regardless of the feature flag, but based on the partNumberSearchable
               attributes;
        false - do not perform a part number search, regardless of the feature flag or attributes state;
        not set - perform a part number search based on the feature flag and partNumberSearchable attributes.
    """;

    String SEARCH_REQUEST_ENABLE_PART_NUMBER_FALLBACK_DESCRIPTION = """
        Controls whether a part number search should fall back to relevancy search when no results are found.
        Only applicable when part number search is used. If not specified, defaults to true.
    """;

    String SEARCH_REQUEST_ENABLE_PART_NUMBER_EXPANSION_DESCRIPTION = """
        Whether to extend part number search with the relevancy search results. Defaults to true. If true, the 'enablePartNumberFallback' parameter is ignored.
    """;

    String SEARCH_REQUEST_CONVERSATIONAL_SEARCH_CONFIG_DESCRIPTION = "Encapsulates arguments for supporting conversational search.";
    String SEARCH_REQUEST_FOLLOWUP_CONVERSATION_REQUESTED_FIELD_DESCRIPTION = "Set to TRUE in order to get a conversational response.";
    String SEARCH_REQUEST_CONVERSATION_ID_FIELD_DESCRIPTION =
        """
            Identifier of ongoing conversational search within user session. Should not be present in the initial query.
            Should restore the this parameter from session storage on the subsequent queries.
            """;
    String SEARCH_REQUEST_USER_ANSWER_FIELD_DESCRIPTION = "What user selects - can be one of the suggested answers(ITEMS array) or text input (text_answer).";
    String SEARCH_REQUEST_SELECTED_ANSWER_FIELD_DESCRIPTION = "What user selects from suggested answers.";
    String SEARCH_REQUEST_TEXT_ANSWER_FIELD_DESCRIPTION = "What user inputs as some text.";
    String SEARCH_REQUEST_USER_ANSWER_ATTRIBUTE_NAME_FIELD_DESCRIPTION = "Selected attribute name.";
    String SEARCH_REQUEST_USER_ANSWER_ATTRIBUTE_VALUE_FIELD_DESCRIPTION = "Selected attribute value.";
    // SearchRequestDto section END

    // SelectedRefinementDto section START
    String SELECTED_REFINEMENT_TITLE = "Selected Refinement";
    String SELECTED_REFINEMENT_DESCRIPTION = "Refinement the shopper has selected for filtering.";

    String SELECTED_REFINEMENT_NAVIGATION_NAME_FIELD_DESCRIPTION = "The name of the navigation the refinement is for.";
    String SELECTED_REFINEMENT_NAVIGATION_NAME_FIELD_EXAMPLE = "brands";

    String SELECTED_REFINEMENT_TYPE_FIELD_DESCRIPTION = "The type of the navigation the refinement is for.";
    String SELECTED_REFINEMENT_TYPE_FIELD_EXAMPLE = "Value";

    String SELECTED_REFINEMENT_VALUE_FIELD_DESCRIPTION = "Value of selected refinement, if type is value.";

    String SELECTED_REFINEMENT_LOW_FIELD_DESCRIPTION = "The lowest end or value of the range, if applicable.";
    String SELECTED_REFINEMENT_LOW_FIELD_TYPE = "number";
    String SELECTED_REFINEMENT_LOW_FIELD_EXAMPLE = "50";

    String SELECTED_REFINEMENT_HIGH_FIELD_DESCRIPTION = "The highest end or value of the range, if applicable.";
    String SELECTED_REFINEMENT_HIGH_FIELD_TYPE = "number";
    String SELECTED_REFINEMENT_HIGH_FIELD_EXAMPLE = "100";

    String SELECTED_REFINEMENT_SOURCE_FIELD_DESCRIPTION = "Field which is indicated that it is dynamic navigation.";
    String SELECTED_REFINEMENT_SOURCE_FIELD_TYPE = "string";
    String SELECTED_REFINEMENT_SOURCE_FIELD_EXAMPLE = "Dynamic";

    String SELECTED_REFINEMENT_OR_FIELD_DESCRIPTION = "Navigation multiselect. Indicate that it is possibly to select " +
        "more than one navigation value due to search request.";
    String SELECTED_REFINEMENT_OR_FIELD_TYPE = "boolean";
    String SELECTED_REFINEMENT_OR_FIELD_EXAMPLE = "false";
    // SelectedRefinementDto section END

    // NavigationTypeDto section START
    String NAVIGATION_TYPE_TITLE = "Navigation Type";
    String NAVIGATION_TYPE_DESCRIPTION = "Type of navigation.";
    // NavigationTypeDto section END

    // NavigationType section START
    String NAVIGATION_TYPE_VALUE = "A navigation that is a single value.";
    String NAVIGATION_TYPE_RANGE = "A navigation that has a range of values to be bucketed on.";
    // NavigationType section END

    // BiasingProfileDto section START
    String BIASING_PROFILE_TITLE = "Biasing Profile";
    String BIASING_PROFILE_DESCRIPTION = "Inline biasing profile, which should be applied to the search. Takes priority over biasing profile.";
    // BiasingProfileDto section END

    // BiasDto section START
    String BIAS_TITLE = "Bias";
    String BIAS_DESCRIPTION = "Biases the search results to either increase or decrease product relevancy for products that match the given field and content.";

    String BIAS_FIELD_DESCRIPTION = "The field the bias refers to.";
    String BIAS_FIELD_EXAMPLE = "colorFamilies";

    String BIAS_CONTENT_FIELD_DESCRIPTION = "The content the field must match for the bias to be applied.";
    String BIAS_CONTENT_EXAMPLE = "Red";
    // BiasDto section END

    // StrengthDto section START
    String STRENGTH_TITLE = "Strength";
    String STRENGTH_DESCRIPTION = "The amount the bias will affect the search results.";
    // StrengthDto section END

    // TypeDto section START
    String TYPE_TITLE = "Type";
    String TYPE_DESCRIPTION = "Bias search type";
    // TypeDto section END

    // NumericContentDto section START
    String NUMERIC_CONTENT_TITLE = "NumericContent";
    String NUMERIC_CONTENT_DESCRIPTION = "The numeric content with values or ranges";

    String NUMERIC_CONTENT_VALUES_TITLE = "Values";
    String NUMERIC_CONTENT_VALUES_DESCRIPTION = "Numeric content values array";

    String NUMERIC_CONTENT_RANGES_TITLE = "Ranges";
    String NUMERIC_CONTENT_RANGES_DESCRIPTION = "Numeric content ranges array";
    // NumericContentDto section END

    // RangeDto section START
    String RANGE_TITLE = "Range";
    String RANGE_DESCRIPTION = "The high/low range";

    String RANGE_HIGH_TITLE = "High";
    String RANGE_HIGH_DESCRIPTION = "Range upper value";

    String RANGE_LOW_TITLE = "Low";
    String RANGE_LOW_DESCRIPTION = "Range lower value";

    String RANGE_DESCRIPTION_TITLE = "Description";
    String RANGE_DESCRIPTION_DESCRIPTION = "Range description";
    // RangeDto section END

    // CustomParameterDto section START
    String CUSTOM_PARAMETER_TITLE = "Custom Parameter";
    String CUSTOM_PARAMETER_DESCRIPTION = "A key=value combination to allow for further triggering of rules or redirects.";

    String CUSTOM_PARAMETER_KEY_FIELD_DESCRIPTION = "Key of the custom parameter.";
    String CUSTOM_PARAMETER_KEY_FIELD_EXAMPLE = "landing-page";

    String CUSTOM_PARAMETER_VALUE_FIELD_DESCRIPTION = "Value of the custom parameter";
    String CUSTOM_PARAMETER_VALUE_FIELD_EXAMPLE = "easter-2021";
    // CustomParameterDto section END

    // SortDto section START
    String SORT_TITLE = "Sort";
    String SORT_DESCRIPTION = "Order the returned products should appear on the page.";

    String SORT_FIELD_DESCRIPTION = "Field the order will be applied to.";
    String SORT_FIELD_EXAMPLE = "rating";

    String SORT_ORDER_TITLE = "Order";
    String SORT_ORDER_DESCRIPTION = "Order the products will appear in";
    String SORT_ORDER_DEFAULT_VALUE = "Ascending";
    String SORT_ORDER_EXAMPLE = "Ascending";
    // SortDto section END

    // SearchResponseDto section START
    String SEARCH_RESPONSE_TITLE = "Search Response";
    String SEARCH_RESPONSE_DESCRIPTION = "Response of calling the search API, including various elements of the original request context, matching records and general " +
        "metadata relating to the results.";

    String SEARCH_RESPONSE_ID_FIELD_DESCRIPTION = "Unique identifier for the search.";
    String SEARCH_RESPONSE_ID_FIELD_EXAMPLE = "1d7dc797-e1cb-49dc-8791-f2317a218f13";

    String SEARCH_RESPONSE_AREA_FIELD_DESCRIPTION = "Area Id the search was performed in.";
    String SEARCH_RESPONSE_AREA_FIELD_EXAMPLE = "Production";

    String SEARCH_RESPONSE_QUERY_FIELD_DESCRIPTION = "Original search query.";
    String SEARCH_RESPONSE_QUERY_FIELD_EXAMPLE = "blue swetter";

    String SEARCH_RESPONSE_ORIGINAL_QUERY_FIELD_DESCRIPTION =
        """
            Base textual search query. This parameter is required for directSearchBeacon requests and by default contains
            the same value as query parameter
            """;

    String SEARCH_RESPONSE_CORRECTED_QUERY_FIELD_DESCRIPTION = "Search query after any changes/corrections are done by the engine.";
    String SEARCH_RESPONSE_CORRECTED_QUERY_FIELD_EXAMPLE = "blue sweater";

    String SEARCH_RESPONSE_BIASING_PROFILE_FIELD_DESCRIPTION = "Name of the biasing profile which was used to bias products in the search results.";
    String SEARCH_RESPONSE_BIASING_PROFILE_FIELD_EXAMPLE = "Query";

    String SEARCH_RESPONSE_BIASING_PROFILE_APPLIED_ID_FIELD_DESCRIPTION = "Id of the biasing profile which was used to bias products in the " +
        "search results.";
    String SEARCH_RESPONSE_BIASING_PROFILE_APPLIED_ID_FIELD_EXAMPLE = "1";

    String SEARCH_RESPONSE_RECORDS_FIELD_DESCRIPTION = "The list of records that match the search.";

    String SEARCH_RESPONSE_SPONSORED_RECORDS_FIELD_DESCRIPTION = "The list of sponsored records that match the search.";

    String SEARCH_RESPONSE_TOTAL_RECORD_COUNT_FIELD_DESCRIPTION = """
        The estimated total count of matched products irrespective of pagination.
        The count of records returned by pagination may be less than the totalRecordCount that matches.
        """;
    String SEARCH_RESPONSE_TOTAL_RECORD_COUNT_FIELD_EXAMPLE = "273";

    String SEARCH_RESPONSE_REDIRECT_FIELD_DESCRIPTION = "URL to which the merchandiser should redirect the shopper to.";
    String SEARCH_RESPONSE_REDIRECT_FIELD_EXAMPLE = "www.example.com/2021-deals";

    String SEARCH_RESPONSE_EMPTY_FIELD_DESCRIPTION = "True if the search yielded no results, otherwise false.";

    String SEARCH_RESPONSE_WARNINGS_FIELD_DESCRIPTION = "Warning messages containing information about invalid products, etc.";

    String SEARCH_RESPONSE_CONVERSATIONAL_SEARCH_RESULT =
        """
            Provides supplementary data that is crucial for UI rendering on the client side.
            It uses nested messages such as SuggestedAnswer and AdditionalFilter to structure the returned data in a clear and comprehensible manner.
            This hierarchical structure ensures that the information exchanged between the client and server is well-defined and interpretable.
            The suggested answers are sorted by which facet values in the catalog were most often engaged in the user events as filters for all queries.
            The sorting uses the engagement_rate field in the follow-up question generated by the ConversationalSearch proto.
            This field captures the historical engagement rate that the filter shows from the retailer's search events sent to the server.
            """;

    String SEARCH_RESPONSE_CONVERSATION_ID = """
        A unique identifier for the ongoing search session, which must be stored and used in subsequent requests. It is empty on the initial query,
        and the server will reply with the conversation_id. The field is used to keep track of "which conversation is going on" - because one shopper
        may have multiple tabs or devices open.
        """;

    String SEARCH_RESPONSE_FOLLOWUP_QUESTION = "Tells the retailer what question to show to the user. It contains the fields question, suggested_answers";

    String SEARCH_RESPONSE_SUGGESTED_ANSWERS = """
        Contains product attribute values containing name and value key-value pairs for each suggested answer. It provides a list of multiple-choice
        options. This field can be set to just show the first N multiple-choice results. These are sorted by an engagement_rate field, which
        represents the probability that the question will be involved in a search.
        """;
    // SearchResponseDto section END

    // RecordDto section START
    String RECORD_TITLE = "Record";
    String RECORD_DESCRIPTION = "Information regarding a product in the proprietary Group By API format.";

    String RECORD_ID_FIELD_NAME = "_id";
    String RECORD_ID_FIELD_DESCRIPTION = "Identifier of the record as an MD5 hash.";
    String RECORD_ID_FIELD_EXAMPLE = "2cfcf5443046e6733a40608af8a35c95";

    String RECORD_URL_FIELD_NAME = "_u";
    String RECORD_URL_FIELD_DESCRIPTION = "Logical URL of the record.";
    String RECORD_URL_FIELD_EXAMPLE = "http://apparel1productsClothing.com/12345";

    String RECORD_TITLE_FIELD_NAME = "_t";
    String RECORD_TITLE_FIELD_DESCRIPTION = "Title of the record.";
    String RECORD_TITLE_FIELD_EXAMPLE = "3/4 length shorts";

    String RECORD_COLLECTION_FIELD_DESCRIPTION = "Collection the record was queried from or resides.";
    String RECORD_COLLECTION_FIELD_EXAMPLE = "productsClothing";

    String RECORD_METADATA_FIELD_NAME = "allMeta";
    String RECORD_METADATA_FIELD_DESCRIPTION = "All other metadata, read fields, the record has.";

    String RECORD_SPONSORED_FIELD_DESCRIPTION = "Boolean flag indicating whether the record is sponsored.";
    String RECORD_SPONSORED_INFO_FIELD_DESCRIPTION = "Additional information about the sponsored record.";
    String RECORD_LABELS_FIELD_DESCRIPTION = "Labels associated with the record, such as 'PINNED', 'SPONSORED' etc.";
    // RecordDto section END

    // SearchMetadataDto section START
    String SEARCH_METADATA_TITLE = "Search Metadata";
    String SEARCH_METADATA_DESCRIPTION = "Metadata relating to the search results, or how they were generated.";

    String SEARCH_METADATA_SEARCH_ATTRIBUTION_TOKEN_FIELD_DESCRIPTION = "Token to assist beacon collectors in correlating searches to user events.";
    String SEARCH_METADATA_SEARCH_ATTRIBUTION_TOKEN_FIELD_EXAMPLE = "NtQKDAjYrrGEBhCWs_v3AhABGiQ2MDlhNjA5Yy0wMDAwLTI2ZDctODQ0OS1mNGY1ZTgwODc1YjQ";

    String SEARCH_METADATA_TOTAL_TIME_FIELD_DESCRIPTION = "Total time spent performing the overall search processing in milliseconds. This includes processing of rules, available navigations and Google search time.";
    String SEARCH_METADATA_TOTAL_TIME_FIELD_EXAMPLE = "153";

    String SEARCH_METADATA_RETAIL_TIME_FIELD_DESCRIPTION = "Total time spent performing only the Google search (Retail API) in milliseconds.";
    String SEARCH_METADATA_RETAIL_TIME_FIELD_EXAMPLE = "89";
    // SearchMetadataDto section END

    // PageInfoDto section START
    String PAGE_INFO_TITLE = "Page Info";
    String PAGE_INFO_DESCRIPTION = "Information regarding where the page of results starts and ends.";

    String PAGE_INFO_RECORD_START_FIELD_DESCRIPTION = "Where in the list of products the page begins.";
    String PAGE_INFO_RECORD_START_FIELD_MINIMUM = "0";
    String PAGE_INFO_RECORD_START_FIELD_EXAMPLE = "50";

    String PAGE_INFO_RECORD_END_FIELD_DESCRIPTION = "Where in the list of products the page ends.";
    String PAGE_INFO_RECORD_END_FIELD_MINIMUM = "1";
    String PAGE_INFO_RECORD_END_FIELD_EXAMPLE = "75";
    // PageInfoDto section END

    // NavigationDto section START
    String NAVIGATION_TITLE = "Navigation";
    String NAVIGATION_DESCRIPTION = "Navigation available for the shopper to refine the results on.";

    String NAVIGATION_NAME_FIELD_DESCRIPTION = "Name of the field the navigation in on.";
    String NAVIGATION_NAME_FIELD_EXAMPLE = "brands";

    String NAVIGATION_DISPLAY_NAME_FIELD_DESCRIPTION = "Name of the navigation for display purposes.";
    String NAVIGATION_DISPLAY_NAME_FIELD_EXAMPLE = "Brand";

    String NAVIGATION_OR_FIELD_DESCRIPTION = "Flag if this navigation supports or queries.";
    // NavigationDto section END

    // RefinementDto section START
    String REFINEMENT_TITLE = "Refinement";
    String REFINEMENT_DESCRIPTION = "Refinement value or range in the navigation.";

    String REFINEMENT_COUNT_FIELD_DESCRIPTION = "Number of products which match this refinement value or range.";
    String REFINEMENT_COUNT_FIELD_EXAMPLE = "189";

    String REFINEMENT_VALUE_FIELD_DESCRIPTION = "Value of the refinement.";
    String REFINEMENT_VALUE_FIELD_EXAMPLE = "Surf's Up";

    String REFINEMENT_LOW_FIELD_DESCRIPTION = "Lower bound of the refinement range.";
    String REFINEMENT_LOW_FIELD_TYPE = "number";
    String REFINEMENT_LOW_FIELD_EXAMPLE = "50";

    String REFINEMENT_HIGH_FIELD_DESCRIPTION = "Upper bound  of the refinement range.";
    String REFINEMENT_HIGH_FIELD_TYPE = "number";
    String REFINEMENT_HIGH_FIELD_EXAMPLE = "100";

    String REFINEMENT_DESCRIPTION_FIELD_DESCRIPTION = "Refinement range description.";
    String REFINEMENT_DESCRIPTION_FIELD_TYPE = "string";
    String REFINEMENT_DESCRIPTION_FIELD_EXAMPLE = "any string";
    // RefinementDto section END

    // TemplateDto section START
    String TEMPLATE_TITLE = "Template";
    String TEMPLATE_DESCRIPTION = "Template to assist the front end application in rendering the UI. Currently only " +
        "the triggered rule name will be included, or the 'default' template name to indicate no rule was triggered. " +
        "This is to mainly compatibility with the Searchandiser API and the addition of templates in the future. " +
        "Template is search agnostic and do not affect search request or result. Templated selected only by triggered rule.";

    String TEMPLATE_NAME_FIELD_DESCRIPTION = "Name of the template.";
    String TEMPLATE_NAME_FIELD_EXAMPLE = "default";

    String TEMPLATE_RULE_NAME_FIELD_DESCRIPTION = "Name of the rule which may have triggered.";
    String TEMPLATE_RULE_NAME_FIELD_EXAMPLE = "Easter Sale 2021";

    String TEMPLATE_RULE_ID_FIELD_DESCRIPTION = "Id of the rule which may have triggered.";
    String TEMPLATE_RULE_ID_FIELD_EXAMPLE = "123";

    String TEMPLATE_TRIGGER_SET_FIELD_DESCRIPTION = "The trigger set that triggered a rule.";

    String TEMPLATE_ZONES_FIELD_DESCRIPTION = "Zones for linked template.";
    // TemplateDto section END

    // ZoneDto section START
    String ZONE_TITLE = "Zone";
    String ZONE_DESCRIPTION = "UI zones, that may contain code snippets, sub-searches and etc.";

    String ZONE_TYPE_FIELD_EXAMPLE = "Content, Rich_Content, Products or Generated_Content";

    String ZONE_NAME_FIELD_DESCRIPTION = "A name for the zone, ideally human-readable.";
    String ZONE_NAME_FIELD_EXAMPLE = "any string";

    String ZONE_CONTENT_FIELD_DESCRIPTION = "Zone content - it is can be any data, HTML - code, usual text or etc";
    String ZONE_CONTENT_FIELD_EXAMPLE = "any string";

    String ZONE_HTML_CONTENT_FIELD_DESCRIPTION = "Zone html content";
    String ZONE_HTML_CONTENT_FIELD_EXAMPLE = "<div>Feed your pets</div>";
    // ZoneDto section END

    // ZoneDtoType section START
    String ZONE_TYPE_TITLE = "Zone Type";
    String ZONE_TYPE_DESCRIPTION = "Define type of content which is can be stored in zone.";
    // ZoneDtoType section END

    // FacetSearchResponseDto section START
    String FACET_SEARCH_RESPONSE_TITLE = "Facet Search Response";
    String FACET_SEARCH_RESPONSE_DESCRIPTION = "Facet search response representation.";
    // FacetSearchResponseDto section END

    // FacetSearchRequestDto section START
    String FACET_SEARCH_REQUEST_TITLE = "Facet Search Request";
    // FacetSearchRequestDto section END

    // Facet section START
    String FACET_TITLE = "Facet";
    String FACET_DESCRIPTION = "A facet specification to perform faceted search.";

    String FACET_PREFIX_FIELD_DESCRIPTION = "Only get facet values that start with the given string prefix. For example, suppose \"categories\" has three values \"Women " +
        "> Shoe\", \"Women > Dress\" and \"Men > Shoe\". If set \"prefixes\" to \"Women\", the \"categories\" facet will give only \"Women > " +
        "Shoe\" and \"Women > Dress\". Only supported on textual fields. Maximum is 10. This field is case-sensitive";
    String FACET_PREFIX_FIELD_EXAMPLE = "Women";

    String FACET_CONTAINS_FIELD_DESCRIPTION = "Only get facet values that contains the given strings. For example, suppose \"categories\" has three values \"Women > " +
        "Shoe\", \"Women > Dress\" and \"Men > Shoe\". If set \"contains\" to \"Shoe\", the \"categories\" facet will give only \"Women > " +
        "Shoe\" and \"Men > Shoe\". Only supported on textual fields. Maximum is 10. This field is case-sensitive";
    String FACET_CONTAINS_FIELD_EXAMPLE = "Shoe";

    String FACET_DISPLAY_NAME_FILED_DESCRIPTION = "Display name of facet";
    String FACET_DISPLAY_NAME_FILED_EXAMPLE = "anyName";

    String FACET_TYPE_FILED_DESCRIPTION = "Represents the type of navigation. Only navigation with type \"value\" may " +
        "be used in facet search request.";

    String FACET_NAVIGATION_NAME_FILED_DESCRIPTION = "Represents the name of navigation.";
    String FACET_NAVIGATION_NAME_DESCRIPTION = "anyName";
    // Facet section END

    // Tile section START
    String TILE_NAVIGATION_TITLE = "Tile Navigation request data";
    String TILE_NAVIGATION_DESCRIPTION = "Data includes flag and applied tiles to affect search so that it provides " +
        "actual tiles back and applies selected tiles to the search";
    String TILE_TITLE = "Visual representation of attribute value pairs";
    String TILE_DESCRIPTION = "Tiles are a different way to present the same information as dynamic facets, " +
        "showing only the attributes that are most frequently engaged for a particular query " +
        "independent of dynamic facet families";
    String PRODUCT_ATTRIBUTE_VALUE_DESCRIPTION = "Represents key-value pair which contains the attribute value, " +
        "so that you know which tiles to show.";
    String PRODUCT_ATTRIBUTE_VALUE_EXAMPLE = "{'name': 'colors',' value': 'black'}";

    String PRODUCT_REPRESENTATIVE_ID_DESCRIPTION = "Reference for image retrieval: the product ID of a typical product " +
        "that matches the applied filter. It's important to use the representative product ID, rather than " +
        "the primary product ID, to ensure the image accurately reflects the filtered attribute " +
        "(e.g., displaying a red variant for the color = 'red' filter).";
    String PRODUCT_REPRESENTATIVE_ID_EXAMPLE = "1239874";

    String SEARCH_REQUEST_TILE_NAVIGATION_REQUESTED_DESCRIPTION = "Indicates if tiles must be included in response.";
    String SEARCH_REQUEST_APPLIED_TILES = "Tiles selected by client to affect search.";
    String SEARCH_REQUEST_TILES_NAVIGATION_DESCRIPTION = "Tiles navigation data to be applied to search";
    String SEARCH_RESPONSE_TILES_DESCRIPTION = "The list of tiles to be included in response";
    // Tile section END

    // ErrorDto section START
    String ERROR_TITLE = "Error";
    String ERROR_DESCRIPTION = "Error returned by the API.";

    String ERROR_TRACKING_ID_FIELD_DESCRIPTION = "Identifier used for tracking purposes.";
    String ERROR_TRACKING_ID_FIELD_EXAMPLE = "bb25d616-2cd7-44a1-8d11-27159f2aa03c";

    String ERROR_METHOD_FIELD_DESCRIPTION = "HTTP method of the API call which produced the error.";
    String ERROR_METHOD_FIELD_EXAMPLE = "POST";

    String ERROR_PATH_FIELD_DESCRIPTION = "HTTP path of the API call which produced the error.";
    String ERROR_PATH_FIELD_EXAMPLE = "/api/v2/search";

    String ERROR_MESSAGE_FIELD_DESCRIPTION = "Error message.";
    String ERROR_MESSAGE_FIELD_EXAMPLE = "Search request page size must be greater than or equal to 0.";

    String ERROR_MESSAGE_DEBUG_DESCRIPTION = "Debug details, if request passed with related to the debug flag.";
    // ErrorDto section END

    // ExperimentDTO section START
    String EXPERIMENT_TITLE = "Experiment";
    String EXPERIMENT_DESCRIPTION = "Information about Rule based Experiment.";

    String EXPERIMENT_ID_FIELD_DESCRIPTION = "Experiment id.";
    String EXPERIMENT_ID_FIELD_EXAMPLE = "experiment_variant_name";

    String EXPERIMENT_VARIANT_FIELD_DESCRIPTION = "Experiment variant.";
    String EXPERIMENT_VARIANT_FIELD_EXAMPLE = "Variant A";
    // ExperimentDTO section END

    // ScoreDto section START
    String SCORE_TITLE = "Visibility Score Info";
    String SCORE_DESCRIPTION = "Contains scores and other factors for score calculation";

    String SCORE_TOTAL = "Total score of result set";
    String SCORE_ORIGINAL = "Original score of matched products";
    String SCORE_NEW = "New score of matched products";

    String SCORE_TARGET = "Target score of matched products ";
    String SCORE_MULTIPLIER = "Effective score multiplier that is applied to original score";
    String SCORE_VISIBILITY_CAP = "Percent value of score cap from total score";
    String SCORE_MAX_POSSIBLE_SCORE = "Max possible score of matched products";
    String SCORE_MAX_ALLOWED_CAPPED_SCORE = "Max allowed score of matched products";
    // ScoreDto section END

    // Metadata
    String METADATA_DESCRIPTION = "Area metadata used to store additional data from user in format field/value.";

    // PinnedProductDto section START
    String PINNED_PRODUCTS_DESCRIPTION = "List of pinned products that need to be placed in a concrete position"
        + " in the response.";
    String PINNED_PRODUCT_DESCRIPTION = "Pinned product contains product ID and required position in the response.";
    String PINNED_PRODUCT_TITLE = "Pinned Product";
    String PINNED_PRODUCT_ID_DESCRIPTION = "The unique ID of a product.";
    String PINNED_PRODUCT_POSITION_DESCRIPTION = "The position in the response where the product should be pinned to,"
        + " from 1 to 24 inclusive.";
    String PINNED_PRODUCT_POSITION_EXAMPLE = "10";
    // PinnedProductDto section END

    // CRM section START
    String SEARCH_REQUEST_USER_ATTRIBUTES_DESCRIPTION = """
            A list of key/values pairs. By integrating user attributes from your CRM, you can deliver more relevant search
            results, ultimately improving customer engagement and conversion.
        """;
    String SEARCH_REQUEST_USER_ATTRIBUTE_KEY_DESCRIPTION = """
            Case insensitive and will be lower cased, i.e. “Interests” and “interests” will be sent as “interests”.
        """;
    String SEARCH_REQUEST_USER_ATTRIBUTE_KEY_EXAMPLE = "preferred_categories";
    String SEARCH_REQUEST_USER_ATTRIBUTE_VALUES_DESCRIPTION = """
        An array of strings. Each value is case sensitive so “Iphone” is considered different from “iphone”.
        Ensure the values are not too broad or too granular.
        """;
    String SEARCH_REQUEST_USER_ATTRIBUTE_VALUES_EXAMPLE = "[\"fiction\", \"history\"]";
    // CRM section END
}
