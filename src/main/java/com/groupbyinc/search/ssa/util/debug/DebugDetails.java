package com.groupbyinc.search.ssa.util.debug;

import com.groupbyinc.search.ssa.application.core.search.productvisibility.Score;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.core.propagation.PropagatedContextElement;

import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultMap;

@Getter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DebugDetails implements PropagatedContextElement {

    private final Map<String, DebugInfo> debugInfos;

    @Setter
    @Nullable
    private Score visibilityScore;

    public DebugDetails() {
        this.debugInfos = new HashMap<>();
    }

    @JsonCreator
    public DebugDetails(@JsonProperty("debugInfos") @Nullable Map<String, DebugInfo> debugInfos,
                        @JsonProperty("visibilityScore") @Nullable Score visibilityScore) {
        this.debugInfos = notNullOrDefaultMap(debugInfos);
        this.visibilityScore = visibilityScore;
    }

    public void addDebugInfo(String name, DebugInfo debugInfo) {
        debugInfos.put(name, debugInfo);
    }

    @SuppressWarnings("unchecked")
    public <T> T getDebugInfo(String name) {
        return (T) debugInfos.get(name);
    }

}
