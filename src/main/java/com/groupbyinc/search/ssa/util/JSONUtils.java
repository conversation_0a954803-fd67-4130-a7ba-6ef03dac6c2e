package com.groupbyinc.search.ssa.util;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;


public interface JSONUtils {

    Gson GSON = new Gson();

    /**
     * Converts a map to a JSON object.
     *
     * @param objectMap the map to convert
     * @return the {@link JsonObject} representation of the map
     * @throws IllegalStateException if the provided map can't be converted to a valid JSON object
     */
    static JsonObject toJsonObject(Map<String, Object> objectMap) throws IllegalStateException {
        var jsonElement = GSON.toJsonTree(objectMap);
        return jsonElement.getAsJsonObject();
    }

    /**
     * Converts a JSON object to a map.
     *
     * @param jsonObject the JSON object to convert
     * @return the {@code Map<String, Object>} representation of the JSON object
     */
    @SuppressWarnings("unchecked")
    static Map<String, Object> toMap(JsonObject jsonObject) {
        return GSON.fromJson(jsonObject, Map.class);
    }

    /**
     * Builds a stream of JSON elements from a JSON array.
     *
     * @param jsonArray the JSON array to convert
     * @return the stream of {@code JsonElement} from the JSON array
     */
    static Stream<JsonElement> toElementsStream(JsonArray jsonArray) {
        return StreamSupport.stream(jsonArray.spliterator(), false);
    }

    /**
     * Gets a nested JSON element from a JSON object.
     *
     * @param jsonObject the JSON object to search
     * @param path       the path to the nested element
     * @return the nested {@code JsonElement} or {@code null} if the element is not found
     */
    static JsonElement getNestedJsonElement(JsonObject jsonObject, List<String> path) {
        JsonElement current = jsonObject;
        for (String key : path) {
            if (current.isJsonObject()) {
                current = current.getAsJsonObject().get(key);
                if (current == null || current.isJsonNull()) {
                    return null;
                }
            } else {
                return null;
            }
        }
        return current;
    }

    /**
     * Gets a nested JSON object from a JSON object.
     *
     * @param jsonObject the JSON object to search
     * @param path       the path to the nested object
     * @return the nested {@code JsonObject} or {@code null} if the object is not found, or it is not a JsonObject
     */
    static JsonObject getNestedJsonObject(JsonObject jsonObject, List<String> path) {
        JsonObject current = jsonObject;
        for (String key : path) {
            JsonElement element = current.get(key);
            if (element == null || !element.isJsonObject()) {
                return null;
            }
            current = element.getAsJsonObject();
        }
        return current;
    }


    /**
     * Removes a nested object from a JSON object.
     *
     * @param jsonObject the JSON object to modify
     * @param path       the path to the nested object to remove
     */
    static void removeNestedObject(JsonObject jsonObject, List<String> path) {
        if (path.isEmpty()) {
            return;
        }
        // Root-level field
        if (path.size() == 1) {
            jsonObject.remove(path.getFirst());
            return;
        }
        // For nested fields, get the parent JsonObject
        List<String> parentPath = path.subList(0, path.size() - 1);
        var parentObject = getNestedJsonObject(jsonObject, parentPath);
        if (parentObject != null) {
            parentObject.remove(path.getLast());
        }

        // If the parent object is empty, remove it
        if (parentObject != null && parentObject.entrySet().isEmpty()) {
            removeNestedObject(jsonObject, parentPath);
        }
    }

}
