package com.groupbyinc.search.ssa.util;

public interface Constants {

    /**
     * Wildcard mark used everywhere where logic allows all values.
     */
    String WILDCARD = "*";

    /**
     * The Name of the header on incoming HTTP requests that is populated by the
     * API gateway and indicates the customer ID.
     */
    String GROUPBY_CUSTOMER_ID_HEADER = "X-Groupby-Customer-Id";

    /**
     * The Name of the header on incoming HTTP requests that is populated by the
     * API gateway and indicates should we skip caching of this request or not.
     */
    String GROUPBY_SKIP_CACHE_HEADER = "X-Groupby-Skip-Cache";

    String FULFILLMENT_INFO = "fulfillmentInfo";

    String PLACE_IDS = "placeIds";

    String PLACE_ID = "placeId";

    String LOCAL_INVENTORIES = "localInventories";

    String VARIANT_ROLLUP_VALUES = "variantRollUpValues";

    String KEY = "key";

    String VALUE = "value";

    String COUNT = "count";

    String TEXT = "text";

    String NUMBERS = "numbers";

    String PRIMARY_TYPE = "PRIMARY";

    String VARIANT_TYPE = "VARIANT";

    String PART_NUMBER_SEARCHABLE = "partNumberSearchable";

    String CUSTOM_ATTRIBUTES_KEY = "attributes";

    String CUSTOM_ATTRIBUTES_PREFIX = CUSTOM_ATTRIBUTES_KEY + ".";

}
