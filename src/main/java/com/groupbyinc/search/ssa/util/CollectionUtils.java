package com.groupbyinc.search.ssa.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static java.util.Objects.requireNonNullElse;

public class CollectionUtils {

    /**
     * Used to provide a default value "{@link ArrayList}" if passed list is null.
     *
     * @param list to check.
     *
     * @return a passed list object if not null or new {@link ArrayList} otherwise.
     *
     * @param <T> type of objects inside the list.
     */
    public static <T> List<T> notNullOrDefaultList(List<T> list) {
        return requireNonNullElse(list, new ArrayList<>());
    }

    /**
     * Used to provide a default unmodifiable "{@link List#of}" if passed list is null.
     *
     * @param list to check.
     *
     * @return a passed list object if not null or new {@link ArrayList} otherwise.
     *
     * @param <T> type of objects inside the list.
     */
    public static <T> List<T> notNullOrDefaultImmutableList(List<T> list) {
        return requireNonNullElse(list, List.of());
    }

    /**
     * Used to provide a default value "{@link HashSet}" if passed set is null.
     *
     * @param set to check.
     *
     * @return a passed set object if not null or new {@link HashSet} otherwise.
     *
     * @param <T> type of objects inside the set.
     */
    public static <T> Set<T> notNullOrDefaultSet(Set<T> set) {
        return requireNonNullElse(set, new HashSet<>());
    }

    /**
     * Used to provide a default value "{@link HashMap}" if passed map is null.
     *
     * @param map to check.
     *
     * @return a passed map object if not null or new {@link HashMap} otherwise.
     *
     * @param <K> type of keys inside the map.
     * @param <V> type of values inside the map.
     */
    public static <K, V> Map<K, V> notNullOrDefaultMap(Map<K, V> map) {
        return requireNonNullElse(map, new HashMap<>());
    }
}
