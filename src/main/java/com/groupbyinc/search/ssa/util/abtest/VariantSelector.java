package com.groupbyinc.search.ssa.util.abtest;

import com.groupbyinc.search.ssa.api.error.ProcessingException;

import jakarta.validation.ValidationException;

import java.util.Arrays;
import java.util.stream.IntStream;

import static com.groupbyinc.search.ssa.util.abtest.Murmur.MAX_HASH_VALUE;
import static com.groupbyinc.search.ssa.util.abtest.Murmur.hash_x86_32;

/**
 * Class which is used to convert any string to the index in an array passed in constructor.
 * It is used next algorithm:
 * 1) Selected hash algorithm {@link Murmur} convert any string into number from zero to {@link Murmur#MAX_HASH_VALUE}.
 * 2) When an instance of class is created, it accepts an array with percentages.
 * 3) Main idea is to split the range of numbers generated by hashing algorithm on ranges according passed percentages.
 * 4) When new string passed, their hash will be calculated, and it will be associated with some range.
 * 5) Index of range where generated hash included will be returned.
 */
public class VariantSelector {

    /**
     * Constant, which is representing 100 percents. Created to be more clear in arithmetic operations.
     */
    private static final double HUNDRED_PERCENTS = 100.0;

    /**
     * Array represented numeric ranges from zero to the {@link Murmur#MAX_HASH_VALUE}.
     */
    private long[] rangeThresholds;

    public VariantSelector(int[] percentages) {
        if (percentages == null) {
            throw new ValidationException("Variant trigger percentages can not be null");
        } else if (IntStream.of(percentages).sum() != HUNDRED_PERCENTS) {
            throw new ValidationException("Variant trigger percentages should add to 100");
        }

        mapFractionsToRanges(generateRangeFractions(percentages));
    }

    /**
     * Used to calculate range id based on passed string.
     *
     * @param string which is need to be associated with range id.
     *
     * @return id of range which is associated with hash of passed string.
     */
    public int getRangeId(String string) {
        if (string == null) {
            throw new IllegalArgumentException("Target string cannot be null");
        }

        return defineRangeId(hash_x86_32(string.getBytes(), string.length()));
    }

    /**
     * This method used to split range between 'zero' and '{@link Murmur#MAX_HASH_VALUE}' based on passed fractions.
     * Technically, it splits it sequentially.
     * <pre>
     * For example,
     * Passed fractions: [0.0, 0.1, 0.4, 1.0].
     * Returned ranges: [0, 429496729, 1717986918, 4294967296]
     * Where:
     *  [0.0, 0.1] it is a range between 0 and 429496729.
     *  [0.1, 0.4] it is a range between 429496729 and 1717986918.
     *  [0.4, 1.0] it is a range between 1717986918 and 4294967296.
     * </pre>
     *
     * @param fractions array with percentages.
     */
    private void mapFractionsToRanges(double[] fractions) {
        rangeThresholds = new long[fractions.length];

        for (int index = 1; index < fractions.length - 1; index++) {
            var threshold = Math.floor((fractions[index] * HUNDRED_PERCENTS) * MAX_HASH_VALUE / HUNDRED_PERCENTS);
            rangeThresholds[index] = (long) threshold;
        }
        rangeThresholds[fractions.length - 1] = MAX_HASH_VALUE;
    }

    /**
     * This method used to split range between 'zero' and 'one'([0... 1]) based on passed percentages. Technically, it
     * splits it sequentially.
     * <pre>
     * For example,
     * Passed percentages: [10, 30, 60].
     * Returned fractions: [0.0, 0.1, 0.4, 1.0]
     * Where:
     *  10% of range [0... 1] it is a range between 0.0 and 0.1.
     *  30% of range [0... 1] it is a range between 0.1 and 0.4.
     *  60% of range [0... 1] it is a range between 0.4 and 1.0.
     * </pre>
     *
     * @param percentages array with percentages.
     *
     * @return array [0... 1] which is split to the sections based on passed percentages.
     */
    private double[] generateRangeFractions(int[] percentages) {
        var fractions = new double[percentages.length + 1];

        fractions[1] = percentages[0] / HUNDRED_PERCENTS;
        for (int index = 1; index < percentages.length; index++) {
            fractions[index + 1] = percentages[index] / HUNDRED_PERCENTS + fractions[index];
        }

        return fractions;
    }

    /**
     * Used to define a range id base on passed 'value'. It is used {@link this#rangeThresholds} and returns an index
     * of range where passed hash located.
     *
     * @param value number which needs to be associated with range.
     *
     * @return index of range which includes passed hash value.
     * @throws RuntimeException if it is not possibly to define range index based on passed hash value.
     */
    private int defineRangeId(long value) {
        int bucketId = 0;
        for (int index = 0; index < rangeThresholds.length; index++) {
            if (value > rangeThresholds[index]) {
                bucketId = index;
            } else {
                return bucketId;
            }
        }
        throw new ProcessingException(
            "Error while define range id. Value: %s, ranges: %s".formatted(
                value,
                Arrays.toString(rangeThresholds)
            )
        );
    }

}
