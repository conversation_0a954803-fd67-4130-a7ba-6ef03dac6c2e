package com.groupbyinc.search.ssa.util.abtest;

import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;

import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;

import static java.nio.charset.StandardCharsets.UTF_8;

/**
 * Util class for AB Testing functionality
 */
@Deprecated
public class ABTestUtils {

    private static final HashFunction murmur3 = Hashing.murmur3_128(1);

    /**
     * Function for generation random number in toRangeExclusive based on uniqueId
     *
     * @param uniqueId unique id For example for using in Experiment uniqueId is loginId or visitorId
     * @param toRangeExclusive exclusive range of generated numbers (0-toRangeExclusive-1)
     *                         toRangeExclusive is any number bigger than 1
     * @return number from 0 to (toRangeExclusive -1)
     */
    @Deprecated
    public static Integer abRandomizer(String uniqueId, Integer toRangeExclusive) {
        requireNonBlank(uniqueId, "unique id", MANDATORY);
        if(toRangeExclusive == null || toRangeExclusive < 1) {
            throw new IllegalArgumentException("toRangeExclusive is any number bigger than 1");
        }

        return Math.abs(murmur3.hashString(uniqueId, UTF_8).asInt() % toRangeExclusive);
    }
}
