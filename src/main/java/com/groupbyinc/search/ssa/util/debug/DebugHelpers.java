package com.groupbyinc.search.ssa.util.debug;

import com.groupbyinc.search.ssa.api.dto.DebugDto;
import com.groupbyinc.search.ssa.api.dto.VisibilityScoreDto;
import com.groupbyinc.search.ssa.mongo.request.MongoPipeline;
import com.groupbyinc.search.ssa.mongo.request.MongoQuery;
import com.groupbyinc.search.ssa.mongo.response.MongoQueryResponse;
import com.groupbyinc.search.ssa.productcatalog.model.ProductCatalogRequest;
import com.groupbyinc.search.ssa.topsort.model.AuctionRequest;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.TextNode;
import com.google.cloud.retail.v2.SearchRequestOrBuilder;
import com.google.cloud.retail.v2.SearchResponse;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.core.propagation.PropagatedContext;
import io.micronaut.core.propagation.PropagatedContextElement;
import io.micronaut.core.util.CollectionUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.slf4j.Logger;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.mongo.request.PipelineType.SEARCH;
import static com.groupbyinc.search.ssa.util.StringUtils.DOT;
import static com.groupbyinc.search.ssa.util.StringUtils.EMPTY;
import static com.groupbyinc.search.ssa.util.debug.DataCatalogDebugInfo.REGULAR_SUFFIX;
import static com.groupbyinc.search.ssa.util.debug.MongoDebugInfo.MONGO_DEBUG_INFO_SUFFIX;
import static com.groupbyinc.search.ssa.util.debug.TopSortDebugInfo.TOP_SORT_SUFFIX;

public class DebugHelpers {

    private static final ObjectMapper MAPPER = new ObjectMapper();
    private static final JsonFactory FACTORY = MAPPER.getFactory();
    private static final JsonFormat.Printer J_PRINTER = JsonFormat.printer();

    private static final Function<StrategyNameContext, String> STRATEGY_NAME = StrategyNameContext::strategyName;
    private static final Function<StepNameContext, String> STEP_NAME = StepNameContext::stepName;

    public static final TextNode EMPTY_NODE = new TextNode("");

    public static void saveRetailRequestToDebug(SearchRequestOrBuilder searchRequest, String suffix, Logger log) {
        if (getRequestContext().getRequestOptions().debug()) {
            try {
                RetailDebugInfo debugInfo = getDebugInfoOrDefault(suffix, new RetailDebugInfo());
                debugInfo.setRetailSearchRequest(protoMessageToJson(searchRequest, log));
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    public static void saveRetailResponseToDebug(SearchResponse response, String suffix, Logger log) {
        if (getRequestContext().getRequestOptions().debug()) {
            try {
                RetailDebugInfo debugInfo = getDebugInfoOrDefault(suffix, new RetailDebugInfo());
                debugInfo.setRetailSearchResponse(protoMessageToJson(response, log));
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    public static void saveDataCatalogRequestToDebug(ProductCatalogRequest request, String suffix, Logger log) {
        if (getRequestContext().getRequestOptions().debug()) {
            try {
                DataCatalogDebugInfo debugInfo = getDebugInfoOrDefault(suffix, new DataCatalogDebugInfo());
                var requestNode = MAPPER.valueToTree(request);
                if (suffix.equals(REGULAR_SUFFIX)) {
                    debugInfo.setProductsRequest(requestNode);
                } else {
                    debugInfo.setSponsoredProductsRequest(requestNode);
                }
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    public static <T> void saveDataCatalogResponseToDebug(T response, String suffix, Logger log) {
        if (getRequestContext().getRequestOptions().debug()) {
            try {
                DataCatalogDebugInfo debugInfo = getDebugInfoOrDefault(suffix, new DataCatalogDebugInfo());
                var requestNode = MAPPER.valueToTree(response);
                if (suffix.equals(REGULAR_SUFFIX)) {
                    debugInfo.setProductsResponse(requestNode);
                } else {
                    debugInfo.setSponsoredProductsResponse(requestNode);
                }
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    public static void saveTopSortRequestToDebug(AuctionRequest request, Logger log) {
        if (getRequestContext().getRequestOptions().debug()) {
            try {
                TopSortDebugInfo debugInfo = getDebugInfoOrDefault(TOP_SORT_SUFFIX, new TopSortDebugInfo());
                debugInfo.setTopSortRequest(MAPPER.valueToTree(request));
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    public static <T> void saveTopSortResponseToDebug(T response, Logger log) {
        if (getRequestContext().getRequestOptions().debug()) {
            try {
                TopSortDebugInfo debugInfo = getDebugInfoOrDefault(TOP_SORT_SUFFIX, new TopSortDebugInfo());
                debugInfo.setTopSortResponse(MAPPER.valueToTree(response));
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    public static void saveMongoSearchRequestToDebug(MongoQuery mongoQuery, Logger log) {
        if (getRequestContext().getRequestOptions().debug()) {
            try {
                var debugInfo = getDebugInfoOrDefault(MONGO_DEBUG_INFO_SUFFIX, new MongoDebugInfo());
                debugInfo.setMongoSearchRequest(toJsonNode(mongoQuery.searchPipeline().pipeline(), log));
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    public static void saveMongoFacetRequestsToDebug(List<MongoPipeline> facetPipelines, Logger log) {
        if (getRequestContext().getRequestOptions().debug()) {
            try {
                var debugInfo = getDebugInfoOrDefault(MONGO_DEBUG_INFO_SUFFIX, new MongoDebugInfo());
                var facetRequests = facetPipelines.stream()
                    .map(pipeline -> toJsonNode(pipeline.pipeline(), log))
                    .toList();
                debugInfo.setMongoFacetRequest(MAPPER.valueToTree(facetRequests));
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    public static void saveMongoSearchResponseToDebug(MongoQueryResponse searchResponse, Logger log) {
        if (getRequestContext().getRequestOptions().debug()) {
            try {
                MongoDebugInfo debugInfo = getDebugInfoOrDefault(MONGO_DEBUG_INFO_SUFFIX, new MongoDebugInfo());
                if (searchResponse.request().pipelineType() == SEARCH) {
                    debugInfo.setMongoSearchResponse(toJsonNode(searchResponse.response(), log));
                }
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    public static void saveMongoFacetResponsesToDebug(List<MongoQueryResponse> mongoFacetResponses, Logger log) {
        if (getRequestContext().getRequestOptions().debug()) {
            try {
                var debugInfo = getDebugInfoOrDefault(MONGO_DEBUG_INFO_SUFFIX, new MongoDebugInfo());

                var facetResponses = mongoFacetResponses.stream()
                    .filter(result -> result.request().pipelineType().isSearchFacetRequest())
                    .map(result -> toJsonNode(result.response(), log))
                    .toList();
                debugInfo.setMongoFacetResponse(MAPPER.valueToTree(facetResponses));

                var dynamicFacetResponse = mongoFacetResponses.stream()
                    .filter(result -> result.request().pipelineType().isDynamicFacetRequest())
                    .map(MongoQueryResponse::response)
                    .findFirst();
                dynamicFacetResponse.ifPresent(response ->
                    debugInfo.setMongoDynamicFacetResponse(MAPPER.valueToTree(toJsonNode(response, log)))
                );
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    public static void saveMongoDynamicFacetRequestToDebug(List<Bson> pipeline, Logger log) {
        if (getRequestContext().getRequestOptions().debug()) {
            try {
                MongoDebugInfo debugInfo = getDebugInfoOrDefault(MONGO_DEBUG_INFO_SUFFIX, new MongoDebugInfo());
                debugInfo.setMongoDynamicFacetRequest(MAPPER.valueToTree(toJsonNode(pipeline, log)));
            } catch (Exception e) {
                log.warn(e.getMessage());
            }
        }
    }

    public static DebugDto buildDebugDto(DebugDetails debugDetails) {
        return new DebugDto(
            debugDetails.getDebugInfos(),
            VisibilityScoreDto.fromScore(debugDetails.getVisibilityScore())
        );
    }

    public static <T extends DebugInfo> T getDebugInfoOrDefault(String suffix, T defaultValue) {
        var key = getDebugInfoKey(suffix);

        var requestContext = getRequestContext();
        var fromContext = requestContext.getDebugDetails().<T>getDebugInfo(key);

        if (fromContext == null) {
            requestContext.getDebugDetails().addDebugInfo(key, defaultValue);
            return defaultValue;
        }

        return fromContext;
    }

    public static String getDebugInfoKey(String suffix) {
        var context = PropagatedContext.getOrEmpty();

        var stepPartName = appendNames(context, StepNameContext.class, STEP_NAME);
        var strategyName = appendNames(context, StrategyNameContext.class, STRATEGY_NAME);

        var step = stepPartName.isEmpty() ? EMPTY : stepPartName + DOT;

        return strategyName + DOT + step + suffix;
    }

    private static JsonNode protoMessageToJson(MessageOrBuilder messageOrBuilder, Logger log) {
        try {
            String result = J_PRINTER.print(messageOrBuilder);
            JsonParser parser = FACTORY.createParser(result);
            return MAPPER.readTree(parser);
        } catch (IOException e) {
            log.warn(e.getMessage());
            return EMPTY_NODE;
        }
    }

    private static JsonNode toJsonNode(@Nullable Bson document, Logger log) {
        if (document == null) {
            return EMPTY_NODE;
        }
        try {
            var filtered = Document.parse(
                document.toBsonDocument().toJson()
            );
            return MAPPER.valueToTree(filtered);
        } catch (Exception e) {
            log.warn(e.getMessage());
            return EMPTY_NODE;
        }
    }

    private static JsonNode toJsonNode(@Nullable List<Bson> aggregations, Logger log) {
        if (CollectionUtils.isEmpty(aggregations)) {
            return EMPTY_NODE;
        }
        try {
            var conversions = new ArrayList<Document>();
            // This is to simplify Bson representation in 'debug' mode as it contains ambiguous
            // object representation hard to read for user.
            aggregations.forEach(doc -> {
                var filtered = Document.parse(
                    doc.toBsonDocument().toJson()
                );
                conversions.add(filtered);
            });
            return MAPPER.valueToTree(conversions);
        } catch (Exception e) {
            log.warn(e.getMessage());
            return EMPTY_NODE;
        }
    }

    private static <T extends PropagatedContextElement> String appendNames(PropagatedContext context,
                                                                           Class<T> clazz,
                                                                           Function<T, String> extractNameFunction) {
        var names = context
            .findAll(clazz)
            .map(extractNameFunction)
            .toList()
            .reversed();

        var strategyName = new StringBuilder(EMPTY);
        for (int i = 0; i < names.size(); i++) {
            strategyName.append(names.get(i));

            if (i != names.size() - 1) {
                strategyName.append(DOT);
            }
        }

        return strategyName.toString();
    }

}
