package com.groupbyinc.search.ssa.util.debug;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import io.micronaut.core.annotation.Nullable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataCatalogDebugInfo implements DebugInfo {

    public static final String REGULAR_SUFFIX = "getMetadata";
    public static final String SPONSORED_SUFFIX = "getMetadataSponsored";

    @Nullable
    private JsonNode productsRequest;

    @Nullable
    private JsonNode productsResponse;

    @Nullable
    private JsonNode sponsoredProductsRequest;

    @Nullable
    private JsonNode sponsoredProductsResponse;

    @JsonCreator
    public DataCatalogDebugInfo(
        @Nullable @JsonProperty("productsRequest") JsonNode productsRequest,
        @Nullable @JsonProperty("productsResponse") JsonNode productsResponse,
        @Nullable @JsonProperty("sponsoredProductsRequest") JsonNode sponsoredProductsRequest,
        @Nullable @JsonProperty("sponsoredProductsResponse") JsonNode sponsoredProductsResponse
    ) {
        this.productsRequest = productsRequest;
        this.productsResponse = productsResponse;
        this.sponsoredProductsRequest = sponsoredProductsRequest;
        this.sponsoredProductsResponse = sponsoredProductsResponse;
    }

}