package com.groupbyinc.search.ssa.util.debug;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import io.micronaut.core.annotation.Nullable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TopSortDebugInfo implements DebugInfo {

    public static final String TOP_SORT_SUFFIX = "createAuction";

    @Nullable
    private JsonNode topSortRequest;

    @Nullable
    private JsonNode topSortResponse;

    @JsonCreator
    public TopSortDebugInfo(@Nullable @JsonProperty("topSortRequest") JsonNode topSortRequest,
                            @Nullable @JsonProperty("topSortResponse") JsonNode topSortResponse) {
        this.topSortRequest = topSortRequest;
        this.topSortResponse = topSortResponse;
    }

}