package com.groupbyinc.search.ssa.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;
import io.micronaut.core.annotation.Nullable;
import org.slf4j.Logger;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

public interface StringUtils {

    String EMPTY = "";
    String COMMA = ",";
    String SPACE = " ";
    String DOT = ".";
    String UNDERSCORE = "_";
    String SOURCE_DYNAMIC = "Dynamic";
    String SHA_256 = "SHA-256";

    @SuppressWarnings("deprecation")
    HashFunction MD5 = Hashing.md5();

    static boolean containsIgnoreCase(final String str, final String searchStr) {
        if (str == null || searchStr == null) {
            return false;
        }
        return str.toLowerCase().contains(searchStr.toLowerCase());
    }

    static boolean endsWithIgnoreCase(final String str, final String searchStr) {
        if (str == null || searchStr == null) {
            return false;
        }
        return str.toLowerCase().endsWith(searchStr.toLowerCase());
    }

    static boolean startsWithIgnoreCase(final String str, final String searchStr) {
        if (str == null || searchStr == null) {
            return false;
        }
        return str.toLowerCase().startsWith(searchStr.toLowerCase());
    }

    static boolean regex(final String str, final Pattern pattern) {
        if (pattern == null || str == null) {
            return false;
        }
        try {
            Matcher matcher = pattern.matcher(str);
            return matcher.find();
        } catch (PatternSyntaxException e) {
            return false;
        }
    }

    static boolean matches(final String str, final String searchStr) {
        if (str == null || searchStr == null) {
            return false;
        }
        return str.equalsIgnoreCase(searchStr);
    }

    static String getHash(String salt, String source) throws NoSuchAlgorithmException {
        return Base64.getEncoder()
            .encodeToString(
                MessageDigest
                    .getInstance(SHA_256)
                    .digest((salt + source).getBytes(StandardCharsets.UTF_8))
            );
    }

    static String getHashSafe(String salt, String source) {
        try {
            return getHash(salt, source);
        } catch (NoSuchAlgorithmException e) {
            return source;
        }
    }

    static String safeWriteToJsonString(ObjectMapper mapper, Object o, Logger log) {
        try {
            return mapper.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            log.warn("Fail when convert to json string", e);
            return EMPTY;
        }
    }

    static String castToStringSafely(Object value) {
        return value instanceof String s ? s : null;
    }

    static String removeWhitespaces(@Nullable String key) {
        return key == null ? null : key.replaceAll("\\s+", "");
    }

}
