package com.groupbyinc.search.ssa.util.debug;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import io.micronaut.core.annotation.Nullable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MongoDebugInfo implements DebugInfo {

    public static final String MONGO_DEBUG_INFO_SUFFIX = "mongoSearch";

    @Nullable
    private JsonNode mongoSearchRequest;

    @Nullable
    private JsonNode mongoSearchResponse;

    @Nullable
    private JsonNode mongoFacetRequest;

    @Nullable
    private JsonNode mongoFacetResponse;

    @Nullable
    private JsonNode mongoDynamicFacetRequest;

    @Nullable
    private JsonNode mongoDynamicFacetResponse;

    @JsonCreator
    public MongoDebugInfo(@Nullable @JsonProperty("mongoSearchRequest") JsonNode mongoSearchRequest,
                          @Nullable @JsonProperty("mongoSearchResponse") JsonNode mongoSearchResponse,
                          @Nullable @JsonProperty("mongoFacetRequest") JsonNode mongoFacetRequest,
                          @Nullable @JsonProperty("mongoFacetResponse") JsonNode mongoFacetResponse,
                          @Nullable @JsonProperty("mongoDynamicFacetRequest") JsonNode mongoDynamicFacetRequest,
                          @Nullable @JsonProperty("mongoDynamicFacetResponse") JsonNode mongoDynamicFacetResponse) {
        this.mongoSearchRequest = mongoSearchRequest;
        this.mongoSearchResponse = mongoSearchResponse;
        this.mongoFacetRequest = mongoFacetRequest;
        this.mongoFacetResponse = mongoFacetResponse;
        this.mongoDynamicFacetRequest = mongoDynamicFacetRequest;
        this.mongoDynamicFacetResponse = mongoDynamicFacetResponse;
    }

}