package com.groupbyinc.search.ssa.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_ID;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_VARIANTS;
import static com.groupbyinc.search.ssa.util.StringUtils.DOT;

/**
 * This class is a prefix tree describing the paths in the JSON object.
 *
 * It is used to delete any fields inside JSON object except of fields those whose
 * paths are described by the tree.
 */
public class PrefixTreeMap extends HashMap<String, PrefixTreeMap.PrefixTreeNode> {

    /** Separator between JSON "keys" inside passed configuration. */
    private static final String SEPARATOR = "\\.";

    /** true if passed config contains {@link Constants#WILDCARD} */
    private final boolean allAllowed;

    private PrefixTreeMap(boolean allAllowed) {
        this.allAllowed = allAllowed;
    }

    /**
     * Used to clen-up passed map base on current prefix tree configuration.
     *
     * 1) If {@link PrefixTreeMap#allAllowed} is "true" then full copy of passed
     *    map will be returned.
     * 2) If {@link PrefixTreeMap#allAllowed} is "false" then {@link PrefixTreeMap#cleanUp(Map, Map)}
     *    will be called.
     *
     * @param map map to clean-up.
     * @return new map which contains only values which are defined by current tree
     *         configuration.
     */
    public Map<String, Object> cleanUpMapBaseOnPrefixTree(Map<String, Object> map) {
        var result = new HashMap<>(map);

        if (!allAllowed) {
            cleanUp(this, result);
        }

        return result;
    }

    /**
     * Used to remove extra data from passed map.
     *
     * Passed map it is map representation of JSON object, and current prefix tree
     * describe a paths inside this JSON object which should not be removed.
     *
     * After the cleanup, the passed map must contain only those keys that exist
     * in the constructed tree.
     *
     * For example:
     * Prefix tree looks like:
     * <pre>
     *     key
     *          subKey
     *                 value
     *          subKey_2
     *                 value_2
     *     key_1
     *          value
     * </pre>
     * And passed JSON object is:
     * <pre>
     *  {
     *   "key": {
     *     "subKey": {
     *       "value": "value"
     *     },
     *     "subKey_2": {
     *       "value_2": "value_2"
     *     },
     *     "subKey_3": {
     *       "value_3": "value_3"
     *     }
     *   },
     *   "key_1": {
     *     "value": "value"
     *   },
     *   "key_2": {
     *     "value": "value"
     *   }
     * }
     * </pre>
     *
     * The main idea of this algorithm is that each level of the tree corresponds
     * to nesting in "JSON" object. In order to remove all unnecessary keys from
     * the passed object, it is necessary to go through all the nodes of the prefix
     * tree at the same time clearing the corresponding nesting level in the object.
     *
     * In the process of cleaning the object, we will use the following algorithm:
     *
     * 1) Check if the current node has any child nodes.
     *     a) If there are no child nodes, the algorithm ends.
     * 2) From the passed object, delete all keys whose names do not exist in the
     *    list of child nodes.
     * 3) For each of the remaining keys, we repeat steps 1 and 2 but use the child
     *    node that matches the given key by name.
     *
     * Example based on previous prefix tree and passed object:
     * 1) On the root of prefix tree we have only two keys [key, key_1].
     * 2) Remove from passed object all keys which are not in (1) result:
     *    <pre>
     *       {
     *         "key": {
     *           "subKey": {
     *             "value": "value"
     *           },
     *           "subKey_2": {
     *             "value_2": "value_2"
     *           },
     *           "subKey_3": {
     *             "value_3": "value_3"
     *           }
     *         },
     *         "key_1": {
     *           "value": "value"
     *         }
     *       }
     *    </pre>
     * 3) For each of existing keys going deeper with correspond child node:
     *    "key" contains next child nodes: [subKey, subKey_2] and current key name
     *    is "key".
     * 4) Get from passed JSON object by key "key" and remove all keys which are
     *    not in (3) result:
     *    <pre>
     *       {
     *         "key": {
     *           "subKey": {
     *             "value": "value"
     *           },
     *           "subKey_2": {
     *             "value_2": "value_2"
     *           }
     *         },
     *         "key_1": {
     *           "value": "value"
     *         }
     *       }
     *    </pre>
     * 5) For each of existing keys going deeper with correspond child node:
     *    "subKey" contains next child nodes: [value] and current key name is
     *    "subKey".
     * 6) As we can see "subKey" object contains only "value" key, and there no
     *    more child nodes, so it is "end" of current branch.
     * 7) Do same for another key from (3) "subKey_2".
     * 8) Do same for another key from (2) "key_1".
     * 9) Result:
     *    <pre>
     *       {
     *         "key": {
     *           "subKey": {
     *             "value": "value"
     *           },
     *           "subKey_2": {
     *             "value_2": "value_2"
     *           }
     *         },
     *         "key_1": {
     *           "value": "value"
     *         }
     *       }
     *    </pre>
     *
     * NOTE: inside prefix tree we have only key names. We do not know is this
     *       key contains object or array or value inside. Inside this method next
     *       actions would be done for each of this type:
     *       1) Object - continue clean up with child nodes.
     *       2) Array - clean up will be done for each of array elements.
     *       3) Value - will be ignored, if it is allowed by parent node we leave
     *          it, it is not matter is child nodes exist in tree or not.
     *
     * @param nodeMap       current tree node.
     * @param mapToSanitize map to clean-up.
     */
    private void cleanUp(Map<String, PrefixTreeNode> nodeMap, Map<?, ?> mapToSanitize) {
        if (nodeMap.isEmpty()) {
            return;
        }

        mapToSanitize.entrySet().removeIf(e -> {
            var key = (String) e.getKey();
            if (PRODUCT_FIELD_ID.equals(key)) {
                return false;
            }

            return !nodeMap.containsKey(key);
        });

        nodeMap.forEach((k, v) -> {
            var current = mapToSanitize.get(v.getPrefix());
            if (current instanceof Map map) {
                cleanUp(v.getChildren(), map);
            } else if (current instanceof List list) {
                handleArray(list, v.getChildren());
            }
        });
    }

    /**
     * Used to handle array inside JSON object, and do "cleanUp" operation for each
     * element inside this array. In case if this array contains another arrays as
     * their members this method will recursively call himself for each subarray.
     *
     * @param list list with elements where we need to "cleanUp" fields.
     * @param node current node which child elements contains fields which we should
     *             leave inside each element in passed list.
     */
    private void handleArray(List<?> list, HashMap<String, PrefixTreeNode> node) {
        list.forEach(e -> {
            if (e instanceof Map map) {
                cleanUp(node, map);
            } else if (e instanceof List<?> array) {
                handleArray(array, node);
            }
        });
        // clean-up empty objects and lists.
        list.removeIf(o -> (o instanceof Map m && m.isEmpty()) || (o instanceof List l && l.isEmpty()));
    }

    /**
     * Used to create a prefix tree based on passed list with paths.
     *
     * This method will check passed array to know is it contains {@link PrefixTreeMap#WILDCARD}
     * or not, if so - than empty prefix tree will be created, and {@link PrefixTreeMap#allAllowed}
     * flag will be set to "true". Otherwise {@link PrefixTreeMap#allAllowed} flag
     * will be set to "false", and prefix tree will be created from passed list.
     *
     * To create a prefix tree inside this method each string from passed array
     * will be split by {@link PrefixTreeMap#SEPARATOR} and result parts would be
     * placed to the correct position in tree.
     *
     * For example:
     *
     * Passed array contains next strings:
     * <pre>
     * ["key_1.key_2", "key_1.key_3", "key_4.key_5", "key_6.key_7.key_8"]
     * </pre>
     *
     * So next step it is split all of them by separator ang grouped by same parts.
     * After grouping result will be look like:
     * <pre>
     * {
     *     key_1: {
     *         key_2,
     *         key_3
     *     },
     *     key_4: {
     *         key_5
     *     },
     *     key_6: {
     *         key_7: {
     *             key_8
     *         }
     *     },
     * }
     * </pre>
     *
     *
     * @param prefixes list with strings where each of them describe a path inside JSON object.
     * @return built prefix tree map object.
     */
    public static PrefixTreeMap fromList(List<String> prefixes) {
        if (prefixes == null || prefixes.isEmpty() || prefixes.contains(Constants.WILDCARD)) {
            return new PrefixTreeMap(true);
        }

        var map = new PrefixTreeMap(false);

        List<String> fields = new ArrayList<>(prefixes);

        // always keep product id field
        fields.add(PRODUCT_FIELD_ID);
        fields.add(PRODUCT_FIELD_VARIANTS + DOT + PRODUCT_FIELD_ID);

        fields.forEach(p -> {
            var parts = p.split(SEPARATOR);
            var node = map.computeIfAbsent(parts[0], PrefixTreeNode::new);
            for (int i = 1; i < parts.length; i++) {
                node = node.getChildren().computeIfAbsent(parts[i], PrefixTreeNode::new);
            }
        });

        return map;
    }

    /**
     * Single node object, contains current field name and map with child nodes.
     */
    static class PrefixTreeNode {

        private final String prefix;
        private final HashMap<String, PrefixTreeNode> children = new HashMap<>();

        private PrefixTreeNode(String prefix) {
            this.prefix = prefix;
        }

        public String getPrefix() {
            return prefix;
        }

        public HashMap<String, PrefixTreeNode> getChildren() {
            return children;
        }
    }

}
