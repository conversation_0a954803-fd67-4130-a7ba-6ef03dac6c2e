package com.groupbyinc.search.ssa.util;

import com.groupbyinc.proto.commandcenter.config.AttributeMessage.AttributeType;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.groupbyinc.proto.commandcenter.config.AttributeMessage.AttributeType.NUMERICAL;
import static com.groupbyinc.proto.commandcenter.config.AttributeMessage.AttributeType.TEXTUAL;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.ATTRIBUTES_PREFIX;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.VARIANTS;
import static com.groupbyinc.search.ssa.util.Constants.CUSTOM_ATTRIBUTES_KEY;
import static com.groupbyinc.search.ssa.util.Constants.CUSTOM_ATTRIBUTES_PREFIX;
import static com.groupbyinc.search.ssa.util.Constants.NUMBERS;
import static com.groupbyinc.search.ssa.util.Constants.TEXT;
import static java.util.regex.Pattern.compile;

@Slf4j
@UtilityClass
public class AttributeUtils {

    /**
     * name of fields with id of product.
     */
    public static final String PRODUCT_ID_FIELD = "productId";
    public static final String PRODUCT_ID_FIELD_FOR_MONGO = "id";

    public static final String INVENTORIES_ATTRIBUTE_PREFIX = "inventories.";

    /**
     * Base inventory navigation template, technically it is a way how it should look to be accepted by Google.
     * In this template, first placeholder it is a placeId, second one it is an attribute name.
     * <p>
     * Example: 'inventory(GBL, price)' or 'inventory(GBL, attributes.isSale)' for custom attributes.
     * <p>
     * See <a href="https://cloud.google.com/retail/docs/reference/rest/v2/FacetSpec#facetkey">facet documentation</a>.
     */
    public static final String INVENTORY_NAVIGATION_TEMPLATE = "inventory(%s, %s)";

    /**
     * Two types of patterns to parse inventory navigations from search request, we need bot of them to be compatible
     * with 'searchandizer' system.
     * <p>
     * This patterns used to extract placeId and attribute from inventory navigations.
     *
     * @see AttributeUtils#INVENTORY_NAVIGATION_TEMPLATE
     */
    public static final Pattern INVENTORIES_NAVIGATION_PATTERN = compile("^(.*)\\.inventories\\.(.*)$");
    public static final Pattern INVENTORIES_NAVIGATION_PATTERN_SECOND = compile("^inventory\\((.*), ?(.*)\\)$");

    /**
     * Used to convert attribute name to the 'json' path in the object stored in mongo DB.
     *
     * @param field                   name of attribute used in filter.
     * @param attributeConfigurations attribute configuration.
     *
     * @return 'json' path in the object stored in mongo DB for passed field.
     */
    public static String transformFieldForMongo(String field, Map<String, AttributeConfiguration> attributeConfigurations) {
        var inventoryMatcher = parseInventoryNavigations(field);
        var updatedField = extractInventoryAttribute(field, inventoryMatcher, attributeConfigurations);

        return inventoryMatcher
            .map(matcher -> INVENTORY_NAVIGATION_TEMPLATE.formatted(matcher.group(1), updatedField))
            .orElse(updatedField);
    }

    private String extractInventoryAttribute(String field,
                                             Optional<Matcher> inventoryMatcher,
                                             Map<String, AttributeConfiguration> attributeConfigurations) {
        var fieldToUpdate = field;
        var inventory = false;
        if (inventoryMatcher.isPresent()) {
            inventory = true;
            fieldToUpdate = inventoryMatcher.get().group(2);
        }

        return transformField(
            fieldToUpdate,
            attributeConfigurations,
            inventory
        );
    }

    /**
     * Parse inventory navigation from passed field.
     *
     * @param field to parse.
     *
     * @return optional object with the result of parsing.
     *
     * @see AttributeUtils#INVENTORIES_NAVIGATION_PATTERN
     */
    @NonNull
    public static Optional<Matcher> parseInventoryNavigations(@Nullable String field) {
        if (field == null) {
            return Optional.empty();
        }

        var second = INVENTORIES_NAVIGATION_PATTERN_SECOND.matcher(field);
        if (second.find()) {
            return Optional.of(second);
        }

        var first = INVENTORIES_NAVIGATION_PATTERN.matcher(field);
        if (first.find()) {
            return Optional.of(first);
        }

        return Optional.empty();
    }

    /**
     * Adds a specific prefix for 'SYSTEM' fields.
     * <p>
     * It is use attribute configurations to define a prefix if it is a system field.
     *
     * @param field      to add prefix if required.
     * @param attributes attribute configurations to define a prefix.
     *
     * @return field with prefix it passed field is 'SYSTEM', unchanged field otherwise.
     */
    @NonNull
    public static String transformField(@NonNull String field,
                                        @NonNull Map<String, AttributeConfiguration> attributes,
                                        boolean inventory) {
        if (field.startsWith(ATTRIBUTES_PREFIX) && !inventory) {
            return field;
        }

        if (field.equals(PRODUCT_ID_FIELD)) {
            return PRODUCT_ID_FIELD_FOR_MONGO;
        }

        var attributeName = inventory ? INVENTORIES_ATTRIBUTE_PREFIX + field : field;
        var attributeConfig = attributes.get(attributeName);
        if (attributeConfig != null) {
            return attributeConfig.path();
        }

        return field;
    }

    /**
     * Retrieves untyped distinct attribute values from given {@link Record}. Supports lookup in both system and custom
     * attributes from primary and its variants.
     * <p>
     * For {@link AttributeType#TEXTUAL} attribute guarantees to return only {@link String} elements.
     * <p>
     * For {@link AttributeType#NUMERICAL} attribute guarantees to return only abstract {@link Number} elements.
     *
     * @param record    {@link Record} to lookup attribute from
     * @param attribute {@link AttributeConfiguration}
     *
     * @return a list of distinct attribute values from the record and it variants
     */
    @SuppressWarnings("unchecked")
    public static List<Object> getAttributeValueFromRecord(Record record, AttributeConfiguration attribute) {
        if (record == null || record.getMetadata() == null) {
            return List.of();
        }
        var isCustom = attribute.path().startsWith(CUSTOM_ATTRIBUTES_PREFIX);
        var attrKey = isCustom
            ? attribute.path().substring(CUSTOM_ATTRIBUTES_PREFIX.length())
            : attribute.path();

        var attributeMaps = new ArrayList<Map<String, Object>>();
        attributeMaps.add(record.getMetadata());
        var variants = record.getMetadata().get(VARIANTS);
        if (variants instanceof List<?> vLi && !vLi.isEmpty() && vLi.getFirst() instanceof Map) {
            attributeMaps.addAll((List<Map<String, Object>>) variants);
        }

        return attributeMaps.stream()
            .map(m -> isCustom
                ? extractValueFromMap((Map<String, Object>) m.get(CUSTOM_ATTRIBUTES_KEY), attribute.type(), attrKey)
                : extractValueFromMap(m, attribute.type(), attrKey))
            .flatMap(Collection::stream)
            .filter(o -> attribute.type() == NUMERICAL && o instanceof Number
                || attribute.type() == TEXTUAL && o instanceof String)
            .distinct()
            .toList();
    }

    @SuppressWarnings("unchecked")
    private List<Object> extractValueFromMap(Map<String, Object> attributeMap,
                                             AttributeType type,
                                             String attributePath) {
        if (attributeMap == null) {
            return List.of();
        }
        var attrNode = attributePath.contains(".")
            ? getNested(attributeMap, attributePath.split("\\."))
            : attributeMap.get(attributePath);
        if (attrNode == null) {
            return List.of();
        }
        if (attrNode instanceof Map<?, ?> attrValueMap) {
            var value = type == NUMERICAL ? attrValueMap.get(NUMBERS) : attrValueMap.get(TEXT);
            return value instanceof List<?> ? (List<Object>) value : List.of(value);
        }
        return attrNode instanceof List<?> ? (List<Object>) attrNode : List.of(attrNode);
    }

    @SuppressWarnings("unchecked")
    private Object getNested(Map<String, Object> map, String[] parts) {
        Object current = map;
        for (var p : parts) {
            if (current instanceof Map) {
                current = ((Map<String, Object>) current).get(p);
            } else {
                return null;
            }
        }
        return current;
    }
}
