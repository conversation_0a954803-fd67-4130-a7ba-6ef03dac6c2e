package com.groupbyinc.search.ssa.retail.filteringold.expression.numerical;

import com.groupbyinc.search.ssa.retail.filteringold.expression.Expression;
import io.micronaut.core.annotation.NonNull;
import lombok.Getter;

import javax.annotation.Nonnull;

import static com.groupbyinc.utils.validation.ValidationUtils.requireDefined;

/**
 * Expression for logical comparisons.
 * Used to filter products by specific numeric value in some facet(attribute).
 * <p>
 * NOTE: Currently supports only "=" comparison.
 */
@Getter
public class ComparisonExpression extends Expression {

    /**
     * Used to check if a product has a specific value inside a specific facet(attribute).
     */
    private static final String EQUAL = "=";

    /**
     * Expression template.
     * #1 placeholder – facet(attribute) name.
     * #2 placeholder – comparison operator.
     * #3 placeholder – value which product should have in specific attribute.
     * <p>
     * Example: "price=100"
     */
    private static final String EXPRESSION_TEMPLATE = "%s%s%s";

    /**
     * value which product should have in specific attribute.
     */
    @NonNull
    private final Number value;

    /**
     * comparison operator.
     * <p>
     * NOTE: Currently supports only {@link ComparisonExpression#EQUAL}.
     */
    private final String comparison;

    public ComparisonExpression(@NonNull String field, @NonNull Number value) {
        super(field);
        this.comparison = EQUAL;
        this.value = requireDefined(value, "Comparison expression value");
    }

    @Nonnull
    @Override
    public String toFilter() {
        return EXPRESSION_TEMPLATE.formatted(field, comparison, value);
    }

}
