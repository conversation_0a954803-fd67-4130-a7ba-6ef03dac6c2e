package com.groupbyinc.search.ssa.retail.filtering.expression.text;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.text.TextExpression;
import io.micronaut.core.annotation.NonNull;

import javax.annotation.Nonnull;
import java.util.List;

import static com.groupbyinc.search.ssa.util.StringUtils.COMMA;

import static java.util.stream.Collectors.joining;

/**
 * Expression for text facets(attributes).
 * <p>
 * Used to filter products by specific textual values in some facet(attribute).
 * <p>
 * Example: <pre>{@code color:ANY(\"Red\", \"Green\")}</pre>
 *
 * @see <a href="https://cloud.google.com/retail/docs/filter-and-order">Retail search filter documentation</a>
 */
public class RetailTextExpression extends TextExpression<String> {

    /**
     * Template used to surround text value with escaped quotas.
     */
    private static final String ESCAPE_QUOTES_TEMPLATE = "\"%s\"";

    /**
     * Expression template.
     * #1 placeholder – facet(attribute) name.
     * #2 placeholder – comma separated list of values where each value is surrounded with escaped
     *                  quotes, product should contain any of these values in specific attribute.
     * <p>
     * Examples: <pre>{@code color:ANY(\"Red\", \"Green\")}</pre>
     */
    private static final String EXPRESSION_TEMPLATE = "%s:ANY(%s)";

    /**
     * Creates a new text expression.
     *
     * @param field      facet(attribute) the expression is for.
     * @param refinement value which product should contain in specific attribute.
     *
     * @return Text expression.
     */
    @NonNull
    public static RetailTextExpression contains(@NonNull String field, @NonNull String refinement) {
        return new RetailTextExpression(field, List.of(refinement));
    }

    /**
     * Creates a new text expression.
     *
     * @param field       facet(attribute) the expression is for.
     * @param refinements values which product should contain in specific attribute.
     *
     * @return Text expression.
     */
    @NonNull
    public static RetailTextExpression contains(@NonNull String field, @NonNull List<String> refinements) {
        return new RetailTextExpression(field, refinements);
    }

    public RetailTextExpression(@NonNull String field, @NonNull List<String> refinements) {
        super(field, refinements);
    }

    /**
     * Create a string representation of this expression.
     *
     * @return string like: 'color:ANY(\"Red\", \"Green\")'
     */
    @Nonnull
    @Override
    public String toFilter() {
        return EXPRESSION_TEMPLATE.formatted(
            field,
            refinements
                .stream()
                .map(ESCAPE_QUOTES_TEMPLATE::formatted)
                .collect(joining(COMMA))
        );
    }

}
