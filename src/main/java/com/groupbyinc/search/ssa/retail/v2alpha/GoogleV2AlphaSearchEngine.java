package com.groupbyinc.search.ssa.retail.v2alpha;

import com.google.cloud.retail.v2alpha.*;
import com.groupbyinc.search.ssa.application.Clock;
import com.groupbyinc.search.ssa.application.cache.Cache;
import com.groupbyinc.search.ssa.application.cache.CacheConfig;
import com.groupbyinc.search.ssa.application.cache.CacheKeyGenerator;
import com.groupbyinc.search.ssa.application.cache.CacheOperations;
import com.groupbyinc.search.ssa.application.cache.CacheStoredMetadata;
import com.groupbyinc.search.ssa.application.cache.StoredObject;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngine;
import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngineType;
import com.groupbyinc.search.ssa.application.core.search.variantrollupkeys.VariantRollupValuesConverter;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.RecordLabel;
import com.groupbyinc.search.ssa.core.SearchMetadata;
import com.groupbyinc.search.ssa.core.SearchMode;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.Sort;
import com.groupbyinc.search.ssa.core.SpellCorrectionMode;
import com.groupbyinc.search.ssa.core.project.ProjectConfiguration;
import com.groupbyinc.search.ssa.features.FeatureFlag;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.metrics.opentelemetry.interceptors.ApiLatencyMetricsCollector;
import com.groupbyinc.search.ssa.redis.CacheResponse;
import com.groupbyinc.search.ssa.redis.CacheSource;
import com.groupbyinc.search.ssa.redis.key.browse.BrowseCacheConfig;
import com.groupbyinc.search.ssa.redis.key.browse.BrowseCacheKeyGenerator;
import com.groupbyinc.search.ssa.redis.key.browse.PinToTopCacheKeyGenerator;
import com.groupbyinc.search.ssa.redis.key.search.SearchCacheConfig;
import com.groupbyinc.search.ssa.redis.key.search.SearchCacheKeyGenerator;
import com.groupbyinc.search.ssa.retail.boost.v2alpha.BoostSpecV2AlphaConverter;
import com.groupbyinc.search.ssa.retail.exception.SiteSearchRetailClientException;
import com.groupbyinc.search.ssa.retail.exception.SiteSearchRetailServerException;
import com.groupbyinc.search.ssa.retail.filtering.RetailFilterService;
import com.groupbyinc.search.ssa.retail.filteringold.RetailFilterServiceOld;
import com.groupbyinc.search.ssa.retail.util.v2alpha.UserAttributesHelper;
import com.groupbyinc.search.ssa.util.debug.RetailDebugInfo;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.gax.rpc.FailedPreconditionException;
import com.google.api.gax.rpc.InvalidArgumentException;
import com.google.cloud.retail.v2alpha.SearchRequest.Builder;
import com.google.cloud.retail.v2alpha.SearchResponse.SearchResult;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import com.launchdarkly.sdk.LDContext;
import io.micronaut.context.annotation.Context;
import io.micronaut.core.annotation.Nullable;
import io.micronaut.core.util.CollectionUtils;
import jakarta.inject.Named;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nonnull;
import javax.annotation.ParametersAreNonnullByDefault;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.application.cache.BaseCacheOperations.getResultsFromCacheResponse;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_TITLE;
import static com.groupbyinc.search.ssa.core.product.Product.PRODUCT_FIELD_VARIANTS;
import static com.groupbyinc.search.ssa.core.request.RequestServed.GOOGLE;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_BROWSE_CACHE;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_DATA_CATALOG_PIN_TO_TOP;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_RESPONSE_FIELDS_FROM_FETCH;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_SEARCH_CACHE;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.USE_NEW_RETAIL_FILTERING;
import static com.groupbyinc.search.ssa.redis.CacheSource.BROWSE;
import static com.groupbyinc.search.ssa.redis.CacheSource.SEARCH;
import static com.groupbyinc.search.ssa.retail.filteringold.RetailFilterServiceOld.PRODUCT_ID_FIELD;
import static com.groupbyinc.search.ssa.retail.util.v2alpha.ConversationV2AlphaUtils.buildConversationalSearchSpec;
import static com.groupbyinc.search.ssa.retail.util.v2alpha.ConversationV2AlphaUtils.isConversationalSearchSupported;
import static com.groupbyinc.search.ssa.retail.util.v2alpha.ConversationV2AlphaUtils.isFollowupConversation;
import static com.groupbyinc.search.ssa.retail.util.v2alpha.ConversationV2AlphaUtils.populateConversationalResponse;
import static com.groupbyinc.search.ssa.retail.util.v2alpha.TilesNavigationV2AlphaUtils.populateTiles;
import static com.groupbyinc.search.ssa.retail.util.v2alpha.TilesNavigationV2AlphaUtils.setupTileNavigationSpec;
import static com.groupbyinc.search.ssa.util.Constants.VARIANT_ROLLUP_VALUES;
import static com.groupbyinc.search.ssa.util.debug.DebugHelpers.getDebugInfoKey;
import static com.groupbyinc.search.ssa.util.debug.DebugHelpers.getDebugInfoOrDefault;
import static com.groupbyinc.search.ssa.util.debug.v2alpha.DebugV2AlphaHelpers.saveRetailRequestToDebug;
import static com.groupbyinc.search.ssa.util.debug.v2alpha.DebugV2AlphaHelpers.saveRetailResponseToDebug;
import static com.groupbyinc.search.ssa.util.debug.RetailDebugInfo.SEARCH_CALL_SUFFIX;

import static io.micronaut.core.util.StringUtils.isEmpty;
import static java.util.Objects.requireNonNullElse;

@Slf4j
@Context
@Named("googleV2AlphaSearchEngine")
@ParametersAreNonnullByDefault
@SuppressWarnings("deprecation")
public class GoogleV2AlphaSearchEngine implements SearchEngine {

    public static final String DEFAULT_SERVING_CONFIG = "default_search";
    public static final String PLACEMENT_TEMPLATE = "projects/%s/locations/global/catalogs/default_catalog/placements/%s";
    public static final Function<String, String> API_ERROR_FUN = "Google Cloud Retail. Can't get response. Error message: %s"::formatted;

    private static final Set<String> ATTRIBUTES_TO_DELETE = Set.of(
        "uri",
        "gtin",
        "title",
        "sizes",
        "brands",
        "images",
        "discount",
        "patterns",
        "productId",
        "materials",
        "categories",
        "conditions",
        "description",
        "availability",

        //  rating.ratingCount
        //  rating.averageRating
        //  rating.ratingHistogram
        "rating",

        //  priceInfo.cost
        //  priceInfo.price
        //  priceInfo.currencyCode
        //  priceInfo.originalPrice
        "priceInfo",

         //  colorInfo.colors
         //  colorInfo.colorFamilies
        "colorInfo",

        //  audience.genders
        //  audience.ageGroups
        "audience",

        "attributes"
    );

    private final Clock clock;
    private final ObjectMapper objectMapper;
    private final FeaturesManager featuresManager;
    private final Cache browse;
    private final Cache search;
    private final BoostSpecV2AlphaConverter boostSpecV2AlphaConverter;
    private final RetailFilterServiceOld retailFilterServiceOld;
    private final RetailFilterService retailFilterService;
    private final SearchServiceClient v2AlphaSearchServiceClient;
    private final CacheOperations cacheOperations;
    private final ConfigurationManager configurationManager;
    private final BrowseCacheKeyGenerator browseCacheKeyGenerator;
    private final SearchCacheKeyGenerator searchCacheKeyGenerator;
    private final VariantRollupValuesConverter variantRollupValuesConverter;
    private final UserAttributesHelper userAttributesHelper;

    public GoogleV2AlphaSearchEngine(
        Clock clock,
        ObjectMapper objectMapper,
        FeaturesManager featuresManager,
        @Named("browse") Cache browse,
        @Named("search") Cache search,
        BoostSpecV2AlphaConverter boostSpecV2AlphaConverter,
        RetailFilterServiceOld retailFilterServiceOld,
        RetailFilterService retailFilterService,
        SearchServiceClient v2AlphaSearchServiceClient,
        CacheOperations cacheOperations,
        ConfigurationManager configurationManager,
        BrowseCacheKeyGenerator browseCacheKeyGenerator,
        SearchCacheKeyGenerator searchCacheKeyGenerator,
        PinToTopCacheKeyGenerator pinToTopCacheKeyGenerator,
        VariantRollupValuesConverter variantRollupValuesConverter,
        UserAttributesHelper userAttributesHelper
    ) {
        this.clock = clock;
        this.objectMapper = objectMapper;
        this.featuresManager = featuresManager;
        this.browse = browse;
        this.search = search;
        this.cacheOperations = cacheOperations;
        this.boostSpecV2AlphaConverter = boostSpecV2AlphaConverter;
        this.retailFilterServiceOld = retailFilterServiceOld;
        this.retailFilterService = retailFilterService;
        this.v2AlphaSearchServiceClient = v2AlphaSearchServiceClient;
        this.configurationManager = configurationManager;
        this.browseCacheKeyGenerator = browseCacheKeyGenerator;
        this.searchCacheKeyGenerator = searchCacheKeyGenerator;
        this.variantRollupValuesConverter = variantRollupValuesConverter;
        this.userAttributesHelper = userAttributesHelper;
    }

    @Nonnull
    @Override
    @ApiLatencyMetricsCollector
    public SearchResults search(SearchParameters searchParameters) {
        var context = getRequestContext();

        var isBrowse = isEmpty(searchParameters.getQuery());
        var source = isBrowse ? BROWSE : SEARCH;

        // Browse cache ---------------------
        CacheResponse<BrowseCacheConfig, StoredObject<SearchResults>> fromBrowseCache = null;
        CacheResponse<SearchCacheConfig, StoredObject<SearchResults>> fromSearchCache = null;

        if (isBrowse) {
            fromBrowseCache = getFromCache(
                browse,
                source,
                browseCacheKeyGenerator,
                searchParameters,
                ENABLE_BROWSE_CACHE,
                BrowseCacheConfig.class,
                BrowseCacheConfig.DEFAULT,
                new TypeReference<>(){},
                context.getLdContext()
            );

            if (fromBrowseCache != null && fromBrowseCache.response().isPresent()) {
                return getResultsFromCacheResponse(
                    fromBrowseCache,
                    fromBrowseCache.response().get().metadata().retailDebugInfo(),
                    getDebugInfoKey(SEARCH_CALL_SUFFIX)
                );
            }
        } else {
            fromSearchCache = getFromCache(
                search,
                source,
                searchCacheKeyGenerator,
                searchParameters,
                ENABLE_SEARCH_CACHE,
                SearchCacheConfig.class,
                SearchCacheConfig.DEFAULT,
                new TypeReference<>(){},
                context.getLdContext()
            );

            if (fromSearchCache != null && fromSearchCache.response().isPresent()) {
                return getResultsFromCacheResponse(
                    fromSearchCache,
                    fromSearchCache.response().get().metadata().retailDebugInfo(),
                    getDebugInfoKey(SEARCH_CALL_SUFFIX)
                );
            }
        }
        //End get from cache section ---------------------

        var query = searchParameters.getQuery();
        var projectId = getProjectId(context.getMerchandiser(), context.getCollection());

        var facetConverter = searchParameters.getRetailCRMFacetConverter();
        if (searchParameters.getInventoryStoreId().isEmpty()) {
            var msg = "Property `inventoryStoreId` is missing. All global inventories navigations omitted.";
            log.debug(msg);
        }

        var searchRequestBuilder = SearchRequest.newBuilder()
            .setPlacement(PLACEMENT_TEMPLATE.formatted(projectId, defineServingConfig(searchParameters)))
            .setQuery(query)
            .setPageSize(searchParameters.getPagination().getSize())
            .setOffset((int) searchParameters.getPagination().getOffset())
            .addAllFacetSpecs(facetConverter.createFacetSpecs())
            .setSearchMode(resolveSearchMode(searchParameters))
            .setSpellCorrectionSpec(buildSpellCorrectionSpec(searchParameters))
            .setQueryExpansionSpec(buildQueryExpansionSpec(searchParameters))
            .addAllPageCategories(searchParameters.getPageCategories())
            .setBoostSpec(boostSpecV2AlphaConverter.convert(searchParameters))
            .setTileNavigationSpec(setupTileNavigationSpec(searchParameters))
            .putAllUserAttributes(
                userAttributesHelper.buildUserAttributes( // to Google request
                    userAttributesHelper.convertUserAttributes( // UserAttributeDto -> UserAttribute
                        context.getUserAttributes() // merged and encrypted at the very beginning or the request
                    )));

        if (isFollowupConversation(searchParameters)) {
            searchRequestBuilder
                .setConversationalSearchSpec(buildConversationalSearchSpec(searchParameters));
        }

        var randomVisitorId = isRandomVisitorId(
            isBrowse,
            searchParameters.getPagination().getSize() - searchParameters.getPinnedProducts().size(),
            fromBrowseCache,
            fromSearchCache
        );

        if (randomVisitorId) {
            searchRequestBuilder.setVisitorId(UUID.randomUUID().toString());
        } else {
            searchRequestBuilder.setVisitorId(context.getVisitorId());
        }

        var includeLoginId = includeLoginId(isBrowse, fromBrowseCache, fromSearchCache);
        if (context.getLoginId() != null && includeLoginId) {
            searchRequestBuilder.setUserInfo(
                UserInfo.newBuilder()
                    .setUserId(context.getLoginId())
                    .build()
            );
        }

        var useNewRetailFiltering = featuresManager.getBooleanFlagConfiguration(
            context.getLdContext(),
            USE_NEW_RETAIL_FILTERING
        );
        var filter = useNewRetailFiltering
            ? retailFilterService.createFilter(searchParameters)
            : retailFilterServiceOld.createFilter(searchParameters);

        searchRequestBuilder
            .setFilter(filter)
            .setCanonicalFilter(searchParameters.getPreFilter());

        var merchandisingConfiguration = searchParameters.getMerchandisingConfiguration();
        if (merchandisingConfiguration.siteFilter() != null) {
            searchRequestBuilder.setEntity(
                merchandisingConfiguration.siteFilter().name().toLowerCase()
            );
        }

        createSorts(searchParameters.getSorts())
            .ifPresent(searchRequestBuilder::setOrderBy);

        // Dynamic facet mode
        Boolean dynamicFacet = searchParameters.getDynamicFacet();
        SearchRequest.DynamicFacetSpec.Mode mode = dynamicFacet == null
            ? SearchRequest.DynamicFacetSpec.Mode.MODE_UNSPECIFIED
            : dynamicFacet
                ? SearchRequest.DynamicFacetSpec.Mode.ENABLED
                : SearchRequest.DynamicFacetSpec.Mode.DISABLED;
        searchRequestBuilder.setDynamicFacetSpec(SearchRequest.DynamicFacetSpec.newBuilder().setMode(mode));

        // Variant rollup keys
        searchRequestBuilder.addAllVariantRollupKeys(
            facetConverter.buildVariantRollupKeys(searchParameters.getVariantRollupKeys())
        );

        // Start timer for retail search processing.
        saveRetailRequestToDebug(searchRequestBuilder, SEARCH_CALL_SUFFIX, log);
        var retailStartTime = clock.now();
        var response = performSearch(searchRequestBuilder);
        var retailSearchDuration = Duration.between(retailStartTime, clock.now());
        saveRetailResponseToDebug(response, SEARCH_CALL_SUFFIX, log);

        var searchResultsBuilder = SearchResults.builder();

        if (SearchMode.PRODUCT_SEARCH.equals(searchParameters.getSearchMode())) {
            var records = response.getResultsList().stream()
                .map(searchResult -> convertSearchResultToRecord(
                    searchParameters,
                    searchResult,
                    searchParameters.getLabeledProductIds().get(searchResult.getId()))
                ).toList();

            populateTiles(searchParameters.getTilesNavigation(), response, searchResultsBuilder);
            searchResultsBuilder
                .correctedQuery(response.getCorrectedQuery())
                .query(searchParameters.getQuery())
                .biasingProfile(searchParameters.getBiasingProfile())
                .records(records)
                .selectedNavigations(
                    facetConverter.convertSelectedRefinementsToNavigations(
                        searchParameters.getRefinements()
                    )
                )
                .siteFilter(
                    merchandisingConfiguration.siteFilter() != null
                        ? merchandisingConfiguration.siteFilter()
                        : null
                )
                .pageCategories(searchParameters.getPageCategories())
                .filter(filter)
                .includeExpandedResults(searchParameters.getIncludeExpandedResults())
                .facetLimit(facetConverter.getFacetLimit())
                .build();

            if (isConversationalSearchSupported(response)) {
                populateConversationalResponse(response, searchResultsBuilder);
            }
        }

        var metadata = SearchMetadata.builder()
            .attributionToken(response.getAttributionToken())
            .retailTime(retailSearchDuration)
            .cached(false)
            .build();

        var convertedResponse = searchResultsBuilder
            .metadata(metadata)
            .numTotalRecords(response.getTotalSize())
            .searchEngine(SearchEngineType.GOOGLE)
            .navigations(facetConverter.convertFacetsToNavigations(response.getFacetsList()))
            .build();

        CacheResponse<?, StoredObject<SearchResults>> fromCache =
            fromBrowseCache != null ? fromBrowseCache : fromSearchCache;
        cacheOperations.saveToCache(
            fromCache,
            source,
            new StoredObject<>(
                CacheStoredMetadata.retail(getDebugInfoOrDefault(SEARCH_CALL_SUFFIX, new RetailDebugInfo())),
                convertedResponse
            )
        );

        convertedResponse.setRequestServed(GOOGLE);
        return convertedResponse;
    }

    SearchResponse performSearch(Builder searchRequestBuilder) {
        try {
            return v2AlphaSearchServiceClient.search(searchRequestBuilder.build())
                .getPage()
                .getResponse();
        } catch (FailedPreconditionException | InvalidArgumentException e) {
            throw new SiteSearchRetailClientException(API_ERROR_FUN.apply(e.getMessage()), e);
        } catch (Exception e) {
            throw new SiteSearchRetailServerException(API_ERROR_FUN.apply(e.getMessage()), e);
        }
    }

    private boolean includeLoginId(boolean isBrowse,
                                   @Nullable CacheResponse<BrowseCacheConfig, StoredObject<SearchResults>> browse,
                                   @Nullable CacheResponse<SearchCacheConfig, StoredObject<SearchResults>> search) {
        return (isBrowse && browse != null && !browse.config().enabled())
            || (!isBrowse && search != null && (search.config().includeLoginId() || !search.config().enabled())
        );
    }

    private boolean isRandomVisitorId(boolean isBrowse,
                                      int pageSize,
                                      @Nullable CacheResponse<BrowseCacheConfig, StoredObject<SearchResults>> browse,
                                      @Nullable CacheResponse<SearchCacheConfig, StoredObject<SearchResults>> search) {
        return (isBrowse && browse != null && browse.config().enabled())
            || (search != null && search.config().enabled() && !search.config().includeVisitorId())
            || (search != null && search.config().enableSaytCache() && pageSize == search.config().saytPageSize());
    }

    private String getProjectId(Merchandiser merchandiser, String collection) {
        return configurationManager
            .getProjectConfiguration(merchandiser, collection)
            .map(ProjectConfiguration::projectId)
            .orElseThrow(() -> new RuntimeException("Cannot find client configuration."));
    }

    @SuppressWarnings("unchecked")
    @SneakyThrows({ InvalidProtocolBufferException.class, JsonProcessingException.class })
    private Record convertSearchResultToRecord(SearchParameters sp, SearchResult searchResult, RecordLabel label) {
        var context = getRequestContext();

        var id = searchResult.getId();
        var product = searchResult.getProduct();
        var title = product.getTitle();
        var primaryProductId = product.getPrimaryProductId();

        var metaFromSearch = objectMapper.readValue(
            JsonFormat.printer().print(product),
            new TypeReference<HashMap<String, Object>>() {
            }
        );

        var ldContext = context.getLdContext();
        boolean mergeDisabled = featuresManager.getBooleanFlagConfiguration(ldContext, ENABLE_DATA_CATALOG_PIN_TO_TOP)
            && featuresManager.getBooleanFlagConfiguration(ldContext, ENABLE_RESPONSE_FIELDS_FROM_FETCH);

        Map<String, Object> metadata;
        if (mergeDisabled) {
            sanitizeMap(metaFromSearch);
            metadata = metaFromSearch;
            if (metadata.containsKey(PRODUCT_FIELD_VARIANTS)) {
                ((List<Map<String, Object>>) metadata.get(PRODUCT_FIELD_VARIANTS)).forEach(this::sanitizeMap);
            }
        } else {
            metadata = sp.getPrefixTreeMap().cleanUpMapBaseOnPrefixTree(metaFromSearch);
        }
        metadata.put(PRODUCT_ID_FIELD, id);
        metadata.put(PRODUCT_FIELD_TITLE, title);

        var variants = searchResult.getVariantRollupValuesMap();
        if (!variants.isEmpty()) {
            metadata.put(VARIANT_ROLLUP_VALUES, variantRollupValuesConverter.convertVariantRollupValues(variants));
        }

        return Record.of(
            context.getMerchandiser(),
            context.getCollection(),
            id,
            primaryProductId,
            product.getTitle(),
            metadata,
            label
        );
    }

    private void sanitizeMap(Map<String, Object> map) {
        map.entrySet().removeIf(e -> ATTRIBUTES_TO_DELETE.contains(e.getKey()));
    }

    static Optional<String> createSorts(List<Sort> sorts) {

        if (CollectionUtils.isEmpty(sorts)) {
            return Optional.empty();
        }

        return Optional.of(sorts.stream()
            .map(sort ->
                switch (sort.getOrder()) {
                    case ASCENDING -> sort.getField();
                    case DESCENDING -> sort.getField() + " desc";
                }
            ).collect(Collectors.joining(", "))
        );
    }

    private SearchRequest.SearchMode resolveSearchMode(SearchParameters searchParameters) {
        if (searchParameters.getSearchMode() == SearchMode.FACETED_SEARCH) {
            return SearchRequest.SearchMode.FACETED_SEARCH_ONLY;
        }
        return SearchRequest.SearchMode.SEARCH_MODE_UNSPECIFIED;
    }

    private SearchRequest.SpellCorrectionSpec buildSpellCorrectionSpec(SearchParameters searchParameters) {
        SearchRequest.SpellCorrectionSpec.Mode mode;

        if (SpellCorrectionMode.SUGGESTION_ONLY == searchParameters.getSpellCorrectionMode()) {
            mode = SearchRequest.SpellCorrectionSpec.Mode.SUGGESTION_ONLY;
        } else {
            mode = SearchRequest.SpellCorrectionSpec.Mode.AUTO;
        }

        return SearchRequest.SpellCorrectionSpec.newBuilder().setMode(mode).build();
    }

    private SearchRequest.QueryExpansionSpec buildQueryExpansionSpec(SearchParameters searchParameters) {
        SearchRequest.QueryExpansionSpec.Condition condition;

        if (searchParameters.getIncludeExpandedResults()) {
            condition = SearchRequest.QueryExpansionSpec.Condition.AUTO;
        } else {
            condition = SearchRequest.QueryExpansionSpec.Condition.DISABLED;
        }

        return SearchRequest.QueryExpansionSpec.newBuilder()
            .setCondition(condition)
            .setPinUnexpandedResults(searchParameters.getPinUnexpandedResults())
            .build();
    }

    private String defineServingConfig(SearchParameters searchParameters) {
        return requireNonNullElse(
            searchParameters.getMerchandisingConfiguration().areaConfiguration().servingConfigName(),
            DEFAULT_SERVING_CONFIG
        );
    }

    private <C extends CacheConfig & FeatureFlag, T> CacheResponse<C, T> getFromCache(
        Cache cache,
        CacheSource source,
        CacheKeyGenerator<C> keyGenerator,
        SearchParameters searchParameters,
        String featureFlagName,
        Class<C> type,
        C defaultObject,
        TypeReference<T> responseType,
        LDContext ldContext
    ) {
        var config = featuresManager.getObjectFlagConfiguration(
            ldContext,
            featureFlagName,
            type,
            defaultObject
        );

        return cacheOperations.getFromCache(
            cache,
            keyGenerator,
            searchParameters,
            responseType,
            config,
            source
        );
    }

}
