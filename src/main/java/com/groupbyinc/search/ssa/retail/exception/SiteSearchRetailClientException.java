package com.groupbyinc.search.ssa.retail.exception;

/**
 * Site Search Retail API exception indicating that client configuration or request is invalid or malformed.
 */
public class SiteSearchRetailClientException extends RuntimeException {

    public SiteSearchRetailClientException(String message) {
        super(message);
    }

    public SiteSearchRetailClientException(String message, Throwable cause) {
        super(message, cause);
    }
}
