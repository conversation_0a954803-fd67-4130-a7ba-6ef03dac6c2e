package com.groupbyinc.search.ssa.retail.util.v2alpha;

import com.groupbyinc.search.ssa.api.dto.crm.UserAttributeDto;
import com.groupbyinc.search.ssa.core.crm.UserAttribute;
import com.groupbyinc.utils.crypto.AesCipherStrategy;

import com.google.cloud.retail.v2alpha.StringList;
import io.micronaut.context.annotation.Context;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import jakarta.inject.Named;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static java.util.stream.Collectors.toSet;

@Context
public class UserAttributesHelper {

    private final AesCipherStrategy ecbCipher;

    public UserAttributesHelper(@Named("ecb") AesCipherStrategy ecbCipher) {
        this.ecbCipher = ecbCipher;
    }

    public Map<String, StringList> buildUserAttributes(@NonNull List<UserAttribute> userAttributes) {
        Map<String, StringList> encryptedUserAttributes = new HashMap<>();
        for (UserAttribute userAttribute : userAttributes) {
            encryptedUserAttributes.put(userAttribute.key(),
                StringList.newBuilder().addAllValues(userAttribute.values()).build());
        }
        return encryptedUserAttributes;
    }

    public List<UserAttributeDto> mergeAndEncrypt(@NonNull List<UserAttributeDto> userAttributes) {
        Map<String, Set<String>> unencryptedUserAttributes = new HashMap<>();
        for (var userAttribute : userAttributes) {
            if (userAttribute.key().isEmpty() || userAttribute.values().isEmpty()) {
                continue;
            }

            var key = userAttribute.key().toLowerCase();
            var values = userAttribute.values();

            var notEmptyValues = values.stream()
                .filter(e -> e != null && !e.isBlank())
                .collect(toSet());
            if (!notEmptyValues.isEmpty()) {
                unencryptedUserAttributes.computeIfAbsent(key, k -> new HashSet<>()).addAll(notEmptyValues);
            }
        }

        Map<String, Set<String>> encryptedUserAttributes = new HashMap<>();
        for (var entry : unencryptedUserAttributes.entrySet()) {
            var encryptedValues = entry.getValue().stream()
                .map(ecbCipher::encrypt)
                .collect(toSet());
            encryptedUserAttributes.put(entry.getKey(), encryptedValues);
        }
        return encryptedUserAttributes.entrySet().stream()
            .map(entry -> new UserAttributeDto(
                entry.getKey(),
                entry.getValue().stream()
                    .toList()
            ))
            .toList();
    }

    public List<UserAttribute> convertUserAttributes(@Nullable List<UserAttributeDto> userAttributes) {
        return userAttributes != null && !userAttributes.isEmpty()
            ? userAttributes.stream().map(UserAttributeDto::toDomain).toList()
            : List.of();
    }
}
