package com.groupbyinc.search.ssa.retail.filteringold;

import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.biasing.NumericContent;
import com.groupbyinc.search.ssa.core.filter.AttributeFilter;
import com.groupbyinc.search.ssa.core.filter.RangeFilter;
import com.groupbyinc.search.ssa.core.filter.ValueFilter;
import com.groupbyinc.search.ssa.core.navigation.Navigation;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;
import com.groupbyinc.search.ssa.retail.filteringold.expression.Expression;
import com.groupbyinc.search.ssa.retail.filteringold.expression.numerical.ComparisonExpression;
import com.groupbyinc.search.ssa.retail.filteringold.expression.numerical.RangeExpression;
import com.groupbyinc.search.ssa.retail.filteringold.filter.Filter;
import com.groupbyinc.search.ssa.retail.filteringold.filter.FilterItem;
import com.groupbyinc.search.ssa.retail.filteringold.filter.FilterItemWrapper;
import com.groupbyinc.search.ssa.retail.filteringold.filter.ToFilter;
import com.groupbyinc.search.ssa.retail.filteringold.filter.Type;
import io.micronaut.context.annotation.Context;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.groupbyinc.search.ssa.core.navigation.NavigationType.RANGE;
import static com.groupbyinc.search.ssa.retail.filteringold.expression.NotExpression.not;
import static com.groupbyinc.search.ssa.retail.filteringold.expression.numerical.RangeExpression.range;
import static com.groupbyinc.search.ssa.retail.filteringold.expression.text.TextExpression.contains;
import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultList;
import static io.micrometer.core.instrument.util.StringEscapeUtils.escapeJson;
import static io.micronaut.core.util.CollectionUtils.isEmpty;
import static io.micronaut.core.util.CollectionUtils.isNotEmpty;
import static io.micronaut.core.util.StringUtils.EMPTY_STRING;
import static io.micronaut.core.util.StringUtils.isEmpty;
import static java.util.Objects.requireNonNull;

@Slf4j
@Context
@RequiredArgsConstructor
public class RetailFilterServiceOld {

    public static final String PRODUCT_ID_FIELD = "id";

    /**
     * Template used to join two filters with logical "AND" condition.
     * For example, Join pre-filter and site-filter.
     */
    public static final String AND_JOIN_TEMPLATE = "(%s) AND (%s)";

    /**
     * Template used to join two filters with logical "OR" condition.
     * For example, price=50.0 OR price=65.0.
     */
    private static final String OR_JOIN_TEMPLATE = " OR ";

    /**
     * Used to build numeric expression based on passed {@link NumericContent}.
     * <p>
     * NOTE: Numeric expression can be:
     *      #1 Range (if passed numericContent has ranges) see: {@link RangeExpression}
     *      #2 Logical condition (if passed numericContent has not null values) see: {@link ComparisonExpression}
     *
     * All created expressions would be joined into one single filter using logical "OR" condition.
     * For example, "price=50.0 OR price=65.0 OR price:IN(100.0i,1000.0e) OR price:IN(1000.0i,10000.0e)"
     *
     * @param field          facet(attribute) the expression is for.
     * @param numericContent object with ranges and values to create filters.
     *
     * @return created filter string.
     */
    @NonNull
    public String createNumericContentFilter(@NonNull String field, @NonNull NumericContent numericContent) {
        var expressions = new ArrayList<Expression>();

        if (numericContent.values() != null) {
            expressions.addAll(
                numericContent
                    .values()
                    .stream()
                    .map(value -> buildComparisonExpression(field, value))
                    .toList()
            );
        }

        if (numericContent.ranges() != null) {
            expressions.addAll(
                numericContent
                    .ranges()
                    .stream()
                    .map(range -> buildRangeExpression(field, range))
                .toList()
            );
        }

        return expressions
            .stream()
            .map(Expression::toFilter)
            .collect(Collectors.joining(OR_JOIN_TEMPLATE));
    }

    /**
     * Creates a filter for a text field to match any of the provided values.
     * <p>
     * The filter syntax consists of an expression language for constructing a
     * predicate-provided field of the products being filtered. Filter expression is case-sensitive.
     * See more details on this:
     * <a href="https://cloud.google.com/retail/docs/filter-and-order#filter">user guide</a>
     *
     * @param field   field to filter on
     * @param values  values to filter on
     * @param negated if true, the filter will be negated
     * @return string representing retail filter.
     */
    public String createTextFieldFilter(@NonNull String field, @NonNull List<String> values, boolean negated) {
        return negated ? not(contains(field, values)).toFilter() : contains(field, values).toFilter();
    }

    /**
     * Used to create a condition for textual fields.
     * The syntax and supported fields are the same as a filter expression.
     * See [SearchRequest.filter][google.cloud.retail.v2.SearchRequest.filter] for detail syntax and limitations.
     * <p>
     * Examples:
     *   products with product ID "product_1" or "product_2", and color "Red" or "Blue":
     *   (id: ANY("product_1", "product_2")) AND (colorFamilies: ANY("Red","Blue"))
     *
     * @param field  facet(attribute) the expression is for.
     * @param values values which product should contain in specific attribute.
     *
     * @return created condition.
     */
    public String createTextFieldFilter(@NonNull String field, @NonNull List<String> values) {
        return contains(field, values).toFilter();
    }

    /**
     * Used to create a filter for products.
     * <p>
     * The filter syntax consists of an expression language for constructing a
     * predicate from one or more fields of the products being filtered. Filter
     * expression is case-sensitive. See more details on this:
     * <a href="https://cloud.google.com/retail/docs/filter-and-order#filter">user guide</a>
     *
     * @param searchParameters object which is representing user request.
     * @return string representing retail filter which needs to be applied to the search request.
     */
    @NonNull
    public String createFilter(@NonNull SearchParameters searchParameters) {
        var filter = createFilterCurateResults(searchParameters);

        var rawFilters = mergePreAndSiteFilters(
            searchParameters.getPreFilter(),
            searchParameters.getMerchandisingConfiguration().siteFilter()
        );

        if (filter.hasFilters()) {
            return rawFilters
                .map(s -> AND_JOIN_TEMPLATE.formatted(s, filter.toFilter()))
                .orElseGet(filter::toFilter);
        }
        return rawFilters.orElse(EMPTY_STRING);
    }

    /**
     * Constructs a filter for curating search results based on the provided search parameters.
     * This method orchestrates the creation of filters derived from attribute filters, product ID
     * inclusion, and exclusion logic, as well as selected refinements.
     *
     * @param searchParameters The search parameters containing attribute filters, product ID filters,
     *                         and selected refinements.
     * @return A Filter object that encapsulates the curated filtering criteria.
     */
    public Filter createFilterCurateResults(SearchParameters searchParameters) {
        var builder = Filter.builder();
        List<ToFilter> filters = aggregateAttributeFilters(searchParameters);
        addProductIdInclusionFilter(searchParameters, filters);
        builder.ors(filters); // Directly using OR logic here instead of a separate method.
        addExclusionAndRefinementFilters(searchParameters, builder);
        return builder.build();
    }

    /**
     * Aggregates attribute filters from the provided search parameters into a list of {@link ToFilter} objects.
     * This method iterates through each attribute filter in the search parameters, checks if there are any
     * value or range filters defined, and then adds these filters to the list after converting them using
     * the {@code createFilterForAttributeFilter} method. This process ensures that all relevant attribute
     * filters are included in the final filter construction for the search query.
     *
     * @param searchParameters The search parameters containing attribute filters to be aggregated.
     * @return A list of {@link ToFilter} objects representing the aggregated attribute filters.
     */
    private List<ToFilter> aggregateAttributeFilters(SearchParameters searchParameters) {
        List<ToFilter> filters = new ArrayList<>();
        for (var attributeFilter : searchParameters.getAttributeFilters()) {
            var valueFilters = notNullOrDefaultList(attributeFilter.valueFilters());
            var rangeFilters = notNullOrDefaultList(attributeFilter.rangeFilters());

            if (!valueFilters.isEmpty() || !rangeFilters.isEmpty()) {
                filters.add(createFilterForAttributeFilter(valueFilters, rangeFilters));
            }
        }
        return filters;
    }

    /**
     * Adds a product ID inclusion filter to the list of filters if the search parameters specify included product IDs.
     * This method checks if the search parameters contain a product ID filter with non-empty included product IDs.
     * If such product IDs are present, it creates a filter that includes these product IDs in the search results
     * and adds this filter to the provided list of filters. The inclusion filter ensures that only search results
     * containing the specified product IDs are included, allowing for targeted filtering based on product ID.
     *
     * @param searchParameters The search parameters containing product ID filters to be evaluated for inclusion.
     * @param filters          The list of {@link ToFilter} objects to which the product ID inclusion filter is added.
     */
    private void addProductIdInclusionFilter(SearchParameters searchParameters, List<ToFilter> filters) {
        var productIdFilter = searchParameters.getProductIdFilter();
        if (productIdFilter == null || isEmpty(productIdFilter.includedProductIds())) {
            return;
        }
        filters.add(
            Filter.builder()
                .or(contains(PRODUCT_ID_FIELD, productIdFilter.includedProductIds()))
                .build()
        );
    }

    /**
     * Adds exclusion filters for product IDs and refinement filters to the filter builder.
     * Handles three cases:
     * 1. Exclusion product IDs and selected refinements: combine them using "AND".
     * 2. Only exclusion product IDs: adds a filter to exclude these IDs.
     * 3. Only selected refinements: adds the refinements as filters.
     * This method enables dynamic search filter construction, catering to various filtering
     * needs such as excluding specific products while applying refinement criteria.
     *
     * @param searchParameters The {@link SearchParameters} with product ID filters and refinements.
     * @param builder The {@link Filter.FilterBuilder} for adding filters.
     */
    private void addExclusionAndRefinementFilters(SearchParameters searchParameters, Filter.FilterBuilder builder) {
        List<ToFilter> filters = new ArrayList<>();
        var refinements = createFilterForSelectedRefinements(searchParameters);
        if (refinements.hasFilters()) {
            filters.add(refinements);
        }

        var productIdFilter = searchParameters.getProductIdFilter();
        if (productIdFilter != null && isNotEmpty(productIdFilter.excludedProductIds())) {
            filters.add(
                not(contains(PRODUCT_ID_FIELD, productIdFilter.excludedProductIds()))
            );
        }

        if (filters.isEmpty()) {
            return;
        }

        builder.ands(filters);
    }

    /**
     * Creates a {@link Filter} object for a given {@link AttributeFilter}. This method processes both range and value
     * filters defined within the {@link AttributeFilter} and consolidates them into a single {@link Filter} object.
     * This consolidation allows for applying multiple filtering criteria to a search query based on the attributes of
     * the items being searched.
     * <p>
     * The method works by first initializing a map to store filter items by their corresponding field names.
     * It then processes any range filters provided in the {@link AttributeFilter}, adding them to the map. Similarly,
     * it processes any value filters, adding them as well. Finally, it converts the accumulated filter items into a
     * {@link Filter} object that represents the combined filtering criteria.
     * <p>
     * This approach enables the application of complex filtering logic, including both range-based and discrete value
     * filters, to the attributes of searchable items, enhancing the flexibility and precision of search operations.
     *
     * @param valueFilters value filters
     * @param rangeFilters range filters
     * @return A {@link Filter} object representing the combined filter criteria derived from the provided
     *         {@link AttributeFilter}.
     * @see AttributeFilter for details on the structure of range and value filters.
     */
    public Filter createFilterForAttributeFilter(
        @NonNull List<ValueFilter> valueFilters,
        @NonNull List<RangeFilter> rangeFilters
    ) {
        Map<String, FilterItemWrapper> filterItemsByField = new HashMap<>();
        addValueFilters(valueFilters, filterItemsByField);
        addRangeFilters(rangeFilters, filterItemsByField);
        return convertFilterItemsToFilter(filterItemsByField);
    }

    /**
     * Constructs a {@link Filter} object from selected refinements specified in {@link SearchParameters}. This method
     * processes the selected refinements (filters chosen by the user) and organizes them into a coherent {@link Filter}
     * object that can be applied to a search query. The purpose is to refine search results based on specific criteria
     * selected by the user, such as filtering products by color, size, brand, etc.
     * <p>
     * The method operates by:
     * 1. Initializing a map to store filter items, with each key representing a field name and each value being a
     *    {@link FilterItemWrapper} that encapsulates the filter criteria for that field.
     * 2. Processing the selected refinements provided in {@link SearchParameters}, adding them to the map.
     * 3. Converting the map of filter items into a single {@link Filter} object that encapsulates all the selected
     *    refinement criteria.
     * <p>
     * This structured approach enables the application of multiple selected refinements to a search query, thereby
     * allowing users to narrow down their search results based on a combination of attributes.
     *
     * @param searchParameters The {@link SearchParameters} containing the list of refinements (selected filters)
     *                         to be applied to the search query.
     * @return A {@link Filter} object that represents the combined filter criteria based on the selected refinements
     *         provided in the {@link SearchParameters}.
     */
    public Filter createFilterForSelectedRefinements(SearchParameters searchParameters) {
        Map<String, FilterItemWrapper> filterItemsByField = new HashMap<>();
        addSelectedRefinementsFilters(searchParameters.getRefinements(), filterItemsByField);
        return convertFilterItemsToFilter(filterItemsByField);
    }

    /**
     * Used to convert selected refinements passed in search request to the retail filter object.
     *
     * @param selectedRefinements refinements selected by user from search request.
     * @param filterItemsByField  map to accumulate created filters.
     */
    private void addSelectedRefinementsFilters(@NonNull List<SelectedRefinement> selectedRefinements,
                                               @NonNull Map<String, FilterItemWrapper> filterItemsByField) {
        selectedRefinements.forEach(refinement -> {
            var wrapper = filterItemsByField.computeIfAbsent(
                refinement.getField(),
                key -> new FilterItemWrapper(
                    getTypeFromNavigationType(refinement.getType()),
                    refinement.isOr()
                )
            );

            wrapper.getFilterItems().add(
                FilterItem
                    .builder()
                    .value(refinement.getValue())
                    .range(refinement.getRange())
                    .build()
            );
        });
    }

    /**
     * Used to define a retail filter type based on a passed navigation type.
     *
     * @param type navigation type used to define {@link Type}
     *
     * @return {@link Type} value defined based on passed navigation type.
     */
    @NonNull
    private Type getTypeFromNavigationType(@NonNull NavigationType type) {
        if (type == RANGE) {
            return Type.NUMERIC;
        }
        return Type.TEXT;
    }

    /**
     * Used to convert value filter from triggered rule to the retail filter object.
     *
     * @param values             list of {@link ValueFilter}.
     * @param filterItemsByField map to accumulate created filters.
     */
    private void addValueFilters(@NonNull List<ValueFilter> values,
                                 @NonNull Map<String, FilterItemWrapper> filterItemsByField) {
        values.forEach(valueFilter -> {
            var wrapper = filterItemsByField.computeIfAbsent(
                valueFilter.getField(),
                key -> new FilterItemWrapper(
                    getTypeFromValueFilterType(valueFilter.getType())
                )
            );

            wrapper.getFilterItems().add(
                FilterItem
                    .builder()
                    .exclude(valueFilter.isExclude())
                    .value(valueFilter.getValue())
                    .numberValue(valueFilter.getNumberValue())
                    .build()
            );
        });
    }

    /**
     * Used to define a retail filter type based on a passed value filter type.
     *
     * @param type value filter type used to define {@link Type}
     *
     * @return {@link Type} value defined based on passed value filter type.
     */
    @NonNull
    private Type getTypeFromValueFilterType(@NonNull ValueFilter.ValueFilterType type) {
        if (type == ValueFilter.ValueFilterType.NUMERIC) {
            return Type.NUMERIC;
        }
        return Type.TEXT;
    }

    /**
     * Used to convert range filter from triggered rule to the retail filter object.
     *
     * @param ranges             list of {@link RangeFilter}
     * @param filterItemsByField map to accumulate created filters.
     */
    private void addRangeFilters(@NonNull List<RangeFilter> ranges,
                                 @NonNull Map<String, FilterItemWrapper> filterItemsByField) {
        ranges.forEach(rangeFilter -> {
            var wrapper = filterItemsByField.computeIfAbsent(
                rangeFilter.getField(),
                key -> new FilterItemWrapper(Type.NUMERIC)
            );

            wrapper.getFilterItems().add(
                FilterItem
                    .builder()
                    .range(rangeFilter.getRange())
                    .exclude(rangeFilter.getRange().exclude())
                    .build()
            );
        });
    }

    /**
     * Converts a collection of filter expressions, grouped by field names, into a single {@link Filter} object.
     * This method iterates over each entry in the provided map, where each key represents a field name and each value
     * is a {@link FilterItemWrapper} that encapsulates filter items for that field. Depending on the type of filter
     * (text or numeric) associated with each field, it delegates to specific methods to add these filters to the
     * {@link Filter.FilterBuilder}.
     * <p>
     * The conversion process includes:
     * - For text filters: invoking {@link #addTextExpressions(String, FilterItemWrapper, Filter.FilterBuilder)}
     *   to handle text-based filter expressions.
     * - For numeric filters: invoking {@link #addNumericExpressions(String, FilterItemWrapper, Filter.FilterBuilder)}
     *   to handle numeric-based filter expressions.
     * <p>
     * This method ensures that all filter expressions are properly integrated into a unified filter object that
     * can be applied to a retail search operation, facilitating complex filtering logic that may involve multiple
     * fields and types of filter criteria.
     *
     * @param filterItemsByField A map where keys are field names and values are {@link FilterItemWrapper} instances,
     *                           each containing a collection of filter items to be converted into part of the final
     *                           filter object.
     * @return A {@link Filter} object constructed from the aggregated filter items, ready to be applied to a search
     *         operation to refine results based on the specified criteria.
     */
    @NonNull
    private Filter convertFilterItemsToFilter(@NonNull Map<String, FilterItemWrapper> filterItemsByField) {
        var builder = Filter.builder();

        filterItemsByField.forEach((fieldName, wrapperWithMetadata) -> {
            var type = wrapperWithMetadata.getType();
            if (type == Type.TEXT) {
                addTextExpressions(fieldName, wrapperWithMetadata, builder);
            }
            if (type == Type.NUMERIC) {
                addNumericExpressions(fieldName, wrapperWithMetadata, builder);
            }
        });

        return builder.build();
    }

    /**
     * Used to convert text expressions to the retail filter object.
     *
     * @param field   facet(attribute) the expression is for.
     * @param wrapper object to stores {@link FilterItem} with required meta-information.
     * @param builder object used to accumulate retail filters.
     */
    private void addTextExpressions(@NonNull String field,
                                    @NonNull FilterItemWrapper wrapper,
                                    @NonNull Filter.FilterBuilder builder) {
        if (wrapper.isOr()) {
            addMultivaluedTextExpression(field, wrapper, builder);
        } else {
            // here we assume that we can use only one value, so we get only a first element of a passed list.
            var item = wrapper.getFilterItems().stream().findFirst().orElseThrow();
            addSinglenessTextExpression(field, item, builder);
        }
    }

    /**
     * Used to convert text expressions with more than one value to the retail filter object.
     * <p>
     * #1 regular text expressions are added to the passed builder using "AND" condition.
     * #2 expressions which are need to be negated wrapped with logical "NOT" condition before
     *    they are added to the passed builder using "AND" condition.
     *<p>
     * NOTE: Each value added to the retail filter object will be JSON escaped.
     *
     * @param field   facet(attribute) the expression is for.
     * @param wrapper object to stores {@link FilterItem} with required meta-information.
     * @param builder object used to accumulate retail filters.
     *
     * @see Navigation#isMultiSelect()
     */
    private void addMultivaluedTextExpression(@NonNull String field,
                                              @NonNull FilterItemWrapper wrapper,
                                              @NonNull Filter.FilterBuilder builder) {
        // Expressions accumulated with "AND" condition.
        var includeFilters = wrapper.getFilterItems()
            .stream()
            .filter(filterItem -> !filterItem.isExclude())
            .map(s -> requireNonNull(escapeJson(s.getValue())))
            .toList();
        if (!includeFilters.isEmpty()) {
            builder.and(contains(field, includeFilters));
        }

        // Expressions which are negated before accumulated with "AND" condition.
        var excludeFilters = wrapper.getFilterItems()
            .stream()
            .filter(FilterItem::isExclude)
            .map(s -> requireNonNull(escapeJson(s.getValue())))
            .toList();
        if (!excludeFilters.isEmpty()) {
            builder.and(not(contains(field, excludeFilters)));
        }
    }

    /**
     * Used to convert text expressions with one value to the retail filter object.
     * <p>
     * #1 regular text expression is added to the passed builder using "AND" condition.
     * #2 expressions which need to be negated wrapped with logical "NOT" condition before
     *    it is added to the passed builder using "AND" condition.
     *<p>
     * NOTE: Value added to the retail filter object will be JSON escaped.
     *
     * @param field   facet(attribute) the expression is for.
     * @param item    object represents expression.
     * @param builder object used to accumulate retail filters.
     *
     * @see Navigation#isMultiSelect()
     */
    private void addSinglenessTextExpression(@NonNull String field,
                                             @NonNull FilterItem item,
                                             @NonNull Filter.FilterBuilder builder) {
        var expression = contains(
            field,
            requireNonNull(escapeJson(item.getValue()))
        );

        // Define is expression needs to be negated.
        if (item.isExclude()) {
            builder.and(not(expression));
        } else {
            builder.and(expression);
        }
    }

    /**
     * Used to convert numeric expressions to the retail filter object.
     *
     * @param field   facet(attribute) the expression is for.
     * @param wrapper object to stores {@link FilterItem} with required meta-information.
     * @param builder object used to accumulate retail filters.
     */
    private void addNumericExpressions(@NonNull String field,
                                       @NonNull FilterItemWrapper wrapper,
                                       @NonNull Filter.FilterBuilder builder) {
        if (wrapper.isOr()) {
            addMultivaluedNumericExpression(field, wrapper, builder);
        } else {
            // here we assume that we can use only one value, so we get only a first element of a passed list.
            var item = wrapper.getFilterItems().stream().findFirst().orElseThrow();
            addSinglenessNumericExpression(field, item, builder);
        }
    }

    /**
     * Used to convert numeric expressions with more than one value to the retail filter object.
     * <p>
     * #1 regular numeric expressions are added to the passed builder using "OR" condition.
     * #2 expressions which are need to be negated wrapped with logical "NOT" condition before
     *    they are added to the passed builder using "AND" condition.
     *<p>
     * NOTE: Numeric expression can be:
     *      #1 Range see: {@link RangeExpression}
     *      #2 Logical condition see: {@link ComparisonExpression}
     *
     * @param field   facet(attribute) the expression is for.
     * @param wrapper object to stores {@link FilterItem} with required meta-information.
     * @param builder object used to accumulate retail filters.
     */
    private void addMultivaluedNumericExpression(@NonNull String field,
                                                 @NonNull FilterItemWrapper wrapper,
                                                 @NonNull Filter.FilterBuilder builder) {
        var expressions = new ArrayList<ToFilter>();

        // Not negated
        wrapper
            .getFilterItems()
            .stream()
            .filter(filterItem -> !filterItem.isExclude())
            .map(item -> buildNumericExpression(field, item.getRange(), item.getNumberValue()))
            .forEach(expressions::add);

        if (!expressions.isEmpty()) {
            var filter = Filter.builder().ors(expressions).build();
            builder.and(filter);
        }

        // Not negated
        wrapper
            .getFilterItems()
            .stream()
            .filter(FilterItem::isExclude)
            .forEach(item -> builder.and(
                not(
                    buildNumericExpression(field, item.getRange(), item.getNumberValue())
                )
            ));
    }

    /**
     * Used to convert numeric expressions with one value to the retail filter object.
     * <p>
     * #1 regular range expression is added to the passed builder using "AND" condition.
     * #2 expressions which need to be negated wrapped with logical "NOT" condition before
     *    it is added to the passed builder using "AND" condition.
     *<p>
     * NOTE: Numeric expression can be:
     *      #1 Range see: {@link RangeExpression}
     *      #2 Logical condition see: {@link ComparisonExpression}
     *
     * @param field   facet(attribute) the expression is for.
     * @param item    object represents expression.
     * @param builder object used to accumulate retail filters.
     */
    private void addSinglenessNumericExpression(@NonNull String field,
                                                @NonNull FilterItem item,
                                                @NonNull Filter.FilterBuilder builder) {
        var expression = buildNumericExpression(field, item.getRange(), item.getNumberValue());

        // Define is expression needs to be negated.
        if (item.isExclude()) {
            builder.and(not(expression));
        } else {
            builder.and(expression);
        }
    }

    /**
     * Used to build numeric expression based on passed range or value.
     * <p>
     * NOTE: Numeric expression can be:
     *      #1 Range (if passed range is not null) see: {@link RangeExpression}
     *      #2 Logical condition (if passed range is null and value is not null) see: {@link ComparisonExpression}
     *
     * @param field facet(attribute) the expression is for.
     * @param range values the field value can be.
     * @param value single values the field value can be.
     *
     * @return created numeric expression.
     */
    @NonNull
    private Expression buildNumericExpression(@NonNull String field, @Nullable Range range, @Nullable Double value) {
        if (range != null) {
            return buildRangeExpression(field, range);
        }

        return buildComparisonExpression(field, value);
    }

    /**
     * Used to build numeric expression based on passed range.
     * <p>
     * NOTE: see: {@link RangeExpression}
     *
     * @param field facet(attribute) the expression is for.
     * @param range values the field value can be.
     *
     * @return created numeric expression.
     */
    @NonNull
    private Expression buildRangeExpression(@NonNull String field, @NonNull Range range) {
        return range(field)
            .lowerInclusive(range.low())
            .upperExclusive(range.high())
            .build();
    }

    /**
     * Used to build numeric expression based on passed value.
     * <p>
     * NOTE: see: {@link ComparisonExpression}
     *
     * @param field facet(attribute) the expression is for.
     * @param value single values the field value can be.
     *
     * @return created numeric expression.
     */
    @NonNull
    private Expression buildComparisonExpression(@NonNull String field, @Nullable Double value) {
        return new ComparisonExpression(field, requireNonNull(value));
    }

    /**
     * Merge pre-filter and site-filter if both are present and not null or return
     * non-null value of two of them.
     * <p>
     * NOTE: site-filter originates from two sources:
     *  #1 User supplied through request.
     *  #2 Customer configuration through ccapi.
     *
     * @param preFilter pre-filters passed to the request.
     * @param site      site-filter passed to the request or configured for area.
     *
     * @return Optional<String> with a filter object or empty.
     */
    private Optional<String> mergePreAndSiteFilters(String preFilter, SiteFilterConfiguration site) {
        var preFilterCheck = preFilter != null && !preFilter.isBlank();
        var siteFilterCheck = (site != null && site.rawFilter() != null) && !site.rawFilter().isBlank();

        if (preFilterCheck && site != null) {
            var siteFilter = site.rawFilter();
            if (isEmpty(siteFilter) || siteFilter.isBlank()) {
                return Optional.of(preFilter);
            }
            return Optional.of(AND_JOIN_TEMPLATE.formatted(preFilter, siteFilter));
        }

        if (preFilterCheck) {
            return Optional.of(preFilter);
        }

        if (siteFilterCheck) {
            return Optional.of(site.rawFilter());
        }

        return Optional.empty();
    }
}
