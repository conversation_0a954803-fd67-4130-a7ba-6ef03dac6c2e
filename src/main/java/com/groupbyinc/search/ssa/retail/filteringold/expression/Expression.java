package com.groupbyinc.search.ssa.retail.filteringold.expression;

import com.groupbyinc.search.ssa.retail.filteringold.filter.ToFilter;
import io.micronaut.core.annotation.NonNull;
import lombok.Getter;

import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;

/**
 * A superclass for all expressions which can be converted into retail filters.
 */
@Getter
public abstract class Expression implements ToFilter {

    /**
     * Name of the facet(attribute) the expression is for.
     */
    @NonNull
    protected final String field;

    public Expression(@NonNull String field) {
        this.field = requireNonBlank(field, "Expression field", MANDATORY);
    }

}
