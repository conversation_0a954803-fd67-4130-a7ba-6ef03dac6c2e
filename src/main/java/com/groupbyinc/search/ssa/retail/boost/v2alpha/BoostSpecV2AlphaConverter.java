package com.groupbyinc.search.ssa.retail.boost.v2alpha;

import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.biasing.Bias;
import com.groupbyinc.search.ssa.core.biasing.BiasBoostOverrideSettings;
import com.groupbyinc.search.ssa.core.rule.ProductIdsBucket;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.retail.filtering.RetailFilterService;
import com.groupbyinc.search.ssa.retail.filteringold.RetailFilterServiceOld;

import com.google.cloud.retail.v2alpha.SearchRequest;
import com.launchdarkly.sdk.LDContext;
import io.micronaut.context.annotation.Context;
import io.micronaut.core.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.ParametersAreNonnullByDefault;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.DISABLE_QUERY_SPLIT;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_NEW_BIAS_BOOST_VALUES;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.USE_NEW_RETAIL_FILTERING;
import static com.groupbyinc.search.ssa.retail.boost.BoostSpecConverter.BUCKET_TO_BOOST_VALUE;
import static com.groupbyinc.search.ssa.retail.boost.BoostSpecConverter.BUCKET_TO_BURY_VALUE;
import static com.groupbyinc.search.ssa.retail.boost.BoostSpecConverter.FILTER_OUT_BY_PRODUCT_IDS;
import static com.groupbyinc.search.ssa.retail.boost.BoostSpecConverter.MAXIMUM_CONDITION_BOOST_SPEC_AMOUNT;
import static com.groupbyinc.search.ssa.retail.boost.BoostSpecConverter.MAX_BOOSTING_BUCKET_SIZE;
import static com.groupbyinc.search.ssa.retail.boost.BoostSpecConverter.MAX_BURYING_BUCKET_SIZE;
import static com.groupbyinc.search.ssa.retail.filteringold.RetailFilterServiceOld.PRODUCT_ID_FIELD;

import static io.micrometer.core.instrument.util.StringEscapeUtils.escapeJson;
import static io.micronaut.core.util.ArrayUtils.isEmpty;
import static java.util.Objects.requireNonNullElse;

@Slf4j
@Context
@RequiredArgsConstructor
@ParametersAreNonnullByDefault
public final class BoostSpecV2AlphaConverter {

    private final FeaturesManager featuresManager;
    private final RetailFilterService retailFilterService;
    private final RetailFilterServiceOld retailFilterServiceOld;

    /**
     * If some product match for BiasingProfile by field {@link RetailFilterServiceOld#PRODUCT_ID_FIELD}
     * and boostedProductBuckets have this {@link RetailFilterServiceOld#PRODUCT_ID_FIELD} than only apply
     * the boostedProductBuckets
     * <p>
     * limit all boostSpec by {@value com.groupbyinc.search.ssa.retail.boost.BoostSpecConverter#MAXIMUM_CONDITION_BOOST_SPEC_AMOUNT}
     */
    public SearchRequest.BoostSpec convert(SearchParameters searchParameters) {
        var boostSpec = SearchRequest.BoostSpec.newBuilder();

        var boostSpecs = new ArrayList<SearchRequest.BoostSpec.ConditionBoostSpec>();

        boostSpecs.addAll(
            buildBoostSpecFromProductIdsBuckets(
                searchParameters.getBoostedProductBuckets(),
                BUCKET_TO_BOOST_VALUE,
                MAX_BOOSTING_BUCKET_SIZE
            )
        );

        boostSpecs.addAll(
            buildBoostSpecFromProductIdsBuckets(
                searchParameters.getBuriedProductBuckets(),
                BUCKET_TO_BURY_VALUE,
                MAX_BURYING_BUCKET_SIZE
            )
        );

        if (searchParameters.getBiasingProfile() != null) {
            var boosted = searchParameters
                .getBoostedProductBuckets()
                .stream()
                .flatMap(bucket -> bucket.getProducts().stream());

            var buried = searchParameters
                .getBuriedProductBuckets()
                .stream()
                .flatMap(bucket -> bucket.getProducts().stream());

            var biases = filterOutBiasesByProductIds(
                searchParameters.getBiasingProfile().getBiases(),
                Stream.concat(boosted, buried).toList()
            );

            boostSpecs.addAll(
                buildBoostSpecFromBiases(
                    biases,
                    searchParameters.getQuery()
                )
            );
        }

        if (boostSpecs.isEmpty()) {
            return boostSpec.build();
        }

        var limited = boostSpecs
            .stream()
            .limit(MAXIMUM_CONDITION_BOOST_SPEC_AMOUNT)
            .collect(Collectors.toList());

        if (limited.size() != boostSpecs.size()) {
            log.warn(
                "BoostConditionSpec size is more than {}, skipping {} boost specs.",
                MAXIMUM_CONDITION_BOOST_SPEC_AMOUNT,
                boostSpecs.size() - MAXIMUM_CONDITION_BOOST_SPEC_AMOUNT
            );
        }

        return boostSpec.addAllConditionBoostSpecs(limited).build();
    }

    /**
     * Used to create a condition boosting spec from passed biases.
     * <p>
     * NOTE:
     * If any bias has empty value in their content, boost spec for this bias
     * ill be created with a search query as a value.
     *
     * @param biases      to build a boosting spec.
     * @param searchQuery search query to use when bias has no value.
     *
     * @return created boosting specs.
     */
    private List<SearchRequest.BoostSpec.ConditionBoostSpec> buildBoostSpecFromBiases(List<Bias> biases,
                                                                                      String searchQuery) {
        var ldContext = getRequestContext().getLdContext();

        var useNewRetailFiltering = featuresManager.getBooleanFlagConfiguration(
            ldContext,
            USE_NEW_RETAIL_FILTERING
        );

        return biases.stream()
            .filter(bias -> bias.getStrength() != Bias.Strength.LEAVE_UNCHANGED)
            .map(bias -> {
                var type = bias.getType();
                String condition;
                if (type == null || Bias.Type.TEXTUAL == type) {
                    List<String> values;
                    if (containsValue(bias.getContentParsed())) {
                        values = List.of(bias.getContentParsed());
                    } else {
                        var disableQuerySplit = featuresManager.getBooleanFlagConfiguration(
                            ldContext,
                            DISABLE_QUERY_SPLIT
                        );
                        if (disableQuerySplit) {
                            values = List.of(escapeJson(searchQuery));
                        } else {
                            values = List.of(escapeJson(searchQuery).split("\\s+"));
                        }
                    }

                    condition = useNewRetailFiltering
                        ? retailFilterService.createTextFieldFilter(bias.getField(), values)
                        : retailFilterServiceOld.createTextFieldFilter(bias.getField(), values);
                } else {
                    condition = useNewRetailFiltering
                        ? retailFilterService.createNumericContentFilter(bias.getField(), bias.getNumericContent())
                        : retailFilterServiceOld.createNumericContentFilter(bias.getField(), bias.getNumericContent());
                }

                return SearchRequest.BoostSpec.ConditionBoostSpec
                    .newBuilder()
                    .setBoost(getBoostValue(ldContext, bias))
                    .setCondition(condition)
                    .build();
            })
            .toList();
    }

    private float getBoostValue(LDContext ldContext, Bias bias) {
        if (bias.getStrengthValue() != null) {
            return bias.getStrengthValue();
        }

        var enableNewBiasBoostValues = featuresManager.getObjectFlagConfiguration(
            ldContext,
            ENABLE_NEW_BIAS_BOOST_VALUES,
            BiasBoostOverrideSettings.class,
            BiasBoostOverrideSettings.DEFAULT
        );

        if (enableNewBiasBoostValues == null || !enableNewBiasBoostValues.enabled()) {
            return bias.getStrength().getValue();
        }

        var newBoostValue = switch (bias.getStrength()) {
            case ABSOLUTE_INCREASE -> enableNewBiasBoostValues.absoluteIncrease();
            case STRONG_INCREASE -> enableNewBiasBoostValues.strongIncrease();
            case MEDIUM_INCREASE -> enableNewBiasBoostValues.mediumIncrease();
            case WEAK_INCREASE -> enableNewBiasBoostValues.weakIncrease();
            case LEAVE_UNCHANGED -> enableNewBiasBoostValues.leaveUnchanged();
            case WEAK_DECREASE -> enableNewBiasBoostValues.weakDecrease();
            case MEDIUM_DECREASE -> enableNewBiasBoostValues.mediumDecrease();
            case STRONG_DECREASE -> enableNewBiasBoostValues.strongDecrease();
            case ABSOLUTE_DECREASE -> enableNewBiasBoostValues.absoluteDecrease();
        };

        return requireNonNullElse(newBoostValue, bias.getStrength().getValue());
    }

    private static boolean containsValue(String[] content) {
        if (isEmpty(content)) {
            return false;
        }

        return Arrays.stream(content).anyMatch(StringUtils::isNotBlank);
    }

    /**
     * Used to skip biasing profile biases if they have product id which is already boosted or buried.
     * NOTE: biases with empty value also would be filtered out.
     *
     * @param biases           to filter.
     * @param boostedProductId boosted product ids.
     *
     * @return return a filtered list.
     */
    private List<Bias> filterOutBiasesByProductIds(List<Bias> biases, List<String> boostedProductId) {
        if (boostedProductId.isEmpty()) {
            return biases;
        }

        return biases.stream()
            .filter(bias -> FILTER_OUT_BY_PRODUCT_IDS.test(bias, boostedProductId))
            .filter(bias -> {
                var isIdField = PRODUCT_ID_FIELD.equals(bias.getField());
                var isContentEmpty = !containsValue(bias.getContentParsed());

                return !(isIdField && isContentEmpty);
            })
            .toList();
    }

    /**
     * Used to create boost condition spec for passed product IDs.
     *
     * @param productIdsBuckets  product buckets with id to boost or bury.
     * @param mapWithBoostValues tier-based map with boost or bury values.
     * @param maxCount           maximum allowed count of boost or bury tiers.
     *
     * @return ConditionBoostSpec created from passed product buckets.
     */
    private List<SearchRequest.BoostSpec.ConditionBoostSpec> buildBoostSpecFromProductIdsBuckets(
        List<ProductIdsBucket> productIdsBuckets,
        Map<Integer, Float> mapWithBoostValues,
        int maxCount
    ) {
        var result = new ArrayList<SearchRequest.BoostSpec.ConditionBoostSpec>();

        if (productIdsBuckets.isEmpty()) {
            return result;
        }

        for (int i = 0; i < maxCount && i < productIdsBuckets.size(); i++) {
            var products = productIdsBuckets.get(i).getProducts();
            if (CollectionUtils.isEmpty(products)) {
                continue;
            }

            var useNewRetailFiltering = featuresManager.getBooleanFlagConfiguration(
                getRequestContext().getLdContext(),
                USE_NEW_RETAIL_FILTERING
            );

            var condition = useNewRetailFiltering
                ? retailFilterService.createTextFieldFilter(PRODUCT_ID_FIELD, products)
                : retailFilterServiceOld.createTextFieldFilter(PRODUCT_ID_FIELD, products);

            var boostSpec = SearchRequest.BoostSpec.ConditionBoostSpec
                .newBuilder()
                .setCondition(
                    condition
                )
                .setBoost(mapWithBoostValues.get(i))
                .build();
            result.add(boostSpec);
        }

        return result;
    }

}
