package com.groupbyinc.search.ssa.retail.util.v2alpha;

import com.groupbyinc.search.ssa.api.dto.conversation.ConversationalSearchConfigDto;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.conversation.ConversationalSearchConfig;
import com.groupbyinc.search.ssa.core.conversation.ConversationalSearchResult;
import com.groupbyinc.search.ssa.core.conversation.ProductAttributeValue;

import com.google.cloud.retail.v2alpha.SearchRequest;
import com.google.cloud.retail.v2alpha.SearchResponse;

import java.util.List;

import static com.google.cloud.retail.v2alpha.ProductAttributeValue.newBuilder;
import static io.micronaut.core.util.StringUtils.isEmpty;
import static java.util.stream.Collectors.toList;


public final class ConversationV2AlphaUtils {

    private ConversationV2AlphaUtils() {
    }

    /**
     * Indicates if request supports conversational search functionality.
     *
     * @param conversationalSearchConfigDto {@link ConversationalSearchConfigDto} request DTO
     *
     * @return {@code true} if request supports conversational search, otherwise {@code false}.
     */
    public static boolean isFollowupConversation(ConversationalSearchConfigDto conversationalSearchConfigDto) {
        return conversationalSearchConfigDto != null && conversationalSearchConfigDto.followupConversationRequested();
    }

    /**
     * Indicates if request supports conversational search functionality.
     *
     * @param searchParameters search parameters
     *
     * @return {@code true} if request supports conversational search, otherwise {@code false}.
     */
    public static boolean isFollowupConversation(SearchParameters searchParameters) {
        ConversationalSearchConfig conversationalSearchConfig = searchParameters.getConversationalSearchConfig();
        return conversationalSearchConfig != null && conversationalSearchConfig.followupConversationRequested();
    }

    /**
     * Builds conversational search parameters for Google request.
     *
     * @param searchParameters search parameters
     *
     * @return {@link SearchRequest.ConversationalSearchSpec} constructed parameters.
     */
    public static SearchRequest.ConversationalSearchSpec buildConversationalSearchSpec(SearchParameters searchParameters) {
        var conversationalSearchConfig = searchParameters.getConversationalSearchConfig();
        var searchSpecBuilder = SearchRequest.ConversationalSearchSpec.newBuilder()
            .setFollowupConversationRequested(conversationalSearchConfig.followupConversationRequested());

        if (conversationalSearchConfig.conversationId() != null) {
            searchSpecBuilder.setConversationId(conversationalSearchConfig.conversationId());
        }

        if (conversationalSearchConfig.userAnswer() == null) {
            return searchSpecBuilder.build();
        }

        if (conversationalSearchConfig.userAnswer().selectedAnswer() != null) {
            var productAttributeValue = conversationalSearchConfig.userAnswer().selectedAnswer().productAttributeValue();
            searchSpecBuilder.setUserAnswer(
                SearchRequest.ConversationalSearchSpec.UserAnswer.newBuilder()
                    .setSelectedAnswer(
                        SearchRequest.ConversationalSearchSpec.UserAnswer.SelectedAnswer.newBuilder()
                            .setProductAttributeValue(newBuilder()
                                .setName(productAttributeValue.name())
                                .setValue(productAttributeValue.value())
                                .build())
                            .build())
                    .build());
        } else {
            searchSpecBuilder.setUserAnswer(
                SearchRequest.ConversationalSearchSpec.UserAnswer.newBuilder()
                    .setTextAnswer(conversationalSearchConfig.userAnswer().textAnswer())
                    .build());
        }
        return searchSpecBuilder.build();
    }

    /**
     * Indicates if Google response contains conversational search attributes.
     *
     * @param response {@link SearchResponse} Google response
     *
     * @return {@code true} Google response contains conversational search attributes, otherwise {@code false}.
     */
    public static boolean isConversationalSearchSupported(SearchResponse response) {
        return !isEmpty(response.getConversationalSearchResult().getConversationId())
            && !response.getConversationalSearchResult().getConversationId().isEmpty();
    }

    /**
     * Populates conversational search parameters returned by Google.
     *
     * @param response    {@link SearchResponse} Google response
     * @param apiResponse {@link SearchResults} retail response
     */
    public static void populateConversationalResponse(SearchResponse response, SearchResults.SearchResultsBuilder apiResponse) {
        var conversationBuilder = ConversationalSearchResult.builder();
        var conversationalSearchResult = response.getConversationalSearchResult();

        conversationBuilder
            .conversationId(conversationalSearchResult.getConversationId())
            .followupQuestion(conversationalSearchResult.getFollowupQuestion())
            .suggestedAnswers(convertProductAttributeValues(response));
        if (!conversationalSearchResult.getRefinedQuery().equalsIgnoreCase(apiResponse.build().getQuery())) {
            apiResponse.query(conversationalSearchResult.getRefinedQuery());
        }
        apiResponse.conversationalSearchResult(conversationBuilder.build()).build();
    }

    private static List<ProductAttributeValue> convertProductAttributeValues(SearchResponse response) {
        return response.getConversationalSearchResult().getSuggestedAnswersList().stream()
            .map(e -> new ProductAttributeValue(e.getProductAttributeValue().getName(),
                e.getProductAttributeValue().getValue()))
            .collect(toList());
    }
}
