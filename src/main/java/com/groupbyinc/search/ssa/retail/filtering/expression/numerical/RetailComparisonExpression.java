package com.groupbyinc.search.ssa.retail.filtering.expression.numerical;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.numerical.ComparisonExpression;

import io.micronaut.core.annotation.NonNull;

import javax.annotation.Nonnull;

/**
 * Expression with logical comparisons for Google retail search.
 * <p>
 * Used to filter products by specific numeric value in some facet(attribute).
 * <p>
 * NOTE: Currently supports only "=" comparison.
 * <p>
 * Example: <pre>{@code 'price = 100'}</pre>
 *
 * @see <a href="https://cloud.google.com/retail/docs/filter-and-order">Retail search filter documentation</a>
 */
public class RetailComparisonExpression extends ComparisonExpression<String> {

    /**
     * Expression template.
     * #1 placeholder – facet(attribute) name.
     * #2 placeholder – comparison operator.
     * #3 placeholder – value which product should have in specific attribute.
     * <p>
     * Example: "price=100"
     */
    private static final String EXPRESSION_TEMPLATE = "%s%s%s";

    public RetailComparisonExpression(@NonNull String field, @NonNull Number value) {
        super(field, EQUAL, value);
    }

    /**
     * Create a string representation of this expression.
     *
     * @return string like: 'price = 100'
     */
    @Nonnull
    @Override
    public String toFilter() {
        return EXPRESSION_TEMPLATE.formatted(field, operator, value);
    }

}
