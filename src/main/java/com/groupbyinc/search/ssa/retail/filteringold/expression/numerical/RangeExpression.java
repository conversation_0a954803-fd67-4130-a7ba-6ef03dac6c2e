package com.groupbyinc.search.ssa.retail.filteringold.expression.numerical;

import com.groupbyinc.search.ssa.retail.filteringold.expression.Expression;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import lombok.Builder;

import javax.annotation.Nonnull;

/**
 * Expression for a numeric range.
 */
public class RangeExpression extends Expression {

    /**
     * Suffix which can be added to the number, indicates that this number has
     * to be inclusive when range is calculated.
     */
    private static final String INCLUSIVE = "i";

    /**
     * Suffix which can be added to the number, indicates that this number has
     * to be exclusive when range is calculated.
     */
    private static final String EXCLUSIVE = "e";

    /**
     * Represents an infinity bound when range is calculated.
     */
    private static final String INFINITY = "*";

    /**
     * Expression template.
     * #1 placeholder - facet(attribute) name.
     * #2 placeholder - lower bound of numeric range.
     * #3 placeholder - upper bound of numeric range.
     * <p>
     * Examples: "price:IN(1.0i, 55,99e)" | "price:IN(1.0i, *)"
     */
    private static final String EXPRESSION_TEMPLATE = "%s:IN(%s,%s)";

    /** Inclusive lower bounds of the range. */
    @Nullable
    private final Number lowerInclusive;

    /** Exclusive lower bounds of the range. */
    @Nullable
    private final Number lowerExclusive;

    /** Inclusive upper bounds of the range. */
    @Nullable
    private final Number upperInclusive;

    /** Exclusive upper bounds of the range. */
    @Nullable
    private final Number upperExclusive;

    /**
     * Creates a new numeric expression filter builder.
     *
     * @param field facet(attribute) the expression is for.
     *
     * @return Numeric expression builder.
     */
    @NonNull
    public static RangeExpression.RangeExpressionBuilder range(@NonNull String field) {
        return RangeExpression
            .builder()
            .field(field);
    }

    @Builder
    public RangeExpression(@NonNull String field,
                           @Nullable Number lowerInclusive,
                           @Nullable Number lowerExclusive,
                           @Nullable Number upperInclusive,
                           @Nullable Number upperExclusive) {
        super(field);
        this.lowerInclusive = lowerInclusive;
        this.lowerExclusive = lowerExclusive;
        this.upperInclusive = upperInclusive;
        this.upperExclusive = upperExclusive;
    }

    @Nonnull
    @Override
    public String toFilter() {
        return EXPRESSION_TEMPLATE.formatted(field, getLowerBounds(), getUpperBounds());
    }

    /**
     * Used to calculate lower bound of created expression.
     * This lower bound can be inclusive, exclusive or infinity.
     *
     * @return string representation of lower bound.
     */
    @NonNull
    private String getLowerBounds() {
        if (lowerInclusive != null) {
            return lowerInclusive + INCLUSIVE;
        }
        if (lowerExclusive != null) {
            return lowerExclusive + EXCLUSIVE;
        }
        return INFINITY;
    }

    /**
     * Used to calculate upper bound of created expression.
     * This upper bound can be inclusive, exclusive or infinity.
     *
     * @return string representation of upper bound.
     */
    @NonNull
    private String getUpperBounds() {
        if (upperInclusive != null) {
            return upperInclusive + INCLUSIVE;
        }
        if (upperExclusive != null) {
            return upperExclusive + EXCLUSIVE;
        }
        return INFINITY;
    }

}
