package com.groupbyinc.search.ssa.retail.filteringold.filter;

import io.micronaut.core.annotation.NonNull;

/**
 * Interface for defining something that can be converted to a retail search filter string.
 * @see <a href="https://cloud.google.com/retail/docs/filter-and-order">Filter and order results</a>
 */
@FunctionalInterface
public interface ToFilter {

    /**
     * Convert the object into a retail search filter string.
     *
     * @return BoolFilter string.
     */
    @NonNull
    String toFilter();

}
