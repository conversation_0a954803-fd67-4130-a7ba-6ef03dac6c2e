package com.groupbyinc.search.ssa.retail.util;

import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.tiles.ProductAttributeValue;
import com.groupbyinc.search.ssa.core.tiles.Tile;
import com.groupbyinc.search.ssa.core.tiles.TilesNavigation;

import com.google.cloud.retail.v2.SearchRequest;
import com.google.cloud.retail.v2.SearchResponse;
import io.micronaut.core.util.CollectionUtils;

import java.util.List;

import static com.google.cloud.retail.v2.ProductAttributeValue.newBuilder;

public class TilesNavigationUtils {
    public static SearchRequest.TileNavigationSpec setupTileNavigationSpec(SearchParameters searchParameters) {
        if(searchParameters.getTilesNavigation() == null) {
            return SearchRequest.TileNavigationSpec.newBuilder().setTileNavigationRequested(false).build();
        }

        SearchRequest.TileNavigationSpec.Builder builder = setupAppliedTiles(
            SearchRequest.TileNavigationSpec.newBuilder(),
            searchParameters.getTilesNavigation().appliedTiles()
        );
        builder.setTileNavigationRequested(searchParameters.getTilesNavigation().tileNavigationRequested());

        return builder.build();
    }

    public static SearchRequest.TileNavigationSpec.Builder setupAppliedTiles(
        SearchRequest.TileNavigationSpec.Builder tileNavigationSpecBuilder,
        List<ProductAttributeValue> appliedTiles
    ) {
        if (CollectionUtils.isEmpty(appliedTiles)) {
            return tileNavigationSpecBuilder;
        }

        for (int i = 0; i < appliedTiles.size(); i++) {
            var productValue = newBuilder()
                .setValue(appliedTiles.get(i).value())
                .setName(appliedTiles.get(i).name())
                .build();
            var tile = com.google.cloud.retail.v2.Tile.newBuilder().setProductAttributeValue(productValue).build();

            tileNavigationSpecBuilder.addAppliedTiles(i, tile);
        }
        return tileNavigationSpecBuilder;
    }

    public static void populateTiles(
        TilesNavigation tilesNavigation,
        SearchResponse response,
        SearchResults.SearchResultsBuilder apiResponse
    ) {
        if (tilesNavigation != null && tilesNavigation.tileNavigationRequested()) {
            apiResponse.tiles(response.getTileNavigationResult().getTilesList().stream()
                .map(Tile::toDomain)
                .toList());
        }
    }

}
