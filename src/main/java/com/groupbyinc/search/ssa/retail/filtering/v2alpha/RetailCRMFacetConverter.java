package com.groupbyinc.search.ssa.retail.filtering.v2alpha;

import com.groupbyinc.search.ssa.application.core.search.converter.AbstractFacetConverter;
import com.groupbyinc.search.ssa.application.core.search.converter.ResolvedNavigationConfiguration;
import com.groupbyinc.search.ssa.core.Order;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.navigation.Facet;
import com.groupbyinc.search.ssa.core.navigation.Navigation;
import com.groupbyinc.search.ssa.core.navigation.NavigationRefinement;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;

import com.google.cloud.retail.v2alpha.Interval;
import com.google.cloud.retail.v2alpha.SearchRequest;
import com.google.cloud.retail.v2alpha.SearchResponse;
import io.micronaut.core.annotation.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.groupbyinc.search.ssa.core.navigation.NavigationRefinement.applyPinnedRefinements;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.RANGE;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.VALUE;
import static com.groupbyinc.search.ssa.util.AttributeUtils.INVENTORIES_NAVIGATION_PATTERN;
import static com.groupbyinc.search.ssa.util.AttributeUtils.INVENTORY_NAVIGATION_TEMPLATE;

@Slf4j
public class RetailCRMFacetConverter extends AbstractFacetConverter {

    private final Facet facet;
    private final List<String> selectedRefinements;

    public RetailCRMFacetConverter(@NonNull SearchParameters searchParameters) {
        super(searchParameters);

        this.facet = searchParameters.getFacet();
        this.selectedRefinements = searchParameters
            .getRefinements()
            .stream()
            .map(SelectedRefinement::getField)
            .toList();
    }

    /*
     * Here we create FacetSpec to send it to the Google.
     *
     * For each configured navigation while we don't reach a limit, we're creating
     *  a FacetSpec, for each navigation we will check is it selected navigation
     * or not.
     *
     * Default Google behavior: selected navigation will not be returned as part
     * of "availableNavigations" within response.
     *
     * But we have a special flag "or" for each navigation; this flag indicates
     * that multiply values from this navigation can be selected.
     * So if navigation is
     * selected and has multiselect enabled, we should also add this navigation to
     * the "excluded" list.
     * After that, this navigation will be returned within
     * response as part of "availableNavigations" and user may want to select more
     * than one option within next requests.
     *
     * NOTE:
     * Google handles dynamic navigations, so there is no reason to add them
     * to the request as FacetSpec.
     * Also, we shouldn't add their field to the list
     * with "excluded" navigations.
     */
    public List<SearchRequest.FacetSpec> createFacetSpecs() {
        var facets = resolveNavigations().stream()
            .map(this::navigationConfigToFactSpec)
            .limit(SPEC_LIMIT)
            .collect(Collectors.toList());

        if (facet != null && facets.stream().noneMatch(f -> f.getFacetKey().getKey().equals(facet.navigationName()))) {
            var specBuilder = SearchRequest.FacetSpec
                .newBuilder();

            var keyBuilder = SearchRequest.FacetSpec.FacetKey
                .newBuilder()
                .setCaseInsensitive(true)
                .setKey(facet.navigationName());

            if (facet.prefix() != null) {
                keyBuilder.addPrefixes(facet.prefix());
            }
            if (facet.contains() != null) {
                keyBuilder.addContains(facet.contains());
            }

            facets.add(specBuilder.setFacetKey(keyBuilder.build()).build());
        }

        return facets;
    }

    public List<Navigation> convertFacetsToNavigations(List<SearchResponse.Facet> facets) {
        return facets.stream()
            .filter(entry -> !excludedNavigations.contains(entry.getKey()))
            .map(this::convertFacetToNavigation)
            .filter(Objects::nonNull)
            .sorted(navigationComparator)
            .collect(Collectors.toList());
    }

    public Navigation convertFacetToNavigation(SearchResponse.Facet facet) {
        if (facet.getValuesList().isEmpty()) {
            return null;
        }

        final String facetKey = facet.getKey();
        final NavigationType navigationType = identifyNavigationType(facet);
        var refinements = getRefinements(facet);

        return convertFacetToNavigation(facetKey, navigationType, refinements);
    }

    public List<String> buildVariantRollupKeys(List<String> variantRollupKeys) {
        List<String> converted = new ArrayList<>();
        variantRollupKeys.forEach(s -> {
            var m = INVENTORIES_NAVIGATION_PATTERN.matcher(s);
            if (m.find()) {
                converted.add(
                    INVENTORY_NAVIGATION_TEMPLATE.formatted(m.group(1), m.group(2))
                );
            } else {
                converted.add(s);
            }
        });
        return converted;
    }

    private SearchRequest.FacetSpec navigationConfigToFactSpec(ResolvedNavigationConfiguration navigation) {
        var field = navigation.field();
        var specBuilder = SearchRequest.FacetSpec.newBuilder();
        var keyBuilder = SearchRequest.FacetSpec.FacetKey.newBuilder().setKey(field);

        addIntervalsIfNeeded(navigation, keyBuilder);
        applySortIfNeeded(navigation, keyBuilder);
        addFacetPrefixOrContainsIfNeeded(field, keyBuilder);
        specBuilder.setFacetKey(keyBuilder.build());
        addExcludedFilterKeysIfNeeded(field, navigation, specBuilder);

        specBuilder.setLimit(facetLimit);
        return specBuilder.build();
    }

    private void addIntervalsIfNeeded(ResolvedNavigationConfiguration navigation,
                                      SearchRequest.FacetSpec.FacetKey.Builder keyBuilder) {
        if (navigation.type() == RANGE && navigation.ranges() != null) {
            navigation
                .ranges()
                .stream()
                .map(this::createInterval)
                .forEach(keyBuilder::addIntervals);
        }
    }

    private void applySortIfNeeded(ResolvedNavigationConfiguration navigation,
                                   SearchRequest.FacetSpec.FacetKey.Builder keyBuilder) {
        var sort = navigation.sort();

        // Refinements may be sorted only in descending order by field or count.
        // https://cloud.google.com/retail/docs/reference/rest/v2beta/projects.locations.catalogs.controls#FacetSpec
        if (navigation.type() == VALUE && sort != null && sort.orderType() == Order.DESCENDING) {
            var order = switch (sort.field()) {
                case COUNT -> "count desc";
                case VALUE -> "value desc";
            };

            keyBuilder.setOrderBy(order);
        }
    }

    private void addFacetPrefixOrContainsIfNeeded(String field,
                                                  SearchRequest.FacetSpec.FacetKey.Builder keyBuilder) {
        if (facet != null && field.equals(facet.navigationName()) && facet.type() == VALUE) {
            keyBuilder.setCaseInsensitive(true);
            if (facet.prefix() != null) {
                keyBuilder.addPrefixes(facet.prefix());
            }
            if (facet.contains() != null) {
                keyBuilder.addContains(facet.contains());
            }
        }
    }

    private void addExcludedFilterKeysIfNeeded(String field,
                                               ResolvedNavigationConfiguration navigation,
                                               SearchRequest.FacetSpec.Builder specBuilder) {
        if (selectedRefinements.contains(field) && navigation.multiSelect()) {
            specBuilder.addExcludedFilterKeys(field);
        }
    }

    private List<NavigationRefinement> getRefinements(SearchResponse.Facet facet) {
        if (facet.getValuesList().isEmpty()) {
            return Collections.emptyList();
        }

        // we assume here facet can't contain values of a different type
        // so any value from list can be used to define type
        NavigationType navigationType = identifyNavigationType(facet);

        List<NavigationRefinement> refinements = facet.getValuesList()
            .stream()
            .map(facetValue -> covertToNavigationRefinement(facet.getKey(), facetValue, navigationType))
            .collect(Collectors.toList());

        if (navigationType == VALUE && pinnedRefinements != null) {
            applyPinnedRefinements(facet.getKey(), refinements, pinnedRefinements);
        }

        return refinements;
    }

    private NavigationType identifyNavigationType(SearchResponse.Facet facet) {
        return facet.getValuesList().getFirst().hasInterval() ? RANGE : VALUE;
    }

    private NavigationRefinement covertToNavigationRefinement(String facetKey,
                                                              SearchResponse.Facet.FacetValue facetValue,
                                                              NavigationType navigationType) {
        return switch (navigationType) {
            case VALUE -> constructValueRefinement(
                facetKey,
                facetValue,
                isPinnedRefinement(facetKey, facetValue.getValue())
            );
            case RANGE -> constructRangeRefinement(facetValue);
        };
    }

    private Interval createInterval(Range range) {
        var interval = Interval.newBuilder();

        if (range.low() != null) {
            interval.setMinimum(range.low());
        }

        if (range.high() != null) {
            interval.setExclusiveMaximum(range.high());
        }

        return interval.build();
    }

    private static NavigationRefinement constructRangeRefinement(SearchResponse.Facet.FacetValue facetValue) {
        Interval interval = facetValue.getInterval();
        Double min = interval.hasMinimum() ? interval.getMinimum() : null;
        Double max = interval.hasExclusiveMaximum() ? interval.getExclusiveMaximum() : null;

        return NavigationRefinement.rangeRefinement(
            new Range(min, max, null),
            facetValue.getCount()
        );
    }

    private static NavigationRefinement constructValueRefinement(String facetKey,
                                                                 SearchResponse.Facet.FacetValue facetValue,
                                                                 boolean isPinned) {
        if (facetValue.getValue().isBlank()) {
            log.warn(NULL_VALUE_LOG_MESSAGE_TEMPLATE, facetKey);
        }

        return NavigationRefinement.valueRefinement(
            facetValue.getValue(),
            facetValue.getCount(),
            isPinned
        );
    }

}
