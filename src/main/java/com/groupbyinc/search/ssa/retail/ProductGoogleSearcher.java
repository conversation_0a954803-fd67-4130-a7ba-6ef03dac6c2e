package com.groupbyinc.search.ssa.retail;

import com.groupbyinc.search.ssa.application.logging.LoggingContext;
import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.application.core.pdp.ProductSearch;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.SearchMode;
import com.groupbyinc.search.ssa.core.product.Audience;
import com.groupbyinc.search.ssa.core.product.ColorInfo;
import com.groupbyinc.search.ssa.core.product.FieldMask;
import com.groupbyinc.search.ssa.core.product.FulfillmentInfo;
import com.groupbyinc.search.ssa.core.product.Image;
import com.groupbyinc.search.ssa.core.product.PriceInfo;
import com.groupbyinc.search.ssa.core.product.Product;
import com.groupbyinc.search.ssa.core.product.ProductLocalInventory;
import com.groupbyinc.search.ssa.core.product.Promotion;
import com.groupbyinc.search.ssa.core.product.Rating;
import com.groupbyinc.search.ssa.core.product.Timestamp;
import com.groupbyinc.search.ssa.core.product.util.ProductUtils;
import com.groupbyinc.search.ssa.core.project.ProjectConfiguration;

import com.google.cloud.retail.v2.LocalInventory;
import com.google.cloud.retail.v2.ProductServiceClient;
import io.micronaut.context.annotation.Context;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.core.product.util.ProductUtils.convertAttributes;
import static com.groupbyinc.search.ssa.core.product.util.ProductUtils.prioritizeVariants;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.ContextUtils.attachContextForProductLookup;

import static io.micronaut.core.util.CollectionUtils.isEmpty;

@Slf4j
@Context
@RequiredArgsConstructor
public class ProductGoogleSearcher implements ProductSearch {

    private static final String SEARCH_PRODUCT_URL = "projects/%s/locations/global/catalogs/default_catalog/branches/default_branch/products/%s";

    private final ProductServiceClient productServiceClient;
    private final ConfigurationManager configurationManager;

    @Override
    public Optional<Product> getProductDetails(String productId, List<String> requestVariantIds) {
        var context = getRequestContext();
        LoggingContext.set(context, SearchMode.PRODUCT_SEARCH.name());

        var projectConfiguration = configurationManager
            .getProjectConfiguration(context.getMerchandiser(), context.getCollection())
            .orElseThrow(() -> new IllegalArgumentException(
                "Can't find project config for collection: " + context.getCollection()
            ));

        log.debug(
            "Search product '{}' for collection '{}' with variants: {}",
            productId,
            context.getCollection(),
            requestVariantIds
        );
        var product = this.call(projectConfiguration, productId, context.getMerchandiser());
        if (product.isEmpty()) {
            return product;
        }
        var prioritized = prioritizeVariants(requestVariantIds, product.get().getVariants());
        product.get().setVariants(prioritized);
        return product;
    }

    private Optional<Product> call(ProjectConfiguration projectConfiguration,
                                   String productId,
                                   Merchandiser merchandiser) {
        try {
            var productName = SEARCH_PRODUCT_URL.formatted(projectConfiguration.projectId(), productId);
            var product = productServiceClient.getProduct(productName);
            attachContextForProductLookup(merchandiser, projectConfiguration.collection());
            if (product == null) {
                log.error("Google search. Merchandiser: {} , collection: {} , Can't retrieve search metrics for product: {}",
                    merchandiser.merchandiserId(),
                    projectConfiguration.collection(), productId);
                return Optional.empty();
            }
            var fulfillmentInfos = product.getFulfillmentInfoList().stream().map(FulfillmentInfo::new).toList();
            var images = product.getImagesList().stream().map(Image::new).toList();
            var promotions = product.getPromotionsList().stream().map(Promotion::new).toList();
            var result = Product.builder()
                .name(product.getName())
                .id(product.getId())
                .type(product.getType().name())
                .primaryProductId(product.getPrimaryProductId())
                .collectionMemberIds(product.getCollectionMemberIdsList())
                .gtin(product.getGtin())
                .categories(product.getCategoriesList())
                .title(product.getTitle())
                .brands(product.getBrandsList())
                .description(product.getDescription())
                .languageCode(product.getLanguageCode())
                .attributes(convertAttributes(product.getAttributesMap()))
                .tags(product.getTagsList())
                .priceInfo(new PriceInfo(product.getPriceInfo()))
                .rating(new Rating(product.getRating()))
                .availableTime(new Timestamp(product.getAvailableTime()))
                .availability(product.getAvailability().name())
                .availableQuantity(product.getAvailableQuantity().getValue())
                .fulfillmentInfos(fulfillmentInfos)
                .uri(product.getUri())
                .images(images)
                .audience(new Audience(product.getAudience()))
                .colorInfo(new ColorInfo(product.getColorInfo()))
                .sizes(product.getSizesList())
                .materials(product.getMaterialsList())
                .patterns(product.getPatternsList())
                .conditions(product.getConditionsList())
                .promotions(promotions)
                .publishTime(new Timestamp(product.getPublishTime()))
                .retrievableFields(new FieldMask(product.getRetrievableFields()))
                .variants(product.getVariantsList().stream().map(Product::new).toList())
                .localInventories(getLocalInventories(product.getLocalInventoriesList()))
                .build();
            return Optional.of(result);
        } catch (Exception e) {
            log.error("Google search. Merchandiser: {} , collection: {} , Can't find product by id: {}",
                merchandiser.merchandiserId(), projectConfiguration.collection(), productId, e
            );
            return Optional.empty();
        }
    }

    private static List<ProductLocalInventory> getLocalInventories(List<LocalInventory> inventories) {
        if (isEmpty(inventories)) {
            return List.of();
        }

        return inventories
            .stream()
            .map(localInventory ->
                new ProductLocalInventory(
                    localInventory.getPlaceId(),
                    new PriceInfo(localInventory.getPriceInfo()),
                    ProductUtils.convertAttributes(localInventory.getAttributesMap())
                )
            ).toList();
    }

}
