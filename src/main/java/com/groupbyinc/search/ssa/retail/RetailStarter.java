package com.groupbyinc.search.ssa.retail;

import com.groupbyinc.search.ssa.application.configuration.ConfigurationManager;
import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngine;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.project.ProjectConfiguration;

import com.google.cloud.retail.v2.SearchRequest;
import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.annotation.Value;
import io.micronaut.context.event.ApplicationEventListener;
import io.micronaut.core.util.StringUtils;
import io.micronaut.discovery.event.ServiceReadyEvent;
import jakarta.inject.Named;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;
import java.util.UUID;

import static com.groupbyinc.search.ssa.retail.GoogleSearchEngine.DEFAULT_SERVING_CONFIG;
import static com.groupbyinc.search.ssa.retail.GoogleSearchEngine.PLACEMENT_TEMPLATE;

import static io.micronaut.core.util.StringUtils.FALSE;
import static io.micronaut.core.util.StringUtils.TRUE;

@Slf4j
@Context
@Requires(property = "retail.warmup.enabled", value = TRUE, defaultValue = FALSE)
public class RetailStarter implements ApplicationEventListener<ServiceReadyEvent> {

    private final SearchEngine googleSearchEngine;
    private final ConfigurationManager configurationManager;

    public RetailStarter(ConfigurationManager configurationManager,
                         @Named("googleSearchEngine") SearchEngine googleSearchEngine) {
        this.googleSearchEngine = googleSearchEngine;
        this.configurationManager = configurationManager;
    }

    @Nullable
    @Value("${retail.warmup.area}")
    private String area;

    @Nullable
    @Value("${retail.warmup.collection}")
    private String collection;

    @Nullable
    @Value("${retail.warmup.merchandiser-id}")
    private String merchandiserId;

    @Override
    public void onApplicationEvent(ServiceReadyEvent event) {
        StringBuilder warnings = new StringBuilder();
        if(StringUtils.isEmpty(area)) {
            warnings.append("required parameter retail.warmup.area is empty\n");
        }
        if(StringUtils.isEmpty(collection)) {
            warnings.append("required parameter retail.warmup.collection is empty\n");
        }
        if(StringUtils.isEmpty(merchandiserId)) {
            warnings.append("required parameter retail.warmup.merchandiserId is empty");
        }
        if(!warnings.isEmpty()) {
            log.warn(warnings.toString());
            return;
        }

        log.info("Init Retail on startup");

        try {
            var uuid = UUID.randomUUID().toString();
            var merchandiser = Merchandiser.of(merchandiserId);

            var projectId = configurationManager
                .getProjectConfiguration(merchandiser, collection)
                .map(ProjectConfiguration::projectId)
                .orElseThrow();

            var searchRequest = SearchRequest
                .newBuilder()
                .setPlacement(PLACEMENT_TEMPLATE.formatted(projectId, DEFAULT_SERVING_CONFIG))
                .setVisitorId(uuid);

            ((GoogleSearchEngine) googleSearchEngine).performSearch(searchRequest);
            log.info("Retail classes initialized");
        } catch (Exception e) {
            log.error("Error while init retail classes: ", e);
        }
    }

}
