package com.groupbyinc.search.ssa.retail.filtering.expression;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.Expression;
import com.groupbyinc.search.ssa.application.core.search.filtering.expression.NotExpression;

import io.micronaut.core.annotation.NonNull;

import javax.annotation.Nonnull;

/**
 * Represents a negation expression for Google retail search.
 * <p>
 * It will create a string represented filter with "NOT" condition for the defined expression object.
 * Technically, any expression can be negated, but we have to remember that we can negate only one expression,
 * so if we want to negate several expressions, we have to negate each of them.
 * <p>
 * <pre>{@code
 * Incorrect example:
 *   (NOT (colors: ANY(\"Grey\") AND price: IN(10.0i,19.99e)))
 * Correct example:
 *   (NOT colors: ANY(\"Grey\")) AND (NOT price: IN(10.0i,19.99e))
 * }</pre>
 *
 * @see <a href="https://cloud.google.com/retail/docs/filter-and-order">Retail search filter documentation</a>
 */
public class RetailNotExpression extends NotExpression<String> {

    private static final String EXPRESSION_TEMPLATE = "(NOT %s)";

    /**
     * Creates a negated version of the expression.
     *
     * @param expression to be negated.
     *
     * @return Negated expression.
     */
    @NonNull
    public static RetailNotExpression not(@NonNull Expression<String> expression) {
        return new RetailNotExpression(expression);
    }

    public RetailNotExpression(@NonNull Expression<String> expression) {
        super(expression);
    }

    /**
     * Create a string representation of this expression.
     *
     * @return string like: '(NOT price = 100)' | '(NOT price:IN(1.0i, 55,99e))' | '(NOT color:ANY(\"Red\"))'
     */
    @Nonnull
    @Override
    public String toFilter() {
        return EXPRESSION_TEMPLATE.formatted(expression.toFilter());
    }

}
