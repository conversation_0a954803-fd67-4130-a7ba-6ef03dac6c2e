package com.groupbyinc.search.ssa.retail.filtering.filter;

import com.groupbyinc.search.ssa.application.core.search.filtering.filter.Filter;
import com.groupbyinc.search.ssa.application.core.search.filtering.filter.ToFilter;

import io.micronaut.core.annotation.Nullable;
import lombok.Builder;
import lombok.Singular;

import javax.annotation.Nonnull;

import java.util.ArrayList;
import java.util.List;

import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultList;

import static io.micronaut.core.util.StringUtils.EMPTY_STRING;
import static java.util.stream.Collectors.joining;

/**
 * Used to accumulate all filters applied to the Google retail search request.
 *
 * @see <a href="https://cloud.google.com/retail/docs/filter-and-order">Retail search filter documentation</a>
 */
public class RetailFilter extends Filter<String> {

    /**
     * Used to join two filter expressions with logical "AND" condition.
     */
    private static final String AND_JOINER = " AND ";

    /**
     * Used to join two filter expressions with logical "OR" condition.
     */
    private static final String OR_JOINER = " OR ";
    private static final String LEFT_OPEN_BRACE = "(";
    private static final String RIGHT_CLOSE_BRACE = ")";
    private static final String OR_JOIN_TEMPLATE = "(%s)";

    public RetailFilter() {
        super(new ArrayList<>(), new ArrayList<>());
    }

    @Builder
    @SuppressWarnings("unused")
    public RetailFilter(@Nullable @Singular List<ToFilter<String>> ands,
                        @Nullable @Singular List<ToFilter<String>> ors) {
        super(notNullOrDefaultList(ands), notNullOrDefaultList(ors));
    }

    /**
     * Transforms stored "and" and "or" expressions into a retail search filter string.
     * This method combines expressions with logical "AND" and "OR" conditions to construct
     * a query that can be used to filter search results in a retail context.
     * <p>
     * Process:
     * 1. All "and" expressions are joined using the {@link RetailFilter#AND_JOINER}.
     * 2. All "or" expressions are individually wrapped with parentheses and then joined
     *    using the {@link RetailFilter#OR_JOINER}. Finally, if both "and" and "or" expressions
     *    exist, they're combined with the {@link RetailFilter#AND_JOINER}.
     * <p>
     * Example:
     * <pre>{@code
     * Given the following stored conditions:
     * "and" conditions:
     * - contains("color", "Red", false) // represents color:ANY("Red")
     * - contains("color", "Green", false) // represents color:ANY("Green")
     * "or" conditions grouped by attribute:
     * - range("price")
     *    .lowerInclusive(1.0)
     *    .upperExclusive(9.99)
     *    .build()
     *   and
     *   range("price")
     *     .lowerInclusive(10.0)
     *     .upperExclusive(19.99)
     *     .build()
     * - range("attributes.socialMediaLikes")
     *     .lowerInclusive(10.0)
     *     .upperExclusive(19.99)
     *     .build()
     *   and not(
     *     range("attributes.socialMediaLikes")
     *       .lowerInclusive(20.0)
     *       .upperExclusive(30.0)
     *       .build()
     *   )
     * }</pre>
     * <p>
     * The resulting filter string will be:
     * <pre>{@code
     * (NOT color:ANY("Red"))
     *   AND color:ANY("Red","Green")
     *   AND price:IN(1.0i,9.99e)
     *   AND price:IN(10.0i,19.99e)
     *   AND attributes.socialMediaLikes:IN(10.0i,19.99e)
     *   AND (NOT attributes.socialMediaLikes:IN(20.0i,30.0e))
     * }</pre>
     * <p>
     * This method ensures that the final filter string is properly formatted for use in retail search queries,
     * applying all specified filters to refine search results.
     *
     * @return A filter string formatted for retail search or an empty string if no expressions are stored.
     *
     * @see <a href="https://cloud.google.com/retail/docs/filter-and-order">Retail search filter documentation</a>
     */
    @Nonnull
    @Override
    public String toFilter() {
        var andFilters = ands.stream()
            .map(ToFilter::toFilter)
            .collect(joining(AND_JOINER));

        String orFilters = null;
        if (!ors.isEmpty()) {
            orFilters = ors
                .stream()
                .map(toFilter -> OR_JOIN_TEMPLATE.formatted(toFilter.toFilter()))
                .collect(joining(OR_JOINER, LEFT_OPEN_BRACE, RIGHT_CLOSE_BRACE));
        }

        if (!andFilters.isBlank() && orFilters != null) {
            return andFilters + AND_JOINER + orFilters;
        }
        if (orFilters != null) {
            return orFilters;
        }
        if (!andFilters.isBlank()) {
            return andFilters;
        }

        return EMPTY_STRING;
    }

}
