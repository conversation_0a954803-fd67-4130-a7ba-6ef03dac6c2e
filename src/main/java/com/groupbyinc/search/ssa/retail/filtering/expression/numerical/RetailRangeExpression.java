package com.groupbyinc.search.ssa.retail.filtering.expression.numerical;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.numerical.RangeExpression;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import lombok.Builder;

import javax.annotation.Nonnull;

/**
 * Expression for a numeric range for Google retail search.
 * <p>
 * Used to filter products by specific numeric range in some facet(attribute).
 * <p>
 * Examples: <pre>{@code 'price: IN(10i, 100.0e)' | 'price: IN(*, 100.0i)'}</pre>
 *
 * @see <a href="https://cloud.google.com/retail/docs/filter-and-order">Retail search filter documentation</a>
 */
public class RetailRangeExpression extends RangeExpression<String> {

    /**
     * Suffix which can be added to the number, indicates that this number has
     * to be inclusive when range is calculated.
     */
    private static final String INCLUSIVE = "i";

    /**
     * Suffix which can be added to the number, indicates that this number has
     * to be exclusive when range is calculated.
     */
    private static final String EXCLUSIVE = "e";

    /**
     * Represents an infinity bound when range is calculated.
     */
    private static final String INFINITY = "*";

    /**
     * Expression template.
     * #1 placeholder – facet(attribute) name.
     * #2 placeholder – lower bound of numeric range.
     * #3 placeholder – upper bound of numeric range.
     * <p>
     * Examples: "price:IN(1.0i, 55,99e)" | "price:IN(1.0i, *)"
     */
    private static final String EXPRESSION_TEMPLATE = "%s:IN(%s,%s)";

    /**
     * Creates a new numeric expression filter builder.
     *
     * @param field facet(attribute) the expression is for.
     *
     * @return Numeric expression builder.
     */
    public static @NonNull RetailRangeExpression.RetailRangeExpressionBuilder range(@NonNull String field) {
        return RetailRangeExpression.builder().field(field);
    }

    @Builder
    public RetailRangeExpression(@NonNull String field,
                                 @Nullable Number lowerInclusive,
                                 @Nullable Number upperExclusive) {
        super(field, lowerInclusive, upperExclusive);
    }

    /**
     * Create a string representation of this expression.
     *
     * @return string like: 'price:IN(1.0i, 55,99e)' | 'price:IN(1.0i, *)'
     */
    @Nonnull
    @Override
    public String toFilter() {
        return EXPRESSION_TEMPLATE.formatted(field, getLowerBounds(), getUpperBounds());
    }

    /**
     * Used to calculate lower bound of created expression.
     * This lower bound can be inclusive or infinity.
     *
     * @return string representation of lower bound.
     */
    @NonNull
    private String getLowerBounds() {
        if (lower != null) {
            return lower + INCLUSIVE;
        }

        return INFINITY;
    }

    /**
     * Used to calculate upper bound of created expression.
     * This upper bound can be exclusive or infinity.
     *
     * @return string representation of upper bound.
     */
    @NonNull
    private String getUpperBounds() {
        if (upper != null) {
            return upper + EXCLUSIVE;
        }
        return INFINITY;
    }

}
