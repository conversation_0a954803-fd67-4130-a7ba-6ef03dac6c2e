package com.groupbyinc.search.ssa.retail.filteringold.filter;

import io.micronaut.core.annotation.NonNull;
import lombok.Getter;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * Object to store {@link FilterItem} with required meta information.
 */
@Getter
public class FilterItemWrapper {

    /**
     * Type of filter expression.
     */
    @NonNull
    private final Type type;

    /**
     * Used to indicate is filter items can contain more than one item or not.
     */
    private final boolean or;

    private final Set<FilterItem> filterItems = new LinkedHashSet<>();

    public FilterItemWrapper(@NonNull Type type, boolean or) {
        this.type = type;
        this.or = or;
    }

    public FilterItemWrapper(@NonNull Type type) {
        this.type = type;
        this.or = true;
    }

}
