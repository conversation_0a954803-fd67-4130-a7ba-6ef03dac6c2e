package com.groupbyinc.search.ssa.retail.filteringold.expression;

import com.groupbyinc.search.ssa.retail.filteringold.filter.ToFilter;
import io.micronaut.core.annotation.NonNull;

import javax.annotation.Nonnull;

import static com.groupbyinc.utils.validation.ValidationUtils.requireDefined;

/**
 * Represents a negation expression.
 * <p>
 * Technically, any expression can be negated, but we have to remember that we can
 * negate only one expression, so if we want to negate several expressions, we have
 * to negate each of them.
 * <p>
 * Incorrect example:
 *          (NOT (colors: ANY(\"Grey\") AND price: IN(10.0i,19.99e)))
 * Correct example:
 *          (NOT colors: ANY(\"Grey\")) AND (NOT price: IN(10.0i,19.99e))
 */
public class NotExpression implements ToFilter {

    private static final String EXPRESSION_TEMPLATE = "(NOT %s)";

    /**
     * Expression which is needs to be wrapped with "NOT" condition.
     */
    @NonNull
    private final Expression expression;

    /**
     * Creates a negated version of the expression.
     *
     * @param expression to be negated.
     *
     * @return Negated expression.
     */
    @NonNull
    public static NotExpression not(@NonNull Expression expression) {
        return new NotExpression(expression);
    }

    public NotExpression(@NonNull Expression expression) {
        this.expression = requireDefined(expression, "Not expression");
    }

    @Nonnull
    @Override
    public String toFilter() {
        return EXPRESSION_TEMPLATE.formatted(expression.toFilter());
    }

}
