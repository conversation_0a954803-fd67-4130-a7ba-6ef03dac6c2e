package com.groupbyinc.search.ssa.retail.filtering;

import com.groupbyinc.search.ssa.application.core.search.filtering.FilterServiceBase;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.biasing.NumericContent;
import com.groupbyinc.search.ssa.core.filter.ProductIdFilter;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.application.core.search.filtering.expression.Expression;
import com.groupbyinc.search.ssa.application.core.search.filtering.expression.numerical.ComparisonExpression;
import com.groupbyinc.search.ssa.application.core.search.filtering.expression.numerical.RangeExpression;
import com.groupbyinc.search.ssa.application.core.search.filtering.filter.FilterItemWrapper;
import com.groupbyinc.search.ssa.application.core.search.filtering.filter.ToFilter;
import com.groupbyinc.search.ssa.retail.filtering.expression.RetailNotExpression;
import com.groupbyinc.search.ssa.retail.filtering.expression.numerical.RetailComparisonExpression;
import com.groupbyinc.search.ssa.retail.filtering.expression.numerical.RetailRangeExpression;
import com.groupbyinc.search.ssa.retail.filtering.expression.text.RetailTextExpression;
import com.groupbyinc.search.ssa.retail.filtering.filter.RetailFilter;

import io.micronaut.context.annotation.Context;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import jakarta.inject.Named;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static io.micrometer.core.instrument.util.StringEscapeUtils.escapeJson;
import static io.micronaut.core.util.StringUtils.EMPTY_STRING;
import static java.util.Objects.requireNonNull;

@Slf4j
@Context
@RequiredArgsConstructor
@Named("retailFilterService")
public class RetailFilterService extends FilterServiceBase<String> {

    /**
     * name of fields with id of product.
     */
    public static final String PRODUCT_ID_FIELD = "id";

    /**
     * Function to convert attribute to their filter pats analog.
     */
    private static final Function<String, String> FIELD_TO_PATH = field -> field;

    /**
     * Template used to join two filters with logical "OR" condition.
     * For example, price=50.0 OR price=65.0.
     */
    private static final String OR_JOIN_TEMPLATE = " OR ";

    /**
     * Used to build numeric expression based on passed {@link NumericContent}.
     * <p>
     * NOTE: Numeric expression can be:
     *      #1 Range (if passed numericContent has ranges) see: {@link RangeExpression}
     *      #2 Logical condition (if passed numericContent has not null values) see: {@link ComparisonExpression}
     *
     * All created expressions would be joined into one single filter using logical "OR" condition.
     * For example, "price=50.0 OR price=65.0 OR price:IN(100.0i,1000.0e) OR price:IN(1000.0i,10000.0e)"
     *
     * @param field          facet(attribute) the expression is for.
     * @param numericContent object with ranges and values to create filters.
     *
     * @return created filter string.
     */
    @NonNull
    public String createNumericContentFilter(@NonNull String field, @NonNull NumericContent numericContent) {
        var expressions = new ArrayList<Expression<String>>();

        if (numericContent.values() != null) {
            expressions.addAll(
                numericContent
                    .values()
                    .stream()
                    .map(value -> buildComparisonExpression(field, value))
                    .toList()
            );
        }

        if (numericContent.ranges() != null) {
            expressions.addAll(
                numericContent
                    .ranges()
                    .stream()
                    .map(range -> buildRangeExpression(field, range))
                    .toList()
            );
        }

        return expressions
            .stream()
            .map(Expression::toFilter)
            .collect(Collectors.joining(OR_JOIN_TEMPLATE));
    }

    /**
     * Creates a filter for a text field to match any of the provided values.
     * <p>
     * The filter syntax consists of an expression language for constructing a
     * predicate-provided field of the products being filtered. Filter expression is case-sensitive.
     * See more details on this:
     * <a href="https://cloud.google.com/retail/docs/filter-and-order#filter">user guide</a>
     *
     * @param field   field to filter on
     * @param values  values to filter on
     * @param negated if true, the filter will be negated
     *
     * @return string representing retail filter.
     */
    @NonNull
    public String createTextFieldFilter(@NonNull String field, @NonNull List<String> values, boolean negated) {
        return negated
            ? buildNotExpression(RetailTextExpression.contains(field, values)).toFilter()
            : buildTextExpression(field, values).toFilter();
    }

    /**
     * Used to create a condition for textual fields.
     * The syntax and supported fields are the same as a filter expression.
     * See [SearchRequest.filter][google.cloud.retail.v2.SearchRequest.filter] for detail syntax and limitations.
     * <p>
     * Examples:
     *   products with product ID "product_1" or "product_2", and color "Red" or "Blue":
     *   (id: ANY("product_1", "product_2")) AND (colorFamilies: ANY("Red","Blue"))
     *
     * @param field  facet(attribute) the expression is for.
     * @param values values which product should contain in specific attribute.
     *
     * @return created condition.
     */
    @NonNull
    public String createTextFieldFilter(@NonNull String field, @NonNull List<String> values) {
        return buildTextExpression(field, values).toFilter();
    }

    /**
     * Used to create a filter for search request.
     * <p>
     * Created filter contains:
     * 1) Attribute filters from rule.
     * 2) Included productId filters from rule
     * 3) Excluded productId filters from rule
     * 4) Filters created based on user selected refinements
     * 5) Pre-filter passed in request
     * 6) Site-filter configured fo are associated with current request
     * <p>
     * The filter syntax consists of an expression language for constructing a
     * predicate from one or more fields of the products being filtered. Filter
     * expression is case-sensitive.
     * <p>
     * See more details on this:
     * <a href="https://cloud.google.com/retail/docs/filter-and-order#filter">user guide</a>
     *
     * @param searchParameters object which is representing user request.
     *
     * @return string representing full filter which needs to be applied to the Google retail search request.
     */
    @NonNull
    public String createFilter(@NonNull SearchParameters searchParameters) {
        var filter = new RetailFilter();
        fillFilterWithFilters(filter, FIELD_TO_PATH, searchParameters);

        var rawFilters = mergePreAndSiteFilters(
            searchParameters.getPreFilter(),
            searchParameters.getMerchandisingConfiguration().siteFilter()
        );

        if (filter.hasFilters()) {
            return rawFilters
                .map(s -> AND_JOIN_TEMPLATE.formatted(s, filter.toFilter()))
                .orElseGet(filter::toFilter);
        }
        return rawFilters.orElse(EMPTY_STRING);
    }

    @NonNull
    @Override
    protected RetailFilter createIncludedProductIdFilter(@NonNull ProductIdFilter productIdFilter) {
        var filter = new RetailFilter();
        filter.addToOrs(RetailTextExpression.contains(PRODUCT_ID_FIELD, productIdFilter.includedProductIds()));

        return filter;
    }

    @NonNull
    @Override
    protected RetailNotExpression createExcludedProductIdsFilter(@NonNull ProductIdFilter productIdFilter) {
        return RetailNotExpression
            .not(RetailTextExpression.contains(PRODUCT_ID_FIELD, productIdFilter.excludedProductIds()));
    }

    @NonNull
    @Override
    protected RetailFilter convertFilterItemsToFilter(@NonNull Map<String, FilterItemWrapper> filterItemsByField) {
        var filter = new RetailFilter();
        addExpressionsToFilter(filter, filterItemsByField);

        return filter;
    }

    /**
     * Build a textual expression for the multiple values.
     * <p>
     * NOTE: Value added to the filter object will be JSON escaped.
     *
     * @param field  facet(attribute) the expression is for.
     * @param values the field values can be.
     *
     * @return textual filter expression around passed field and values.
     */
    @NonNull
    @Override
    protected RetailTextExpression buildTextExpression(@NonNull String field, @NonNull List<String> values) {
        return RetailTextExpression.contains(field, values);
    }

    @NonNull
    @Override
    protected RetailNotExpression buildNotExpression(Expression<String> expression) {
        return RetailNotExpression.not(expression);
    }

    /**
     * Build a textual expression for the multiple value.
     * <p>
     * NOTE: Value added to the filter object will be JSON escaped.
     *
     * @param field facet(attribute) the expression is for.
     * @param value the field values can be.
     *
     * @return textual filter expression around passed field and value.
     */
    @NonNull
    @Override
    protected RetailTextExpression buildSinglenessTextExpression(@NonNull String field, @NonNull String value) {
        return RetailTextExpression.contains(field, requireNonNull(escapeJson(value)));
    }

    @NonNull
    @Override
    protected Expression<String> buildRangeExpression(@NonNull String field, @NonNull Range range) {
        return RetailRangeExpression.range(field)
            .lowerInclusive(range.low())
            .upperExclusive(range.high())
            .build();
    }

    @NonNull
    @Override
    protected RetailComparisonExpression buildComparisonExpression(@NonNull String field, @Nullable Double value) {
        return new RetailComparisonExpression(field, requireNonNull(value));
    }

    @NonNull
    @Override
    protected RetailFilter combineFiltersAsOrs(List<ToFilter<String>> expressions) {
        var filter = new RetailFilter();
        filter.addToOrs(expressions);
        return filter;
    }

}
