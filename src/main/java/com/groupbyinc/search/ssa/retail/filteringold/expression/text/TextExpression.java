package com.groupbyinc.search.ssa.retail.filteringold.expression.text;

import com.groupbyinc.search.ssa.retail.filteringold.expression.Expression;
import io.micronaut.core.annotation.NonNull;

import javax.annotation.Nonnull;
import java.util.List;

import static com.groupbyinc.search.ssa.util.StringUtils.COMMA;
import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonEmpty;
import static java.util.stream.Collectors.joining;

/**
 * Expression for text facets(attributes).
 */
public class TextExpression extends Expression {

    /**
     * Template used to surround text value with escaped quotas.
     */
    private static final String ESCAPE_QUOTES_TEMPLATE = "\"%s\"";

    /**
     * Expression template.
     * #1 placeholder - facet(attribute) name.
     * #2 placeholder - comma separated list of values where each value is surrounded with escaped
     *                  quotes, product should contain any of these values in specific attribute.
     * <p>
     * Examples: "color:ANY(\"Red\", \"Green\")"
     */
    private static final String EXPRESSION_TEMPLATE = "%s:ANY(%s)";

    /**
     * Values which product should contain in specific attribute.
     */
    @NonNull
    private final List<String> refinements;

    /**
     * Creates a new text expression.
     *
     * @param field      facet(attribute) the expression is for.
     * @param refinement value which product should contain in specific attribute.
     *
     * @return Text expression.
     */
    @NonNull
    public static TextExpression contains(@NonNull String field, @NonNull String refinement) {
        return new TextExpression(field, List.of(refinement));
    }

    /**
     * Creates a new text expression.
     *
     * @param field       facet(attribute) the expression is for.
     * @param refinements values which product should contain in specific attribute.
     *
     * @return Text expression.
     */
    @NonNull
    public static TextExpression contains(@NonNull String field, @NonNull List<String> refinements) {
        return new TextExpression(field, refinements);
    }

    public TextExpression(@NonNull String field, @NonNull List<String> refinements) {
        super(field);
        this.refinements = requireNonEmpty(refinements, "Text expression refinements", MANDATORY);
    }

    @Nonnull
    @Override
    public String toFilter() {
        return EXPRESSION_TEMPLATE.formatted(
            field,
            refinements
                .stream()
                .map(ESCAPE_QUOTES_TEMPLATE::formatted)
                .collect(joining(COMMA))
        );
    }

}
