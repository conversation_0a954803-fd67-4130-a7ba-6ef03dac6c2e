package com.groupbyinc.search.ssa.retail.cipher;

import com.groupbyinc.utils.crypto.AesCbcCipher;
import com.groupbyinc.utils.crypto.AesCipherStrategy;
import com.groupbyinc.utils.crypto.AesEcbCipher;

import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Value;
import jakarta.inject.Named;
import jakarta.inject.Singleton;

@Factory
public class AesCipherFactory {

    @Value("${security.crypto.secret-aes}")
    private String aesSecret;

    @Singleton
    @Named("cbc")
    public AesCipherStrategy cbcCipher() throws Exception {
        return new AesCbcCipher(aesSecret);
    }

    @Singleton
    @Named("ecb")
    public AesCipherStrategy ecbCipher() throws Exception {
        return new AesEcbCipher(aesSecret);
    }
}
