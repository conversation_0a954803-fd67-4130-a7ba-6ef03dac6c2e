package com.groupbyinc.search.ssa.core.authz;

import com.groupbyinc.search.ssa.core.Merchandiser;

import lombok.Value;

import java.util.Set;

import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireDefined;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;

/**
 * Represents the verified identity of an entity that is invoking an action such as performing a search.
 */
@Value
public class Identity {

    /** Identifier for the authenticated entity, such as user ID or customer ID. */
    String subject;

    /** Company the entity belongs to. This is expected to be either a customer or GroupBy. */
    String company;

    /** Roles this entity possesses. */
    Set<Role> roles;

    /**
     * Creates a new instance.
     *
     * @param subject Identifier for the authenticated entity. Has to be defined and cannot be blank.
     * @param company Company the entity belongs to. Has to be defined and cannot be blank.
     * @param roles   Roles this entity possesses. Has to be defined but could be empty.
     */
    public Identity(String subject, String company, Set<Role> roles) {
        this.subject = requireNonBlank(subject, "Identity subject", MANDATORY);
        this.company = requireNonBlank(company, "Identity company", MANDATORY);
        this.roles = requireDefined(roles, "Identity roles");
    }

    /**
     * @param merchandiser Merchandiser for whom a search is going to be performed.
     *
     * @return Whether or not this identity is authorized to perform searches for the specified merchandiser.
     */
    public boolean canPerformSearchFor(Merchandiser merchandiser) {
        return isSuperUser() || isCustomer(merchandiser);
    }

    /**
     * @return Whether or not this identity is a superuser.
     */
    private boolean isSuperUser() {
        return roles.contains(Role.SUPERUSER);
    }

    /**
     * @param merchandiser Customer used to check this identity against.
     *
     * @return Whether or not this identity is the specified customer itself.
     */
    private boolean isCustomer(Merchandiser merchandiser) {
        return roles.contains(Role.CUSTOMER_DEFAULT) && company.equals(merchandiser.merchandiserId());
    }
}
