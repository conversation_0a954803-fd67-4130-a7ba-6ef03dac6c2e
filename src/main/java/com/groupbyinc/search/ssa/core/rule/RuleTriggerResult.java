package com.groupbyinc.search.ssa.core.rule;

import com.groupbyinc.search.ssa.core.trigger.TriggerSet;

/**
 * Represents a result of rule trigger process.
 *
 * @param triggered  boolean value indicating is rule triggered or not.
 * @param triggerSet will contain a triggered trigger set in case if triggered rule
 *                   has trigger sets.
 */
public record RuleTriggerResult(boolean triggered, TriggerSet triggerSet) {

    /**
     * Constant object for cases when rule triggered but there was no trigger set exists.
     */
    public static final RuleTriggerResult TRIGGERED_NO_TRIGGER_SET = new RuleTriggerResult(true, null);

    /**
     * Constant object for cases when rule not triggered.
     */
    public static final RuleTriggerResult NOT_TRIGGERED = new RuleTriggerResult(false, null);

}
