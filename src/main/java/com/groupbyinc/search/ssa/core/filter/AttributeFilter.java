package com.groupbyinc.search.ssa.core.filter;

import java.util.List;

/**
 * Describes a filter with an attributes groups
 * Each attribute group can contain a list of ValueFilters and list of RangeFilters.
 */
public record AttributeFilter (

    /** List of value filters in the given attribute filter */
    List<ValueFilter> valueFilters,

    /** List of range filters in the given attribute filter */
    List<RangeFilter> rangeFilters) {
}
