package com.groupbyinc.search.ssa.core.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.PROMOTION_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PROMOTION_ID_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PROMOTION_ID_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PROMOTION_TITLE;

@Schema(
    title = PROMOTION_TITLE,
    description = PROMOTION_DESCRIPTION
)
@Getter
public class Promotion {

    @Schema(
        description = PROMOTION_ID_FIELD_DESCRIPTION,
        example = PROMOTION_ID_FIELD_EXAMPLE
    )
    private final String promotionId;

    public Promotion(com.google.cloud.retail.v2.Promotion promotion) {
        this.promotionId = promotion.getPromotionId();
    }

    public Promotion(String promotionId) {
        this.promotionId = promotionId;
    }

}
