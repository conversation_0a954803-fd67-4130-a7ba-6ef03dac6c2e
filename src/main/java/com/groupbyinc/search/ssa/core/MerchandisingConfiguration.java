package com.groupbyinc.search.ssa.core;

import com.groupbyinc.search.ssa.core.area.AreaConfiguration;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfile;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfileConfiguration;
import com.groupbyinc.search.ssa.core.features.Features;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.redirect.RedirectConfiguration;
import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;
import com.groupbyinc.search.ssa.core.zone.ZoneConfiguration;

import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Stream;

@Slf4j
@Builder(toBuilder = true)
public record MerchandisingConfiguration(
    AreaConfiguration areaConfiguration,
    Set<RedirectConfiguration> redirectConfigurations,
    Set<NavigationConfiguration> navigationConfigurations,
    Set<RuleConfiguration> ruleConfigurations,
    List<BiasingProfileConfiguration> biasingProfileConfigurations,
    Map<String, AttributeConfiguration> attributeConfigurations,
    Supplier<Instant> instantSupplier,
    SiteFilterConfiguration siteFilter,
    List<ZoneConfiguration> zones,
    Features features) {

    public MerchandisingConfiguration {
        if (redirectConfigurations == null) {
            redirectConfigurations = Set.of();
        }
        if (navigationConfigurations == null) {
            navigationConfigurations = Set.of();
        }
        if (ruleConfigurations == null) {
            ruleConfigurations = Set.of();
        }
        if (biasingProfileConfigurations == null) {
            biasingProfileConfigurations = List.of();
        }
        if (attributeConfigurations == null) {
            attributeConfigurations = Map.of();
        }
        if (zones == null) {
            zones = List.of();
        }
    }

    public static MerchandisingConfiguration EMPTY = new MerchandisingConfiguration(
        null, Set.of(), Set.of(), Set.of(), List.of(), Map.of(), null, null, List.of(), null
    );

    public Optional<BiasingProfile> getBiasingProfileByName(String biasingProfileName) {
        return biasingProfileConfigurations
            .stream()
            .filter(biasingProfileConfiguration -> biasingProfileConfiguration.name().equals(biasingProfileName))
            .map(MerchandisingConfiguration::convertBiasingProfileConfiguration)
            .findFirst();
    }

    public Optional<BiasingProfile> getAreaDefaultBiasingProfile() {
        return biasingProfileConfigurations
            .stream()
            .filter(BiasingProfileConfiguration::areaDefault)
            .map(MerchandisingConfiguration::convertBiasingProfileConfiguration)
            .findFirst();
    }

    public Stream<AttributeConfiguration> getPartNumberSearchableAttributes() {
        return attributeConfigurations
            .values()
            .stream()
            .filter(AttributeConfiguration::partNumberSearchable);
    }

    private static BiasingProfile convertBiasingProfileConfiguration(BiasingProfileConfiguration configuration) {
        return BiasingProfile.builder()
            .name(configuration.name())
            .biases(configuration.biases())
            .build();
    }

}
