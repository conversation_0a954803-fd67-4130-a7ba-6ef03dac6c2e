package com.groupbyinc.search.ssa.core.trigger;

import com.groupbyinc.search.ssa.core.CustomParameter;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nonnull;
import java.util.Collection;
import java.util.List;

/**
 * Provides a way to group multiple triggers together. All triggers in a set should be matched, unless the 'nested' behavior is desired, in which case
 * the matching becomes more flexible e.g. 2 custom parameter triggers match, but the search parameters actually have 3 custom parameters present.
 */
@Data
@NoArgsConstructor
public class TriggerSet implements Triggered {

    /** Query pattern triggers. */
    private List<QueryPatternTrigger> queryPatternTriggers = List.of();

    /** Selected refinement triggers. */
    private List<SelectedRefinementTrigger> selectedRefinementTriggers = List.of();

    /** Custom parameter triggers. */
    private List<CustomParameterTrigger> customParameterTriggers = List.of();

    /**
     * Creates a new instance of a trigger set.
     *
     * @param queryPatternTriggers       Query pattern triggers. Defaults to an empty list if not provided.
     * @param selectedRefinementTriggers Selected refinement triggers. Defaults to an empty list if not provided.
     * @param customParameterTriggers    Custom parameter triggers. Defaults to an empty list if not provided.
     */
    @Builder
    public TriggerSet(List<QueryPatternTrigger> queryPatternTriggers,
                      List<SelectedRefinementTrigger> selectedRefinementTriggers,
                      List<CustomParameterTrigger> customParameterTriggers) {
        this.queryPatternTriggers = queryPatternTriggers == null
            ? List.of()
            : queryPatternTriggers;
        this.selectedRefinementTriggers = selectedRefinementTriggers == null
            ? List.of()
            : selectedRefinementTriggers;
        this.customParameterTriggers = customParameterTriggers == null
            ? List.of()
            : customParameterTriggers;
    }

    /**
     * Does every trigger in the set match against the given search parameters and/or search results.
     *
     * @param searchParameters Search parameters the triggers can match against.
     *
     * @return {@code true} if all the triggers match, otherwise {@code false}.
     */
    public boolean trigger(@Nonnull SearchParameters searchParameters) {

        return queryMatches(searchParameters.getQuery())
            && selectedRefinementsMatches(searchParameters.getRefinements())
            && customParametersMatches(searchParameters.getCustomParameters());
    }

    private boolean queryMatches(String query) {

        if (query.isBlank() && !queryPatternTriggers.isEmpty()) {
            return false;
        }

        if (!query.isBlank() && queryPatternTriggers.isEmpty()) {
            return true; // nested not supported yet, normally instead of "true" here we may need to return "nested".
        }

        if (queryPatternTriggers.isEmpty()) {
            return true;
        }

        return queryPatternTriggers.stream()
            .allMatch(queryPatternTrigger -> queryPatternTrigger.matches(query));
    }

    private boolean selectedRefinementsMatches(List<SelectedRefinement> refinements) {
        return collectionMatches(refinements, selectedRefinementTriggers);
    }

    private boolean customParametersMatches(List<CustomParameter> customParameters) {
        return collectionMatches(customParameters, customParameterTriggers);
    }

    private static <T, Q extends Trigger<T>> boolean collectionMatches(Collection<T> collection,
                                                                       Collection<Q> triggers) {
        return collectionMatches(collection, triggers, false);
    }

    public static <T, Q extends Trigger<T>> boolean collectionMatches(Collection<T> collection,
                                                                      Collection<Q> triggers,
                                                                      boolean exactMatch) {
        if (collection.isEmpty() && !triggers.isEmpty()) {
            return false;
        }

        if (!collection.isEmpty() && triggers.isEmpty()) {
            return true; // nested not supported yet, normally instead of "true" here we may need to return "nested".
        }

        if (exactMatch && collection.size() != triggers.size()) {
            return false;
        }

        if (collection.isEmpty()) {
            return true;
        }

        var matchedTriggers = triggers.stream()
            .filter(trigger -> collection.stream().anyMatch(trigger::matches))
            .toList();

        return matchedTriggers.size() == triggers.size();
    }
}
