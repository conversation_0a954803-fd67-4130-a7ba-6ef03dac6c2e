package com.groupbyinc.search.ssa.core.filter;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireDefined;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;

/**
 * Describes a filter with a value. e.g. color that is red.
 */
@Data
@NoArgsConstructor
public class ValueFilter {

    /** Field the value applies to. */
    private String field;
    /** Value to filter on. */
    private String value;
    /** Numeric value to filter on. **/
    private Double numberValue;
    /** Describing whether the filter is negated or not: color that is NOT red. */
    private boolean exclude;
    /** Determine which field we need to use - value if 'TEXTUAL' type or numberValue if 'NUMERIC' type. **/
    private ValueFilterType type;

    public enum ValueFilterType {
        TEXTUAL, NUMERIC
    }

    /**
     * Creates a new instance of a value filter.
     *
     * @param field   Field the value applies to. This must be specified, and not blank.
     * @param value   Value to filter on. This must be specified, and not blank.
     * @param exclude Flag describing whether the filter is negated or not. e.g color that is NOT red.
     */
    @Builder
    public ValueFilter(String field, String value, Double numberValue, boolean exclude, ValueFilterType type) {
        this.field = requireNonBlank(field, "Value filter field", MANDATORY);

        this.type = requireDefined(type, "Value filter type");
        if (type == ValueFilterType.TEXTUAL) {
            this.value = requireNonBlank(value, "Value filter value", MANDATORY);
        } else {
            this.numberValue = requireDefined(numberValue, "Value filter number value");
        }

        this.exclude = exclude;
    }
}
