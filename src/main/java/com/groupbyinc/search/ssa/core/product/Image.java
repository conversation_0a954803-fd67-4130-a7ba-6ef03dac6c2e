package com.groupbyinc.search.ssa.core.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.IMAGE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.IMAGE_HEIGHT_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.IMAGE_HEIGHT_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.IMAGE_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.IMAGE_URI_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.IMAGE_URI_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.IMAGE_WIDTH_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.IMAGE_WIDTH_FIELD_EXAMPLE;

@Schema(
    title = IMAGE_TITLE,
    description = IMAGE_DESCRIPTION
)
@Getter
@AllArgsConstructor
public class Image {

    @Schema(
        description = IMAGE_URI_FIELD_DESCRIPTION,
        example = IMAGE_URI_FIELD_EXAMPLE
    )
    private final String uri;

    @Schema(
        description = IMAGE_HEIGHT_FIELD_DESCRIPTION,
        example = IMAGE_HEIGHT_FIELD_EXAMPLE
    )
    private final Integer height;

    @Schema(
        description = IMAGE_WIDTH_FIELD_DESCRIPTION,
        example = IMAGE_WIDTH_FIELD_EXAMPLE
    )
    private final Integer width;

    public Image(com.google.cloud.retail.v2.Image image) {
        this.uri = image.getUri();
        this.height = image.getHeight();
        this.width = image.getWidth();
    }
}
