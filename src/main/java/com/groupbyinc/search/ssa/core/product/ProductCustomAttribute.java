package com.groupbyinc.search.ssa.core.product;

import com.google.cloud.retail.v2.CustomAttribute;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_CUSTOM_ATTRIBUTE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_CUSTOM_ATTRIBUTE_NUMBERS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_CUSTOM_ATTRIBUTE_NUMBERS_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_CUSTOM_ATTRIBUTE_TEXT_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_CUSTOM_ATTRIBUTE_TEXT_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRODUCT_CUSTOM_ATTRIBUTE_TITLE;

@Schema(
    title = PRODUCT_CUSTOM_ATTRIBUTE_TITLE,
    description = PRODUCT_CUSTOM_ATTRIBUTE_DESCRIPTION
)
@Getter
@AllArgsConstructor
public class ProductCustomAttribute {

    @Schema(
        description = PRODUCT_CUSTOM_ATTRIBUTE_TEXT_FIELD_DESCRIPTION,
        example = PRODUCT_CUSTOM_ATTRIBUTE_TEXT_FIELD_EXAMPLE
    )
    private final List<String> text;

    @Schema(
        description = PRODUCT_CUSTOM_ATTRIBUTE_NUMBERS_FIELD_DESCRIPTION,
        example = PRODUCT_CUSTOM_ATTRIBUTE_NUMBERS_FIELD_EXAMPLE
    )
    private final List<Double> numbers;

    public ProductCustomAttribute(CustomAttribute customAttribute) {
        this.text = customAttribute.getTextList();
        this.numbers = customAttribute.getNumbersList();
    }
}
