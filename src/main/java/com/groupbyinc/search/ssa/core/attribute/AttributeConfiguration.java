package com.groupbyinc.search.ssa.core.attribute;

import com.groupbyinc.proto.commandcenter.config.AttributeMessage;
import com.groupbyinc.proto.commandcenter.config.MessageType;
import com.groupbyinc.search.ssa.application.utils.LongToLocalDateTimeConverter;
import com.groupbyinc.search.ssa.core.Metadata;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.micronaut.core.annotation.Nullable;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

@Builder
public record AttributeConfiguration(
    String key,
    String path,
    boolean retrievable,
    boolean indexable,
    boolean searchable,
    boolean partNumberSearchable,
    boolean dynamicFacetable,
    MessageType messageType,
    AttributeMessage.AttributeType type,
    AttributeMessage.AttributeGroup attributeGroup,
    String displayName,
    List<Metadata> metadata,
    Integer collectionId,
    @Nullable
    @JsonDeserialize(converter = LongToLocalDateTimeConverter.class)
    LocalDateTime lastModifiedDate,
    @Nullable
    String lastModifiedField) {

}
