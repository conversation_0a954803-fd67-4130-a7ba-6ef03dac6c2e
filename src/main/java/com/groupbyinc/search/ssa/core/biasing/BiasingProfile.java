package com.groupbyinc.search.ssa.core.biasing;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.util.List;
import java.util.Optional;

import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.FieldRequirement.OPTIONAL;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonEmpty;


@Data
@NoArgsConstructor
public class BiasingProfile {

    private String name;
    private List<Bias> biases;

    @Builder
    public BiasingProfile(String name, List<Bias> biases) {
        this.name = requireNonBlank(name, "Biasing profile name", OPTIONAL);
        this.biases = requireNonEmpty(biases, "Biasing profile biases", MANDATORY);
    }

    public Optional<String> getName() {
        return Optional.ofNullable(name);
    }
}
