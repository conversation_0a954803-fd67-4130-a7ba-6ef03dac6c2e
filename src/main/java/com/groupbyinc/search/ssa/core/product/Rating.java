package com.groupbyinc.search.ssa.core.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.RATING_AVERAGE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RATING_AVERAGE_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RATING_COUNT_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RATING_COUNT_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RATING_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RATING_HISTOGRAM_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RATING_HISTOGRAM_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.RATING_TITLE;

@Schema(
    title = RATING_TITLE,
    description = RATING_DESCRIPTION
)
@Getter
@AllArgsConstructor
public class Rating {

    @Schema(
        description = RATING_COUNT_FIELD_DESCRIPTION,
        example = RATING_COUNT_FIELD_EXAMPLE
    )
    private final Integer ratingCount;

    @Schema(
        description = RATING_AVERAGE_FIELD_DESCRIPTION,
        example = RATING_AVERAGE_FIELD_EXAMPLE
    )
    private final Float averageRating;

    @Schema(
        description = RATING_HISTOGRAM_FIELD_DESCRIPTION,
        example = RATING_HISTOGRAM_FIELD_EXAMPLE
    )
    private final List<Integer> ratingHistogram;

    public Rating(com.google.cloud.retail.v2.Rating rating) {
        this.ratingCount = rating.getRatingCount();
        this.averageRating = rating.getAverageRating();
        this.ratingHistogram = rating.getRatingHistogramList();
    }
}
