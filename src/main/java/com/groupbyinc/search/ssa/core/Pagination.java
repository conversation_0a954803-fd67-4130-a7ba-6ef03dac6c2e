package com.groupbyinc.search.ssa.core;

import lombok.Value;

import javax.annotation.Nullable;

import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireRange;

/**
 * Class that encompasses all info for pagination, including number of products to be returned on each page and at which point in the results to begin
 * building the page.
 */
@Value
public class Pagination {
    public static final int DEFAULT_SIZE = 10;
    public static final long DEFAULT_OFFSET = 0;

    /** Number of products per page. */
    int size;
    /** Product at which to start page. */
    long offset;

    /**
     * Creates a new instance of Pagination.
     *
     * @param size   Number of products oer page. Defaults to 10 if not provided. If provided, the value must be >= 0.
     * @param offset Product at which to start page. Defaults to 0 if not provided. If provided, the value must be >= 0.
     */
    public Pagination(@Nullable Integer size, @Nullable Long offset) {
        this.size = size == null ? DEFAULT_SIZE : requireRange(size, "Pagination size", 0, null, MANDATORY);
        this.offset = offset == null ? DEFAULT_OFFSET : requireRange(offset, "Pagination offset", 0L, null, MANDATORY);
    }

    /**
     * Static factory method for a default instance with a size of 10 and an offset of 0.
     *
     * @return default instance of Pagination.
     */
    public static Pagination defaultPagination() {
        return new Pagination(null, null);
    }
}
