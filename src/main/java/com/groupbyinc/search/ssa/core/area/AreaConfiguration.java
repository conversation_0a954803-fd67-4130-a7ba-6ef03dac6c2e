package com.groupbyinc.search.ssa.core.area;

import com.groupbyinc.proto.commandcenter.config.MessageType;
import com.groupbyinc.search.ssa.core.Metadata;
import com.groupbyinc.search.ssa.topsort.model.TopsortConfiguration;

import lombok.Builder;
import lombok.With;

import java.util.List;

@Builder
public record AreaConfiguration(
    Integer id,
    String name,
    Integer tenantId,
    MessageType messageType,
    List<Metadata> metadata,
    Integer collectionId,
    String servingConfigName,
    Integer siteFilterId,
    @With TopsortConfiguration topsortConfiguration) {

}
