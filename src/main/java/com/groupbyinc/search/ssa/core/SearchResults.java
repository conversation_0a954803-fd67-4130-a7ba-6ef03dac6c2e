package com.groupbyinc.search.ssa.core;

import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngineType;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfile;
import com.groupbyinc.search.ssa.core.conversation.ConversationalSearchResult;
import com.groupbyinc.search.ssa.core.navigation.Navigation;
import com.groupbyinc.search.ssa.core.request.RequestServed;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;
import com.groupbyinc.search.ssa.core.template.Template;
import com.groupbyinc.search.ssa.core.tiles.Tile;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.micronaut.core.util.CollectionUtils;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.annotation.Nullable;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultImmutableList;
import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultList;
import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireRange;

import static java.util.Objects.requireNonNullElse;

@Getter
@EqualsAndHashCode
public class SearchResults {

    private final String query;
    private final String filter;
    private final String redirectUrl;

    private final Integer facetLimit;
    private final Long numTotalRecords;
    private final List<String> rewrites;
    private final String correctedQuery;
    private final SearchMetadata metadata;
    private final List<String> pageCategories;
    private final List<Navigation> navigations;
    private final BiasingProfile biasingProfile;
    private final List<Record> sponsoredRecords;
    private final Boolean includeExpandedResults;
    private final List<Metadata> redirectMetadata;
    private final SiteFilterConfiguration siteFilter;
    private final List<Navigation> selectedNavigations;
    private final ConversationalSearchResult conversationalSearchResult;
    private final List<Tile> tiles;

    @Setter private Template template;
    @Setter private List<Record> records;
    @Setter private String ruleVariantName;
    @Setter private List<Metadata> siteParams;
    @Setter private RequestServed requestServed;
    @Setter private SearchEngineType searchEngine;
    @Setter private boolean fallbackSearchEngineUsed;

    private final List<String> searchStrategies;


    /**
     *
     * @param numTotalRecords        Total number of matching records.
     * @param query                  Query after modification by the configuration but before correction.
     * @param correctedQuery         Corrected version of the query from the Google retail engine.
     * @param biasingProfile         Biasing profile that was used to boost or buries products in the results.
     * @param records                List of records returned in a search.
     * @param sponsoredRecords       List of sponsored records returned in a search.
     * @param navigations            List of available navigations.
     * @param selectedNavigations    List of selected navigations.
     * @param metadata               Metadata (key-value) associated with the results.
     * @param redirectUrl            Redirect url. If any, configured redirect is triggered.
     * @param template               Template from the triggered rule, or potentially the area default.
     * @param pageCategories         Category of requested records.
     * @param filter                 A filter query used for request.
     * @param siteParams             Metadata (key-value) associated with the site filter used with the request.
     * @param rewrites               Array represents the intent to rewrite the original query rewrites.
     * @param includeExpandedResults Whether to pin unexpanded results.
     * @param ruleVariantName        Name of triggered rule variant in case of rule A/B testing.
     * @param facetLimit             Limit of returned facets on Google side,
     * @param siteFilter             A site filter used with the request.
     * @param redirectMetadata       Metadata (key-value) associated with the triggered redirect.
     */
    @JsonCreator
    @Builder(toBuilder = true)
    public SearchResults(@JsonProperty("numTotalRecords") long numTotalRecords,
                         @JsonProperty("query") @Nullable String query,
                         @JsonProperty("correctedQuery") @Nullable String correctedQuery,
                         @JsonProperty("biasingProfile") @Nullable BiasingProfile biasingProfile,
                         @JsonProperty("records") @Nullable List<Record> records,
                         @JsonProperty("sponsoredRecords") @Nullable List<Record> sponsoredRecords,
                         @JsonProperty("navigations") @Nullable List<Navigation> navigations,
                         @JsonProperty("selectedNavigations") @Nullable List<Navigation> selectedNavigations,
                         @JsonProperty("metadata") @Nullable SearchMetadata metadata,
                         @JsonProperty("redirectUrl") @Nullable String redirectUrl,
                         @JsonProperty("template") @Nullable Template template,
                         @JsonProperty("pageCategories") @Nullable List<String> pageCategories,
                         @JsonProperty("filter") @Nullable String filter,
                         @JsonProperty("siteParams") @Nullable List<Metadata> siteParams,
                         @JsonProperty("rewrites") @Nullable List<String> rewrites,
                         @JsonProperty("includeExpandedResults") @Nullable Boolean includeExpandedResults,
                         @JsonProperty("ruleVariantName") @Nullable String ruleVariantName,
                         @JsonProperty("facetLimit") @Nullable Integer facetLimit,
                         @JsonProperty("tiles") @Nullable List<Tile> tiles,
                         @JsonProperty("siteFilter") @Nullable SiteFilterConfiguration siteFilter,
                         @JsonProperty("redirectMetadata") List<Metadata> redirectMetadata,
                         @JsonProperty("searchStrategies") @Nullable List<String> searchStrategies,
                         @JsonProperty("searchEngine") SearchEngineType searchEngine,
                         @JsonProperty("requestServed") RequestServed requestServed,
                         @JsonProperty("conversationalSearchResult") @Nullable ConversationalSearchResult conversationalSearchResult,
                         @JsonProperty("fallbackSearchEngineUsed") boolean fallbackSearchEngineUsed) {
        this.numTotalRecords = requireRange(numTotalRecords, "Search results num total records", 0L, null, MANDATORY);
        this.query = requireNonNullElse(query, "");
        this.correctedQuery = requireNonNullElse(correctedQuery, "");
        this.biasingProfile = biasingProfile;
        this.records = notNullOrDefaultImmutableList(records);
        this.sponsoredRecords = notNullOrDefaultImmutableList(sponsoredRecords);
        this.navigations = notNullOrDefaultImmutableList(navigations);
        this.selectedNavigations = notNullOrDefaultImmutableList(selectedNavigations);
        this.metadata = requireNonNullElse(metadata, SearchMetadata.builder().build());
        this.redirectUrl = redirectUrl;
        this.template = template;
        this.pageCategories = pageCategories;
        this.filter = filter;
        this.siteParams = notNullOrDefaultList(siteParams);
        this.rewrites = notNullOrDefaultList(rewrites);
        this.includeExpandedResults = includeExpandedResults;
        this.ruleVariantName = ruleVariantName;
        this.facetLimit = facetLimit;
        this.tiles = tiles;
        this.siteFilter = siteFilter;
        this.redirectMetadata = notNullOrDefaultImmutableList(redirectMetadata);

        this.searchStrategies = notNullOrDefaultList(searchStrategies);
        this.searchEngine = searchEngine;
        this.requestServed = requestServed;
        this.conversationalSearchResult = conversationalSearchResult;
        this.fallbackSearchEngineUsed = fallbackSearchEngineUsed;
    }

    public Optional<BiasingProfile> getBiasingProfile() {
        return Optional.ofNullable(biasingProfile);
    }

    public List<Record> getRecords() {
        return Collections.unmodifiableList(records);
    }

    public List<Navigation> getNavigations() {
        return Collections.unmodifiableList(navigations);
    }

    public List<Navigation> getSelectedNavigations() {
        return Collections.unmodifiableList(selectedNavigations);
    }

    public List<Tile> getTiles() {
        return tiles == null ? null : Collections.unmodifiableList(tiles);
    }

    public Optional<String> getRedirectUrl() {
        return Optional.ofNullable(redirectUrl);
    }

    public Optional<Template> getTemplate() {
        return Optional.ofNullable(template);
    }

    /**
     * @return {@code true} if the search yielded no records, otherwise {@code false}.
     */
    public boolean isNoResults() {
        return numTotalRecords == null || numTotalRecords == 0;
    }

    public boolean hasNoRecords() {
        return CollectionUtils.isEmpty(records);
    }

}
