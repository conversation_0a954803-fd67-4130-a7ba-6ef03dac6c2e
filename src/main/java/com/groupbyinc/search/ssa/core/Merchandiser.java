package com.groupbyinc.search.ssa.core;

/**
 * Represents a merchandiser (GroupBy inc.'s customer) that is identified by a unique value.
 */
public record Merchandiser (

    /* A unique identifier for the merchandiser, e.g. 'signet'. This has to be specified, and not be blank. */
    String merchandiserId) {

    /**
     * Creates a new instance.
     *
     * @param merchandiserId The unique identifier for the merchandiser, e.g. 'signet'. This has to be specified and be all alpha.
     *
     * @return A new merchandiser if the specified identifier is valid.
     */
    public static Merchandiser of(String merchandiserId) {
        return new Merchandiser(merchandiserId);
    }
}
