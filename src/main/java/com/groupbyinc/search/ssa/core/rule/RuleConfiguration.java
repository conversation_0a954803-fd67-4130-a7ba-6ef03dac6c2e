package com.groupbyinc.search.ssa.core.rule;

import com.groupbyinc.proto.commandcenter.config.MessageType;
import com.groupbyinc.search.ssa.api.error.ProcessingException;
import com.groupbyinc.search.ssa.core.ActiveTimeEnabled;
import com.groupbyinc.search.ssa.core.Prioritized;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.filter.AttributeFilter;
import com.groupbyinc.search.ssa.core.filter.ProductIdFilter;
import com.groupbyinc.search.ssa.core.filter.SearchFilter;
import com.groupbyinc.search.ssa.core.trigger.TriggerSet;
import com.groupbyinc.search.ssa.util.abtest.VariantSelector;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.groupbyinc.search.ssa.core.rule.RuleTriggerResult.NOT_TRIGGERED;
import static com.groupbyinc.search.ssa.core.rule.RuleTriggerResult.TRIGGERED_NO_TRIGGER_SET;
import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultList;
import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultSet;

/**
 * Provides a mechanism for matching against multiple trigger conditions. Should
 * a rule be triggered, the rule's configuration should be applied to the
 * search parameters.
 */
@Data
@Slf4j
public class RuleConfiguration implements Prioritized<RuleConfiguration>, ActiveTimeEnabled {

    private final Integer id;

    /* A name for the rule, ideally human-readable. */
    private final String name;

    /* ID of the area which this rule is associated with. */
    private final Integer areaId;

    /*
     *  Priority of the navigation. A lower number indicates a higher priority.
     *  min: 1, max: Integer.MAX_VALUE
     */
    private final  int priority;

    /* Define is time range enabled for this rule or not. */
    private final boolean activeHoursEnabled;

    /*
     * Used if RuleConfiguration#activeHoursEnabled is true.
     * Start-date for "rule active" time range.
     */
    private final Long activeFrom;

    /*
     * Used if RuleConfiguration#activeHoursEnabled is true.
     * End-date for "rule active" time range.
     */
    private final Long activeTo;

    /*
     * Triggers sets which ultimately determine if the rule is triggered.
     */
    private final Set<TriggerSet> triggerSets;

    /* Name of a biasing profile which should take precedence. */
    private final String biasingProfileName;

    /* List of navigations that will be included on search results. The order will also be used for sorting navigations */
    private final List<String> includedNavigations;

    /* List of search terms to append on the search query. */
    private final List<SearchFilter> searchFilters;

    private final List<AttributeFilter> attributeFilters;

    private final ProductIdFilter productIdFilter;

    /*
     * Rule template object with contains name of template which is
     * linked to this rule. Additionally, it contains a list of zones.
     */
    private final RuleTemplate template;

    private final List<ProductIdsBucket> boostedProductBuckets;

    private final List<ProductIdsBucket> buriedProductBuckets;

    private final List<PinnedRefinement> pinnedRefinements;

    private final List<PinnedProduct> pinnedProducts;

    private final MessageType messageType;

    private final RuleType type;

    private final List<ExperimentVariant> variants;

    private final ProductVisibilityBias productVisibilityBias;

    @JsonIgnore
    private VariantSelector variantSelector;

    @Builder
    @JsonCreator
    public RuleConfiguration(@JsonProperty("id") Integer id,
                             @JsonProperty("name") String name,
                             @JsonProperty("areaId") Integer areaId,
                             @JsonProperty("priority") int priority,
                             @JsonProperty("activeHoursEnabled") boolean activeHoursEnabled,
                             @JsonProperty("activeFrom") Long activeFrom,
                             @JsonProperty("activeTo") Long activeTo,
                             @JsonProperty("triggerSets") Set<TriggerSet> triggerSets,
                             @JsonProperty("biasingProfileName") String biasingProfileName,
                             @JsonProperty("includedNavigations") List<String> includedNavigations,
                             @JsonProperty("searchFilters") List<SearchFilter> searchFilters,
                             @JsonProperty("attributeFilters") List<AttributeFilter> attributeFilters,
                             @JsonProperty("productIdFilter") ProductIdFilter productIdFilter,
                             @JsonProperty("template") RuleTemplate template,
                             @JsonProperty("boostedProductBuckets") List<ProductIdsBucket> boostedProductBuckets,
                             @JsonProperty("buriedProductBuckets") List<ProductIdsBucket> buriedProductBuckets,
                             @JsonProperty("pinnedRefinements") List<PinnedRefinement> pinnedRefinements,
                             @JsonProperty("pinnedProducts") List<PinnedProduct> pinnedProducts,
                             @JsonProperty("messageType") MessageType messageType,
                             @JsonProperty("type") RuleType type,
                             @JsonProperty("variants") List<ExperimentVariant> variants,
                             @JsonProperty("productVisibilityBias") ProductVisibilityBias productVisibilityBias) {
        this.id = id;
        this.name = name;
        this.areaId = areaId;
        this.priority = priority;
        this.activeHoursEnabled = activeHoursEnabled;
        this.activeFrom = activeFrom;
        this.activeTo = activeTo;
        this.biasingProfileName = biasingProfileName;
        this.template = template;
        this.type = type;
        this.messageType = messageType;
        this.productVisibilityBias = productVisibilityBias;

        this.triggerSets = notNullOrDefaultSet(triggerSets);
        this.includedNavigations = notNullOrDefaultList(includedNavigations);
        this.searchFilters = notNullOrDefaultList(searchFilters);
        this.attributeFilters = notNullOrDefaultList(attributeFilters);
        this.productIdFilter = productIdFilter;
        this.boostedProductBuckets = notNullOrDefaultList(boostedProductBuckets);
        this.buriedProductBuckets = notNullOrDefaultList(buriedProductBuckets);
        this.pinnedRefinements = notNullOrDefaultList(pinnedRefinements);
        this.pinnedProducts = notNullOrDefaultList(pinnedProducts);

        // We have an edge case when the 'merchandiser' creates a variants for test purposes, and this creates
        // variant has 0% of the trigger percentage.
        // We do not need to route traffic to this variant, so we filter them out.
        this.variants = notNullOrDefaultList(variants)
            .stream()
            .filter(variant -> (
                variant.getVariantTriggerPercentage() != null && variant.getVariantTriggerPercentage() > 0)
            )
            .collect(Collectors.toList());

        if (!this.variants.isEmpty() && this.type == RuleType.EXPERIMENT) {
            // For backward compatibility
            fixVariantsTriggerPercentage(variants);

            try {
                var filteredVariants = this.variants
                    .stream()
                    .mapToInt(ExperimentVariant::getVariantTriggerPercentage)
                    .toArray();

                if (filteredVariants.length > 0) {
                    this.variantSelector = new VariantSelector(filteredVariants);
                }
            } catch (Exception e) {
                log.error("Fail to create variant selector for rule id: " + id + " Error: " + e.getMessage());
            }
        }
    }

    /**
     * For backward compatibility, if variantTriggerPercentage is not specified in the variant then assume even traffic
     * split as described:
     * Given 5 variants, A, B, C, D, E where A = 20, B = 20, C = 20, D = 20 and E = 20
     * If E is deleted, the traffic split automatically becomes A = 25, B = 25, C = 25, D = 25
     * If the division results in a remainder, the remainder is added equally to the all variants starting from the
     * first one.
     *
     * @param variants to fix percentages.
     */
    private void fixVariantsTriggerPercentage(List<ExperimentVariant> variants) {
        if (variants.getFirst().getVariantTriggerPercentage() == null) {
            var remainder = 100 % variants.size();
            var percent = 100 / variants.size();

            variants.forEach(variant -> variant.setVariantTriggerPercentage(percent));

            if (remainder == 0) {
                return;
            }

            for (int i = 0; remainder > 0; i++, remainder--) {
                variants.get(i).setVariantTriggerPercentage(variants.get(i).getVariantTriggerPercentage() + 1);
            }
        }
    }

    /**
     * Do any trigger sets fire against the given search parameters and/or search results. If the trigger sets are empty,
     * then the rule will always trigger.
     *
     * @param searchParameters Search parameters the triggers can match against.
     *
     * @return {@code Optional<TriggerSet>} if at least one trigger set is fired, otherwise {@code Optional.empty()}.
     */
    public RuleTriggerResult triggersOn(SearchParameters searchParameters) {
        if (triggerSets.isEmpty()) {
            return TRIGGERED_NO_TRIGGER_SET;
        }

        return triggerSets
            .stream()
            .filter(triggerSet -> triggerSet.trigger(searchParameters))
            .findFirst()
            .map(triggerSet -> new RuleTriggerResult(true, triggerSet))
            .orElse(NOT_TRIGGERED);
    }

    public ExperimentVariant getTriggeredVariant(String string) {
        if (variantSelector == null) {
            throw new ProcessingException("Not possibly to define variant.");
        }
        return variants.get(variantSelector.getRangeId(string));
    }

    public boolean isRuleInExperiment() {
        return type == RuleType.EXPERIMENT;
    }

    /**
     * Copy rule and set max priority.
     *
     * @return copied rule with new max priority.
     */
    public RuleConfiguration copyRuleForOverwrite() {
        return new RuleConfiguration(
            this.id,
            this.name,
            this.areaId,
            Integer.MAX_VALUE,
            this.activeHoursEnabled,
            this.activeFrom,
            this.activeTo,
            this.triggerSets,
            this.biasingProfileName,
            this.includedNavigations,
            this.searchFilters,
            this.attributeFilters,
            this.productIdFilter,
            this.template,
            this.boostedProductBuckets,
            this.buriedProductBuckets,
            this.pinnedRefinements,
            this.pinnedProducts,
            this.messageType,
            this.type,
            this.variants,
            this.productVisibilityBias
        );
    }

    @Override
    public int compareTo(RuleConfiguration o) {
        var result = Integer.compare(priority, o.priority);
        if(result == 0) {
            result = Integer.compare(id, o.id);
        }
        return result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RuleConfiguration that = (RuleConfiguration) o;
        return id.equals(that.id) && areaId.equals(that.areaId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, areaId);
    }

    @Override
    public boolean activeHoursEnabled() {
        return activeHoursEnabled;
    }

    @Override
    public Long activeFrom() {
        return activeFrom;
    }

    @Override
    public Long activeTo() {
        return activeTo;
    }

    @Override
    public Integer id() {
        return id;
    }

    @Override
    public Integer areaId() {
        return areaId;
    }

    @Override
    public MessageType messageType() {
        return messageType;
    }

}
