package com.groupbyinc.search.ssa.core.trigger;

import com.groupbyinc.search.ssa.core.CustomParameter;

/**
 * Trigger that matches a specific custom parameter being present on the search parameters.
 */
public record CustomParameterTrigger(
    String key,
    String value) implements Trigger<CustomParameter> {

    @Override
    public boolean matches(CustomParameter target) {
        return target.key().equals(key) && target.value().equals(value);
    }

}
