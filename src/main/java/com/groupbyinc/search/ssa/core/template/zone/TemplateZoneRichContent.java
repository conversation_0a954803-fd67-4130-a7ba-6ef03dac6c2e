package com.groupbyinc.search.ssa.core.template.zone;

import com.groupbyinc.search.ssa.core.zone.ZoneType;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Value;

@Value
@EqualsAndHashCode(callSuper = true)
public class TemplateZoneRichContent extends TemplateZoneBase{

    String richContent;

    @Builder
    public TemplateZoneRichContent(String name, ZoneType type, String richContent) {
        super(name, type);
        this.richContent = richContent;
    }

}
