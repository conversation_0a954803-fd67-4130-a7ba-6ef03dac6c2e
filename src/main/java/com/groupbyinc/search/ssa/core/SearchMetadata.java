package com.groupbyinc.search.ssa.core;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.time.Duration;

import static com.groupbyinc.utils.validation.FieldRequirement.OPTIONAL;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;

@Getter
@EqualsAndHashCode
public class SearchMetadata {

    private final Duration retailTime;
    private final String attributionToken;

    @Setter
    private Duration totalTime;

    @Setter
    private boolean cached;

    /**
     * Creates a new instance of search metadata.
     *
     * @param attributionToken Token to support beacon collectors in correlating searches to user events.
     * @param cached           The search results cached from a previous call or not.
     * @param totalTime        Total time spent performing the search.
     * @param retailTime       Time spent performing only the Google search (Retail API).
     */
    @Builder
    @JsonCreator
    public SearchMetadata(@JsonProperty("attributionToken") String attributionToken,
                          @JsonProperty("cached") boolean cached,
                          @JsonProperty("totalTime") Duration totalTime,
                          @JsonProperty("retailTime") Duration retailTime) {
        this.attributionToken = requireNonBlank(attributionToken, "Search metadata attribution token", OPTIONAL);
        this.cached = cached;
        this.totalTime = totalTime;
        this.retailTime = retailTime;
    }

}
