package com.groupbyinc.search.ssa.core;

import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;

/**
 * A custom parameter can be provided at query time to add another target for a
 * trigger to match on.
 */
public record CustomParameter(String key, String value) {

    /**
     * Creates a new instance of a custom parameter.
     *
     * @param key   Key of the parameter. This must be: specified, not blank.
     * @param value Value of the parameter. This must be: specified, not blank.
     */
    public CustomParameter(String key, String value) {
        this.key = requireNonBlank(key, "Custom url parameter key", MANDATORY);
        this.value = requireNonBlank(value, "Custom url parameter value", MANDATORY);
    }

}
