package com.groupbyinc.search.ssa.core;

import com.groupbyinc.utils.validation.FieldRequirement;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.With;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultSet;
import static com.groupbyinc.search.ssa.util.StringUtils.MD5;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;

import static java.nio.charset.StandardCharsets.UTF_8;

@Getter
@EqualsAndHashCode
public class Record {

    private final String id;
    private final String productId;
    private final String primaryProductId;
    private final String url;
    private final String title;
    private final String collection;
    @Setter
    private Map<String, Object> metadata;
    @With
    @Deprecated(forRemoval = true)
    private final RecordLabel label;
    private final Boolean sponsored;
    private final RecordSponsoredInfo sponsoredInfo;
    @With
    private final LinkedHashSet<RecordLabel> labels;

    @JsonCreator
    public Record(@JsonProperty("id") @Nonnull String id,
                  @JsonProperty("productId") @Nonnull String productId,
                  @JsonProperty("primaryProductId") @Nullable String primaryProductId,
                  @JsonProperty("url") @Nonnull String url,
                  @JsonProperty("title") @Nullable String title,
                  @JsonProperty("collection") @Nonnull String collection,
                  @JsonProperty("metadata") @Nullable Map<String, Object> metadata,
                  @JsonProperty("label") @Nullable RecordLabel label,
                  @JsonProperty("sponsored") @Nullable Boolean sponsored,
                  @JsonProperty("sponsoredInfo") @Nullable RecordSponsoredInfo sponsoredInfo,
                  @JsonProperty("labels") @Nullable Set<RecordLabel> labels) {
        this.id = requireNonBlank(id, "Record id", FieldRequirement.MANDATORY);
        this.productId = requireNonBlank(productId, "Product id", FieldRequirement.MANDATORY);
        this.primaryProductId = StringUtils.isNotBlank(primaryProductId) ? primaryProductId : productId;
        this.url = requireNonBlank(url, "Record url", FieldRequirement.MANDATORY);
        this.title = StringUtils.stripToEmpty(title);
        this.collection = requireNonBlank(collection, "Record collection", FieldRequirement.MANDATORY);
        this.metadata = metadata == null ? Map.of() : metadata;
        this.label = label;
        this.sponsored = sponsored;
        this.sponsoredInfo = sponsoredInfo;
        this.labels = labels != null ? new LinkedHashSet<>(labels) : null;
    }

    public static Record of(Merchandiser merchandiser,
                            String collection,
                            String productId,
                            String primaryProductId,
                            String title,
                            Map<String, Object> metadata,
                            RecordLabel label) {
        Set<RecordLabel> labels = label != null ? Set.of(label) : null;
        return of(merchandiser, collection, productId, primaryProductId, title, metadata, label, labels, null, null);
    }

    public static Record of(Merchandiser merchandiser,
                            String collection,
                            String productId,
                            String primaryProductId,
                            String title,
                            Map<String, Object> metadata) {
        return of(merchandiser, collection, productId, primaryProductId, title, metadata, null, null, null, null);
    }

    public static Record of(Merchandiser merchandiser,
                            String collection,
                            String productId,
                            String primaryProductId,
                            String title,
                            Map<String, Object> metadata,
                            RecordLabel label,
                            Set<RecordLabel> labels,
                            Boolean sponsored,
                            RecordSponsoredInfo sponsoredInfo) {

        var url = "http://%s1%s.com/%s".formatted(
            merchandiser.merchandiserId(),
            collection,
            productId
        );

        return new Record(
            // use hashed product url as id
            MD5.hashString(url, UTF_8).toString(),
            productId,
            primaryProductId,
            url,
            title,
            collection,
            metadata,
            label,
            sponsored,
            sponsoredInfo,
            labels
        );
    }

    public static Record forSponsored(Merchandiser merchandiser,
                                      String collection,
                                      String productId,
                                      RecordSponsoredInfo info) {
        return of(
            merchandiser,
            collection,
            productId,
            productId,
            null,
            null,
            RecordLabel.SPONSORED,
            Set.of(RecordLabel.SPONSORED),
            true,
            info);
    }

    public static Record copyWithSponsoredInfo(Record record, RecordSponsoredInfo sponsoredInfo) {
        return new Record(
            record.id,
            record.productId,
            record.primaryProductId,
            record.url,
            record.title,
            record.collection,
            record.metadata,
            RecordLabel.SPONSORED,
            sponsoredInfo != null ? true : null,
            sponsoredInfo,
            addLabel(record, RecordLabel.SPONSORED)
        );
    }

    /**
     * Adds a {@link RecordLabel} to the set of labels associated with a given {@link java.lang.Record}.
     * If the record's label set is null, a default empty set is created.
     *
     * @param record the record to which the label will be added
     * @param recordLabel the label to add to the record's set of labels
     * @return the updated set of {@link RecordLabel} objects associated with the given record
     */
    public static LinkedHashSet<RecordLabel> addLabel(Record record, RecordLabel recordLabel) {
        var labels = Objects.requireNonNullElse(record.getLabels(), new LinkedHashSet<RecordLabel>());
        labels.add(recordLabel);
        return labels;
    }

}
