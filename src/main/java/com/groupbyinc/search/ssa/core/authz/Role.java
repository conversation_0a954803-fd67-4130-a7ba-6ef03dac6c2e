package com.groupbyinc.search.ssa.core.authz;

import lombok.Value;

import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;

/**
 * Represents a role possessed by an {@link Identity}. A role is defined by its name, which is always converted to upper case and is therefore
 * case-insensitive.
 */
@Value
public class Role {

    /** Superuser role. */
    public static final Role SUPERUSER = Role.of("SUPERUSER");

    /** Default role for a customer. This role is expected to be possessed only by identities verified using the legacy client-key mechanism. */
    public static final Role CUSTOMER_DEFAULT = Role.of("DEFAULT");

    /** Name of this role, which is an arbitrary string like 'DEFAULT'. */
    String name;

    private Role(String name) {
        this.name = requireNonBlank(name, "Role name", MANDATORY).toUpperCase();
    }

    public static Role of(String name) {
        return new Role(name);
    }
}
