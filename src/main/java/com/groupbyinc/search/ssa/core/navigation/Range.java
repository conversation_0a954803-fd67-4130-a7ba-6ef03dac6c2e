package com.groupbyinc.search.ssa.core.navigation;

import java.util.Objects;

import static com.groupbyinc.utils.validation.ValidationUtils.requireCondition;

/**
 * Represents a range consisting of a low and high value.
 */
public record Range(Double low, Double high, String description, boolean exclude) {

    public Range {
        requireCondition(
            !(low == null && high == null),
            "Range, 'low' and 'high' can not be null in same time."
        );
    }

    public Range(Double low, Double high, String description) {
        this(low, high, description, false);
    }

    public Range(Double low, Double high) {
        this(low, high, null, false);
    }

    public boolean isIdenticalRange(Range range) {
        if (range == null) {
            return false;
        }

        if (low == null) {
            return range.low == null && Objects.equals(high, range.high);
        } else if (high == null) {
            return range.high == null && Objects.equals(low, range.low);
        }

        return low.equals(range.low)
            && high.equals(range.high)
            && Objects.equals(description, range.description)
            && exclude == range.exclude;
    }
}
