package com.groupbyinc.search.ssa.core.filter;

import com.groupbyinc.search.ssa.core.navigation.Range;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireDefined;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;

/**
 * Describes a filter with a range. e.g. rating between 0 and 5.
 */
@Data
@NoArgsConstructor
public class RangeFilter {

    /** Field the range applies to. */
    private String field;
    /** Range of values the field value can be. */
    private Range range;

    /**
     * Creates a new instance of a range filter.
     *
     * @param field Field the range applies to. This must be: specified, no blank.
     * @param range Range of values the field value can be. This must be specified.
     */
    @Builder
    public RangeFilter(String field, Range range) {
        this.field = requireNonBlank(field, "Range filter field", MANDATORY);
        this.range = requireDefined(range, "Range filter range");
    }
}
