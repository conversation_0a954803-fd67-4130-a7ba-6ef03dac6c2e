package com.groupbyinc.search.ssa.core.filter;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;

/**
 * Describes a filter that is a string to be appended to the search query.
 */
@Data
@NoArgsConstructor
public class SearchFilter {

    /** String to be appended to the search query. */
    private String value;

    /**
     * Creates a new instance of a search filter.
     *
     * @param value String to be appended to the search query. This must be specified and not blank.
     */
    @Builder
    public SearchFilter(String value) {
        this.value = requireNonBlank(value, "Search filter value", MANDATORY);
    }
}
