package com.groupbyinc.search.ssa.core;

import java.time.Instant;

public interface ActiveTimeEnabled {

    boolean activeHoursEnabled();

    Long activeFrom();

    Long activeTo();

    /**
     * Determines id a config is active in the specified 'evaluationTime'.
     */
    default boolean isActive(Instant evaluationTime) {
        if (!activeHoursEnabled()) {
            return true;
        }
        if (activeFrom() != null && activeTo() != null) {
            return evaluationTime.isAfter(Instant.ofEpochMilli(activeFrom())) && evaluationTime.isBefore(Instant.ofEpochMilli(activeTo()));
        }
        if (activeFrom() != null) {
            return evaluationTime.isAfter(Instant.ofEpochMilli(activeFrom()));
        }
        if (activeTo() != null) {
            return evaluationTime.isBefore(Instant.ofEpochMilli(activeTo()));
        }
        return false;
    }
}
