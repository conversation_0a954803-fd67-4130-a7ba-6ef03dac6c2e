package com.groupbyinc.search.ssa.core.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

import static com.groupbyinc.search.ssa.core.product.util.ProductUtils.convertAttributes;

@Builder
@Data
@AllArgsConstructor
public class Product {
    public static final String PRODUCT_FIELD_TYPE = "type";
    public static final String PRODUCT_FIELD_ID = "id";
    public static final String PRODUCT_FIELD_PRIMARY_ID = "primaryProductId";
    public static final String PRODUCT_FIELD_TITLE = "title";
    public static final String PRODUCT_FIELD_URI = "uri";
    public static final String PRODUCT_FIELD_VARIANTS = "variants";
    public static final String PRODUCT_FIELD_ATTRIBUTES = "attributes";

    private String name;
    private String id;
    private String type;
    private String primaryProductId;
    private List<String> collectionMemberIds;
    private String gtin;
    private List<String> categories;
    private String title;
    private List<String> brands;
    private String description;
    private String languageCode;
    private Map<String, ProductCustomAttribute> attributes;
    private List<String> tags;
    private PriceInfo priceInfo;
    private Rating rating;
    private Timestamp availableTime;
    private String availability;
    private Integer availableQuantity;
    @JsonProperty("fulfillmentInfo")
    private List<FulfillmentInfo> fulfillmentInfos;
    private String uri;
    private List<Image> images;
    private Audience audience;
    private ColorInfo colorInfo;
    private List<String> sizes;
    private List<String> materials;
    private List<String> patterns;
    private List<String> conditions;
    private List<Promotion> promotions;
    private Timestamp publishTime;
    private FieldMask retrievableFields;
    private List<Product> variants;
    private List<ProductLocalInventory> localInventories;

    public Product(com.google.cloud.retail.v2.Product product) {
        this.name = product.getName();
        this.id = product.getId();
        this.type = product.getType().name();
        this.primaryProductId = product.getPrimaryProductId();
        this.collectionMemberIds = product.getCollectionMemberIdsList();
        this.gtin = product.getGtin();
        this.categories = product.getCategoriesList();
        this.title = product.getTitle();
        this.brands = product.getBrandsList();
        this.description = product.getDescription();
        this.languageCode = product.getLanguageCode();
        this.attributes = convertAttributes(product.getAttributesMap());
        this.tags = product.getTagsList();
        this.priceInfo = new PriceInfo(product.getPriceInfo());
        this.rating = new Rating(product.getRating());
        this.availableTime = new Timestamp(product.getAvailableTime());
        this.availability = product.getAvailability().name();
        this.availableQuantity = product.getAvailableQuantity().getValue();
        this.fulfillmentInfos = product.getFulfillmentInfoList().stream().map(FulfillmentInfo::new).toList();
        this.uri = product.getUri();
        this.images = product.getImagesList().stream().map(Image::new).toList();
        this.audience = new Audience(product.getAudience());
        this.colorInfo = new ColorInfo(product.getColorInfo());
        this.sizes = product.getSizesList();
        this.materials = product.getMaterialsList();
        this.patterns = product.getPatternsList();
        this.conditions = product.getConditionsList();
        this.publishTime = new Timestamp(product.getPublishTime());
        this.retrievableFields = new FieldMask(product.getRetrievableFields());
        this.promotions = product.getPromotionsList().stream().map(Promotion::new).toList();
    }
}
