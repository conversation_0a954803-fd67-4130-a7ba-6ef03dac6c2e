package com.groupbyinc.search.ssa.core.tiles;

public record Tile(
    ProductAttributeValue productAttributeValue,
    String representativeProductId
) {

    public static Tile toDomain(com.google.cloud.retail.v2.Tile tile) {
        return new Tile(new ProductAttributeValue(tile.getProductAttributeValue().getName(),
            tile.getProductAttributeValue().getValue()),
            tile.getRepresentativeProductId());
    }

    public static Tile v2AlphaToDomain(com.google.cloud.retail.v2alpha.Tile tile) {
        return new Tile(new ProductAttributeValue(tile.getProductAttributeValue().getName(),
            tile.getProductAttributeValue().getValue()),
            tile.getRepresentativeProductId());
    }
}
