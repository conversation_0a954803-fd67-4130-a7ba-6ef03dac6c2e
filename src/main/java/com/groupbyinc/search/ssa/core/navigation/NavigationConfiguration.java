package com.groupbyinc.search.ssa.core.navigation;

import com.groupbyinc.proto.commandcenter.config.MessageType;
import com.groupbyinc.search.ssa.core.Metadata;
import com.groupbyinc.search.ssa.core.Prioritized;
import com.groupbyinc.search.ssa.core.rule.Refinement;

import io.micronaut.core.annotation.Nullable;
import lombok.Builder;

import java.util.List;
import java.util.Objects;

@Builder
public record NavigationConfiguration (

    Integer id,

    /* Display name of the field the navigation should be applied to. */
    String name,

    /* Name of the field the navigation should be applied to. */
    String field,

    /* ID of the area which this navigation is associated with. */
    Integer areaId,

    /*
     *  Priority of the navigation. A lower number indicates a higher priority.
     *  min: 1, max: Integer.MAX_VALUE
     */
    int priority,

    /* Type of navigation */
    NavigationType type,

    /*
     * Buckets for range type navigations. Used only if NavigationType#RANGE
     * used as NavigationConfiguration#type of this navigation.
     */
    @Nullable List<Range> ranges,

    /*
     * Sort for the refinements in this navigation. Used only if NavigationType#VALUE
     * used as NavigationConfiguration#type of this navigation.
     */
    @Nullable NavigationSort sort,

    /*
     * Flag for enabling the navigation to have multiple refinements selected for it.
     */
    boolean multiSelect,

    List<Metadata> metadata,

    /* Type of configuration update message. Used for PubSub updates.*/
    MessageType messageType,

    /* Global pinned refinements for the current navigation. */
    List<Refinement> pinnedRefinements) implements Prioritized<NavigationConfiguration> {

    @Override
    public int compareTo(NavigationConfiguration o) {
        var result = Integer.compare(priority, o.priority);
        if(result == 0) {
            result = Integer.compare(id, o.id);
        }
        return result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        NavigationConfiguration that = (NavigationConfiguration) o;
        return Objects.equals(id, that.id) && Objects.equals(areaId, that.areaId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, areaId);
    }

}
