package com.groupbyinc.search.ssa.core.navigation;

import io.micronaut.core.annotation.Nullable;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireDefined;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;

/**
 * Represents a refinement value a shopper has selected to filter the results on.
 */
@Data
@NoArgsConstructor
public class SelectedRefinement {

    /** Field the refinement applies to. */
    private String field;

    /** Type of navigation this refinement belongs to. */
    private NavigationType type;

    /** Range of a refinement value, if applicable. */
    private Range range;

    /** Value of refinement, if applicable. */
    private String value;

    /** Field which is indicated that it is dynamic navigation */
    private boolean dynamic;

    private boolean or;

    /**
     * Creates a new selected refinement.
     *
     * @param field Field the navigation applies to. This must be specified, and not be blank.
     * @param type  Type of navigation this refinement belongs to. This must be specified.
     * @param range Range of a refinement value, if the refinement is not a value type. This must be specified.
     * @param value Value of refinement, if the refinement is not a range type. This must be specified, and not blank.
     */
    @Builder
    public SelectedRefinement(String field,
                              @Nullable NavigationType type,
                              @Nullable Range range,
                              @Nullable String value,
                              boolean dynamic,
                              Boolean or) {
        this.field = requireNonBlank(field, "Selected refinement", MANDATORY);
        this.type = type;
        if (type != null) {
            switch (type) {
                case RANGE -> {
                    this.range = requireDefined(range, "Refinement range");
                    this.value = null;
                }
                case VALUE -> {
                    this.range = null;
                    this.value = requireNonBlank(value, "Refinement value", MANDATORY);
                }
            }
        }
        this.dynamic = dynamic;
        this.or = requireDefined(or, "or");
    }

}
