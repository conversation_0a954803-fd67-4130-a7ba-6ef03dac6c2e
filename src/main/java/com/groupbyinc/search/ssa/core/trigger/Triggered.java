package com.groupbyinc.search.ssa.core.trigger;

import com.groupbyinc.search.ssa.core.SearchParameters;

import javax.annotation.ParametersAreNonnullByDefault;

/**
 * Interface for defining some as being able to be triggered based on the state
 * of the search parameters or results.
 */
@ParametersAreNonnullByDefault
public interface Triggered {

    String ERROR_MESSAGE = "Operation was not supported for current object.";

    default boolean trigger(SearchParameters searchParameters) {
        throw new UnsupportedOperationException(ERROR_MESSAGE);
    }

}
