package com.groupbyinc.search.ssa.core.template.zone;

import com.groupbyinc.search.ssa.core.zone.ZoneType;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;

import java.util.Base64;

@Slf4j
@Value
@EqualsAndHashCode(callSuper = true)
public class TemplateZoneHTML extends TemplateZoneBase {

    String htmlContentDecoded;

    @Builder
    public TemplateZoneHTML(String name, ZoneType type, String htmlContentEncoded) {
        super(name, type);
        this.htmlContentDecoded = decodeHtmlContent(htmlContentEncoded);
    }

    private String decodeHtmlContent(String base64) {
        try {
            return new String(Base64.getDecoder().decode(base64));
        } catch (Exception e) {
            log.warn("Invalid Base64 string for zone: [{}], zone type: [{}]", name, type);
            // in case it's raw html for some reason
            return base64;
        }
    }
}
