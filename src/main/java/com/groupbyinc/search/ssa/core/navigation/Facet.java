package com.groupbyinc.search.ssa.core.navigation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_CONTAINS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_CONTAINS_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_DISPLAY_NAME_FILED_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_DISPLAY_NAME_FILED_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_NAVIGATION_NAME_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_NAVIGATION_NAME_FILED_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_PREFIX_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_PREFIX_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FACET_TYPE_FILED_DESCRIPTION;

@Schema(
    title = FACET_TITLE,
    description = FACET_DESCRIPTION
)
@Builder
public record Facet(

    @Schema(
        description = FACET_PREFIX_FIELD_DESCRIPTION,
        example = FACET_PREFIX_FIELD_EXAMPLE
    )
    String prefix,

    @Schema(
        description = FACET_CONTAINS_FIELD_DESCRIPTION,
        example = FACET_CONTAINS_FIELD_EXAMPLE
    )
    String contains,

    @Schema(
        description = FACET_DISPLAY_NAME_FILED_DESCRIPTION,
        example = FACET_DISPLAY_NAME_FILED_EXAMPLE
    )
    String displayName,

    @Schema(
        description = FACET_TYPE_FILED_DESCRIPTION
    )
    NavigationType type,

    @Schema(
        description = FACET_NAVIGATION_NAME_FILED_DESCRIPTION,
        example = FACET_NAVIGATION_NAME_DESCRIPTION,
        required = true
    )
    String navigationName) {

    public static final Facet EMPTY = new Facet("", "", "", null, "");

}
