package com.groupbyinc.search.ssa.core.tiles;

import com.groupbyinc.search.ssa.api.dto.tiles.TilesNavigationDto;

import java.util.List;

public record TilesNavigation(
    Boolean tileNavigationRequested,
    List<ProductAttributeValue> appliedTiles
) {

    public static TilesNavigation toDomain(TilesNavigationDto tilesNavigationDto) {
        if (tilesNavigationDto == null) {
            return new TilesNavigation(false, List.of());
        }
        return new TilesNavigation(
            tilesNavigationDto.tileNavigationRequested(),
            convertTiles(tilesNavigationDto)
        );
    }

    private static List<ProductAttributeValue> convertTiles(TilesNavigationDto tilesNavigationDto) {
        if (tilesNavigationDto.appliedTiles() == null) {
            return List.of();
        }
        return tilesNavigationDto.appliedTiles().stream()
            .map(ProductAttributeValue::toDomain)
            .toList();
    }
}