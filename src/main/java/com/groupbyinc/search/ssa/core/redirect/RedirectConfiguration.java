package com.groupbyinc.search.ssa.core.redirect;

import com.groupbyinc.proto.commandcenter.config.MessageType;
import com.groupbyinc.search.ssa.core.ActiveTimeEnabled;
import com.groupbyinc.search.ssa.core.Metadata;
import com.groupbyinc.search.ssa.core.Prioritized;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.trigger.QueryPatternTrigger;
import com.groupbyinc.search.ssa.core.trigger.Triggered;
import com.groupbyinc.search.ssa.util.StringUtils;

import io.micronaut.core.annotation.NonNull;
import lombok.Builder;

import java.util.List;
import java.util.Objects;

@Builder
public record RedirectConfiguration (

    Integer id,

    /* ID of the area which this redirect is associated with. */
    Integer areaId,

    /* URL the redirect should redirect to. */
    String url,

    /* Priority of the navigation. A lower number indicates a higher priority. min: 1, max: Integer.MAX_VALUE */
    Integer priority,

    /* Is time range enabled for this redirect. */
    boolean activeHoursEnabled,

    /* Used if RedirectConfiguration#activeHoursEnabled is true. Start-date for "redirect active" time range. */
    Long activeFrom,

    /* Used if RedirectConfiguration#activeHoursEnabled is true. End-date for "redirect active" time range. */
    Long activeTo,

    /* Trigger set capturing the logic how the redirect should be triggered. Must contain at lest one trigger. */
    List<QueryPatternTrigger> triggers,

    /* Type of configuration update message. Used for PubSub updates. */
    MessageType messageType,

    List<Metadata> metadata) implements Triggered, Prioritized<RedirectConfiguration>, ActiveTimeEnabled {

    @Override
    public boolean trigger(@NonNull SearchParameters searchParameters)  {
        return triggers.stream()
            .anyMatch(trigger -> applyTrigger(searchParameters, trigger));
    }

    @Override
    public int compareTo(RedirectConfiguration o) {
        var result = Integer.compare(priority, o.priority);
        if (result == 0) {
            result = Integer.compare(id, o.id);
        }
        return result;
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, areaId);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RedirectConfiguration that = (RedirectConfiguration) o;
        return id.equals(that.id) && areaId.equals(that.areaId);
    }

    /**
     * All operations are case-insensitive, except REGEX
     */
    private static boolean applyTrigger(SearchParameters searchParameters, QueryPatternTrigger trigger) {
        return switch (trigger.getType()) {
            case CONTAINS -> trigger.getValues().stream().anyMatch(str -> StringUtils.containsIgnoreCase(searchParameters.getQuery(), str));
            case ENDS_WITH -> trigger.getValues().stream().anyMatch(str -> StringUtils.endsWithIgnoreCase(searchParameters.getQuery(), str));
            case REGEX -> trigger.getPatterns().stream().anyMatch(pt -> StringUtils.regex(searchParameters.getQuery(), pt));
            case STARTS_WITH -> trigger.getValues().stream().anyMatch(str -> StringUtils.startsWithIgnoreCase(searchParameters.getQuery(), str));
            case MATCHES -> trigger.getValues().stream().anyMatch(str -> StringUtils.matches(searchParameters.getQuery(), str));
        };
    }

}
