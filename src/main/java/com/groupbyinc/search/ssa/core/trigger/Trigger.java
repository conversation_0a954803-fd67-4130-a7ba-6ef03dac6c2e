package com.groupbyinc.search.ssa.core.trigger;

/**
 * Interface for defining a trigger based on a typed target.
 *
 * @param <T> Type of object the trigger targets.
 */
public interface Trigger<T> {

    /**
     * @param triggerTarget Target for the trigger to match on.
     *
     * @return {@code true} if the trigger matches the target, otherwise {@code false}.
     */
    boolean matches(T triggerTarget);
}
