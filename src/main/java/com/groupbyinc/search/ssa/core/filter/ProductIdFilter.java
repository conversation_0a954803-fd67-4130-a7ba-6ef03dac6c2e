package com.groupbyinc.search.ssa.core.filter;

import io.micronaut.core.annotation.NonNull;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultList;

/**
 * Describes a filter with included product ids and excluded product ids
 */
public record ProductIdFilter(

    /** Included to the search product ids */
    List<String> includedProductIds,

    /** Excluded from the search product ids */
    List<String> excludedProductIds) {

    public ProductIdFilter {
        includedProductIds = notNullOrDefaultList(includedProductIds);
        excludedProductIds = notNullOrDefaultList(excludedProductIds);
    }

    public ProductIdFilter withAdditionalIncludedIds(@NonNull Set<String> productIdsToInclude) {
        var idsToInclude = new HashSet<>(productIdsToInclude); // make it mutable
        idsToInclude.addAll(includedProductIds);
        return new ProductIdFilter(new ArrayList<>(idsToInclude), excludedProductIds);
    }
}
