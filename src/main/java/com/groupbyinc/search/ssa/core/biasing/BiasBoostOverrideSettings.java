package com.groupbyinc.search.ssa.core.biasing;

import com.groupbyinc.search.ssa.features.FeatureFlag;

public record BiasBoostOverrideSettings(boolean enabled,
                                        Float absoluteIncrease,
                                        Float strongIncrease,
                                        Float mediumIncrease,
                                        Float weakIncrease,
                                        Float leaveUnchanged,
                                        Float weakDecrease,
                                        Float mediumDecrease,
                                        Float strongDecrease,
                                        Float absoluteDecrease) implements FeatureFlag {

    public static final BiasBoostOverrideSettings DEFAULT = new BiasBoostOverrideSettings(
        false,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null
    );

}
