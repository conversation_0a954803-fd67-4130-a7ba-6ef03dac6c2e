package com.groupbyinc.search.ssa.core;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.micronaut.core.util.CollectionUtils;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Objects;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_SPONSORED_RECORDS_COUNT_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_SPONSORED_RECORDS_COUNT_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_SPONSORED_RECORDS_POSITIONS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_REQUEST_SPONSORED_RECORDS_POSITIONS_FIELD_EXAMPLE;

public record SponsoredRecordsRequest(

    @Schema(
        description = SEARCH_REQUEST_SPONSORED_RECORDS_COUNT_FIELD_DESCRIPTION,
        example = SEARCH_REQUEST_SPONSORED_RECORDS_COUNT_FIELD_EXAMPLE)
    Integer count,

    @Schema(
        description = SEARCH_REQUEST_SPONSORED_RECORDS_POSITIONS_FIELD_DESCRIPTION,
        example = SEARCH_REQUEST_SPONSORED_RECORDS_POSITIONS_FIELD_EXAMPLE)
    List<Integer> positions) {

    /**
     * Normalizes index positions, drops out of bound indexes for given page size, removes duplicates.
     *
     * @param pageSize max allowed search response page size
     * @return normalized sorted position indexes
     */
    @JsonIgnore
    public List<Integer> positionsNormalized(int pageSize) {
        if (CollectionUtils.isEmpty(positions)) {
            return List.of();
        }

        return positions.stream()
            .filter(Objects::nonNull)
            .filter(pos -> pos >= 0 && pos < pageSize)
            .distinct()
            .sorted()
            .toList();
    }

}
