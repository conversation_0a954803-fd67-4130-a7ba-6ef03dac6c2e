package com.groupbyinc.search.ssa.core.navigation;

import io.swagger.v3.oas.annotations.media.Schema;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.NAVIGATION_TYPE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NAVIGATION_TYPE_RANGE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NAVIGATION_TYPE_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.NAVIGATION_TYPE_VALUE;

/**
 * Represents the type of navigation.
 * */
@Schema(
    title = NAVIGATION_TYPE_TITLE,
    description = NAVIGATION_TYPE_DESCRIPTION
)
public enum NavigationType {

    /**
     * A navigation that is a single value.
     * */
    @Schema(
        description = NAVIGATION_TYPE_VALUE
    )
    VALUE,

    /**
     * A navigation that has a range of values to be bucketed on.
     * */
    @Schema(
        description = NAVIGATION_TYPE_RANGE
    )
    RANGE
}
