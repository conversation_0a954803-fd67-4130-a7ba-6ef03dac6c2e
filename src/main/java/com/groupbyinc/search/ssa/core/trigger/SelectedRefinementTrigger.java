package com.groupbyinc.search.ssa.core.trigger;

import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;

import static com.groupbyinc.search.ssa.core.trigger.SelectedRefinementTrigger.Type.NAVIGATION_SELECTED;
import static com.groupbyinc.search.ssa.core.trigger.SelectedRefinementTrigger.Type.RANGE;
import static com.groupbyinc.search.ssa.core.trigger.SelectedRefinementTrigger.Type.VALUE;

/**
 * Trigger that matches when the refinement has been selected by the shopper, but with any value or range.
 */
public record SelectedRefinementTrigger(
    String field,
    String value,
    Range range,
    Type type) implements Trigger<SelectedRefinement> {

    public SelectedRefinementTrigger(String field) {
        this(field, null, null, NAVIGATION_SELECTED);
    }

    public SelectedRefinementTrigger(String field, String value) {
        this(field, value, null, VALUE);
    }

    public SelectedRefinementTrigger(String field, Range range) {
        this(field, null, range, RANGE);
    }

    @Override
    public boolean matches(SelectedRefinement target) {
        return switch (type) {
            case NAVIGATION_SELECTED -> field.equals(target.getField());
            case RANGE -> field.equals(target.getField()) && range.isIdenticalRange(target.getRange());
            case VALUE -> field.equals(target.getField()) && value.equals(target.getValue());
        };
    }

    public enum Type {
        RANGE,
        VALUE,
        NAVIGATION_SELECTED,
    }

}
