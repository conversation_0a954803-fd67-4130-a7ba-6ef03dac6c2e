package com.groupbyinc.search.ssa.core.rule;

import com.groupbyinc.search.ssa.core.filter.AttributeFilter;
import com.groupbyinc.search.ssa.core.filter.ProductIdFilter;
import com.groupbyinc.search.ssa.core.filter.SearchFilter;

import lombok.Builder;

import java.util.List;

@Builder
public record RuleVariant (String biasingProfileName,
                           List<String> includedNavigations,
                           RuleTemplate template,
                           List<ProductIdsBucket> boostedProductBuckets,
                           List<ProductIdsBucket> buriedProductBuckets,
                           List<PinnedRefinement> pinnedRefinements,
                           List<PinnedProduct> pinnedProducts,
                           List<SearchFilter> searchFilters,
                           List<AttributeFilter> attributeFilters,
                           ProductIdFilter productIdFilter,
                           ProductVisibilityBias productVisibilityBias){

}
