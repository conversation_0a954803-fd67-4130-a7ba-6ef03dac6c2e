package com.groupbyinc.search.ssa.core.template.zone;

import com.groupbyinc.search.ssa.core.zone.ZoneType;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Value;

@Value
@EqualsAndHashCode(callSuper = true)
public class TemplateZoneContent extends TemplateZoneBase{

    String content;

    @Builder
    public TemplateZoneContent(String name, ZoneType type, String content) {
        super(name, type);
        this.content = content;
    }

}
