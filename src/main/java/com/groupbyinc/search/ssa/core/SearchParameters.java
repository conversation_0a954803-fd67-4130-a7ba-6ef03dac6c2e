package com.groupbyinc.search.ssa.core;

import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfile;
import com.groupbyinc.search.ssa.core.conversation.ConversationalSearchConfig;
import com.groupbyinc.search.ssa.core.crm.UserAttribute;
import com.groupbyinc.search.ssa.core.filter.AttributeFilter;
import com.groupbyinc.search.ssa.core.filter.ProductIdFilter;
import com.groupbyinc.search.ssa.core.filter.RangeFilter;
import com.groupbyinc.search.ssa.core.filter.ValueFilter;
import com.groupbyinc.search.ssa.core.navigation.Facet;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.core.rule.Overwrites;
import com.groupbyinc.search.ssa.core.rule.PinnedProduct;
import com.groupbyinc.search.ssa.core.rule.PinnedRefinement;
import com.groupbyinc.search.ssa.core.rule.ProductIdsBucket;
import com.groupbyinc.search.ssa.core.tiles.TilesNavigation;
import com.groupbyinc.search.ssa.core.trigger.SelectedRefinementTrigger;
import com.groupbyinc.search.ssa.mongo.facet.MongoFacetConverter;
import com.groupbyinc.search.ssa.retail.filtering.v2alpha.RetailCRMFacetConverter;
import com.groupbyinc.search.ssa.retail.filtering.RetailFacetConverter;
import com.groupbyinc.search.ssa.util.PrefixTreeMap;
import com.groupbyinc.utils.validation.ValidationUtils;

import lombok.Builder;
import lombok.Data;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultList;
import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultMap;
import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultSet;

import static java.util.Objects.requireNonNullElse;
import static org.apache.commons.lang3.ObjectUtils.defaultIfNull;

/**
 * Core search parameters which the application supports. Fields in this class should be search engine agnostic.
 */
@Data
public class SearchParameters {

    // region fields with static data
    private PrefixTreeMap prefixTreeMap;
    private MerchandisingConfiguration merchandisingConfiguration;
    private RetailFacetConverter retailFacetConverter;
    private RetailCRMFacetConverter retailCRMFacetConverter;
    private MongoFacetConverter mongoFacetConverter;
    // endregion

    // region fields for request
    private Facet facet;
    private String query;
    private List<Sort> sorts;
    private String preFilter;
    private Integer facetLimit;
    private Boolean dynamicFacet;
    private Pagination pagination;
    private String siteFilterName;
    private Overwrites overwrites;
    private SearchMode searchMode;
    private String ruleVariantName;
    private Boolean topSortEnabled;
    private Boolean topSortV2Enabled;
    private Boolean partNumberSearchEnabled;
    private boolean partNumberFallbackEnabled;
    private boolean partNumberExpansionEnabled;
    private List<AttributeConfiguration> partNumberSearchableAttributes;
    private String inventoryStoreId;
    private String biasingProfileName;
    private List<String> responseMask;

    private List<String> pageCategories;
    private Boolean pinUnexpandedResults;
    private BiasingProfile biasingProfile;
    private Pagination originalPagination;
    private List<ValueFilter> valueFilters;
    private List<RangeFilter> rangeFilters;
    private List<String> variantRollupKeys;
    private Boolean includeExpandedResults;
    private Set<String> excludedNavigations;
    private ProductIdFilter productIdFilter;
    private List<String> includedNavigations;
    private List<PinnedProduct> pinnedProducts;
    private List<SelectedRefinement> refinements;
    private List<CustomParameter> customParameters;
    private List<AttributeFilter> attributeFilters;
    private SpellCorrectionMode spellCorrectionMode;
    private List<PinnedRefinement> pinnedRefinements;
    private Map<String, RecordLabel> labeledProductIds;
    private List<ProductIdsBucket> buriedProductBuckets;
    private List<ProductIdsBucket> boostedProductBuckets;
    private SponsoredRecordsRequest sponsoredRecordsRequest;
    private ConversationalSearchConfig conversationalSearchConfig;
    private TilesNavigation tilesNavigation;
    private List<SelectedRefinementTrigger> selectedRefinementTriggers;
    // endregion

    private Boolean skipProductCatalog;
    private Boolean fetchSponsoredRecordsVariants;
    private List<UserAttribute> userAttributes;

    /**
     * Object representing a search request.
     *
     * @param query                          query string to find matching products. Defaults to an empty string
     * @param customParameters               an additional custom parameters a user can send with the request
     * @param pagination                     pagination details
     * @param originalPagination             original pagination details received with request
     * @param refinements                    list of refinements that have been selected by the shopper
     * @param merchandisingConfiguration     stored static configurations related to the request from cc-api
     * @param biasingProfileName             name of a biasing profile which should take precedence, If provided
     * @param biasingProfile                 biasing profile which should be applied to the search
     * @param sorts                          list of objects describing how the products related to a field will be
     *                                       sorted
     * @param includedNavigations            set of navigations that will be included on search results
     * @param excludedNavigations            set of navigations that will be excluded on search results
     * @param valueFilters                   list of field values the search will be filtered on
     * @param rangeFilters                   ranges the search will be filtered on
     * @param attributeFilters               attribute filters the search will be filtered on
     * @param productIdFilter                included and excluded product ids the search will be filtered on
     * @param dynamicFacet                   enable/disable "dynamic facet" mode
     * @param variantRollupKeys              list the variant rollup keys
     * @param preFilter                      a raw filter value. Which will be applied to the whole request
     * @param siteFilterName                 a name of site filter which has to be applied to the request
     * @param responseMask                   raw response mask. Source for creating
     *                                       {@link SearchParameters#prefixTreeMap}
     * @param prefixTreeMap                  prefix-based tree, which contains parsed "keys" of JSON objects which
     *                                       should be included in meta-information of each record inside response
     * @param boostedProductBuckets          list of boosted products
     * @param buriedProductBuckets           list of buried products
     * @param facet                          object with information about requested facet
     * @param searchMode                     type of search request
     * @param pageCategories                 an array with single page category which is associated with request
     *                                       products
     * @param spellCorrectionMode            configuration which is allowing Google to fix spelling in a query
     * @param includeExpandedResults         define if response should contain unrelated records or not
     * @param pinUnexpandedResults           define is unexpected records should be at the top of response or not
     * @param pinnedRefinements              a list of refinements which needs to be pinned to the fixed position
     * @param pinnedProducts                 a list of products which needs to be pinned to the fixed position
     * @param overwrites                     any cc-api configurations that are needs to be replaced
     * @param ruleVariantName                a name of triggered rule variant
     * @param facetLimit                     a limit of facet keys in a response
     * @param sponsoredRecordsRequest        records which are needs to be placed to the correct position by topSort
     * @param topSortEnabled                 define is top sort service enabled or not
     * @param topSortV2Enabled               define is top sort v2 service enabled or not
     * @param partNumberSearchEnabled        whether the part number search enabled or not
     * @param partNumberFallbackEnabled      whether to use a fallback search when no part number results are found
     * @param partNumberExpansionEnabled     whether to enable part number expansion search
     * @param partNumberSearchableAttributes attributes which are indexed as partNumber searchable
     * @param inventoryStoreId               id of store for inventory related attributes
     * @param conversationalSearchConfig     for conversational search support
     * @param tilesNavigation                tiles data to be allied for tiles navigation on search
     * @param userAttributes                 Customer Relationship Management (CRM) data to personalize search experiences
     */
    @Builder(toBuilder = true)
    public SearchParameters(@Nullable String query,
                            @Nullable List<CustomParameter> customParameters,
                            @Nullable Pagination pagination,
                            @Nullable Pagination originalPagination,
                            @Nullable List<SelectedRefinement> refinements,
                            @Nullable MerchandisingConfiguration merchandisingConfiguration,
                            @Nullable String biasingProfileName,
                            @Nullable BiasingProfile biasingProfile,
                            @Nullable List<Sort> sorts,
                            @Nullable List<String> includedNavigations,
                            @Nullable Set<String> excludedNavigations,
                            @Nullable List<ValueFilter> valueFilters,
                            @Nullable List<RangeFilter> rangeFilters,
                            @Nullable List<AttributeFilter> attributeFilters,
                            @Nullable ProductIdFilter productIdFilter,
                            @Nullable Boolean dynamicFacet,
                            @Nullable List<String> variantRollupKeys,
                            @Nullable String preFilter,
                            @Nullable String siteFilterName,
                            @Nullable List<String> responseMask,
                            @Nullable PrefixTreeMap prefixTreeMap,
                            @Nullable List<ProductIdsBucket> boostedProductBuckets,
                            @Nullable List<ProductIdsBucket> buriedProductBuckets,
                            @Nullable Facet facet,
                            @Nullable SearchMode searchMode,
                            @Nullable List<String> pageCategories,
                            @Nullable SpellCorrectionMode spellCorrectionMode,
                            @Nullable Boolean includeExpandedResults,
                            @Nullable Boolean pinUnexpandedResults,
                            @Nullable List<PinnedRefinement> pinnedRefinements,
                            @Nullable List<PinnedProduct> pinnedProducts,
                            @Nullable Overwrites overwrites,
                            @Nullable String ruleVariantName,
                            @Nullable Integer facetLimit,
                            @Nullable SponsoredRecordsRequest sponsoredRecordsRequest,
                            @Nullable Boolean topSortEnabled,
                            @Nullable Boolean topSortV2Enabled,
                            @Nullable Boolean partNumberSearchEnabled,
                            @Nullable Boolean partNumberFallbackEnabled,
                            @Nullable Boolean partNumberExpansionEnabled,
                            @Nullable List<AttributeConfiguration> partNumberSearchableAttributes,
                            @Nullable String inventoryStoreId,
                            @Nullable Map<String, RecordLabel> labeledProductIds,
                            @Nullable Boolean skipProductCatalog,
                            @Nullable ConversationalSearchConfig conversationalSearchConfig,
                            @Nullable TilesNavigation tilesNavigation,
                            @Nullable List<SelectedRefinementTrigger> selectedRefinementTriggers,
                            @Nullable List<UserAttribute> userAttributes) {
        this.query = requireNonNullElse(query, "");
        this.customParameters = notNullOrDefaultList(customParameters);
        this.pagination = requireNonNullElse(pagination, Pagination.defaultPagination());
        this.originalPagination = requireNonNullElse(originalPagination, Pagination.defaultPagination());
        this.refinements = notNullOrDefaultList(refinements);
        this.merchandisingConfiguration = merchandisingConfiguration == null
            ? MerchandisingConfiguration.EMPTY
            : merchandisingConfiguration;
        this.biasingProfileName = biasingProfileName;
        this.biasingProfile = biasingProfile;
        this.sorts = notNullOrDefaultList(sorts);

        this.includedNavigations = notNullOrDefaultList(includedNavigations);
        this.excludedNavigations = notNullOrDefaultSet(excludedNavigations);
        ValidationUtils.requireCondition(
            !(!this.includedNavigations.isEmpty() && !this.excludedNavigations.isEmpty()),
            "Cannot have both included and excluded navigations."
        );

        this.valueFilters = notNullOrDefaultList(valueFilters);
        this.rangeFilters = notNullOrDefaultList(rangeFilters);
        this.attributeFilters = notNullOrDefaultList(attributeFilters);
        this.productIdFilter = productIdFilter;

        this.dynamicFacet = dynamicFacet;
        this.variantRollupKeys = notNullOrDefaultList(variantRollupKeys);
        this.preFilter = preFilter == null ? "" : preFilter.trim();
        // siteFilterName is allowed to be null because we use this for other checks
        this.siteFilterName = siteFilterName == null ? null : siteFilterName.trim();
        this.responseMask = notNullOrDefaultList(responseMask);
        this.prefixTreeMap = prefixTreeMap;
        this.boostedProductBuckets = notNullOrDefaultList(boostedProductBuckets);
        this.buriedProductBuckets = notNullOrDefaultList(buriedProductBuckets);
        this.facet = facet;
        this.searchMode = searchMode;
        this.pageCategories = notNullOrDefaultList(pageCategories);

        this.spellCorrectionMode = requireNonNullElse(spellCorrectionMode, SpellCorrectionMode.AUTO);
        this.includeExpandedResults = includeExpandedResults;
        this.pinUnexpandedResults = pinUnexpandedResults == null || pinUnexpandedResults;
        this.pinnedRefinements = notNullOrDefaultList(pinnedRefinements);
        this.pinnedProducts = notNullOrDefaultList(pinnedProducts);
        this.overwrites = overwrites;
        this.ruleVariantName = ruleVariantName;
        this.facetLimit = facetLimit;
        this.sponsoredRecordsRequest = sponsoredRecordsRequest;
        this.topSortEnabled = topSortEnabled;
        this.topSortV2Enabled = topSortV2Enabled;
        this.partNumberSearchEnabled = partNumberSearchEnabled;
        this.partNumberFallbackEnabled = defaultIfNull(partNumberFallbackEnabled, true);
        this.partNumberExpansionEnabled = defaultIfNull(partNumberExpansionEnabled, true);
        this.partNumberSearchableAttributes = notNullOrDefaultList(partNumberSearchableAttributes);
        this.inventoryStoreId = requireNonNullElse(inventoryStoreId, "").trim();
        this.labeledProductIds = notNullOrDefaultMap(labeledProductIds);

        this.skipProductCatalog = skipProductCatalog != null && skipProductCatalog;
        this.retailFacetConverter = new RetailFacetConverter(this);
        this.retailCRMFacetConverter = new RetailCRMFacetConverter(this);
        this.mongoFacetConverter = new MongoFacetConverter(this);
        this.conversationalSearchConfig = conversationalSearchConfig;
        this.selectedRefinementTriggers = selectedRefinementTriggers;
        this.tilesNavigation = tilesNavigation;
        this.userAttributes = userAttributes;
    }

}
