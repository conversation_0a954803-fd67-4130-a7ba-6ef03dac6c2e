package com.groupbyinc.search.ssa.core.rule;

import io.micronaut.core.util.StringUtils;

import java.util.List;

import static io.micronaut.core.util.CollectionUtils.isNotEmpty;

public record ProductVisibilityBias(String attributeName,
                                    List<String> attributeValues,
                                    List<Double> attributeNumberValues,
                                    Double multiplier,
                                    Double percentageOffset,
                                    Double visibilityPercentageCap) {

    public boolean isValid(){
        return  StringUtils.isNotEmpty(attributeName)
            && (isNotEmpty(attributeNumberValues) || isNotEmpty(attributeValues))
            && multiplier != null && multiplier > 1
            && percentageOffset != null
            && (visibilityPercentageCap == null || visibilityPercentageCap > 0 && visibilityPercentageCap <= 1);
    }
}
