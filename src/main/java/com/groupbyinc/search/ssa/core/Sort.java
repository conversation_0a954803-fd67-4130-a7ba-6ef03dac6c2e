package com.groupbyinc.search.ssa.core;

import com.groupbyinc.utils.validation.FieldRequirement;
import com.groupbyinc.utils.validation.ValidationUtils;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Describes the order the returned products under a field should appear on the page.
 */
@Data
@NoArgsConstructor
public class Sort {

    /** Field the order will be applied to. */
    private String field;
    /** OrderDto the products will appear in. */
    private Order order;

    /**
     * Creates a new instance of Sort.
     *
     * @param field Field the order will be applied to. Must be defined and not be blank.
     * @param order OrderDto the products will appear in. Defaults to ascending if no order is defined.
     */
    @Builder
    public Sort(String field, Order order) {
        this.field = ValidationUtils
            .requireNonBlank(field, "Sort order", FieldRequirement.MANDATORY);
        this.order = order == null ? Order.ASCENDING : order;
    }

}
