package com.groupbyinc.search.ssa.core.product;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.TIMESTAMP_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TIMESTAMP_NANOS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TIMESTAMP_NANOS_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TIMESTAMP_SECONDS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TIMESTAMP_SECONDS_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.TIMESTAMP_TITLE;

@Schema(
    title = TIMESTAMP_TITLE,
    description = TIMESTAMP_DESCRIPTION
)
@Slf4j
@JsonDeserialize(using = Timestamp.Deserializer.class)
@Getter
@AllArgsConstructor
public class Timestamp {

    @Schema(
        description = TIMESTAMP_SECONDS_FIELD_DESCRIPTION,
        example = TIMESTAMP_SECONDS_FIELD_EXAMPLE
    )
    private final Long seconds;

    @Schema(
        description = TIMESTAMP_NANOS_FIELD_DESCRIPTION,
        example = TIMESTAMP_NANOS_FIELD_EXAMPLE
    )
    private final Integer nanos;

    public Timestamp(com.google.protobuf.Timestamp timestamp) {
        this.seconds = timestamp.getSeconds();
        this.nanos = timestamp.getNanos();
    }

    public Timestamp(Instant instant) {
        this.seconds = instant.getEpochSecond();
        this.nanos = instant.getNano();
    }

    public static class Deserializer extends JsonDeserializer<Timestamp> {

        @Override
        public Timestamp deserialize(JsonParser p, DeserializationContext ctxt) {
            var value = "";
            try {
                value = p.getValueAsString();
                return new Timestamp(
                    LocalDateTime.parse(value, DateTimeFormatter.ISO_DATE_TIME).toInstant(ZoneOffset.UTC)
                );
            } catch (Exception e) {
                log.error("Failed to parse timestamp: {}", value);
                return null;
            }
        }
    }

}
