package com.groupbyinc.search.ssa.core.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_INFO_COST_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_INFO_COST_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_INFO_CURRENCY_CODE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_INFO_CURRENCY_CODE_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_INFO_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_INFO_ORIGINAL_PRICE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_INFO_ORIGINAL_PRICE_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_INFO_PRICE_EFFECTIVE_TIME_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_INFO_PRICE_EXPIRE_TIME_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_INFO_PRICE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_INFO_PRICE_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_INFO_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_RANGE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_RANGE_ORIGINAL_PRICE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_RANGE_PRICE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.PRICE_RANGE_TITLE;

@Schema(
    title = PRICE_INFO_TITLE,
    description = PRICE_INFO_DESCRIPTION
)
@Getter
@AllArgsConstructor
public class PriceInfo {

    @Schema(
        description = PRICE_INFO_CURRENCY_CODE_FIELD_DESCRIPTION,
        example = PRICE_INFO_CURRENCY_CODE_FIELD_EXAMPLE
    )
    private final String currencyCode;

    @Schema(
        description = PRICE_INFO_PRICE_FIELD_DESCRIPTION,
        example = PRICE_INFO_PRICE_FIELD_EXAMPLE
    )
    private final Float price;

    @Schema(
        description = PRICE_INFO_ORIGINAL_PRICE_FIELD_DESCRIPTION,
        example = PRICE_INFO_ORIGINAL_PRICE_FIELD_EXAMPLE
    )
    private final Float originalPrice;

    @Schema(
        description = PRICE_INFO_COST_FIELD_DESCRIPTION,
        example = PRICE_INFO_COST_FIELD_EXAMPLE
    )
    private final Float cost;

    @Schema(
        description = PRICE_INFO_PRICE_EFFECTIVE_TIME_FIELD_DESCRIPTION
    )
    private final Timestamp priceEffectiveTime;

    @Schema(
        description = PRICE_INFO_PRICE_EXPIRE_TIME_FIELD_DESCRIPTION
    )
    private final Timestamp priceExpireTime;

    @Schema(
        description = PRICE_RANGE_DESCRIPTION
    )
    private final PriceRange priceRange;

    public PriceInfo(com.google.cloud.retail.v2.PriceInfo priceInfo) {
        this.currencyCode = priceInfo.getCurrencyCode();
        this.price = priceInfo.getPrice();
        this.originalPrice = priceInfo.getOriginalPrice();
        this.cost = priceInfo.getCost();
        this.priceEffectiveTime = new Timestamp(priceInfo.getPriceEffectiveTime());
        this.priceExpireTime = new Timestamp(priceInfo.getPriceEffectiveTime());
        this.priceRange = new PriceRange(priceInfo.getPriceRange().getPrice(), priceInfo.getPriceRange().getOriginalPrice());
    }

    @Schema(
        title = PRICE_RANGE_TITLE,
        description = PRICE_RANGE_DESCRIPTION
    )
    @Getter
    @AllArgsConstructor
    public static class PriceRange {

        @Schema(
            description = PRICE_RANGE_PRICE_FIELD_DESCRIPTION
        )
        private final Interval price;

        @Schema(
            description = PRICE_RANGE_ORIGINAL_PRICE_FIELD_DESCRIPTION
        )
        private final Interval originalPrice;

        public PriceRange(com.google.cloud.retail.v2.Interval price,
                          com.google.cloud.retail.v2.Interval originalPrice) {
            this.price = new Interval(price);
            this.originalPrice = new Interval(originalPrice);
        }
    }
}
