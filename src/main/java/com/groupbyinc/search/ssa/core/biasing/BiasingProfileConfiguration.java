package com.groupbyinc.search.ssa.core.biasing;

import com.groupbyinc.proto.commandcenter.config.MessageType;
import com.groupbyinc.search.ssa.core.Config;

import lombok.Builder;

import java.util.List;


@Builder
public record BiasingProfileConfiguration (

    Integer id,

    String name,

    /* ID of area which is associated with this Biasing profile. */
    Integer areaId,

    /* Type of configuration update message.*/
    MessageType messageType,

    /*
     * List of biases which are configured for this Biasing profile.
     * This list mast be defined and contain at lest one bias.
     */
    List<Bias> biases,

    /*
     * Flag which is indicating that this Biasing profile will be used as default
     * for area which is associated with this Biasing profile.
     * Default value is "false".
     */
    boolean areaDefault) implements Config {

}
