package com.groupbyinc.search.ssa.core;

import com.groupbyinc.search.ssa.core.area.AreaConfiguration;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.biasing.BiasingProfileConfiguration;
import com.groupbyinc.search.ssa.core.features.Features;
import com.groupbyinc.search.ssa.core.navigation.NavigationConfiguration;
import com.groupbyinc.search.ssa.core.project.ProjectConfiguration;
import com.groupbyinc.search.ssa.core.redirect.RedirectConfiguration;
import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
import com.groupbyinc.search.ssa.core.sitefilter.SiteFilterConfiguration;
import com.groupbyinc.search.ssa.core.tenant.TenantConfiguration;
import com.groupbyinc.search.ssa.core.zone.ZoneConfiguration;
import com.groupbyinc.search.ssa.topsort.model.TopsortConfiguration;

import java.util.List;

public record AllConfigurations (
    List<RuleConfiguration> rules,
    List<ZoneConfiguration> zones,
    List<AreaConfiguration> areas,
    List<TenantConfiguration> tenants,
    List<RedirectConfiguration> redirects,
    List<AttributeConfiguration> attributes,
    List<NavigationConfiguration> navigations,
    List<BiasingProfileConfiguration> biasingProfiles,
    List<ProjectConfiguration> projectConfigurations,
    List<SiteFilterConfiguration> siteFilters,
    List<TopsortConfiguration> topsortConfigs,
    Features features) {

}
