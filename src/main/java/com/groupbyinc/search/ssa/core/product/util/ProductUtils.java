package com.groupbyinc.search.ssa.core.product.util;

import com.groupbyinc.search.ssa.core.product.Product;
import com.groupbyinc.search.ssa.core.product.ProductCustomAttribute;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.annotation.Nulls;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.retail.v2.CustomAttribute;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static io.micronaut.core.util.CollectionUtils.isEmpty;
import static java.util.stream.Collectors.toMap;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ProductUtils {

    private static final ObjectMapper MAPPER = new ObjectMapper()
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    static {
        // Write nulls as empty values for compatibility
        MAPPER.configOverride(String.class).setSetterInfo(JsonSetter.Value.forValueNulls(Nulls.AS_EMPTY));
        MAPPER.configOverride(List.class).setSetterInfo(JsonSetter.Value.forValueNulls(Nulls.AS_EMPTY));
    }

    /**
     * Instantiates {@link Product} object from given map
     *
     * @param map map representation of product
     *
     * @return parsed product
     */
    public static Product fromMap(Map<String, Object> map) {
        return MAPPER.convertValue(map, Product.class);
    }

    /**
     * Prioritizes provided variantIds within variants collection, pushing them to the front of the collection, keeping
     * given order.
     *
     * @param variantIds variant Ids to be prioritized
     * @param variants   Product variants
     *
     * @return rearranged variants collection with prioritized variantIds at the front of the collection
     */
    public static List<Product> prioritizeVariants(List<String> variantIds, List<Product> variants) {
        if (isEmpty(variantIds) || isEmpty(variants)) {
            return variants;
        }
        var map = variants.stream().collect(toMap(Product::getId, Function.identity()));
        var list = new LinkedList<Product>();
        variantIds.forEach(e -> {
            var variant = map.get(e);
            if (variant != null) {
                list.add(variant);
                map.remove(e);
            }
        });
        list.addAll(map.values());
        return list;
    }

    public static Map<String, ProductCustomAttribute> convertAttributes(Map<String, CustomAttribute> attributes) {
        return attributes.entrySet().stream()
            .collect(toMap(Map.Entry::getKey, e -> new ProductCustomAttribute(e.getValue())));
    }
}
