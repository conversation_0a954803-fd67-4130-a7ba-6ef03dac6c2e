package com.groupbyinc.search.ssa.core.conversation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

import java.util.List;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_CONVERSATION_ID;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_FOLLOWUP_QUESTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.SEARCH_RESPONSE_SUGGESTED_ANSWERS;

@Builder
public record ConversationalSearchResult(
    @Schema(description = SEARCH_RESPONSE_CONVERSATION_ID)
    String conversationId,

    @Schema(description = SEARCH_RESPONSE_FOLLOWUP_QUESTION)
    String followupQuestion,

    @Schema(description = SEARCH_RESPONSE_SUGGESTED_ANSWERS)
    List<ProductAttributeValue> suggestedAnswers) {
}
