package com.groupbyinc.search.ssa.core.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.AUDIENCE_AGE_GROUPS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.AUDIENCE_AGE_GROUPS_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.AUDIENCE_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.AUDIENCE_GENDERS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.AUDIENCE_GENDERS_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.AUDIENCE_TITLE;

@Schema(
    title = AUDIENCE_TITLE,
    description = AUDIENCE_DESCRIPTION
)
@Getter
@AllArgsConstructor
public class Audience {

    @Schema(
        description = AUDIENCE_GENDERS_FIELD_DESCRIPTION,
        example = AUDIENCE_GENDERS_FIELD_EXAMPLE
    )
    private final List<String> genders;

    @Schema(
        description = AUDIENCE_AGE_GROUPS_FIELD_DESCRIPTION,
        example = AUDIENCE_AGE_GROUPS_FIELD_EXAMPLE
    )
    private final List<String> ageGroups;

    public Audience(com.google.cloud.retail.v2.Audience audience) {
        this.genders = audience.getGendersList();
        this.ageGroups = audience.getAgeGroupsList();
    }
}
