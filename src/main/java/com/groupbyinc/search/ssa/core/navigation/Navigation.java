package com.groupbyinc.search.ssa.core.navigation;

import com.groupbyinc.search.ssa.core.Metadata;
import com.groupbyinc.search.ssa.core.rule.PinnedRefinement;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;

import static com.groupbyinc.search.ssa.core.navigation.NavigationRefinement.applyPinnedRefinements;
import static com.groupbyinc.search.ssa.core.navigation.NavigationRefinement.getRefinementKey;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.RANGE;
import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireDefined;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonEmpty;

/** Represents a navigation as appears in a search result. */
@Getter
@EqualsAndHashCode
public class Navigation {

    private final String name;
    private final String field;
    private final String source;
    private final String placeId;
    private final boolean pinned;
    private final NavigationType type;
    private final boolean multiSelect;
    private final List<Metadata> metadata;
    private final List<NavigationRefinement> refinements;
    private final NavigationSort sort;

    /**
     * Creates a new instance of Navigation.
     *
     * @param name        Name of the navigation for display purposes. This must be specified, and not be blank.
     * @param field       Field tha navigation applies to. This must be specified, and not be blank.
     * @param type        Type of navigation. This must be specified.
     * @param multiSelect Flag for enabling the navigation to have multiple refinements selected for it.
     * @param refinements List of refinements for the navigation, e.g., Nike for the brand navigation.
     * @param source      Used to define is it Dynamic navigation or not.
     * @param metadata    any metadata (key-value) associated with the navigation.
     * @param placeId     store ID in case of inventory navigations.
     * @param pinned      Flag to confirm that navigation is pinned.
     * @param sort        Sort for the refinements in this navigation. Used only if {@code NavigationType#VALUE}
     */
    @Builder(toBuilder = true)
    @JsonCreator
    public Navigation(@JsonProperty("name") String name,
                      @JsonProperty("field") String field,
                      @JsonProperty("type") NavigationType type,
                      @JsonProperty("multiSelect") boolean multiSelect,
                      @JsonProperty("refinements") List<NavigationRefinement> refinements,
                      @JsonProperty("source") String source,
                      @JsonProperty("metadata") List<Metadata> metadata,
                      @JsonProperty("placeId") String placeId,
                      @JsonProperty("pinned") boolean pinned,
                      @JsonProperty("sort") NavigationSort sort) {

        this.name = requireNonBlank(name, "Navigation name", MANDATORY);
        this.field = requireNonBlank(field, "NavigationField", MANDATORY);
        this.type = requireDefined(type, "Navigation type");
        this.multiSelect = multiSelect;
        this.refinements = requireNonEmpty(refinements, "NavigationRefinements", MANDATORY);
        this.source = source;
        this.metadata = metadata;
        this.placeId = placeId;
        this.pinned = pinned;
        this.sort = sort;
    }

    /**
     * Merges refinements for similar navigations from different sources (e.g., main and secondary search)
     * according to the following rules:
     * <ul>
     *   <li>For common refinements, sum their counts</li>
     *   <li>Append unique refinements from both navigations</li>
     *   <li>Sort all merged refinements according to navigation's sort configuration:
     *       <ul>
     *         <li>VALUE navigations: alphabetical ascending (default) or descending by value/count</li>
     *         <li>RANGE navigations: descending by range low value (numerical order)</li>
     *       </ul>
     *   </li>
     * </ul>
     *
     * @param mainNav           The main navigation (its refinements and sort configuration take precedence)
     * @param secondaryNav      The secondary navigation (its refinements are merged into the main one)
     * @param pinnedRefinements Pinned refinements
     * @return A new Navigation with merged and sorted refinements
     */
    public static Navigation mergeNavigationRefinements(Navigation mainNav,
                                                        Navigation secondaryNav,
                                                        List<PinnedRefinement> pinnedRefinements) {
        var refinementMap = new LinkedHashMap<String, NavigationRefinement>();

        for (var mainRef : mainNav.getRefinements()) {
            var key = getRefinementKey(mainRef);
            refinementMap.put(key, mainRef);
        }

        for (var secondaryRef : secondaryNav.getRefinements()) {
            var key = getRefinementKey(secondaryRef);

            if (refinementMap.containsKey(key)) {
                // This is a common refinement - sum the counts
                var existingRef = refinementMap.get(key);
                if (secondaryRef.getCount() != null && existingRef.getCount() != null) {
                    // Create a new refinement with summed counts
                    var updatedRef = existingRef.toBuilder()
                        .count(existingRef.getCount() + secondaryRef.getCount())
                        .build();
                    refinementMap.put(key, updatedRef);
                } else if (secondaryRef.getCount() != null) {
                    // Use secondary refinement count if the main refinement's count is null
                    var updatedRef = existingRef.toBuilder()
                        .count(secondaryRef.getCount())
                        .build();
                    refinementMap.put(key, updatedRef);
                }
            } else { // This refinement only exists in secondary navigation - add it
                refinementMap.put(key, secondaryRef);
            }
        }

        // Sort the merged refinements according to the main navigation's sort order
        var sortedRefinements = new ArrayList<>(refinementMap.values());
        sortedRefinements.sort(defineSortOrder(mainNav));
        applyPinnedRefinements(mainNav.getField(), sortedRefinements, pinnedRefinements);

        return mainNav.toBuilder()
            .refinements(sortedRefinements)
            .build();
    }

    /**
     * Defines the sort order for refinements based on the navigation's sort configuration.
     * Uses the same logic as {@code MongoFacetConverter#defineSortOrder()} for {@code  VALUE} navigation type,
     * and descending by the lower bound for {@code RANGE} navigations.
     *
     * @param navigation The navigation containing sort configuration
     * @return Comparator for sorting NavigationRefinements
     */
    private static Comparator<NavigationRefinement> defineSortOrder(Navigation navigation) {
        if (navigation.getType() == RANGE) {
            // Descending by range low value (numerical order)
            return Comparator.comparing((NavigationRefinement r) -> r.getRange().low(),
                Comparator.nullsLast(Comparator.naturalOrder())).reversed();
        }
        if (navigation.getSort() == null) {
            // Default to ascending by value
            return Comparator.comparing(NavigationRefinement::getValue);
        }
        if (navigation.getSort().field() == NavigationSort.SortField.VALUE) {
            // Always descending regardless of orderType (matches Google behavior)
            return Comparator.comparing(NavigationRefinement::getValue).reversed();
        }
        // null-safe: nulls last
        return Comparator.comparing(
            NavigationRefinement::getCount, Comparator.nullsLast(Comparator.naturalOrder())
        ).reversed();
    }

}
