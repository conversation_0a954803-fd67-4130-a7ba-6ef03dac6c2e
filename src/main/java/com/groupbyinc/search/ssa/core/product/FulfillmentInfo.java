package com.groupbyinc.search.ssa.core.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.FULFILMENT_INFO_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FULFILMENT_INFO_PLACE_IDS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FULFILMENT_INFO_PLACE_IDS_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FULFILMENT_INFO_TITLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FULFILMENT_INFO_TYPE_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FULFILMENT_INFO_TYPE_FIELD_EXAMPLE;

@Schema(
    title = FULFILMENT_INFO_TITLE,
    description = FULFILMENT_INFO_DESCRIPTION
)
@Getter
@AllArgsConstructor
public class FulfillmentInfo {

    @Schema(
        description = FULFILMENT_INFO_TYPE_FIELD_DESCRIPTION,
        example = FULFILMENT_INFO_TYPE_FIELD_EXAMPLE
    )
    private final String type;

    @Schema(
        description = FULFILMENT_INFO_PLACE_IDS_FIELD_DESCRIPTION,
        example = FULFILMENT_INFO_PLACE_IDS_FIELD_EXAMPLE
    )
    private final List<String> placeIds;

    public FulfillmentInfo(com.google.cloud.retail.v2.FulfillmentInfo fulfillmentInfo) {
        this.type = fulfillmentInfo.getType();
        this.placeIds = fulfillmentInfo.getPlaceIdsList();
    }
}
