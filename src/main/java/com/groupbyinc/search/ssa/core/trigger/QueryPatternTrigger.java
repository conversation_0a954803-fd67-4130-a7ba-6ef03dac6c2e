package com.groupbyinc.search.ssa.core.trigger;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.groupbyinc.search.ssa.util.StringUtils;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

@Data
@NoArgsConstructor
public class QueryPatternTrigger implements Trigger<String> {

    public static final String COMMA_SEPARATOR_REGEX = "\\s*,\\s*";

    private Type type;
    private List<String> values;
    private List<Pattern> patterns;

    @Builder
    @JsonCreator
    public QueryPatternTrigger(@JsonProperty("type") Type type, @JsonProperty("value") String value) {
        this.type = type;
        if (type == Type.REGEX) {
            this.patterns = Arrays
                .stream(value.trim().split(COMMA_SEPARATOR_REGEX))
                .map(Pattern::compile)
                .toList();
        } else {
            this.values = Arrays.asList(value.trim().split(COMMA_SEPARATOR_REGEX));
        }
    }

    @Override
    public boolean matches(String triggerTarget) {
        return switch (type) {
            case CONTAINS -> values.stream().anyMatch(str -> StringUtils.containsIgnoreCase(triggerTarget, str));
            case ENDS_WITH -> values.stream().anyMatch(str -> StringUtils.endsWithIgnoreCase(triggerTarget, str));
            case REGEX -> patterns.stream().anyMatch(pt -> StringUtils.regex(triggerTarget, pt));
            case STARTS_WITH -> values.stream().anyMatch(str -> StringUtils.startsWithIgnoreCase(triggerTarget, str));
            case MATCHES -> values.stream().anyMatch(str -> StringUtils.matches(triggerTarget, str));
        };
    }

    public enum Type {
        CONTAINS,
        ENDS_WITH,
        REGEX,
        STARTS_WITH,
        MATCHES
    }

}
