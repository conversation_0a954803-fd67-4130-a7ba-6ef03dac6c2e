package com.groupbyinc.search.ssa.core.product;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.FIELD_MASK_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FIELD_MASK_PATHS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FIELD_MASK_PATHS_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.FIELD_MASK_TITLE;

@Schema(
    title = FIELD_MASK_TITLE,
    description = FIELD_MASK_DESCRIPTION
)
@Slf4j
@JsonDeserialize(using = FieldMask.Deserializer.class)
@Getter
@AllArgsConstructor
public class FieldMask {

    @Schema(
        description = FIELD_MASK_PATHS_FIELD_DESCRIPTION,
        example = FIELD_MASK_PATHS_FIELD_EXAMPLE
    )
    private final List<String> paths;

    public FieldMask(com.google.protobuf.FieldMask fieldMask) {
        this.paths = fieldMask.getPathsList();
    }

    public static class Deserializer extends JsonDeserializer<FieldMask> {

        @SuppressWarnings("unchecked")
        @Override
        public FieldMask deserialize(JsonParser p, DeserializationContext ctxt) {
            try {
                if (p.isExpectedStartArrayToken()) {
                    return new FieldMask((List<String>) p.readValueAs(List.class));
                }
                return new FieldMask(Arrays.stream(p.getValueAsString().split(",")).map(StringUtils::strip).toList());
            } catch (Exception e) {
                log.error("Failed to parse fieldMask, err: {}", e.getMessage());
                return new FieldMask(List.of());
            }
        }
    }
}
