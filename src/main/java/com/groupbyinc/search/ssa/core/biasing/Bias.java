package com.groupbyinc.search.ssa.core.biasing;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;
import static com.groupbyinc.search.ssa.util.StringUtils.COMMA;
import static org.apache.commons.lang3.StringUtils.contains;
import static org.apache.commons.lang3.StringUtils.split;

@Getter
@EqualsAndHashCode
public class Bias {

    private final String field;
    private String content;
    @JsonIgnore
    private String[] contentParsed;
    private final Strength strength;
    private final Float strengthValue;
    private final Type type;
    private NumericContent numericContent;

    @Builder
    @JsonCreator
    public Bias(@JsonProperty("field") String field,
                @JsonProperty("content") String content,
                @JsonProperty("strength") Strength strength,
                @JsonProperty("strengthValue") Float strengthValue,
                @JsonProperty("type") Type type,
                @JsonProperty("numericContent") NumericContent numericContent) {

        this.field = requireNonBlank(field, "Bias field", MANDATORY);
        this.strength = strength;
        this.strengthValue = strengthValue;

        if (type == null) {
            this.type = Type.TEXTUAL;
            this.content = content;
            this.contentParsed = parseContent(content);
        } else {
            this.type = type;
            switch (type) {
                case TEXTUAL -> {
                    this.content = content;
                    this.contentParsed = parseContent(content);
                }
                case NUMERIC -> this.numericContent = numericContent;
            }
        }
    }

    @Getter
    public enum Strength {
        ABSOLUTE_INCREASE(0.9F),
        STRONG_INCREASE(0.8F),
        MEDIUM_INCREASE(0.7F),
        WEAK_INCREASE(0.6F),
        LEAVE_UNCHANGED(0F),
        WEAK_DECREASE(-0.6F),
        MEDIUM_DECREASE(-0.7F),
        STRONG_DECREASE(-0.9F),
        ABSOLUTE_DECREASE(-1F);

        private final float value;

        Strength(float value) {
            this.value = value;
        }
    }

    public enum Type {
        TEXTUAL,
        NUMERIC
    }

    private static String[] parseContent(String content) {
        if (content == null) {
            return null;
        }

        return contains(content, COMMA) ? split(content, COMMA) : new String[] { content };
    }

}
