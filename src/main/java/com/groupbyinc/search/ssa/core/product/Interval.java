package com.groupbyinc.search.ssa.core.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.INTERVAL_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.INTERVAL_EXCLUSIVE_MAXIMUM_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.INTERVAL_EXCLUSIVE_MAXIMUM_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.INTERVAL_EXCLUSIVE_MINIMUM_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.INTERVAL_EXCLUSIVE_MINIMUM_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.INTERVAL_MAXIMUM_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.INTERVAL_MAXIMUM_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.INTERVAL_MINIMUM_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.INTERVAL_MINIMUM_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.INTERVAL_TITLE;

@Schema(
    title = INTERVAL_TITLE,
    description = INTERVAL_DESCRIPTION
)
@Getter
@AllArgsConstructor
public class Interval {

    @Schema(
        description = INTERVAL_MINIMUM_FIELD_DESCRIPTION,
        example = INTERVAL_MINIMUM_FIELD_EXAMPLE
    )
    private final Double minimum;

    @Schema(
        description = INTERVAL_EXCLUSIVE_MINIMUM_FIELD_DESCRIPTION,
        example = INTERVAL_EXCLUSIVE_MINIMUM_FIELD_EXAMPLE
    )
    private final Double exclusiveMinimum;

    @Schema(
        description = INTERVAL_MAXIMUM_FIELD_DESCRIPTION,
        example = INTERVAL_MAXIMUM_FIELD_EXAMPLE
    )
    private final Double maximum;

    @Schema(
        description = INTERVAL_EXCLUSIVE_MAXIMUM_FIELD_DESCRIPTION,
        example = INTERVAL_EXCLUSIVE_MAXIMUM_FIELD_EXAMPLE
    )
    private final Double exclusiveMaximum;

    public Interval(com.google.cloud.retail.v2.Interval interval) {
        this.maximum = interval.getMaximum();
        this.exclusiveMaximum = interval.getExclusiveMaximum();
        this.minimum = interval.getMinimum();
        this.exclusiveMinimum = interval.getExclusiveMinimum();
    }
}
