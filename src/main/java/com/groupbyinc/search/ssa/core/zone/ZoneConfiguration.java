package com.groupbyinc.search.ssa.core.zone;

import com.groupbyinc.proto.commandcenter.config.MessageType;
import com.groupbyinc.search.ssa.core.Config;
import lombok.Builder;

@Builder
public record ZoneConfiguration (

    /* ID of Zone */
    Integer id,

    /* ID of the area which this zone is associated with. */
    Integer areaId,

    /* A name for the zone, ideally human-readable. */
    String name,

    /* Zone "value - it is can be any data, HTML - code, usual text or etc. */
    String value,

    /* Type of this zone. */
    ZoneType zoneType,

    MessageType messageType) implements Config {

}
