package com.groupbyinc.search.ssa.core.navigation;

import com.groupbyinc.search.ssa.core.rule.PinnedRefinement;
import com.groupbyinc.search.ssa.core.rule.Refinement;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.List;
import java.util.Map;

import static com.groupbyinc.search.ssa.core.navigation.NavigationType.RANGE;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.VALUE;
import static com.groupbyinc.utils.validation.FieldRequirement.OPTIONAL;
import static com.groupbyinc.utils.validation.ValidationUtils.requireDefined;
import static com.groupbyinc.utils.validation.ValidationUtils.requireRange;

import static io.micronaut.core.util.CollectionUtils.isNotEmpty;
import static java.util.Comparator.comparingInt;
import static java.util.stream.Collectors.toMap;

/** Represents a further refinement on a navigation. */
@Getter
@EqualsAndHashCode
public class NavigationRefinement {

    private final Long count;
    private final Range range;
    private final String value;
    private final boolean pinned;
    private final NavigationType navigationType;

    /**
     * Creates an instance of a Refinement.
     *
     * @param navigationType Type of navigation this refinement belongs to.
     * @param count          Number of products that match the refinement.
     * @param range          Range of a refinement value, if the refinement is not a value type.
     * @param value          Value of refinement, if the refinement is not a range type.
     * @param pinned         Flag to confirm that refinement is pinned. Applicable to the value refinements only.
     */
    @JsonCreator
    @Builder(toBuilder = true)
    private NavigationRefinement(@JsonProperty("navigationType") NavigationType navigationType,
                                 @JsonProperty("count") Long count,
                                 @JsonProperty("range") Range range,
                                 @JsonProperty("value") String value,
                                 @JsonProperty("pinned") boolean pinned) {

        this.navigationType = requireDefined(navigationType, "Refinement navigation type");
        this.count = requireRange(count, "Refinement count", 0L, null, OPTIONAL);
        this.pinned = pinned;

        switch (navigationType) {
            case RANGE -> {
                this.range = requireDefined(range, "Refinement range");
                this.value = null;
            }
            case VALUE -> {
                this.range = null;
                this.value = value;
            }
            default -> throw new RuntimeException("Unhandled enum value %s".formatted(navigationType));
        }
    }

    /**
     * Static factory method for creating a value refinement with a count.
     *
     * @param value Value of the refinement.
     * @param count Number of products that match the refinement.
     *
     * @return Refinement instance.
     */
    public static NavigationRefinement valueRefinement(String value, long count, boolean pinned) {
        return new NavigationRefinement(VALUE, count, null, value, pinned);
    }

    /**
     * Static factory method for creating a value refinement.
     *
     * @param value Value of the refinement.
     *
     * @return Refinement instance.
     */
    public static NavigationRefinement valueRefinement(String value) {
        return new NavigationRefinement(VALUE, null, null, value, false);
    }

    /**
     * Static factory method for creating a range refinement with a count.
     *
     * @param range Range of the refinement.
     * @param count Number of products that match the refinement.
     *
     * @return Refinement instance.
     */
    public static NavigationRefinement rangeRefinement(Range range, long count) {
        return new NavigationRefinement(RANGE, count, range, null, false);
    }

    /**
     * Static factory method for creating a range refinement.
     *
     * @param range Range of the refinement.
     *
     * @return Refinement instance.
     */
    public static NavigationRefinement rangeRefinement(Range range) {
        return new NavigationRefinement(RANGE, null, range, null, false);
    }

    /**
     * Creates a unique key for a refinement based on its type, value, or range.
     * This key is used to identify matching refinements across similar Navigations.
     * For range refinements, values are formatted to 2 decimal places to ensure
     * consistent keys regardless of floating-point precision.
     *
     * @param refinement The refinement to create a key for
     * @return A string key that uniquely identifies this refinement
     */
    public static String getRefinementKey(NavigationRefinement refinement) {
        if (refinement.getNavigationType() == VALUE) {
            return refinement.getValue();
        } else {
            var range = refinement.getRange();
            var lowStr = range.low() != null ? "%.2f".formatted(range.low()) : null;
            var highStr = range.high() != null ? "%.2f".formatted(range.high()) : null;
            return lowStr + "-" + highStr;
        }
    }

    /**
     * Applies pinned refinements to a list of refinements by sorting them based on their priority.
     *
     * @param facetKey          The key of the facet to which the refinements belong.
     * @param refinements       The list of refinements to apply the pinned refinements to.
     * @param pinnedRefinements The list of pinned refinements to apply.
     */
    public static void applyPinnedRefinements(String facetKey,
                                              List<NavigationRefinement> refinements,
                                              List<PinnedRefinement> pinnedRefinements) {
        Map<String, Integer> map = pinnedRefinements
            .stream()
            .filter(pinnedRefinement -> pinnedRefinement.navigation().equals(facetKey))
            .filter(pinnedRefinement -> isNotEmpty(pinnedRefinement.refinements()))
            .flatMap(pinnedRefinement -> pinnedRefinement.refinements().stream())
            .collect(
                toMap(
                    Refinement::value,
                    Refinement::priority
                )
            );
        if (!map.isEmpty()) {
            refinements.sort(comparingInt(o -> map.getOrDefault(o.getValue(), Integer.MAX_VALUE)));
        }
    }

}
