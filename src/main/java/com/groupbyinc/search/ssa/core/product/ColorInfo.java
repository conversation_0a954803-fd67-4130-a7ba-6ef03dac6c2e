package com.groupbyinc.search.ssa.core.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

import static com.groupbyinc.search.ssa.util.SwaggerDocs.COLOR_INFO_COLORS_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.COLOR_INFO_COLORS_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.COLOR_INFO_COLOR_FAMILIES_FIELD_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.COLOR_INFO_COLOR_FAMILIES_FIELD_EXAMPLE;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.COLOR_INFO_DESCRIPTION;
import static com.groupbyinc.search.ssa.util.SwaggerDocs.COLOR_INFO_TITLE;

@Schema(
    title = COLOR_INFO_TITLE,
    description = COLOR_INFO_DESCRIPTION
)
@Getter
@AllArgsConstructor
public class ColorInfo {

    @Schema(
        description = COLOR_INFO_COLOR_FAMILIES_FIELD_DESCRIPTION,
        example = COLOR_INFO_COLOR_FAMILIES_FIELD_EXAMPLE
    )
    private final List<String> colorFamilies;

    @Schema(
        description = COLOR_INFO_COLORS_FIELD_DESCRIPTION,
        example = COLOR_INFO_COLORS_FIELD_EXAMPLE
    )
    private final List<String> colors;

    public ColorInfo(com.google.cloud.retail.v2.ColorInfo colorInfo) {
        this.colorFamilies = colorInfo.getColorFamiliesList();
        this.colors = colorInfo.getColorsList();
    }
}
