package com.groupbyinc.search.ssa.core.template;

import com.groupbyinc.search.ssa.core.rule.RuleConfiguration;
import com.groupbyinc.search.ssa.core.rule.RuleTemplate;
import com.groupbyinc.search.ssa.core.template.zone.TemplateZoneBase;
import com.groupbyinc.search.ssa.core.trigger.TriggerSet;

import io.micronaut.core.annotation.NonNull;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.groupbyinc.utils.validation.FieldRequirement.MANDATORY;
import static com.groupbyinc.utils.validation.FieldRequirement.OPTIONAL;
import static com.groupbyinc.utils.validation.ValidationUtils.requireNonBlank;

@Data
public class Template {

    /**
     * Name of template.
     */
    private final String name;

    /**
     * Name of triggered rule.
     */
    private final String ruleName;

    /**
     * Id of triggered rule.
     */
    private final Integer ruleId;

    /**
     * Trigger set that have been fired.
     */
    private TriggerSet triggerSet;

    /**
     * Zones associated with triggered rule and template name.
     */
    private final List<TemplateZoneBase> zones = new ArrayList<>();

    private Template(String name, String ruleName, Integer ruleId, TriggerSet triggerSet) {
        this.name = requireNonBlank(name, "Template name", OPTIONAL);
        this.ruleName = requireNonBlank(ruleName, "Template rule name", MANDATORY);
        this.ruleId = ruleId;
        this.triggerSet = triggerSet;
    }

    @NonNull
    public static Template ofRule(RuleConfiguration rule) {
        return new Template(
            Optional.ofNullable(rule.getTemplate()).map(RuleTemplate::name).orElse(null),
            rule.getName(),
            rule.getId(),
            null
        );
    }

    public void addZones(List<TemplateZoneBase> zones) {
        this.zones.addAll(zones);
    }

}
