package com.groupbyinc.search.ssa.beacon.client;

import io.micronaut.http.HttpResponse;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Header;
import io.micronaut.http.annotation.Post;
import io.micronaut.http.annotation.RequestAttribute;
import io.micronaut.http.client.annotation.Client;
import reactor.core.publisher.Flux;

import static com.groupbyinc.search.ssa.beacon.config.DirectSearchBeaconConfiguration.REQUEST_ATTRIBUTE_NAME;

import static io.micronaut.http.HttpHeaders.AUTHORIZATION;

@Client(id = "beacon")
public interface SearchBeaconClient {

    @Post("/wisdom/v2/internal/directSearch")
    Flux<HttpResponse<?>> sendDirectBeacon(
        @RequestAttribute(REQUEST_ATTRIBUTE_NAME) String merchandiserId,
        @Header(AUTHORIZATION) String authorization,
        @Body DirectSearchBeaconRequest<?> directSearchBeaconRequest
    );

}
