package com.groupbyinc.search.ssa.beacon.client;

import io.micronaut.http.HttpResponse;
import io.micronaut.http.MutableHttpRequest;
import io.micronaut.http.annotation.Filter;
import io.micronaut.http.client.annotation.Client;
import io.micronaut.http.filter.ClientFilterChain;
import io.micronaut.http.filter.HttpClientFilter;
import org.reactivestreams.Publisher;
import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.util.function.Consumer;

import static com.groupbyinc.search.ssa.beacon.config.DirectSearchBeaconConfiguration.REQUEST_ATTRIBUTE_NAME;
import static com.groupbyinc.search.ssa.util.Constants.GROUPBY_CUSTOMER_ID_HEADER;

/**
 * This component is used to replace the '__merchandiser-id__' template string in the request Uri with the actual
 * Merchandiser ID at runtime.
 * <p>
 * It exists because unfortunately there is no way to:
 * <ul>
 *     <li>
 *         Not set a base Url for a Micronaut {@link Client}, as this will cause an exception to be thrown.
 *     </li>
 *     <li>
 *         Have the subdomain replaced using the conventional {@code {param}} format, as the base Url must only contain
 *         valid characters.
 *     </li>
 * </ul>
 */
@Slf4j
@SuppressWarnings("all")
@Filter(serviceId = "beacon")
@Deprecated(since = "application moved to the 'Micronaut 4'")
public class MerchandiserSubDomainFilter implements HttpClientFilter {

    // WARNING!!! Not working with Micronaut 4, url have to be fully specified in consul.
    public static final String MERCHANDISER_ID_TEMPLATE = "__merchandiser-id__";

    @Override
    public Publisher<? extends HttpResponse<?>> doFilter(MutableHttpRequest<?> request, ClientFilterChain chain) {
        request.getAttribute(REQUEST_ATTRIBUTE_NAME).ifPresent(replaceMerchandiserTemplateInRequestUri(request));
        var merchandiserId = request.getAttribute(REQUEST_ATTRIBUTE_NAME, String.class).orElse("");
        if (merchandiserId.isEmpty()) {
            log.error("Empty merchandiserId. Issue extracting {} from request {}", REQUEST_ATTRIBUTE_NAME, request);
        }
        log.debug("Mutating header to set x-groupby-customer-id: {}", merchandiserId);
        request.getHeaders().add(GROUPBY_CUSTOMER_ID_HEADER, merchandiserId);
        return chain.proceed(request);
    }

    private static Consumer<Object> replaceMerchandiserTemplateInRequestUri(MutableHttpRequest<?> request) {
        return merchandiserId -> {
            var replacedUri = request.getUri().toString().replace(MERCHANDISER_ID_TEMPLATE, merchandiserId.toString());
            request.uri(URI.create(replacedUri));
        };
    }

}
