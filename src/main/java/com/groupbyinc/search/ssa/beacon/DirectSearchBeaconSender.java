package com.groupbyinc.search.ssa.beacon;

import com.groupbyinc.search.ssa.api.dto.RecordDto;
import com.groupbyinc.search.ssa.api.dto.SearchRequestDto;
import com.groupbyinc.search.ssa.api.dto.SearchResponseDto;
import com.groupbyinc.search.ssa.api.dto.TemplateDto;
import com.groupbyinc.search.ssa.api.dto.ZoneDto;
import com.groupbyinc.search.ssa.api.event.SearchSuccessEvent;
import com.groupbyinc.search.ssa.application.logging.LoggingContext;
import com.groupbyinc.search.ssa.beacon.client.DirectSearchBeaconRequest;
import com.groupbyinc.search.ssa.beacon.client.SearchBeaconClient;
import com.groupbyinc.search.ssa.beacon.config.DirectSearchBeaconConfiguration;
import com.groupbyinc.search.ssa.api.RequestContext;
import com.groupbyinc.search.ssa.features.FeaturesManager;
import com.groupbyinc.search.ssa.pubsub.PubsubWriterService;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Requires;
import io.micronaut.runtime.event.annotation.EventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.groupbyinc.search.ssa.api.utils.SearchUtils.getRequestPageCategories;
import static com.groupbyinc.search.ssa.beacon.config.DirectSearchBeaconConfiguration.EVENT_TYPE;
import static com.groupbyinc.search.ssa.metrics.opentelemetry.MeterRegistry.monotonicTime;
import static com.groupbyinc.search.ssa.util.StringUtils.EMPTY;

import static io.micronaut.core.util.CollectionUtils.isEmpty;
import static java.util.stream.Stream.concat;

/**
 * This adapter should not be in the API adapter package, but the beacons require the original JSON request, so we have
 * little choice but to do the beacon in the API. The beacons are tightly coupled to the search response by the legacy
 * design, so changes to either model need to be kept in sync. There is some limited logic in the conversion from core
 * → api, and it doesn't make sense to duplicate or try and push the logic down. It is, after all, just converting
 * some Java classes to JSON POJOs.
 */
@Slf4j
@Context
@RequiredArgsConstructor
@Requires(property = "direct-search-beacon.enabled", value = "true", defaultValue = "false")
public class DirectSearchBeaconSender {
    // https://github.com/groupby/beacons-assembler/blob/fea873b15dadced5ceb649d7db1a7e11d46bc4b9/internal/pipelines/models/partials/direct_search.go#L82-L85
    public static final List<String> META_FIELDS = List.of("sku", "productId");
    private static final String FAILED_MESSAGE_TEMPLATE = "Failed to send http direct search beacon. Took: {} ms.";
    private static final String SUCCESS_MESSAGE_TEMPLATE = "Success to send http direct search beacon. Took: {} ms.";

    private final FeaturesManager featuresManager;
    private final PubsubWriterService pubsubService;
    private final SearchBeaconClient searchBeaconClient;
    private final DirectSearchBeaconConfiguration directSearchBeaconConfiguration;

    @EventListener
    public void onSearchSuccessEvent(SearchSuccessEvent searchSuccessEvent) {
        LoggingContext.set(searchSuccessEvent.context(), EVENT_TYPE);

        var context = searchSuccessEvent.context();

        var searchEvent = searchEvent(context, searchSuccessEvent.response());

        if (isPubsubDirectSearchEnabled(context)) {
            boolean success = pubsubService.sendDirectBeacon(searchEvent);
            if (success) {
                return;
            }
        }

        sendHttpDirectSearch(context.getMerchandiser().merchandiserId(), searchEvent);
    }

    private DirectSearchBeaconRequest<SearchResponseDto> searchEvent(RequestContext context,
                                                                     SearchResponseDto response) {
        var updatedOriginalRequest = getUpdatedOriginalRequest(response);
        var recordsForBeacon = getRecords(response);

        var responseForBeacon = clearUnneededSearchBeaconFields(response, updatedOriginalRequest, recordsForBeacon);

        return new DirectSearchBeaconRequest<>(
            context.getMerchandiser().merchandiserId(),
            response.getId(),
            responseForBeacon,
            EVENT_TYPE,
            response.getExperiments()
        );
    }

    private List<RecordDto> getRecords(SearchResponseDto response) {

        //no sponsored records
        if (isEmpty(response.getSponsoredRecords())) {
            return cleanUpRecords(response.getRecords());
        }


        if (response.getOriginalRequest().getSponsoredRecords() == null) {
            log.warn("Field $.sponsoredRecords is null. Pushing sponsored to the start.");
            return cleanUpRecords(mergeSponsoredRecords(response));
        }

        var pageSize = response.getOriginalRequest().getPageSize();
        var normalizedPositions = response.getOriginalRequest().getSponsoredRecords().positionsNormalized(pageSize);

        if (isEmpty(normalizedPositions)) {
            log.warn("Field $.sponsoredRecords.positions is missing valid values. Pushing sponsored to the start.");
            mergeSponsoredRecords(response);
        }

        var sponsoredIds = new HashSet<String>();
        // cut sponsored up to positions[] size
        var sponsoredRecords = response.getSponsoredRecords().stream()
            .filter(record -> sponsoredIds.size() < normalizedPositions.size())
            .peek(record -> sponsoredIds.add(record.getPrimaryProductId()))
            .toList();

        // remove records that will be sponsored from a regular list
        var regularRecordsFiltered = response.getRecords().stream()
            .filter(record -> !sponsoredIds.contains(record.getPrimaryProductId()))
            .toList();

        // track insertion indexes
        var sponsoredIndex = 0;
        var regularIndex = 0;
        var recordsResult = new ArrayList<RecordDto>(pageSize);

        // walk through max page size as index and lookup for record that should be inserted at index position
        // a new list is preferred bc of slice shifting (closer to start indexes) and trimming
        for (int i = 0; i < pageSize; i++) {
            if (sponsoredIndex < sponsoredRecords.size() && normalizedPositions.contains(i)) {
                recordsResult.add(sponsoredRecords.get(sponsoredIndex));
                sponsoredIndex++;
                continue;
            }
            if (regularIndex < regularRecordsFiltered.size()) {
                recordsResult.add(regularRecordsFiltered.get(regularIndex));
                regularIndex++;
            }
        }
        recordsResult.trimToSize();

        return cleanUpRecords(recordsResult);
    }

    private List<RecordDto> cleanUpRecords(List<RecordDto> records) {
        if (records == null) {
            return List.of();
        }

        return records.stream().map(r ->
            new RecordDto(
                r.getId(),
                r.getUrl(),
                r.getTitle(),
                r.getCollection(),
                cleanMetadata(r.getMetadata()),
                null,
                null,
                null,
                r.getPrimaryProductId(),
                r.getLabels()
            )
        ).toList();
    }

    private Map<String, Object> cleanMetadata(Map<String, Object> meta) {
        if (meta == null) {
            return Map.of();
        }
        return meta.entrySet().stream()
            .filter(e -> META_FIELDS.contains(e.getKey()))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    private SearchRequestDto getUpdatedOriginalRequest(SearchResponseDto response) {
        var pageCategories = getPageCategories(response);

        return response
            .getOriginalRequest()
            .toBuilder()
            .pageCategories(pageCategories)
            .build();
    }

    private List<String> getPageCategories(SearchResponseDto response) {
        if (ObjectUtils.isEmpty(response.getOriginalRequest().getQuery())) {
            return getRequestPageCategories(response.getOriginalRequest());
        }

        return response.getOriginalRequest().getPageCategories();
    }

    private List<RecordDto> mergeSponsoredRecords(SearchResponseDto searchResponse) {
        return concat(searchResponse.getSponsoredRecords().stream(), searchResponse.getRecords().stream())
            .limit(searchResponse.getOriginalRequest().getPageSize())
            .toList();
    }

    private boolean isPubsubDirectSearchEnabled(RequestContext context) {
        return featuresManager.getBooleanFlagConfiguration(
            context.getLdContext(),
            FeaturesManager.FeatureFlagNames.ENABLE_PUBSUB_DIRECT_SEARCH
        );
    }

    private void sendHttpDirectSearch(String merchandiserId, DirectSearchBeaconRequest<SearchResponseDto> request) {
        var beaconStartTime = monotonicTime();

        searchBeaconClient
            .sendDirectBeacon(merchandiserId, directSearchBeaconConfiguration.getAuthToken(), request)
            .doOnNext(r -> log.debug(SUCCESS_MESSAGE_TEMPLATE, monotonicTime() - beaconStartTime))
            .doOnError(t -> log.warn(FAILED_MESSAGE_TEMPLATE, monotonicTime() - beaconStartTime, t))
            .subscribe();
    }

    private SearchResponseDto clearUnneededSearchBeaconFields(SearchResponseDto response,
                                                              SearchRequestDto originalRequest,
                                                              List<RecordDto> records) {
        var template = response.getTemplate();

        var cleanedTemplate = template != null
            ? new TemplateDto(template.getName(), template.getRuleName(), template.getRuleId(), null)
            : null;

        if (template != null && !isEmpty(template.getZones())) {
            var cleanedZones = template.getZones().stream().map(
                zone -> ZoneDto.builder()
                    .type(zone.type())
                    .name(zone.name())
                    .html(EMPTY)
                    .richContent(EMPTY)
                    .content(EMPTY)
                    .build()
            ).toList();

            cleanedTemplate.getZones().addAll(cleanedZones);
        }

        return response
            .toBuilder()
            .originalQuery(response.getOriginalRequest().getQuery())
            .originalRequest(originalRequest)
            .records(records)
            .sponsoredRecords(List.of())
            .debug(null)
            .template(cleanedTemplate)
            .warnings(List.of())
            .build();
    }

}
