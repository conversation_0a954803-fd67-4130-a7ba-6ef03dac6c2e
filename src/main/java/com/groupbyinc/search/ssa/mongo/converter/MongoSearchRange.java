package com.groupbyinc.search.ssa.mongo.converter;

import lombok.Getter;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.GTE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.LTE;

@Getter
public class MongoSearchRange {
    private final String key;
    private final Object value;

    private MongoSearchRange(String key, Object value) {
        this.key = key;
        this.value = value;
    }

    public static MongoSearchRange gte(Object value) {
        return new MongoSearchRange(GTE, value);
    }

    public static MongoSearchRange lte(Object value) {
        return new MongoSearchRange(LTE, value);
    }
}
