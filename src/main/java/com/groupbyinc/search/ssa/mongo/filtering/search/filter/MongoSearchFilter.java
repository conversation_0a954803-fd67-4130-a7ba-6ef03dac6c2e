package com.groupbyinc.search.ssa.mongo.filtering.search.filter;

import com.groupbyinc.search.ssa.application.core.search.filtering.filter.Filter;

import org.bson.Document;

import javax.annotation.Nonnull;
import java.util.ArrayList;
import java.util.List;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.COMPOUND;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.FILTER;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.SHOULD;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.getInternalFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.hasInternalFilter;

/**
 * Used to accumulate all filters applied to the mongo search request.
 *
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/compound/">Compound operator Doc</a>
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/compound/#mongodb-data-filter">Filter clause Doc</a>
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/equals/">Equals operator Doc</a>
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/in/">In operator Doc</a>
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/range/">Range operator Doc</a>
 */
public class MongoSearchFilter extends Filter<Document> {

    public MongoSearchFilter() {
        super(new ArrayList<>(), new ArrayList<>());
    }

    /**
     * Transforms stored "and" and "or" expressions into a retail search filter string.
     * This method combines expressions with logical "AND" and "OR" conditions to construct
     * a query that can be used to filter search results in a retail context.
     * <p>
     * Process:
     * 1. All "and" expressions are joined using the logical "AND".
     * 2. All "or" expressions are individually wrapped with parentheses and then joined using the logical ""OR.
     * <p>
     * Finally, if both "and" and "or" expressions joined using the logical "AND".
     * This method ensures that the final filter string is properly formatted for use
     * in search queries, applying all specified filters to refine search results.
     * <p>
     * Example:
     * Input data:
     *. <pre>{@code
     *
     * Selected refinements from request:
     * {
     *   "refinements": [
     *     {
     *       "navigationName": "price",
     *       "type": "Range",
     *       "low": 100,
     *       "high": 999998.99,
     *       "or": true
     *     }
     *   ]
     * }
     *
     * }</pre>
     * <p>
     * Created filter:
     * <pre>{@code
     * {
     *  "filter": [
     *    {
     *     "range": {
     *       "gte": 100.0,
     *       "lt": 999998.99,
     *       "path": [
     *         "priceInfo.price",
     *         "indexables.priceInfo.price"
     *       ]
     *     }
     *   }
     *  ]
     * }
     * }</pre>
     *
     * @return A filter formatted for mongo search.
     */
    @Nonnull
    @Override
    public Document toFilter() {
        List<Document> filters = new ArrayList<>();

        if (!ands.isEmpty()) {
            addAndFilters(filters);
        }

        if (!ors.isEmpty()) {
            addOrFilters(filters);
        }

        return new Document(FILTER, filters);
    }

    private void addAndFilters(List<Document> filters) {
        var andFilters = new ArrayList<Document>();
        ands.forEach(and -> {
            if (and instanceof MongoSearchFilter mf) {
                var filter = mf.toFilter().getList(FILTER, Document.class);
                if (filter.size() == 1) {
                    // Special case when we can combine two 'filter' operators, because technically
                    // all of them it is 'AND' conditions.
                    if (hasInternalFilter(filter.getFirst())) {
                        andFilters.addAll(getInternalFilter(filter.getFirst()));
                    } else {
                        andFilters.add(filter.getFirst());
                    }
                } else {
                    andFilters.addAll(filter);
                }
            } else {
                andFilters.add(and.toFilter());
            }
        });

        addToFilters(filters, andFilters, FILTER);
    }

    private void addOrFilters(List<Document> filters) {
        var orFilters = new ArrayList<Document>();
        ors.forEach(or -> {
            if (or instanceof MongoSearchFilter mf) {
                orFilters.addAll(mf.toFilter().getList(FILTER, Document.class));
            } else {
                orFilters.add(or.toFilter());
            }
        });

        addToFilters(filters, orFilters, SHOULD);
    }

    private void addToFilters(List<Document> filters, List<Document> filtersToAdd, String operator) {
        if (filtersToAdd.size() == 1) {
            filters.add(filtersToAdd.getFirst());
        } else {
            filters.add(
                new Document(
                    COMPOUND,
                    new Document(
                        operator,
                        filtersToAdd
                    )
                )
            );
        }
    }

}
