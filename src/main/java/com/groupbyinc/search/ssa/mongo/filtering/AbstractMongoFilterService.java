package com.groupbyinc.search.ssa.mongo.filtering;

import com.groupbyinc.search.ssa.antlr.MongoRawFilterFieldsCollectorVisitor;
import com.groupbyinc.search.ssa.application.core.search.filtering.FilterServiceBase;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.filter.RangeFilter;
import com.groupbyinc.search.ssa.core.filter.ValueFilter;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;

import io.micronaut.core.annotation.NonNull;
import org.bson.Document;

import java.util.HashSet;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.LOCAL_INVENTORIES_PLACE_ID_PATH;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.hasRawFilters;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.parseRawFilter;
import static com.groupbyinc.search.ssa.util.AttributeUtils.parseInventoryNavigations;
import static com.groupbyinc.search.ssa.util.AttributeUtils.transformField;
import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultList;

public abstract class AbstractMongoFilterService extends FilterServiceBase<Document> {

    @NonNull
    public static Set<String> getUsedFields(@NonNull SearchParameters searchParameters) {
        var usedFields = new HashSet<String>();

        addFieldsFromFilters(searchParameters, usedFields);
        addFieldsFromRawFilters(searchParameters, usedFields);

        return usedFields;
    }

    private static void addFieldsFromFilters(@NonNull SearchParameters searchParameters, @NonNull Set<String> fields) {
        Function<String, Set<String>> fieldToPath = getFieldToPathFunction(searchParameters);

        searchParameters
            .getAttributeFilters()
            .stream()
            .flatMap(attributeFilter ->
                Stream.concat(
                    notNullOrDefaultList(attributeFilter.rangeFilters()).stream().map(RangeFilter::getField),
                    notNullOrDefaultList(attributeFilter.valueFilters()).stream().map(ValueFilter::getField)
                )
            )
            .map(fieldToPath)
            .forEach(fields::addAll);

        searchParameters
            .getRefinements()
            .stream()
            .map(SelectedRefinement::getField)
            .map(fieldToPath)
            .forEach(fields::addAll);
    }

    private static Function<String, Set<String>> getFieldToPathFunction(@NonNull SearchParameters searchParameters) {
        var attributeConfig =  searchParameters.getMerchandisingConfiguration().attributeConfigurations();
        return field -> {
            var inventoryMatcher = parseInventoryNavigations(field);

            return inventoryMatcher
                .map(matchedField -> Set.of(
                    transformField(matchedField.group(2), attributeConfig, true),
                    LOCAL_INVENTORIES_PLACE_ID_PATH
                ))
                .orElse(Set.of(transformField(field, attributeConfig, false)));
        };
    }

    private static void addFieldsFromRawFilters(@NonNull SearchParameters searchParameters,
                                                @NonNull Set<String> fields) {
        if (hasRawFilters(searchParameters)) {
            var rawFilter = mergePreAndSiteFilters(
                searchParameters.getPreFilter(),
                searchParameters.getMerchandisingConfiguration().siteFilter()
            );

            if (rawFilter.isPresent()) {
                var filterVisitor = new MongoRawFilterFieldsCollectorVisitor(
                    searchParameters.getMerchandisingConfiguration().attributeConfigurations()
                );

                var parsed = parseRawFilter(
                    rawFilter.get(),
                    filterVisitor
                );
                fields.addAll(parsed);
            }
        }
    }

}
