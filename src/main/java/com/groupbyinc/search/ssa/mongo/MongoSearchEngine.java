package com.groupbyinc.search.ssa.mongo;

import com.groupbyinc.search.ssa.api.error.ProcessingException;
import com.groupbyinc.search.ssa.application.cache.Cache;
import com.groupbyinc.search.ssa.application.cache.CacheOperations;
import com.groupbyinc.search.ssa.application.cache.CacheStoredMetadata;
import com.groupbyinc.search.ssa.application.cache.StoredObject;
import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngine;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.filter.ProductIdFilter;
import com.groupbyinc.search.ssa.metrics.opentelemetry.interceptors.ApiLatencyMetricsCollector;
import com.groupbyinc.search.ssa.mongo.converter.MongoRequestConverter;
import com.groupbyinc.search.ssa.mongo.converter.MongoResponseConverter;
import com.groupbyinc.search.ssa.mongo.facet.MongoFacetProvider;
import com.groupbyinc.search.ssa.mongo.filtering.search.MongoSearchFilterService;
import com.groupbyinc.search.ssa.mongo.request.MongoPipeline;
import com.groupbyinc.search.ssa.mongo.request.PipelineType;
import com.groupbyinc.search.ssa.mongo.response.MongoQueryResponse;
import com.groupbyinc.search.ssa.mongo.response.MongoResponse;
import com.groupbyinc.search.ssa.mongo.settings.MongoSettingsStorage;
import com.groupbyinc.search.ssa.redis.CacheResponse;
import com.groupbyinc.search.ssa.redis.CacheSource;
import com.groupbyinc.search.ssa.redis.key.mongo.MongoCacheConfig;
import com.groupbyinc.search.ssa.redis.key.mongo.MongoCacheKeyGenerator;
import com.groupbyinc.search.ssa.util.debug.MongoDebugInfo;

import com.fasterxml.jackson.core.type.TypeReference;
import com.mongodb.MongoExecutionTimeoutException;
import com.mongodb.client.MongoClient;
import io.micronaut.context.annotation.Context;
import io.micronaut.core.propagation.PropagatedContext;
import io.micronaut.core.util.CollectionUtils;
import jakarta.inject.Named;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import javax.annotation.Nonnull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.application.cache.BaseCacheOperations.getResultsFromCacheResponse;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.SEARCH_COLLECTION_SUFFIX;
import static com.groupbyinc.search.ssa.mongo.facet.MongoFacetConverter.getActiveMultiSelectFields;
import static com.groupbyinc.search.ssa.util.debug.DebugHelpers.getDebugInfoOrDefault;
import static com.groupbyinc.search.ssa.util.debug.DebugHelpers.getDebugInfoKey;
import static com.groupbyinc.search.ssa.util.debug.DebugHelpers.saveMongoDynamicFacetRequestToDebug;
import static com.groupbyinc.search.ssa.util.debug.DebugHelpers.saveMongoFacetRequestsToDebug;
import static com.groupbyinc.search.ssa.util.debug.DebugHelpers.saveMongoFacetResponsesToDebug;
import static com.groupbyinc.search.ssa.util.debug.DebugHelpers.saveMongoSearchRequestToDebug;
import static com.groupbyinc.search.ssa.util.debug.DebugHelpers.saveMongoSearchResponseToDebug;
import static com.groupbyinc.search.ssa.util.debug.MongoDebugInfo.MONGO_DEBUG_INFO_SUFFIX;

import static java.util.concurrent.CompletableFuture.supplyAsync;
import static java.util.concurrent.TimeUnit.SECONDS;

@Slf4j
@Context
@Named("mongoSearchEngine")
public class MongoSearchEngine implements SearchEngine {

    private final Cache mongoCache;
    private final MongoClient mongoClient;
    private final ExecutorService executorService;
    private final CacheOperations cacheOperations;
    private final MongoFacetProvider mongoFacetProvider;
    private final MongoSettingsStorage mongoSettingsStorage;
    private final MongoRequestConverter mongoRequestConverter;
    private final MongoResponseConverter mongoResponseConverter;
    private final MongoCacheKeyGenerator mongoCacheKeyGenerator;
    private final MongoSearchFilterService mongoSearchFilterService;


    public MongoSearchEngine(MongoClient mongoClient,
                             @Named("mongo") Cache mongoCache,
                             CacheOperations cacheOperations,
                             MongoFacetProvider mongoFacetProvider,
                             MongoSettingsStorage mongoSettingsStorage,
                             MongoRequestConverter mongoRequestConverter,
                             MongoResponseConverter mongoResponseConverter,
                             MongoCacheKeyGenerator mongoCacheKeyGenerator,
                             MongoSearchFilterService mongoSearchFilterService) {
        this.mongoCache = mongoCache;
        this.mongoClient = mongoClient;
        this.cacheOperations = cacheOperations;
        this.mongoFacetProvider = mongoFacetProvider;
        this.mongoSettingsStorage = mongoSettingsStorage;
        this.mongoRequestConverter = mongoRequestConverter;
        this.mongoResponseConverter = mongoResponseConverter;
        this.mongoCacheKeyGenerator = mongoCacheKeyGenerator;
        this.mongoSearchFilterService = mongoSearchFilterService;
        this.executorService = Executors.newVirtualThreadPerTaskExecutor();
    }

    @Nonnull
    @Override
    @ApiLatencyMetricsCollector
    public SearchResults search(@Nonnull SearchParameters searchParameters) {
        var fromCache = getFromCache(searchParameters);

        if (fromCache.response().isPresent()) {
            return getResultsFromCacheResponse(
                fromCache,
                fromCache.response().get().metadata().mongoDebugInfo(),
                getDebugInfoKey(MONGO_DEBUG_INFO_SUFFIX)
            );
        }

        var result = getResultsFromMongo(searchParameters);

        cacheOperations.saveToCache(
            fromCache,
            CacheSource.MONGO,
            new StoredObject<>(
                CacheStoredMetadata.mongo(getDebugInfoOrDefault(MONGO_DEBUG_INFO_SUFFIX, new MongoDebugInfo())),
                result
            )
        );

        return result;
    }

    private CacheResponse<MongoCacheConfig, StoredObject<SearchResults>> getFromCache(
        SearchParameters searchParameters
    ) {
        var mongoSettings = mongoSettingsStorage.getMongoSettings();

        return cacheOperations.getFromCache(
            mongoCache,
            mongoCacheKeyGenerator,
            searchParameters,
            new TypeReference<>() {},
            mongoSettings.cacheConfig(),
            CacheSource.MONGO
        );
    }

    private SearchResults getResultsFromMongo(SearchParameters searchParameters) {
        MongoQueryResponse mainSearchResponse;
        // Map to hold product IDs from PN expanded Mongo search results for each multi-select navigation field
        Map<String, Set<String>> multiSelectNavScopedProductIds = new HashMap<>();

        var mongoQuery = mongoRequestConverter.buildMongoQuery(searchParameters);
        saveMongoSearchRequestToDebug(mongoQuery, log);

        List<MongoPipeline> standardFacetPipelines;
        SearchParameters facetSearchParameters;
        Document baseFilterForFacets; // Filter derived from the above

        // For PartNumber Expanded Search we need to perform parallel search request to get product IDs
        // for each active multi-select navigation or dynamic Facet.
        if (Boolean.TRUE.equals(searchParameters.getPartNumberSearchEnabled())
            && searchParameters.isPartNumberExpansionEnabled()) {
            var activeMultiSelectFields = getActiveMultiSelectFields(searchParameters);

            // Prepare and run Search pipelines (PN Primary Search + Product ID Lookups with excluded nav refinements)
            List<MongoPipeline> pnExpandedSearchPipelines =
                buildMongoPipelines(mongoQuery.searchPipeline(), searchParameters, activeMultiSelectFields);
            List<MongoQueryResponse> pnExpandedSearchResponses = searchFromMongoInParallel(pnExpandedSearchPipelines);

            // Process responses to set mainSearchResponse and multiSelectNavScopedProductIds
            mainSearchResponse = pnExpandedSearchResponses.stream()
                .filter(resp -> resp.request().pipelineType() == PipelineType.SEARCH)
                .findFirst()
                .orElseThrow(() -> new ProcessingException("Primary search pipeline not found in responses"));

            if (mainSearchResponse.failed()) {
                throw new ProcessingException(mainSearchResponse.reason());
            }
            saveMongoSearchResponseToDebug(mainSearchResponse, log);

            var pnMainProductIds = mongoResponseConverter.extractProductIds(mainSearchResponse.response());

            for (MongoQueryResponse resp : pnExpandedSearchResponses) {
                if (resp.request().pipelineType() == PipelineType.PRODUCT_ID_LOOKUP) {
                    String excludedNavField = resp.request().excludedNavigation();
                    if (excludedNavField != null) {
                        if (!resp.failed()) {
                            var extractedProductIds = mongoResponseConverter.extractProductIds(resp.response());
                            multiSelectNavScopedProductIds.put(excludedNavField, extractedProductIds);
                        } else {
                            log.warn("PartNumber Expanded Product ID lookup failed for navigation field: {}", excludedNavField);
                            multiSelectNavScopedProductIds.put(excludedNavField, pnMainProductIds);
                        }
                    }
                }
            }

            // Define search params for non-multi-select standard facets and all dynamic facets
            var originalProductIdFilter = searchParameters.getProductIdFilter();
            facetSearchParameters = searchParameters.toBuilder()
                .productIdFilter(
                    originalProductIdFilter == null
                        ? new ProductIdFilter(new ArrayList<>(pnMainProductIds), null)
                        : originalProductIdFilter.withAdditionalIncludedIds(pnMainProductIds)
                )
                .build();

        } else {
            mainSearchResponse = searchFromMongo(mongoQuery.searchPipeline());
            saveMongoSearchResponseToDebug(mainSearchResponse, log);

            if (mainSearchResponse.failed()) {
                throw new ProcessingException(mainSearchResponse.reason());
            }

            facetSearchParameters = searchParameters;
        }

        // Generate standardFacetPipelines
        baseFilterForFacets = mongoSearchFilterService.createFilter(facetSearchParameters);
        standardFacetPipelines = mongoFacetProvider.createFacetsSearchPipelinesForRefinements(
            facetSearchParameters,
            baseFilterForFacets,
            multiSelectNavScopedProductIds
        );

        // Prepare standardFacetPipelines for mongoQuery
        mongoQuery.facetPipelines().addAll(standardFacetPipelines);
        saveMongoFacetRequestsToDebug(standardFacetPipelines, log);

        List<MongoPipeline> allFacetPipelines = new ArrayList<>(standardFacetPipelines);
        var dynamicFacetsToRequest = mongoFacetProvider.extractDynamicFacetKeys(mainSearchResponse.response());

        if (!dynamicFacetsToRequest.isEmpty()) {
            var dynamicFacetsPipeline = mongoFacetProvider.createDynamicFacetsSearchPipeline(
                facetSearchParameters,
                dynamicFacetsToRequest,
                baseFilterForFacets
            );
            saveMongoDynamicFacetRequestToDebug(dynamicFacetsPipeline.pipeline(), log);
            allFacetPipelines.add(dynamicFacetsPipeline);
        }

        var facetResults = searchFromMongoInParallel(allFacetPipelines);
        saveMongoFacetResponsesToDebug(facetResults, log);

        reorderDynamicFacets(facetResults, mainSearchResponse);

        var allResults = new ArrayList<>(facetResults);
        allResults.add(mainSearchResponse);

        return mongoResponseConverter.convert(searchParameters, new MongoResponse(mongoQuery, allResults));
    }

    private List<MongoPipeline> buildMongoPipelines(MongoPipeline mainSearchPipeline,
                                                    SearchParameters searchParameters,
                                                    Set<String> activeMultiSelectFields) {
        List<MongoPipeline> pnExpandedSearchPipelines = new ArrayList<>();
        pnExpandedSearchPipelines.add(mainSearchPipeline);

        for (String navFieldToExclude : activeMultiSelectFields) {
            var searchParamsForProductLookup =
                getSearchParamsWithExcludedRefinement(searchParameters, navFieldToExclude);
            var productIdsLookupQuery = mongoRequestConverter.buildMongoQuery(searchParamsForProductLookup);
            pnExpandedSearchPipelines.add(
                new MongoPipeline(
                    productIdsLookupQuery.searchPipeline().pipeline(),
                    PipelineType.PRODUCT_ID_LOOKUP,
                    navFieldToExclude
                )
            );
        }
        return pnExpandedSearchPipelines;
    }

    private List<MongoQueryResponse> searchFromMongoInParallel(List<MongoPipeline> pipelines) {
        List<CompletableFuture<MongoQueryResponse>> tasks = pipelines
            .stream()
            .map(request -> {
                Supplier<MongoQueryResponse> task = () -> searchFromMongo(request);
                return supplyAsync(PropagatedContext.wrapCurrent(task), executorService);
            })
            .toList();

        CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0])).join();

        return tasks.stream().map(CompletableFuture::join).collect(Collectors.toList());
    }

    private MongoQueryResponse searchFromMongo(MongoPipeline pipeline) {
        var context = getRequestContext();

        var tenant = context.getMerchandiser().merchandiserId();
        var collection = context.getCollection() + SEARCH_COLLECTION_SUFFIX;

        var searchResults = new ArrayList<Document>();
        try {
            var query = mongoClient
                .getDatabase(tenant)
                .getCollection(collection)
                .aggregate(pipeline.pipeline());

            if (pipeline.pipelineType().isFacetRequest()) {
                query.maxTime(mongoSettingsStorage.getSearchMetaTimeoutInSeconds(), SECONDS);
            } else {
                query.maxTime(mongoSettingsStorage.getSearchTimeoutInSeconds(), SECONDS);
            }

            query.into(searchResults);
        } catch (MongoExecutionTimeoutException e) {
            return handleException(
                collection,
                pipeline,
                "Timeout processing %s request for collection %s.",
                e
            );
        } catch (Exception e) {
            return handleException(
                collection,
                pipeline,
                "Error while processing %s request for collection %s.",
                e
            );
        }
        return new MongoQueryResponse(
            CollectionUtils.isNotEmpty(searchResults) ? searchResults.getFirst() : new Document(),
            pipeline
        );
    }

    private MongoQueryResponse handleException(String collection,
                                               MongoPipeline pipeline,
                                               String messageTemplate,
                                               Throwable e) {
        var errorMessage = messageTemplate.formatted(pipeline.pipelineType(), collection);
        log.error("{} Pipeline: {}", errorMessage, pipeline, e);
        getRequestContext().addWarning(errorMessage);
        return new MongoQueryResponse(new Document(), pipeline, true, errorMessage);
    }

    private void reorderDynamicFacets(List<MongoQueryResponse> facetResults, MongoQueryResponse mainSearchResponse) {
        facetResults.stream()
            .filter(facetResult ->
                facetResult.request().pipelineType().isDynamicFacetRequest() && !facetResult.failed()
            )
            .findFirst()
            .ifPresent(facetResult -> mongoFacetProvider.setTopDynamicNavigations(
                mainSearchResponse.response(),
                facetResult.response()
            ));
    }

    public static SearchParameters getSearchParamsWithExcludedRefinement(SearchParameters originalParams,
                                                                         String navFieldToExcludeRefinements) {
        var filteredRefinements = originalParams.getRefinements().stream()
            .filter(r -> !r.getField().equals(navFieldToExcludeRefinements))
            .collect(Collectors.toList());
        return originalParams.toBuilder()
            .refinements(filteredRefinements)
            .build();
    }

}
