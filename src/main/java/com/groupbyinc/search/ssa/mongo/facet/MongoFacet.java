package com.groupbyinc.search.ssa.mongo.facet;

import com.groupbyinc.search.ssa.mongo.converter.MongoCompoundableSearch;

import org.bson.Document;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.FACETS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.OPERATOR;

public class MongoFacet {

    private final Document facet;
    private MongoCompoundableSearch operator;

    public MongoFacet() {
        this.facet = new Document();
        this.operator = null;
    }

    public void appendFacets(Document facets) {
        getFacets().putAll(facets);
    }

    public MongoCompoundableSearch operator() {
        if (this.operator == null) {
            this.operator = new MongoCompoundableSearch();
        }
        return this.operator.compound();
    }

    public Document toDocument() {
        var doc = new Document(this.facet);

        if (this.operator != null) {
            doc.append(OPERATOR, this.operator.toDocument());
        }

        return doc;
    }

    public String toJson() {
        return toDocument().toJson();
    }

    @Override
    public String toString() {
        return toJson();
    }

    private Document getFacets() {
        return (Document) this.facet.computeIfAbsent(
            FACETS,
            s -> new Document()
        );
    }

}
