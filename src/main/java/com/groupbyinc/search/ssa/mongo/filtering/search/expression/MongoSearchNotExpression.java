package com.groupbyinc.search.ssa.mongo.filtering.search.expression;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.Expression;
import com.groupbyinc.search.ssa.application.core.search.filtering.expression.NotExpression;

import io.micronaut.core.annotation.NonNull;
import org.bson.Document;

import javax.annotation.Nonnull;

import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.wrapWithMustNotBasedOnInternalStructure;

/**
 * Represents a negation expression.
 * <p>
 * Technically, any expression can be negated.
 *
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/compound/#mongodb-data-mustNot">Must-Not Doc</a>
 */
public class MongoSearchNotExpression extends NotExpression<Document> {

    /**
     * Creates a negated version of the expression.
     *
     * @param expression to be negated.
     */
    public MongoSearchNotExpression(@NonNull Expression<Document> expression) {
        super(expression);
    }

    /**
     * Create a mongo filter representation of this expression.
     * Example:
     *
     * <pre>{@code
     * {
     *   "compound": {
     *     "mustNot": [
     *       {
     *         "in": {
     *           "value": [
     *             "example",
     *             "example_1"
     *           ],
     *           "path": "indexables.attribute"
     *         }
     *       }
     *     ]
     *   }
     * }
     *
     * }</pre>
     *
     * Note: provided example contains "in" operator, but technically any expression may be negotiated/
     *
     * @return mongo filter object.
     */
    @Nonnull
    @Override
    public Document toFilter() {
        return wrapWithMustNotBasedOnInternalStructure(expression.toFilter());
    }

}
