package com.groupbyinc.search.ssa.mongo.converter;

import org.bson.Document;

import java.util.ArrayList;
import java.util.List;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.COMPOUND;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.FILTER;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.MINIMUM_SHOULD_MATCH;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.MUST;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.MUST_NOT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.SCORE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.SHOULD;

import static com.google.common.collect.Lists.newArrayList;

public class MongoCompoundableSearch {

    private final Document search;
    private MongoCompoundableSearch compound;

    public MongoCompoundableSearch() {
        this(new Document());
    }

    public MongoCompoundableSearch(Document search) {
        this.search = search;
    }

    public MongoCompoundableSearch append(Document document) {
        this.search.putAll(document);
        return this;
    }

    public MongoCompoundableSearch append(String key, Object value) {
        this.search.append(key, value);
        return this;
    }

    public MongoCompoundableSearch appendMust(MongoCompoundableSearch condition) {
        return appendMust(condition.toDocument());
    }

    public MongoCompoundableSearch appendMust(Document condition) {
        must().add(condition);
        return this;
    }

    public MongoCompoundableSearch appendMust(List<Document> conditions) {
        must().addAll(conditions);
        return this;
    }

    public MongoCompoundableSearch appendMustNot(MongoCompoundableSearch condition) {
        return appendMustNot(condition.toDocument());
    }

    public MongoCompoundableSearch appendMustNot(Document condition) {
        mustNot().add(condition);
        return this;
    }

    public MongoCompoundableSearch appendMustNot(List<Document> conditions) {
        mustNot().addAll(conditions);
        return this;
    }

    public MongoCompoundableSearch appendShould(MongoCompoundableSearch condition) {
        return appendShould(condition.toDocument());
    }

    public MongoCompoundableSearch appendShould(List<Document> conditions) {
        should().addAll(conditions);
        return this;
    }

    public MongoCompoundableSearch appendShould(Document condition) {
        should().add(condition);
        return this;
    }

    public MongoCompoundableSearch appendFilter(Document condition) {
        filter().add(condition);
        return this;
    }

    public MongoCompoundableSearch appendFilters(List<Document> conditions) {
        filter().addAll(conditions);
        return this;
    }

    public MongoCompoundableSearch compound() {
        // avoid potential cycles by separating this out into its own field
        if (compound == null) {
            compound = new MongoCompoundableSearch();
        }
        return compound;
    }

    public <T extends Number> MongoCompoundableSearch scoreConstant(T value) {
        return score(new Document("constant", new Document("value", value)));
    }

    public MongoCompoundableSearch scoreBoost(Double value) {
        return score(new Document("boost", new Document("value", value)));
    }

    public MongoCompoundableSearch scoreMultiply(String defaultPath, Double defaultValue, Double multiplier) {
        return score(new Document("function", new Document("multiply", newArrayList(
            new Document("path", new Document("value", defaultPath).append("undefined", defaultValue)),
            new Document("score", "relevance"),
            new Document("constant", multiplier)
        ))));
    }

    public MongoCompoundableSearch score(Document docScore) {
        getConditionOrDefault(SCORE, docScore);
        return this;
    }

    public MongoCompoundableSearch minimumShouldMatch(Integer value) {
        search.put(MINIMUM_SHOULD_MATCH, value);
        return this;
    }

    public MongoCompoundableSearch copy() {
        MongoCompoundableSearch copied = new MongoCompoundableSearch(Document.parse(this.search.toJson()));

        if (this.compound != null) {
            copied.compound = new MongoCompoundableSearch(Document.parse(this.compound.toJson()));
        }

        return copied;
    }

    public Document toDocument() {
        if (this.compound == null) {
            return this.search;
        }

        var outDoc = new Document();

        outDoc.putAll(this.search);

        outDoc.put(COMPOUND, this.compound.toDocument());

        return outDoc;
    }

    public String toJson() {
        return toDocument().toJson();
    }

    @Override
    public String toString() {
        return toJson();
    }

    private List<Document> filter() {
        return getConditionOrDefault(FILTER, new ArrayList<>());
    }

    private List<Document> must() {
        return getConditionOrDefault(MUST, new ArrayList<>());
    }

    private List<Document> mustNot() {
        return getConditionOrDefault(MUST_NOT, new ArrayList<>());
    }

    private List<Document> should() {
        return getConditionOrDefault(SHOULD, new ArrayList<>());
    }

    private <T> T getConditionOrDefault(String key, T defaultValue) {
        if (!search.containsKey(key)) {
            search.put(key, defaultValue);
        }
        return search.get(key, defaultValue);
    }
}
