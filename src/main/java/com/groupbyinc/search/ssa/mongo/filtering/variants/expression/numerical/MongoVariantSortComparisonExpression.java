package com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.numerical.ComparisonExpression;
import com.groupbyinc.search.ssa.mongo.filtering.variants.expression.MongoVariantSortNotExpression;
import com.groupbyinc.search.ssa.mongo.filtering.variants.expression.text.MongoVariantSortTextExpression;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import org.bson.Document;

import javax.annotation.Nonnull;

import java.util.List;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_EQ;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_IN;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_NOT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_OR;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.VARIANT_DOUBLE_DOLLAR_PREFIX;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.VARIANT_LOCAL_INVENTORIES_SHORT_DOUBLE_DOLLAR_PREFIX;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.caseBodyWithIsArrayCheck;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.createInventorySortFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.switchWithOneCase;
import static com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortRangeExpression.greaterThan;
import static com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortRangeExpression.greaterThanOrEqual;
import static com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortRangeExpression.lessThan;
import static com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortRangeExpression.lessThanOrEqual;

/**
 * Expression for logical comparisons for mongo variant sorting filter.
 * <p>
 * Used to filter products by specific numeric value in some facet(attribute).
 * In case when inventory attribute is used, it will create a special filter.
 *
 * @see MongoVariantSortTextExpression
 * @see MongoVariantSortRangeExpression
 * @see MongoVariantSortNotExpression
 */
public class MongoVariantSortComparisonExpression extends ComparisonExpression<Document> {

    private final String prefix;
    private final String placeId;

    public MongoVariantSortComparisonExpression(@NonNull String field,
                                                @Nullable String placeId,
                                                @NonNull String operator,
                                                @NonNull Number value) {
        super(field, operator, value);

        this.placeId = placeId;
        this.prefix = placeId == null
            ? VARIANT_DOUBLE_DOLLAR_PREFIX
            : VARIANT_LOCAL_INVENTORIES_SHORT_DOUBLE_DOLLAR_PREFIX;
    }

    public MongoVariantSortComparisonExpression(@NonNull String field,
                                                @NonNull String operator,
                                                @NonNull Number value) {
        super(field, operator, value);

        this.placeId = null;
        this.prefix = VARIANT_DOUBLE_DOLLAR_PREFIX;
    }

    /**
     * Create a mongo filter based on 'equals' operator.
     * @return created range filter for variant sort filter.
     *
     * @see MongoVariantSortTextExpression
     * @see MongoVariantSortRangeExpression
     */
    @Nonnull
    @Override
    public Document toFilter() {
        return switch (operator) {
            case EQUAL -> forEquals();
            case GREATER_THAN -> greaterThan(field, placeId, value).toFilter();
            case GREATER_THAN_OR_EQUAL -> greaterThanOrEqual(field, placeId, value).toFilter();
            case LESS_THAN -> lessThan(field, placeId, value).toFilter();
            case LESS_THAN_OR_EQUAL -> lessThanOrEqual(field, placeId, value).toFilter();
            case NOT_EQUAL -> wrapWithNot(forEquals());
            default -> throw new UnsupportedOperationException(
                "Unsupported operator (%s), use one of[> , >= , < , <= , = , !=]".formatted(operator)
            );
        };
    }

    private Document forEquals() {
        var path = prefix + field;

        var caseBody = caseBodyWithIsArrayCheck(path, new Document($_IN, List.of(value, path)));
        var defaultBody = new Document($_OR, new Document($_EQ, List.of(path, value)));

        var sortFilter = switchWithOneCase(caseBody, defaultBody);

        if (placeId == null) {
            return sortFilter;
        }

        return createInventorySortFilter(sortFilter, placeId);
    }

    private Document wrapWithNot(Document document) {
        return new Document($_NOT, document);
    }

}
