package com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.numerical.ComparisonExpression;
import com.groupbyinc.search.ssa.mongo.filtering.search.expression.MongoSearchNotExpression;
import com.groupbyinc.search.ssa.mongo.filtering.search.expression.text.MongoSearchTextExpression;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import org.bson.Document;

import javax.annotation.Nonnull;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.EQUALS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.INDEXABLES_PREFIX;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.createDocumentForSearchFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.createInventoryFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical.MongoSearchRangeExpression.greaterThan;
import static com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical.MongoSearchRangeExpression.greaterThanOrEqual;
import static com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical.MongoSearchRangeExpression.lessThan;
import static com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical.MongoSearchRangeExpression.lessThanOrEqual;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.wrapWithMustNot;

/**
 * Expression for logical comparisons for mongo search.
 * <p>
 * Used to filter products by specific numeric value in some facet(attribute).
 * In case when inventory attribute is used, it will create a special filter based on an embedded document.
 *
 * @see MongoSearchTextExpression
 * @see MongoSearchRangeExpression
 * @see MongoSearchNotExpression
 */
public class MongoSearchComparisonExpression extends ComparisonExpression<Document> {

    private final String placeId;

    public MongoSearchComparisonExpression(@NonNull String field,
                                           @Nullable String placeId,
                                           @NonNull String operator,
                                           @NonNull Number value) {
        super(field, operator, value);

        this.placeId = placeId;
    }

    public MongoSearchComparisonExpression(@NonNull String field,
                                           @NonNull String operator,
                                           @NonNull Number value) {
        super(field, operator, value);

        this.placeId = null;
    }

    /**
     * Create a mongo filter based on 'equals' operator.
     */
    @Nonnull
    @Override
    public Document toFilter() {
        return switch (operator) {
            case EQUAL -> forEquals();
            case GREATER_THAN -> greaterThan(field, placeId, value).toFilter();
            case GREATER_THAN_OR_EQUAL -> greaterThanOrEqual(field, placeId, value).toFilter();
            case LESS_THAN -> lessThan(field, placeId, value).toFilter();
            case LESS_THAN_OR_EQUAL -> lessThanOrEqual(field, placeId, value).toFilter();
            case NOT_EQUAL -> wrapWithMustNot(forEquals());
            default -> throw new UnsupportedOperationException(
                "Unsupported operator (%s), use one of[> , >= , < , <= , = , !=]".formatted(operator)
            );
        };
    }

    private Document forEquals() {
        var filter = createDocumentForSearchFilter(EQUALS, INDEXABLES_PREFIX + field, value);

        if (placeId == null) {
            return filter;
        }

        return createInventoryFilter(placeId, filter);
    }

}
