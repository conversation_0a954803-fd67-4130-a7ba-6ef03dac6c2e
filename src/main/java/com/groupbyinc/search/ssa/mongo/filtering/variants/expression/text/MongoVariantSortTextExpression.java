package com.groupbyinc.search.ssa.mongo.filtering.variants.expression.text;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.text.TextExpression;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import org.bson.Document;

import javax.annotation.Nonnull;
import java.util.ArrayList;
import java.util.List;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_EQ;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_IN;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_OR;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.VARIANT_LOCAL_INVENTORIES_SHORT_DOUBLE_DOLLAR_PREFIX;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.VARIANT_DOUBLE_DOLLAR_PREFIX;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.caseBodyWithIsArrayCheck;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.createInventorySortFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.switchWithOneCase;

/**
 * Expression for text facets(attributes) for mongo variant sorting filter.
 * In case when inventory attribute is used, it will create a special filter.
 *
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/eq/"> $eq expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/in/"> $in expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/or/"> $or expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/isArray/"> $isArray Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/switch/"> $switch Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/map/"> $map Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/and/"> $and Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/anyElementTrue/">
 *     $anyElementTrue Doc
 *     </a>
 */
public class MongoVariantSortTextExpression extends TextExpression<Document> {

    private final String prefix;
    private final String placeId;

    /**
     * Creates a new text expression.
     *
     * @param field       facet(attribute) the expression is for.
     * @param refinements values which product should contain in specific attribute.
     */
    public MongoVariantSortTextExpression(@NonNull String field,
                                          @Nullable String placeId,
                                          @NonNull List<String> refinements) {
        super(field, refinements);

        this.placeId = placeId;
        this.prefix = placeId == null
            ? VARIANT_DOUBLE_DOLLAR_PREFIX
            : VARIANT_LOCAL_INVENTORIES_SHORT_DOUBLE_DOLLAR_PREFIX;
    }

    public MongoVariantSortTextExpression(@NonNull String field, @NonNull List<String> refinements) {
        super(field, refinements);

        this.placeId = null;
        this.prefix = VARIANT_DOUBLE_DOLLAR_PREFIX;
    }

    /**
     * Create a mongo filter representation of this expression.
     * Example:
     *
     * <pre>{@code
     * {
     *   $switch: {
     *     branches: [
     *       {
     *         case: {
     *           $isArray: "$$variant.colorInfo.colorFamilies"
     *         },
     *         then: {
     *           $or: [
     *             {
     *               $in: [
     *                 "Negro",
     *                 "$$variant.colorInfo.colorFamilies"
     *               ]
     *             }
     *           ]
     *         }
     *       }
     *     ],
     *     default: {
     *       $or: [
     *         {
     *           $eq: [
     *             "$$variant.colorInfo.colorFamilies",
     *             "Negro"
     *           ]
     *         }
     *       ]
     *     }
     *   }
     * }
     * }</pre>
     *
     * LocalInventories:
     * <pre>{@code
     * {
     *   $anyElementTrue: {
     *     $map: {
     *       input: "$$variant.localInventories",
     *       as: "lv",
     *       in: {
     *         $and: [
     *           {
     *             $eq: [
     *               "$$lv.placeId",
     *               "3",
     *             ],
     *           },
     *           {
     *             $switch: {
     *               branches: [
     *                 {
     *                   case: {
     *                     $isArray: "$$lv.attributes.totalStoreSales"
     *                   },
     *                   then: {
     *                     $in: [
     *                       1.3,
     *                       "$$lv.attributes.totalStoreSales",
     *                     ],
     *                   },
     *                 }
     *               ],
     *               default: {
     *                 $or: {
     *                   $eq: [
     *                     "$$lv.attributes.totalStoreSales",
     *                     1.3,
     *                   ],
     *                 },
     *               },
     *             },
     *           },
     *         ]
     *       },
     *     },
     *   },
     * }
     * }</pre>
     *
     * @return mongo variant sort filter object.
     */
    @Nonnull
    @Override
    public Document toFilter() {
        var path = prefix + field;

        List<Document> eqFilters = new ArrayList<>();
        List<Document> inFilters = new ArrayList<>();
        for (String refinement : refinements) {
            eqFilters.add(new Document($_EQ, List.of(path, refinement)));
            inFilters.add(new Document($_IN, List.of(refinement, path)));
        }

        var caseBody = caseBodyWithIsArrayCheck(path, new Document($_OR, inFilters));
        var defaultBody = new Document($_OR, eqFilters);

        var sortFilter = switchWithOneCase(caseBody, defaultBody);

        if (placeId == null) {
            return sortFilter;
        }

        return createInventorySortFilter(sortFilter, placeId);
    }

}
