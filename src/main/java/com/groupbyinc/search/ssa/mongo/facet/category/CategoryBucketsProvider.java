package com.groupbyinc.search.ssa.mongo.facet.category;

import com.groupbyinc.search.ssa.api.utils.SearchUtils;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.metrics.opentelemetry.interceptors.ApiLatencyMetricsCollector;

import com.mongodb.Function;
import com.mongodb.client.MongoClient;
import com.mongodb.client.model.Filters;
import io.micronaut.context.annotation.Context;
import io.micronaut.core.util.CollectionUtils;
import io.micronaut.core.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.BUCKETS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.CATEGORY;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.CATEGORY_BUCKET_COLLECTION_SUFFIX;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.COUNT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.DEFAULT_NUM_RANGES;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.ID_UNDERSCORE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.SEARCH_COLLECTION_SUFFIX;

@Slf4j
@Context
@RequiredArgsConstructor
public class CategoryBucketsProvider {
    private static final Function<String, String> PROJECTION_SOURCE_FUN = "$fields.%s.buckets"::formatted;
    private static final Function<String, String> BUCKET_COLL_FUN = collection ->
        "%s%s%s".formatted(collection, SEARCH_COLLECTION_SUFFIX, CATEGORY_BUCKET_COLLECTION_SUFFIX);

    private final MongoClient mongoClient;
    private final CategoryBucketsCache cache;

    @ApiLatencyMetricsCollector
    public Map<String, List<Range>> resolveNumRanges(SearchParameters searchParameters, List<String> fields) {
        try {
            if (!validCategories(searchParameters.getPageCategories())) {
                // no categories - return default buckets
                return fields.stream().collect(Collectors.toMap(field -> field, field -> DEFAULT_NUM_RANGES));
            }

            var context = getRequestContext();
            var tenant = context.getMerchandiser().merchandiserId();
            var collection = BUCKET_COLL_FUN.apply(context.getCollection());
            var categories = new ArrayList<>(searchParameters.getPageCategories());

            var cached = context.getRequestOptions().skipCache()
                ? List.<CategoryBuckets>of()
                : categories.stream().map(cache::get).filter(Objects::nonNull).toList();

            categories.removeAll(cached.stream().map(CategoryBuckets::name).toList());

            List<CategoryBuckets> resultCategories = new ArrayList<>();
            if (CollectionUtils.isEmpty(categories)) {
                resultCategories = cached;
            } else {
                var resultBson = mongoClient.getDatabase(tenant).getCollection(collection)
                    .find(Filters.in(CATEGORY, categories))
                    .projection(project(fields, searchParameters))
                    .into(new ArrayList<>());

                var fromMongo = CategoryBuckets.fromBson(resultBson);
                if (CollectionUtils.isNotEmpty(fromMongo)) {
                    resultCategories.addAll(CategoryBuckets.fromBson(resultBson));
                    cache.put(fromMongo);
                }
                resultCategories.addAll(cached);
            }

            if (CollectionUtils.isEmpty(resultCategories)) {
                log.warn(
                    "[{}] No categories found for: [{}]. Returning default buckets...",
                    collection, String.join(", ", categories)
                );
                return fields.stream().collect(Collectors.toMap(field -> field, field -> DEFAULT_NUM_RANGES));
            }

            var resultCategory = getDeepestCategory(resultCategories);
            if (resultCategory == null) {
                var category = resultCategories.stream()
                    .max(Comparator.comparingInt(CategoryBuckets::count));
                resultCategory = category.orElseGet(resultCategories::getFirst);
            }
            return extractFieldRanges(resultCategory, fields);
        } catch (Exception e) {
            log.error("Error resolving numerical ranges: {}", e.getMessage(), e);
            return fields.stream().collect(Collectors.toMap(field -> field, field -> DEFAULT_NUM_RANGES));
        }
    }

    private boolean validCategories(List<String> categories) {
        return CollectionUtils.isNotEmpty(categories) && !categories.equals(SearchUtils.DEFAULT_PAGE_CATEGORIES);
    }

    private Map<String, List<Range>> extractFieldRanges(CategoryBuckets category, List<String> fields) {
        var fieldRanges = new HashMap<String, List<Range>>();
        fields.forEach(field -> {
            var ranges = category.fieldRanges().get(field);
            if (CollectionUtils.isNotEmpty(ranges)) {
                fieldRanges.put(field, ranges);
            } else {
                fieldRanges.put(field, DEFAULT_NUM_RANGES);
            }
        });
        return fieldRanges;
    }

    /**
     * Creates projection with remapped (flattened) fields:
     * <pre>
     * {@code
     * {
     *   "$project": {
     *     "_id": 0,
     *     "category": 1,
     *     "count": 1,
     *     "buckets": {
     *       "originalPrice": "$fields.priceInfo.originalPrice.buckets",
     *       "attributes.clientMetrics_socialMediaLikes": "$fields.attributes.clientMetrics_socialMediaLikes.buckets"
     *     }
     *   }
     * }
     * }
     * </pre>
     * <p>
     * Example result:
     * <pre>
     * {@code
     * [
     *   {
     *     "category": "Bed & Bath",
     *     "count": 2539,
     *     "buckets": {
     *       "attributes": {
     *         "clientMetrics_socialMediaLikes": [ [6.0, 8.0], [8.0, 10.0] ]
     *       },
     *       "originalPrice": [ [20.0, 30.0], [30.0, 40.0] ]
     *     }
     *   }
     * ]
     * }
     * </pre>
     */
    private Bson project(List<String> fields, SearchParameters sp) {
        var projection = new Document()
            .append(ID_UNDERSCORE, 0)
            .append(CATEGORY, 1)
            .append(COUNT, 1);

        var fieldsProj = new Document();
        for (var f : fields) {
            fieldsProj.append(f, PROJECTION_SOURCE_FUN.apply(attributePath(sp, f)));
        }
        projection.append(BUCKETS, fieldsProj);
        return projection;
    }

    private String attributePath(SearchParameters sp, String field) {
        return Optional.ofNullable(sp.getMerchandisingConfiguration().attributeConfigurations().get(field))
            .map(AttributeConfiguration::path)
            .orElse(field);
    }

    /**
     * Returns the deepest nested category as a string (with " > " as a delimiter) if the input represents a strong
     * (un-branched) hierarchy.
     */
    private CategoryBuckets getDeepestCategory(List<CategoryBuckets> categories) {
        if (CollectionUtils.isEmpty(categories)) {
            return null;
        }
        if (categories.size() == 1) {
            return categories.getFirst();
        }

        var rootOfRoots = new CategoryNode("");
        for (var cat : categories) {
            if (StringUtils.isEmpty(cat.name())) continue;
            var current = rootOfRoots;

            var pathParts = Arrays.stream(cat.name().split(">"))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .toList();
            for (var part : pathParts) {
                current = current.children.computeIfAbsent(part, k -> new CategoryNode(part));
            }
        }

        var chain = new ArrayList<String>();
        var current = rootOfRoots;
        while (!current.children.isEmpty()) {
            if (current.children.size() > 1) {
                return null;
            }
            var child = current.children.values().iterator().next();
            chain.add(child.name);
            current = child;
        }

        var chosenCategory = String.join(" > ", chain);
        Optional<CategoryBuckets> first = categories.stream()
            .filter(cat -> cat.name().equals(chosenCategory))
            .findFirst();

        if (first.isPresent()) {
            return first.get();
        }
        log.warn(
            "No matched category found, bad name. Chosen category: {}, input categories: {}",
            chosenCategory, categories.stream().map(CategoryBuckets::name).toList()
        );
        return null;
    }

    private static class CategoryNode {
        String name;
        Map<String, CategoryNode> children = new HashMap<>();

        CategoryNode(String name) {
            this.name = name;
        }
    }
}
