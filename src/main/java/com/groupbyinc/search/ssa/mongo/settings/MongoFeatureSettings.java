package com.groupbyinc.search.ssa.mongo.settings;

import com.groupbyinc.search.ssa.features.FeatureFlag;
import com.groupbyinc.search.ssa.redis.key.mongo.MongoCacheConfig;

public record MongoFeatureSettings(boolean enabled,
                                   boolean enabledMultiThreading,
                                   MongoABTestSettings abTesting,
                                   MongoCacheConfig cacheConfig) implements FeatureFlag {

    private static final MongoABTestSettings AB_TESTING_DEFAULT = new MongoABTestSettings(false, 0);

    public static MongoFeatureSettings DEFAULT = new MongoFeatureSettings(
        false,
        false,
        AB_TESTING_DEFAULT,
        MongoCacheConfig.DEFAULT
    );

    public MongoFeatureSettings {
        abTesting = abTesting != null ? abTesting : AB_TESTING_DEFAULT;
        cacheConfig = cacheConfig != null ? cacheConfig : MongoCacheConfig.DEFAULT;
    }

}
