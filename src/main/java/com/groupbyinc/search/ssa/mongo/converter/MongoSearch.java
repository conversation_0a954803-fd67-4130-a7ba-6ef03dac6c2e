package com.groupbyinc.search.ssa.mongo.converter;

import com.groupbyinc.search.ssa.mongo.facet.MongoFacet;

import org.bson.Document;

import javax.annotation.Nullable;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.COMPOUND;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.CONCURRENT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.COUNT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.EXISTS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.FACET;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.IN;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.INDEX;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.QUERY_STRING;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.RANGE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.RETURN_STORED_SOURCE_KEY;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.SEARCH_AGGREGATION_STAGE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.SEARCH_META_AGGREGATION_STAGE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.TEXT;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

public class MongoSearch {
    private static final Document RETURN_STORED_FIELDS = new Document(RETURN_STORED_SOURCE_KEY, true);

    private final Document search;
    private MongoCompoundableSearch compound;
    private MongoFacet facet;

    public MongoSearch() {
        this(null);
    }

    public MongoSearch(String index) {
        this.search = new Document();
        if (isNotBlank(index)) {
            this.setIndex(index);
        }
        this.append(RETURN_STORED_FIELDS);
    }

    public MongoSearch append(Document document) {
        document.forEach(this::append);
        return this;
    }

    public MongoSearch append(String key, Object value) {
        this.search.append(key, value);
        return this;
    }

    public MongoSearch setIndex(String index) {
        append(INDEX, index);
        return this;
    }

    public MongoSearch setConcurrent(boolean concurrent) {
        append(CONCURRENT, concurrent);
        return this;
    }

    public MongoSearch text(String path, String value) {
        append(TEXT, MongoSearchOperators.text(path, value).get(TEXT));
        return this;
    }

    public MongoSearch range(String path, MongoSearchRange left, MongoSearchRange right) {
        append(RANGE, MongoSearchOperators.range(path, left, right).get(RANGE));
        return this;
    }

    public MongoSearch queryString(String defaultPath, Object query) {
        append(QUERY_STRING, MongoSearchOperators.queryString(defaultPath, query).get(QUERY_STRING));
        return this;
    }

    public MongoSearch exists(String path) {
        append(EXISTS, MongoSearchOperators.exists(path).get(EXISTS));
        return this;
    }

    public MongoSearch in(String path, Object value) {
        append(IN, MongoSearchOperators.in(path, value).get(IN));
        return this;
    }

    public MongoSearch countTotal() {
        return countTotal(null);
    }

    public MongoSearch countTotal(@Nullable Long threshold) {
        return count("total", threshold);
    }

    public MongoSearch count(String type, @Nullable Long threshold) {
        append(COUNT, MongoSearchOperators.count(type, threshold).get(COUNT));
        return this;
    }

    public MongoFacet facet() {
        // avoid cycles by separating this out
        if (facet == null) {
            facet = new MongoFacet();
        }
        return facet;
    }

    public MongoCompoundableSearch compound() {
        // avoid cycles by separating this out
        if (compound == null) {
            compound = new MongoCompoundableSearch();
        }
        return compound;
    }

    public Document toSearchDocument() {
        var outDoc = toDocument();
        return new Document(SEARCH_AGGREGATION_STAGE, outDoc);
    }

    public Document toSearchMetaDocument() {
        var outDoc = toDocument();
        return new Document(SEARCH_META_AGGREGATION_STAGE, outDoc);
    }

    public boolean isCompound() {
        return this.compound != null;
    }

    private Document toDocument() {
        // clone this search object so that calling .toDocument()
        // does not mutate the original MongoSearch object
        var outDoc = new Document();

        outDoc.putAll(this.search);

        if (this.compound != null) {
            // Avoid potential cycles by leaving this until the very end.
            // There can be only one compound operator on the base search document
            outDoc.put(COMPOUND, this.compound.toDocument());
        }

        if (this.facet != null) {
            // Avoid potential cycles by leaving this until the very end.
            // There can be only one compound operator on the base search document
            outDoc.put(FACET, this.facet.toDocument());
        }
        return outDoc;
    }

    public String toJson() {
        return toSearchDocument().toJson();
    }

    @Override
    public String toString() {
        return toJson();
    }
}
