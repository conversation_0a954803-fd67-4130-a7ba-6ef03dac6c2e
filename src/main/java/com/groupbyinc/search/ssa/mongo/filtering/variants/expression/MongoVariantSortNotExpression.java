package com.groupbyinc.search.ssa.mongo.filtering.variants.expression;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.Expression;
import com.groupbyinc.search.ssa.application.core.search.filtering.expression.NotExpression;

import io.micronaut.core.annotation.NonNull;
import org.bson.Document;

import javax.annotation.Nonnull;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_NOT;

/**
 * Represents a negation expression.
 * <p>
 * Technically, any expression can be negated.
 *
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/eq/"> $eq expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/ne/"> $ne expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/gt/"> $gt expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/gte/"> $gte expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/lt/"> $lt expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/lte/"> $lte expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/or/"> $or expression Doc </a>
 */
public class MongoVariantSortNotExpression extends NotExpression<Document> {

    /**
     * Creates a negated version of the expression.
     *
     * @param expression to be negated.
     *
     * @return Negated expression.
     */
    @NonNull
    public static MongoVariantSortNotExpression not(@NonNull Expression<Document> expression) {
        return new MongoVariantSortNotExpression(expression);
    }

    public MongoVariantSortNotExpression(@NonNull Expression<Document> expression) {
        super(expression);
    }

    /**
     * Create a mongo filter representation of this expression.
     * Example:
     *
     * <pre>{@code
     * {
     *   "$not": {
     *     "$or": [
     *       {
     *         "$eq": [
     *           "$$variant.id",
     *           "1138288168"
     *         ]
     *       },
     *       {
     *         "$eq": [
     *           "$$variant.id",
     *           "1166200351"
     *         ]
     *       }
     *     ]
     *   }
     * }
     *
     * }</pre>
     *
     * Note: provided example contains "in" operator, but technically any expression may be negotiated/
     *
     * @return mongo filter object.
     */
    @Nonnull
    @Override
    public Document toFilter() {
        return new Document($_NOT, expression.toFilter());
    }

}
