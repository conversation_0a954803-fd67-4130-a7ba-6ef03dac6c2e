package com.groupbyinc.search.ssa.mongo.converter;

import org.bson.Document;

import javax.annotation.Nullable;
import java.util.List;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.COMPOUND;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.COUNT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.EQUALS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.EXISTS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.FACET;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.FACETS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.IN;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.OPERATOR;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.QUERY_STRING;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.RANGE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.TEXT;

public class MongoSearchOperators {

    public static Document text(String path, String query) {
        return text(path, List.of(query));
    }

    public static Document compound(Document query) {
        return new Document(COMPOUND, query);
    }

    public static Document equals(String path, String value) {
        return new Document(EQUALS,
            new Document()
                .append("path", path)
                .append("value", value)
        );
    }

    public static Document text(String path, List<String> queries) {
        return new Document(TEXT,
            new Document()
                .append("path", path)
                .append("query", queries)
        );
    }

    public static Document text(List<String> paths, String query) {
        return new Document(TEXT,
            new Document()
                .append("path", paths)
                .append("query", query)
        );
    }

    public static Document text(List<String> paths, List<String> queries) {
        return new Document(TEXT,
            new Document()
                .append("path", paths)
                .append("query", queries)
        );
    }

    public static Document range(String path, MongoSearchRange left, MongoSearchRange right) {
        return new Document(RANGE,
            new Document()
                .append("path", path)
                .append(left.getKey(), left.getValue())
                .append(right.getKey(), right.getValue())
        );
    }

    public static Document range(List<String> paths, MongoSearchRange left, MongoSearchRange right) {
        return new Document(RANGE,
            new Document()
                .append("path", paths)
                .append(left.getKey(), left.getValue())
                .append(right.getKey(), right.getValue())
        );
    }

    public static Document range(List<String> paths, MongoSearchRange range) {
        return new Document(RANGE,
            new Document()
                .append("path", paths)
                .append(range.getKey(), range.getValue())
        );
    }

    public static Document exists(String path) {
        return new Document(EXISTS, new Document("path", path));
    }

    public static Document queryString(String defaultPath, Object query) {
        return new Document(QUERY_STRING,
            new Document()
                .append("defaultPath", defaultPath)
                .append("query", query)
        );
    }

    public static Document in(String path, Object value) {
        return new Document(IN,
            new Document()
                .append("path", path)
                .append("value", value)
        );
    }

    public static Document count(String type, @Nullable Long threshold) {
        var countDoc = new Document("type", type);
        if (threshold != null) {
            countDoc.append("threshold", threshold);
        }
        return new Document(COUNT, countDoc);
    }

    public static Document facet() {
        return new Document(FACETS, new Document());
    }

    public static Document facet(Document facets) {
        return new Document(FACETS, facets);
    }

    public static Document operator() {
        return new Document(OPERATOR, new Document());
    }

    public static Document operator(Document value) {
        return new Document(OPERATOR, value);
    }

    public static Document facets(Object value) {
        return new Document(FACET, value);
    }
}
