package com.groupbyinc.search.ssa.mongo;

import com.groupbyinc.search.ssa.core.navigation.Range;

import com.mongodb.client.model.Projections;
import org.bson.conversions.Bson;

import java.util.List;

public interface MongoConstants {

    String DOUBLE_DOLLAR = "$$";
    String DOLLAR = "$";

    // Additional suffix identifying the collection used for search.
    String SEARCH_COLLECTION_SUFFIX = "_search";
    // Additional suffix identifying the index used for search.
    String SEARCH_INDEX_SUFFIX = "_search";
    // Additional suffix identifying the collection used for fetching buckets.
    String CATEGORY_BUCKET_COLLECTION_SUFFIX = "_buckets";

    // Unique product identifier. Expected to present in each single record.
    String ID = "id";
    // Unique record identifier. Expected to present in each single record.
    String ID_UNDERSCORE = "_id";
    // Name of the Atlas Search index to use. If omitted, defaults to default.
    String INDEX = "index";
    // To request Atlas Search to run the query multithreaded
    String CONCURRENT = "concurrent";
    // Total number of search results matching search criteria.
    String TOTAL = "total";
    // List of numeric values, in ascending order, that specify the boundaries for each bucket.
    String BOUNDARIES = "boundaries";
    // The value of the specified field is greater than or equals.
    String GTE = "gte";
    // The value of the specified field is lower than or equals.
    String LTE = "lte";
    // The value of the specified field is less than.
    String LT = "lt";
    // The value of the specified field is greater than.
    String GT = "gt";
    // Document containing search results.
    String DOCS = "docs";
    String DOC = "doc";
    // https://www.mongodb.com/docs/atlas/atlas-search/return-stored-source/
    String RETURN_STORED_SOURCE_KEY = "returnStoredSource";

    String TYPE = "type";
    String TITLE = "title";
    String CATEGORY = "category";
    String PRIMARY_PRODUCT_ID = "primaryProductId";
    String PATH = "path";
    String NUM_BUCKETS = "numBuckets";
    String META = "meta";
    String FACETS = "facets";
    String OPERATOR = "operator";
    String TEXT = "text";
    String NUMBER = "number";
    String RANGE = "range";
    String QUERY = "query";
    String STRING = "string";
    String BUCKETS = "buckets";
    String EQUALS = "equals";
    String VALUE = "value";
    String INPUT = "input";
    String LET = "let";
    String PIPELINE = "pipeline";
    String LOCAL_FIELD = "localField";
    String FOREIGN_FIELD = "foreignField";
    String DOCS_IDS = "docsIds";
    String SORT_ORDER = "sortOrder";

    String MUST = "must";
    String MUST_NOT = "mustNot";
    String SHOULD = "should";
    String FILTER = "filter";
    String SCORE = "score";
    String COMPOUND = "compound";
    String EXISTS = "exists";
    String IN = "in";
    String AS = "as";
    String IF = "if";
    String ELSE = "else";
    String THEN = "then";
    String FROM = "from";
    String IS_MATCHING = "isMatching";
    String SORT_BY = "sortBy";
    String COUNT = "count";
    String QUERY_STRING = "queryString";
    String FACET = "facet";
    String PLACE_ID = "placeId";
    String MINIMUM_SHOULD_MATCH = "minimumShouldMatch";
    String MATCH_CRITERIA = "matchCriteria";
    String ALL = "all";
    String MULTI = "multi";
    String BOOST = "boost";
    String EMBEDDED_DOCUMENT = "embeddedDocument";
    String VARIANT = "variant";
    String VARIANTS = "variants";
    String VARIANTS_PREFIX = "variants.";
    String ATTRIBUTES_PREFIX = "attributes.";
    String LOCAL_INVENTORIES_PREFIX = "localInventories.";
    String LOCAL_INVENTORIES_PLACE_ID_PATH = "localInventories.placeId";
    String LOCAL_INVENTORIES_PREFIX_NO_DOT = "localInventories";
    String INDEXABLES_PREFIX = "indexables.";
    String DOCS_PREFIX = "docs.";
    String DOC_PREFIX = "doc.";
    String DOCS_DOLLARS = "$docs.";
    String DYNAMIC_FACETS = "dynamicFacets";

    /**
     * When using {@link MongoConstants#RETURN_STORED_SOURCE_KEY} projections will be limited to list of fields defined
     * in the search index. Stored source field controlled by
     * <a href="https://github.com/groupby/mongo-search-indexer">Mongo Search Indexer</a>
     */
    Bson DEFAULT_INCLUDED_FIELDS = Projections.include(ID_UNDERSCORE, ID, VARIANTS_PREFIX + ID);

    // Mongo aggregation stages:
    // Performs a full-text search on the specified field or fields.
    String SEARCH_AGGREGATION_STAGE = "$search";
    // Performs a faceting on the specified field or fields.
    String SEARCH_META_AGGREGATION_STAGE = "$searchMeta";
    // Skips over the specified number of documents that pass into the stage and passes the remaining documents to the next stage in the aggregations.
    String SKIP_AGGREGATION_STAGE = "$skip";
    // Returns the metadata associated with a document, for example, "textScore" when performing text search.
    String META_AGGREGATION_STAGE = "$meta";
    // Performs a left outer join to a collection in the same database to filter in documents from the "joined" collection for processing.
    String LOOKUP_AGGREGATION_STAGE = "$lookup";

    // Returns the element at the specified array index.
    String ARRAY_ELEM_AT_EXP = "$arrayElemAt";
    String INDEX_OF_ARRAY_EXP = "$indexOfArray";
    String MERGE_OBJECTS_EXP = "$mergeObjects";
    // Returns the sorted array in order specified within 'sortBy' condition.
    String SORT_ARRAY_EXP = "$sortArray";
    String CONDITION_EXP = "$cond";
    String MAP_EXP = "$map";
    String $_OR = "$or";
    String $_IN = "$in";
    String $_EQ = "$eq";
    String $_NOT = "$not";
    String $_AND = "$and";
    String $_GT = "$gt";
    String $_GTE = "$gte";
    String $_LT = "$lt";
    String $_LTE = "$lte";
    String $_SWITCH = "$switch";
    String BRANCHES = "branches";
    String CASE = "case";
    String N = "n";
    String DEFAULT = "default";
    String $_IS_ARRAY = "$isArray";
    String $_ARRAY_ELEM_AT = "$arrayElemAt";
    String $_MIN_N = "$minN";
    String $_MAX_N = "$maxN";
    String $_ANY_ELEMENT_TRUE = "$anyElementTrue";
    String $_MAP = "$map";
    String VARIANT_DOUBLE_DOLLAR_PREFIX = "$$variant.";
    String VARIANT_INVENTORIES_DOUBLE_DOLLAR_PATH = "$$variant.localInventories";
    String VARIANT_LOCAL_INVENTORIES_SHORT = "lv";
    String VARIANT_LOCAL_INVENTORIES_SHORT_DOUBLE_DOLLAR_PREFIX = "$$lv.";
    String LOCAL_INVENTORIES_PLACE_ID_DOUBLE_DOLLAR_PATH = "$$lv.placeId";

    // Mongo aggregation variables:
    // Name of Atlas Search variable to view mapping of the defined facet names to an array of buckets for that facet.
    String FACET_AGGREGATION_VAR = "facet";
    // Name of Atlas Search variable to view the metadata results for $search query.
    String SEARCH_META_AGGREGATION_VAR = "$$SEARCH_META";

    // Mongo commands:
    // Counts the number of documents in a collection or a view.
    String COUNT_COMMAND = "count";

    // Part number search:
    String PART_NUMBER_MATCH_FIELD = "partNumberMatch";
    String PART_NUMBER_BOOST_FIELD = "partNumberBoost";

    // numeric facet buckets
    List<Range> DEFAULT_NUM_RANGES = List.of(
        new Range(Double.NEGATIVE_INFINITY, 10d),
        new Range(10d, 20d),
        new Range(20d, 30d),
        new Range(30d, 40d),
        new Range(40d, 50d),
        new Range(50d, 60d),
        new Range(60d, 70d),
        new Range(70d, 80d),
        new Range(80d, 90d),
        new Range(90d, 100d),
        new Range(100d, 200d),
        new Range(200d, 300d),
        new Range(300d, 400d),
        new Range(400d, 500d),
        new Range(500d, 600d),
        new Range(600d, 700d),
        new Range(700d, 800d),
        new Range(800d, 900d),
        new Range(900d, 1_000d),
        new Range(1_000d, 2_000d),
        new Range(2_000d, 3_000d),
        new Range(3_000d, 4_000d),
        new Range(4_000d, 5_000d),
        new Range(5_000d, 10_000d),
        new Range(10_000d, 20_000d),
        new Range(20_000d, 30_000d),
        new Range(30_000d, 40_000d),
        new Range(40_000d, 50_000d),
        new Range(50_000d, Double.POSITIVE_INFINITY)
    );

}
