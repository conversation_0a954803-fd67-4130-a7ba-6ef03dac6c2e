package com.groupbyinc.search.ssa.mongo.converter;

import com.groupbyinc.search.ssa.application.core.search.engine.SearchEngineType;
import com.groupbyinc.search.ssa.core.Merchandiser;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.SearchMetadata;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.SearchResults;
import com.groupbyinc.search.ssa.core.request.RequestServed;
import com.groupbyinc.search.ssa.mongo.response.MongoResponse;
import com.groupbyinc.search.ssa.mongo.settings.MongoSettingsStorage;

import io.micronaut.context.annotation.Context;
import io.micronaut.core.annotation.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import javax.annotation.Nullable;
import javax.annotation.ParametersAreNonnullByDefault;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.COUNT_COMMAND;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.DOCS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.ID;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.META;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.PRIMARY_PRODUCT_ID;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.TITLE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.TOTAL;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.VARIANTS;
import static com.groupbyinc.search.ssa.mongo.util.DocumentUtils.getAsLong;
import static com.groupbyinc.search.ssa.mongo.util.DocumentUtils.getAsString;
import static com.groupbyinc.search.ssa.productcatalog.ProductCatalogFilterService.VARIANTS_LIMIT;

import static java.util.Base64.getUrlEncoder;

@Slf4j
@Context
@RequiredArgsConstructor
@ParametersAreNonnullByDefault
public class MongoResponseConverter {

    private final MongoSettingsStorage mongoSettingsStorage;

    public SearchResults convert(SearchParameters searchParameters, MongoResponse mongoResponse) {
        var context = getRequestContext();

        var records = new ArrayList<Record>();
        var totalRecordsCount = 0L;

        for (var response : mongoResponse.searchResponses()) {
            var document = response.response();
            if (document.containsKey(DOCS)) {
                document.getList(DOCS, Document.class)
                    .stream()
                    .filter(Objects::nonNull)
                    .forEach(searchRecord -> records.add(
                        convertSearchResultToRecord(
                            searchParameters,
                            searchRecord,
                            context.getMerchandiser(),
                            context.getCollection()
                        )
                    ));
            }
            if (document.containsKey(META)) {
                var meta = document.get(META, Document.class);
                totalRecordsCount = totalRecordsCount + getNumTotalRecords(meta.get(COUNT_COMMAND, Document.class));
            }
        }

        var facetConverter = searchParameters.getMongoFacetConverter();

        return SearchResults.builder()
            .query(searchParameters.getQuery())
            .numTotalRecords(totalRecordsCount)
            .biasingProfile(searchParameters.getBiasingProfile())
            .selectedNavigations(
                facetConverter.convertSelectedRefinementsToNavigations(
                    searchParameters.getRefinements()
                )
            )
            .siteFilter(searchParameters.getMerchandisingConfiguration().siteFilter())
            .pageCategories(searchParameters.getPageCategories())
            .metadata(
                SearchMetadata
                    .builder()
                    .attributionToken(generateSearchAttributionToken())
                    .build()
            )
            .records(records)
            .searchEngine(SearchEngineType.MONGO)
            .requestServed(RequestServed.MONGO)
            .navigations(
                facetConverter.convertFacetsToNavigations(
                    searchParameters,
                    mongoResponse.searchResponses(),
                    mongoSettingsStorage.getFacetSamplePercentageSettings()
                    )
            )
            .build();
    }

    private Record convertSearchResultToRecord(SearchParameters searchParameters,
                                               Document product,
                                               Merchandiser merchandiser,
                                               String collection) {
        var metaFromSearch = new HashMap<>(product);

        var id = getAsString(ID, product);
        var primaryProductId = getAsString(PRIMARY_PRODUCT_ID, product);
        var title = getAsString(TITLE, product);

        if (product.containsKey(VARIANTS) && product.get(VARIANTS) != null) {
            var variants = product
                .getList(VARIANTS, Document.class)
                .stream()
                .limit(VARIANTS_LIMIT)
                .map(HashMap::new)
                .toList();

            metaFromSearch.put(VARIANTS, variants);
        }


        return Record.of(
            merchandiser,
            collection,
            id,
            primaryProductId,
            title,
            metaFromSearch,
            searchParameters.getLabeledProductIds().get(id)
        );
    }

    /**
     * @return a random token per request. No need to make this value based on query content as this value is for debugging and backward
     * compatibility. Also, it does not make sense to send events with current value to Google as Retail engine (as expected) will ignore them.
     */
    private String generateSearchAttributionToken() {
        var secureRandom = new SecureRandom();
        byte[] randomBytes = new byte[128];
        secureRandom.nextBytes(randomBytes);
        return getUrlEncoder()
            .withoutPadding()
            .encodeToString(randomBytes);
    }

    /**
     * @param count document
     *
     * @return the count result included in the results indicate whether the count returned in the results is a total count, but not a lower bound
     */
    private long getNumTotalRecords(@Nullable Document count) {
        var total = getAsLong(TOTAL, count);
        return total == null
            ? 0
            : total;
    }

    @NonNull
    public Set<String> extractProductIds(Document resultDocument) {
        var productIds = new HashSet<String>();

        if (resultDocument.containsKey(DOCS)) {
            resultDocument.getList(DOCS, Document.class)
                .stream()
                .filter(Objects::nonNull)
                .forEach(product -> productIds.add(getAsString(ID, product)));
        }

        return productIds;
    }

}
