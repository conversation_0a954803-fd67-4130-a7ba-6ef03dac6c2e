package com.groupbyinc.search.ssa.mongo.facet.category;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.List;

@Slf4j
@Context
public class CategoryBucketsCache {
    private final Cache<String, CategoryBuckets> cache;
    private final boolean enabled;

    public CategoryBucketsCache(@Value("${mongo.facets.category-bucket-cache-ttl:60m}") Duration ttl) {
        this.enabled = ttl.toMillis() != 0;
        if (!enabled) {
            log.info("Category bucket cache is disabled");
            cache = Caffeine.newBuilder().maximumSize(0).build();
            return;
        }
        this.cache = Caffeine.newBuilder().expireAfterWrite(ttl).build();
        log.info("Initializing category bucket cache with ttl: [{}s]", ttl.toSeconds());
    }

    public void put(CategoryBuckets value) {
        if (!enabled || value == null) {
            return;
        }
        cache.put(value.name(), value);
    }

    public void put(List<CategoryBuckets> values) {
        if (!enabled || CollectionUtils.isEmpty(values)) {
            return;
        }
        values.forEach(this::put);
    }

    public CategoryBuckets get(String key) {
        if (!enabled) {
            return null;
        }
        return cache.get(key, k -> null);
    }

    public void remove(String key) {
        if (!enabled) {
            return;
        }
        cache.invalidate(key);
    }
}
