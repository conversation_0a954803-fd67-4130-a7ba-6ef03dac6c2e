package com.groupbyinc.search.ssa.mongo.search;

import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.mongo.converter.MongoCompoundableSearch;
import com.groupbyinc.search.ssa.mongo.settings.MongoSettingsStorage;

import io.micronaut.core.util.StringUtils;
import org.bson.Document;

import java.util.ArrayList;
import java.util.List;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.ALL;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.BOOST;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.EQUALS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.INDEXABLES_PREFIX;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.MATCH_CRITERIA;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.MULTI;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.PART_NUMBER_BOOST_FIELD;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.PART_NUMBER_MATCH_FIELD;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.PATH;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.QUERY;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.SCORE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.TEXT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.VALUE;

import static io.micronaut.core.util.CollectionUtils.isNotEmpty;


/**
 * Responsible for building a compound partNumber search clause to be used in the MongoDB aggregation pipeline.
 */
public class MongoPartNumberSearch {

    private static final int MINIMUM_SHOULD_MATCH_ONE = 1;

    private final String query;
    private final List<AttributeConfiguration> partNumberSearchableAttributes;
    private final MongoSettingsStorage mongoSettingsStorage;

    public MongoPartNumberSearch(SearchParameters searchParameters, MongoSettingsStorage mongoSettingsStorage) {
        this.query = searchParameters.getQuery();
        this.partNumberSearchableAttributes = searchParameters.getPartNumberSearchableAttributes();
        this.mongoSettingsStorage = mongoSettingsStorage;
    }


    /**
     * Builds a compound partNumber search clause as follows:
     * <pre>
     * compound: {
     *   minimumShouldMatch: 1,
     *   should: [
     *     {
     *       equals: {
     *         path: "indexables.attributes.mpn",
     *         value: "way",
     *         score: {
     *           boost: {
     *             value: 2048
     *           }
     *         }
     *       }
     *     },
     *     {
     *       text: {
     *         query: "way",
     *         path: [
     *           "indexables.attributes.mpn"
     *         ],
     *         matchCriteria: "all"
     *       }
     *     },
     *     {
     *       text: {
     *         query: "way",
     *         path: [
     *           {
     *             value: "indexables.attributes.mpn",
     *             multi: "partNumberMatch"
     *           }
     *         ],
     *         score: {
     *           boost: {
     *             value: 2
     *           }
     *         }
     *       }
     *     },
     *     {
     *       text: {
     *         query: "way",
     *         path: [
     *           {
     *             value: "indexables.attributes.mpn",
     *             multi: "partNumberBoost"
     *           }
     *         ],
     *         score: {
     *           boost: {
     *             value: 4
     *           }
     *         }
     *       }
     *     }
     *   ]
     * }
     * </pre>
     *
     * @return a compound PartNumber search clause.
     * @throws IllegalStateException if there is no 'partNumberSearchable' attributes or the search query is empty.
     */
    public Document getCompoundSearchClause() {
        if (!isApplicable()) {
            throw new IllegalStateException("No 'partNumberSearchable' attributes found or search query is empty.");
        }

        var mongoCompoundableSearch = new MongoCompoundableSearch();
        mongoCompoundableSearch
            .compound()
            .minimumShouldMatch(MINIMUM_SHOULD_MATCH_ONE)
            .appendShould(buildPartNumberShouldClauses());

        return mongoCompoundableSearch.toDocument();
    }

    /**
     * Checks if the partNumber search is applicable.
     *
     * @return {@code true} if the partNumber search is applicable, {@code false} otherwise.
     */
    public boolean isApplicable() {
        return isNotEmpty(partNumberSearchableAttributes) && StringUtils.isNotEmpty(query);
    }

    private List<Document> buildPartNumberShouldClauses() {
        List<String> stringPath = new ArrayList<>();
        List<Document> partNumberMatchPath = new ArrayList<>();
        List<Document> partNumberBoostPath = new ArrayList<>();
        List<Document> partNumberShouldClauses = new ArrayList<>();

        partNumberSearchableAttributes.forEach(attributeConfiguration -> {
            var path = INDEXABLES_PREFIX + attributeConfiguration.path();
            stringPath.add(path);
            partNumberMatchPath.add(new Document()
                .append(VALUE, path)
                .append(MULTI, PART_NUMBER_MATCH_FIELD)
            );
            partNumberBoostPath.add(new Document()
                .append(VALUE, path)
                .append(MULTI, PART_NUMBER_BOOST_FIELD)
            );
            partNumberShouldClauses.add(getExactMatchClause(path));
        });

        partNumberShouldClauses.add(
            new Document(
                TEXT,
                new Document()
                    .append(QUERY, query)
                    .append(PATH, stringPath)
                    .append(MATCH_CRITERIA, ALL)
            )
        );
        partNumberShouldClauses.add(
            new Document(
                TEXT,
                new Document()
                    .append(QUERY, query)
                    .append(PATH, partNumberMatchPath)
                    .append(
                        SCORE,
                        new Document(
                            BOOST,
                            new Document(
                                VALUE,
                                mongoSettingsStorage.getPartNumberMatchScoreMultiplier()
                            )
                        )
                    )
            )
        );
        partNumberShouldClauses.add(
            new Document(
                TEXT,
                new Document()
                    .append(QUERY, query)
                    .append(PATH, partNumberBoostPath)
                    .append(
                        SCORE,
                        new Document(
                            BOOST,
                            new Document(
                                VALUE,
                                mongoSettingsStorage.getPartNumberBoostScoreMultiplier()
                            )
                        )
                    )
            )
        );

        return partNumberShouldClauses;
    }

    private Document getExactMatchClause(String path) {
        return new Document()
            .append(EQUALS, new Document()
                .append(PATH, path)
                .append(VALUE, query)
                .append(
                    SCORE,
                    new Document(
                        BOOST,
                        new Document(
                            VALUE,
                            mongoSettingsStorage.getPartNumberExactMatchMultiplier()
                        )
                    )
                )
            );
    }

}
