package com.groupbyinc.search.ssa.mongo.filtering.variants;

import com.groupbyinc.search.ssa.antlr.MongoVariantSortRawFilterVisitor;
import com.groupbyinc.search.ssa.application.core.search.filtering.expression.Expression;
import com.groupbyinc.search.ssa.application.core.search.filtering.filter.FilterItemWrapper;
import com.groupbyinc.search.ssa.application.core.search.filtering.filter.ToFilter;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.filter.ProductIdFilter;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.mongo.filtering.AbstractMongoFilterService;
import com.groupbyinc.search.ssa.mongo.filtering.variants.expression.MongoVariantSortNotExpression;
import com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortComparisonExpression;
import com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical.MongoVariantSortRangeExpression;
import com.groupbyinc.search.ssa.mongo.filtering.variants.expression.text.MongoVariantSortTextExpression;
import com.groupbyinc.search.ssa.mongo.filtering.variants.filter.MongoVariantSortFilter;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import java.util.List;
import java.util.Map;

import static com.groupbyinc.search.ssa.application.core.search.filtering.expression.numerical.ComparisonExpression.EQUAL;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.FILTER;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.getFieldToPathFunction;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.getInternalFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.getParsedRawFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.parseInventoryIfExists;
import static com.groupbyinc.search.ssa.mongo.filtering.variants.expression.MongoVariantSortNotExpression.not;
import static com.groupbyinc.search.ssa.util.AttributeUtils.PRODUCT_ID_FIELD_FOR_MONGO;

import static io.micrometer.core.instrument.util.StringEscapeUtils.escapeJson;
import static java.util.Objects.requireNonNull;

@Slf4j
@Singleton
@RequiredArgsConstructor
public class MongoVariantsSortFilterService extends AbstractMongoFilterService {

    /**
     * Used to create a filter for mongo search.
     * <p>
     * Created filter contains:
     * 1) Attribute filters from rule.
     * 2) Included productId filters from rule
     * 3) Excluded productId filters from rule
     * 4) Filters created based on user selected refinements
     * 5) Pre-filter passed in request
     * 6) Site-filter configured fo are associated with current request
     * <p>
     * The filter syntax consists of an expression language for constructing a
     * predicate from one or more fields of the products being filtered. Filter
     * expression is case-sensitive.
     * <p>
     *
     * @param searchParameters object which is representing user request.
     *
     * @return mongo representing full filter, which needs to be applied to the mongo search request.
     */
    @NonNull
    public Document createFilter(@NonNull SearchParameters searchParameters) {
        var fieldToPath = getFieldToPathFunction(searchParameters);

        var filter = new MongoVariantSortFilter();
        fillFilterWithFilters(filter, fieldToPath, searchParameters);

        var filterVisitor = new MongoVariantSortRawFilterVisitor(
            searchParameters.getMerchandisingConfiguration().attributeConfigurations()
        );
        var raw = getParsedRawFilter(searchParameters, filterVisitor, log);

        var result = filter.toFilter();
        raw.ifPresent(document -> result.getList(FILTER, Document.class).addAll(getInternalFilter(document)));

        return result;
    }

    @NonNull
    @Override
    protected MongoVariantSortFilter createIncludedProductIdFilter(@NonNull ProductIdFilter productIdFilter) {
        var filter = new MongoVariantSortFilter();
        filter.addToOrs(
            new MongoVariantSortTextExpression(PRODUCT_ID_FIELD_FOR_MONGO, productIdFilter.includedProductIds())
        );

        return filter;
    }

    @NonNull
    @Override
    protected MongoVariantSortNotExpression createExcludedProductIdsFilter(@NonNull ProductIdFilter productIdFilter) {
        return not(
            new MongoVariantSortTextExpression(PRODUCT_ID_FIELD_FOR_MONGO, productIdFilter.excludedProductIds())
        );
    }

    @NonNull
    @Override
    protected MongoVariantSortFilter convertFilterItemsToFilter(
        @NonNull Map<String, FilterItemWrapper> filterItemsByField
    ) {
        var filter = new MongoVariantSortFilter();
        addExpressionsToFilter(filter, filterItemsByField);

        return filter;
    }

    @NonNull
    @Override
    protected MongoVariantSortTextExpression buildTextExpression(@NonNull String field,
                                                                 @NonNull List<String> filters) {
        var parsed = parseInventoryIfExists(field, false);
        return new MongoVariantSortTextExpression(parsed.field(), parsed.placeId(), filters);
    }

    @NonNull
    @Override
    protected MongoVariantSortNotExpression buildNotExpression(Expression<Document> expression) {
        return not(expression);
    }

    @NonNull
    @Override
    protected MongoVariantSortTextExpression buildSinglenessTextExpression(@NonNull String field,
                                                                           @NonNull String value) {
        var parsed = parseInventoryIfExists(field, false);
        return new MongoVariantSortTextExpression(
            parsed.field(),
            parsed.placeId(),
            List.of(requireNonNull(escapeJson(value)))
        );
    }

    @NonNull
    @Override
    protected MongoVariantSortRangeExpression buildRangeExpression(@NonNull String field, @NonNull Range range) {
        var parsed = parseInventoryIfExists(field, false);
        return MongoVariantSortRangeExpression.of(parsed.field(), parsed.placeId(), range.low(), range.high());
    }

    @NonNull
    @Override
    protected MongoVariantSortComparisonExpression buildComparisonExpression(@NonNull String field,
                                                                             @Nullable Double value) {
        var parsed = parseInventoryIfExists(field, false);
        return new MongoVariantSortComparisonExpression(
            parsed.field(),
            parsed.placeId(),
            EQUAL,
            requireNonNull(value)
        );
    }

    @NonNull
    @Override
    protected MongoVariantSortFilter combineFiltersAsOrs(List<ToFilter<Document>> expressions) {
        var filter = new MongoVariantSortFilter();
        filter.addToOrs(expressions);
        return filter;
    }

}
