package com.groupbyinc.search.ssa.mongo.settings;

import com.groupbyinc.search.ssa.features.FeatureFlag;

import java.util.List;

import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultList;

public record FacetSamplePercentageSettings(boolean enabled,
                                            double min,
                                            double max,
                                            List<String> sampleFields) implements FeatureFlag {

    public static final String SAMPLE_PRECISION_FIELD = "samplePrecision";

    public static FacetSamplePercentageSettings DEFAULT = new FacetSamplePercentageSettings(
        false,
        0,
        0.1,
        List.of()
    );

    public FacetSamplePercentageSettings {
        max = max == 0 ? 0.1 : max;
        sampleFields = notNullOrDefaultList(sampleFields);
    }

}
