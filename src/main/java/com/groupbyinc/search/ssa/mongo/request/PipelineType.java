package com.groupbyinc.search.ssa.mongo.request;

public enum PipelineType {
    SEAR<PERSON>,
    SEARCH_FACETS,
    SEARCH_FACETS_SAMPLE,
    SEARCH_DYNAMIC_FACETS,
    SEARCH_DYNAMIC_FACETS_SAMPLE,
    PRODUCT_ID_LOOKUP;

    public boolean isFacetRequest() {
        return isDynamicFacetRequest() || isSearchFacetRequest();
    }

    public boolean isDynamicFacetRequest() {
        return this == SEARCH_DYNAMIC_FACETS ||  this == SEARCH_DYNAMIC_FACETS_SAMPLE;
    }

    public boolean isSearchFacetRequest() {
        return this ==  SEARCH_FACETS ||  this == SEARCH_FACETS_SAMPLE;
    }

    public boolean isSampleRequest() {
        return this == SEARCH_FACETS_SAMPLE || this == SEARCH_DYNAMIC_FACETS_SAMPLE;
    }

}
