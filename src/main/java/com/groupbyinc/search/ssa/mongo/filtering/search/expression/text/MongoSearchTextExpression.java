package com.groupbyinc.search.ssa.mongo.filtering.search.expression.text;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.text.TextExpression;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import org.bson.Document;

import java.util.List;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.EQUALS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.IN;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.INDEXABLES_PREFIX;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.createDocumentForSearchFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.createInventoryFilter;

/**
 * Expression for text facets(attributes) for mongo search.
 * <p>
 * Created filters may be with 'equals' or 'in' clause based on how much values exist in this expression.
 * In case when inventory attribute is used, it will create a special filter based on an embedded document.
 *
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/in/">IN Doc</a>
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/equals/">EQUALS Doc</a>
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/embedded-document/">EMBEDDED DOCUMENT Doc</a>
 */
public class MongoSearchTextExpression extends TextExpression<Document> {

    private final String placeId;

    /**
     * Creates a new text expression.
     *
     * @param field       facet(attribute) the expression is for.
     * @param refinements values which product should contain in specific attribute.
     */
    public MongoSearchTextExpression(@NonNull String field,
                                     @Nullable String placeId,
                                     @NonNull List<String> refinements) {
        super(field, refinements);
        this.placeId = placeId;
    }

    public MongoSearchTextExpression(@NonNull String field,
                                     @NonNull List<String> refinements) {
        super(field, refinements);
        this.placeId = null;
    }

    /**
     * Create a mongo filter representation of this expression.
     * Examples:
     *
     * <pre>{@code
     * {
     *     equals: {
     *         value: "value",
     *         path: "indexables.attribute"
     *     }
     * }
     *
     * {
     *     in: {
     *         value: [
     *             "value",
     *             "value_1"
     *         ],
     *         path: "indexables.attribute"
     *     }
     * }
     * }</pre>
     *
     * Local inventories:
     *
     * <pre>{@code
     *
     * {
     *    embeddedDocument: {
     *        path: "indexables.localInventories",
     *        operator: {
     *            compound: {
     *                filter: [
     *                    {
     *                        equals: {
     *                            path: "indexables.localInventories.placeId",
     *                            value: "placeId",
     *                        }
     *                    },
     *                    {
     *                        equals: {
     *                            path: "indexables.localInventories.attribute",
     *                            value: value,
     *                        }
     *                    }
     *                ]
     *            }
     *        }
     *    }
     * }
     *
     * {
     *     embeddedDocument: {
     *         path: "indexables.localInventories",
     *         operator: {
     *             compound: {
     *                 filter: [
     *                     {
     *                         equals: {
     *                             path: "indexables.localInventories.placeId",
     *                             value: "placeId",
     *                         }
     *                     },
     *                     {
     *                         in: {
     *                             value : [
     *                                 "value",
     *                                 "value_1"
     *                             ],
     *                             path: "indexables.attribute"
     *                         }
     *                     }
     *                 ]
     *             }
     *         }
     *     }
     * }
     * }</pre>
     *
     * @return mongo filter object.
     */
    @NonNull
    @Override
    public Document toFilter() {
        var operator = EQUALS;
        Object value = refinements.getFirst();

        if (refinements.size() > 1) {
            operator = IN;
            value = refinements;
        }

        var filter = createDocumentForSearchFilter(operator, INDEXABLES_PREFIX + field, value);

        if (placeId == null) {
            return filter;
        }

        return createInventoryFilter(placeId, filter);
    }

}
