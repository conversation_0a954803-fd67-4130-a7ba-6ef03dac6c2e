package com.groupbyinc.search.ssa.mongo.settings;

import com.groupbyinc.search.ssa.features.FeaturesManager;

import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Value;
import io.micronaut.core.util.StringUtils;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import static com.groupbyinc.search.ssa.api.utils.HttpUtils.getRequestContext;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_FACET_SAMPLE_PERCENTAGE;
import static com.groupbyinc.search.ssa.features.FeaturesManager.FeatureFlagNames.ENABLE_MONGO_BROWSE_ENGINE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.SEARCH_INDEX_SUFFIX;

@Context
@RequiredArgsConstructor
public class MongoSettingsStorage {

    private final FeaturesManager featuresManager;

    @Getter
    @Value("${mongo.timeout.meta}")
    private int searchMetaTimeoutInSeconds;

    @Getter
    @Value("${mongo.timeout.search}")
    private int searchTimeoutInSeconds;

    @Getter
    @Value("${mongo.search-index-suffix-override}")
    private String searchIndexSuffixOverride;

    @Getter
    @Value("${mongo.analytics.default-value}")
    private double analyticsDefaultValue;

    @Getter
    @Value("${mongo.analytics.multiplier}")
    private double analyticsMultiplier;

    @Getter
    @Value("${mongo.part-number.match-score-multiplier}")
    private int partNumberMatchScoreMultiplier;

    @Getter
    @Value("${mongo.part-number.boost-score-multiplier}")
    private int partNumberBoostScoreMultiplier;

    @Getter
    @Value("${mongo.part-number.exact-match-multiplier}")
    private int partNumberExactMatchMultiplier;

    @Getter
    @Value("${mongo.facets.dynamic-facets-count}")
    private int dynamicFacetsCount;

    public MongoFeatureSettings getMongoSettings() {
        return featuresManager.getObjectFlagConfiguration(
            getRequestContext().getLdContext(),
            ENABLE_MONGO_BROWSE_ENGINE,
            MongoFeatureSettings.class,
            MongoFeatureSettings.DEFAULT
        );
    }

    public FacetSamplePercentageSettings getFacetSamplePercentageSettings() {
        return featuresManager.getObjectFlagConfiguration(
            getRequestContext().getLdContext(),
            ENABLE_FACET_SAMPLE_PERCENTAGE,
            FacetSamplePercentageSettings.class,
            FacetSamplePercentageSettings.DEFAULT
        );
    }

    public String getSearchIndexName() {
        var collection = getRequestContext().getCollection();
        var suffixOverride = StringUtils.trimLeadingCharacter(searchIndexSuffixOverride, '_');
        if (StringUtils.isEmpty(suffixOverride)) {
            return collection + SEARCH_INDEX_SUFFIX;
        }
        return "%s_%s".formatted(collection, suffixOverride);
    }

}
