package com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical;

import com.groupbyinc.search.ssa.application.core.search.filtering.expression.numerical.RangeExpression;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import org.bson.Document;

import javax.annotation.Nonnull;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.GT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.GTE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.INDEXABLES_PREFIX;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.LT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.LTE;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.createDocumentForRangeSearchFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.createInventoryFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.parseRange;

/**
 * Expression for a numeric range for mongo search.
 * <p>
 * Used to filter products by specific numeric range in some facet(attribute).
 * In case when inventory attribute is used, it will create a special filter based on an embedded document.
 *
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/range/">Range doc</a>
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/embedded-document/">EMBEDDED DOCUMENT Doc</a>
 */
public class MongoSearchRangeExpression extends RangeExpression<Document> {

    private final String placeId;
    private final String lowerOperator;
    private final String upperOperator;

    /**
     * Create a range expression from string values.
     *
     * @param field      Facet name.
     * @param lowerBound A lower_bound is either a double or "*", which represents negative infinity.
     *                   Explicitly specify inclusive bound with the character 'i' or exclusive bound
     *                   with the character 'e', (double, [ "e" | "i" ]) | "*"
     * @param upperBound An upper_bound is either a double or "*", which represents infinity.
     *                   Explicitly specify inclusive bound with the character 'i' or exclusive bound
     *                   with the character 'e', (double, [ "e" | "i" ]) | "*"
     *
     * @return expression for a numeric range for mongo search.
     */
    public static MongoSearchRangeExpression fromRaw(@NonNull String field,
                                                     @Nullable String placeId,
                                                     @Nullable String lowerBound,
                                                     @Nullable String upperBound) {
        var parsed = parseRange(lowerBound, upperBound, GTE, GT, LT, LTE);

        return new MongoSearchRangeExpression(
            field,
            placeId,
            parsed.lower(),
            parsed.upper(),
            parsed.lowerOperator(),
            parsed.upperOperator()
        );
    }

    public static MongoSearchRangeExpression greaterThan(@NonNull String field,
                                                         @Nullable String placeId,
                                                         @NonNull Number value) {
        return new MongoSearchRangeExpression(field, placeId, value, null, GT, null);
    }

    public static MongoSearchRangeExpression greaterThanOrEqual(@NonNull String field,
                                                                @Nullable String placeId,
                                                                @NonNull Number value) {
        return new MongoSearchRangeExpression(field, placeId, value, null, GTE, null);
    }

    public static MongoSearchRangeExpression lessThan(@NonNull String field,
                                                      @Nullable String placeId,
                                                      @NonNull Number value) {
        return new MongoSearchRangeExpression(field, placeId, value, null, LT, null);
    }

    public static MongoSearchRangeExpression lessThanOrEqual(@NonNull String field,
                                                             @Nullable String placeId,
                                                             @NonNull Number value) {
        return new MongoSearchRangeExpression(field, placeId, value, null, LTE, null);
    }

    public static MongoSearchRangeExpression of(@NonNull String field,
                                                @Nullable String placeId,
                                                @Nullable Number lower,
                                                @Nullable Number upper) {
        return new MongoSearchRangeExpression(field, placeId, lower, upper, GTE, LT);
    }

    private MongoSearchRangeExpression(@NonNull String field,
                                       @Nullable String placeId,
                                       @Nullable Number lower,
                                       @Nullable Number upper,
                                       @Nullable String lowerOperator,
                                       @Nullable String upperOperator) {
        super(field, lower, upper);
        this.placeId = placeId;
        this.lowerOperator = lowerOperator;
        this.upperOperator = upperOperator;
    }

    /**
     * Create a mongo filter representation of this expression.
     * Example:
     *
     * <pre>{@code
     * Usual:
     * {
     *     range: {
     *         gte: 100.0,
     *         lt: 999998.99,
     *         path: "indexables.attribute"
     *     }
     * }
     *
     * Local inventories:
     *
     * {
     *    embeddedDocument: {
     *        path: "indexables.localInventories",
     *        operator: {
     *            compound: {
     *                filter: [
     *                    {
     *                        equals: {
     *                            path: "indexables.localInventories.placeId",
     *                            value: "placeId",
     *                        }
     *                    },
     *                    {
     *                        range: {
     *                            gte: 100.0,
     *                            lt: 999998.99,
     *                            path: "indexables.attribute"
     *                        }
     *                    }
     *                ]
     *            }
     *        }
     *    }
     * }
     *
     * }</pre>
     *
     * @return mongo filter for 'range' operator.
     */
    @Nonnull
    @Override
    public Document toFilter() {
        var filter = createDocumentForRangeSearchFilter(
            INDEXABLES_PREFIX + field,
            lower,
            upper,
            lowerOperator,
            upperOperator
        );

        if (placeId == null) {
            return filter;
        }

        return createInventoryFilter(placeId, filter);
    }

}
