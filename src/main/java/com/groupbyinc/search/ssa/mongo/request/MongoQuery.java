package com.groupbyinc.search.ssa.mongo.request;

import lombok.Builder;
import lombok.With;
import org.bson.Document;

import java.util.List;

import static com.groupbyinc.search.ssa.util.CollectionUtils.notNullOrDefaultList;

@Builder
public record MongoQuery(MongoPipeline searchPipeline, List<MongoPipeline> facetPipelines) {
    public MongoQuery {
        facetPipelines = notNullOrDefaultList(facetPipelines);
    }
}
