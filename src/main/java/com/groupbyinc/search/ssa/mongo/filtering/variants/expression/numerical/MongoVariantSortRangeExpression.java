package com.groupbyinc.search.ssa.mongo.filtering.variants.expression.numerical;

import com.groupbyinc.search.ssa.api.error.ProcessingException;
import com.groupbyinc.search.ssa.application.core.search.filtering.expression.numerical.RangeExpression;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import org.bson.Document;

import javax.annotation.Nonnull;

import java.util.List;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_AND;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_GT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_GTE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_LT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_LTE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_MAX_N;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_MIN_N;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.VARIANT_DOUBLE_DOLLAR_PREFIX;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.VARIANT_LOCAL_INVENTORIES_SHORT_DOUBLE_DOLLAR_PREFIX;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.caseBodyWithIsArrayCheck;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.createInventorySortFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.getBoundOperatorForArray;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.parseRange;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.switchWithOneCase;

import static java.lang.Double.isInfinite;

/**
 * Expression for a numeric range for mongo variant sorting filter.
 * <p>
 * Used to filter products by specific numeric range in some facet(attribute).
 * In case when inventory attribute is used, it will create a special filter.
 *
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/not/"> $not expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/gt/"> $gt expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/gte/"> $gte expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/lt/"> $lt expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/lte/"> $lte expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/or/"> $or expression Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/map/"> $map Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/and/"> $and Doc </a>
 * @see <a href="https://www.mongodb.com/docs/manual/reference/operator/aggregation/anyElementTrue/">
 *      $anyElementTrue Doc
 *      </a>
 */
public class MongoVariantSortRangeExpression extends RangeExpression<Document> {

    private final String prefix;
    private final String placeId;
    private final String lowerOperator;
    private final String upperOperator;

    /**
     * Create a range expression from string values.
     *
     * @param field      Facet name.
     * @param lowerBound A lower_bound is either a double or "*", which represents negative infinity.
     *                   Explicitly specify inclusive bound with the character 'i' or exclusive bound
     *                   with the character 'e', (double, [ "e" | "i" ]) | "*"
     * @param upperBound An upper_bound is either a double or "*", which represents infinity.
     *                   Explicitly specify inclusive bound with the character 'i' or exclusive bound
     *                   with the character 'e', (double, [ "e" | "i" ]) | "*"
     *
     * @return expression for a numeric range for mongo search.
     */
    public static MongoVariantSortRangeExpression fromRaw(@NonNull String field,
                                                          @Nullable String placeId,
                                                          @Nullable String lowerBound,
                                                          @Nullable String upperBound) {
        var parsed = parseRange(lowerBound, upperBound, $_GTE, $_GT, $_LT, $_LTE);

        return new MongoVariantSortRangeExpression(
            field,
            placeId,
            parsed.lower(),
            parsed.upper(),
            parsed.lowerOperator(),
            parsed.upperOperator()
        );
    }

    public static MongoVariantSortRangeExpression greaterThan(@NonNull String field,
                                                              @Nullable String placeId,
                                                              @NonNull Number value) {
        return new MongoVariantSortRangeExpression(field, placeId, value, null, $_GT, null);
    }

    public static MongoVariantSortRangeExpression greaterThanOrEqual(@NonNull String field,
                                                                     @Nullable String placeId,
                                                                     @NonNull Number value) {
        return new MongoVariantSortRangeExpression(field, placeId, value, null, $_GTE, null);
    }

    public static MongoVariantSortRangeExpression lessThan(@NonNull String field,
                                                           @Nullable String placeId,
                                                           @NonNull Number value) {
        return new MongoVariantSortRangeExpression(field, placeId, value, null, $_LT, null);
    }

    public static MongoVariantSortRangeExpression lessThanOrEqual(@NonNull String field,
                                                                  @Nullable String placeId,
                                                                  @NonNull Number value) {
        return new MongoVariantSortRangeExpression(field, placeId, value, null, $_LTE, null);
    }

    public static MongoVariantSortRangeExpression of(@NonNull String field,
                                                     @Nullable String placeId,
                                                     @Nullable Number lower,
                                                     @Nullable Number upper) {
        return new MongoVariantSortRangeExpression(field, placeId, lower, upper, $_GTE, $_LT);
    }

    private MongoVariantSortRangeExpression(@NonNull String field,
                                            @Nullable String placeId,
                                            @Nullable Number lower,
                                            @Nullable Number upper,
                                            @Nullable String lowerOperator,
                                            @Nullable String upperOperator) {
        super(field, lower, upper);
        this.placeId = placeId;
        this.lowerOperator = lowerOperator;
        this.upperOperator = upperOperator;

        this.prefix = placeId == null
            ? VARIANT_DOUBLE_DOLLAR_PREFIX
            : VARIANT_LOCAL_INVENTORIES_SHORT_DOUBLE_DOLLAR_PREFIX;
    }

    /**
     * Create a mongo filter representation of this expression.
     * Example:
     *
     * <pre>{@code
     * $switch: {
     *   branches: [
     *     {
     *       case: {
     *         $isArray:
     *         "$$variant.attributes.discountPercentage",
     *       },
     *       then: {
     *         $and: [
     *           {
     *             $gte: [
     *               {
     *                 $arrayElemAt:
     *                 [
     *                   {
     *                     $minN:
     *                     {
     *                       n: 1,
     *                       input:
     *                       "$$variant.attributes.discountPercentage",
     *                     },
     *                   },
     *                   0,
     *                 ],
     *               },
     *               10.0,
     *             ],
     *           },
     *           {
     *             $lte: [
     *               {
     *                 $arrayElemAt:
     *                 [
     *                   {
     *                     $maxN:
     *                     {
     *                       n: 1,
     *                       input:
     *                       "$$variant.attributes.discountPercentage",
     *                     },
     *                   },
     *                   0,
     *                 ],
     *               },
     *               20.0,
     *             ],
     *           },
     *         ],
     *       },
     *     },
     *   ],
     *   default: {
     *     $and: [
     *       {
     *         $gte: [
     *           "$$variant.attributes.discountPercentage",
     *           10.0,
     *         ],
     *       },
     *       {
     *         $lte: [
     *           "$$variant.attributes.discountPercentage",
     *           20.0,
     *         ],
     *       },
     *     ],
     *   },
     * }
     * }</pre>
     *
     * LocalInventories:
     * <pre>{@code
    {
     *   $anyElementTrue: {
     *     $map: {
     *       input: "$$variant.localInventories",
     *       as: "lv",
     *       in: {
     *         $and: [
     *           {
     *             $eq: [
     *               "$$lv.placeId",
     *               "3",
     *             ],
     *           },
     *           {
     *             $switch: {
     *               branches: [
     *                 {
     *                   case: {
     *                     $isArray:
     *                     "$$lv.attributes.discountPercentage",
     *                   },
     *                   then: {
     *                     $and: [
     *                       {
     *                         $gte: [
     *                           {
     *                             $arrayElemAt:
     *                             [
     *                               {
     *                                 $minN:
     *                                 {
     *                                   n: 1,
     *                                   input:
     *                                   "$$lv.attributes.discountPercentage",
     *                                 },
     *                               },
     *                               0,
     *                             ],
     *                           },
     *                           10.0,
     *                         ],
     *                       },
     *                       {
     *                         $lte: [
     *                           {
     *                             $arrayElemAt:
     *                             [
     *                               {
     *                                 $maxN:
     *                                 {
     *                                   n: 1,
     *                                   input:
     *                                   "$$lv.attributes.discountPercentage",
     *                                 },
     *                               },
     *                               0,
     *                             ],
     *                           },
     *                           20.0,
     *                         ],
     *                       },
     *                     ],
     *                   },
     *                 },
     *               ],
     *               default: {
     *                 $and: [
     *                   {
     *                     $gte: [
     *                       "$$lv.attributes.discountPercentage",
     *                       10.0,
     *                     ],
     *                   },
     *                   {
     *                     $lte: [
     *                       "$$lv.attributes.discountPercentage",
     *                       20.0,
     *                     ],
     *                   },
     *                 ],
     *               },
     *             }
     *           },
     *         ]
     *       },
     *     },
     *   },
     * }
     * }</pre>
     *
     * @return mongo variant sort filter for 'range' operator.
     */
    @Nonnull
    @Override
    public Document toFilter() {
        var path = prefix + field;

        Document caseBody = null;
        Document defaultBody = null;

        if (lower != null && upper != null && !isInfinite(upper.doubleValue()) && !isInfinite(lower.doubleValue())) {
            caseBody = new Document(
                $_AND,
                List.of(
                    new Document(
                        lowerOperator,
                        List.of(getBoundOperatorForArray(path, $_MIN_N), lower)
                    ),
                    new Document(
                        upperOperator,
                        List.of(getBoundOperatorForArray(path, $_MAX_N), upper)
                    )
                )
            );
            defaultBody = new Document(
                $_AND,
                List.of(
                    new Document(lowerOperator, List.of(path, lower)),
                    new Document(upperOperator, List.of(path, upper))
                )
            );
        } else if (lower != null && !isInfinite(lower.doubleValue())) {
            caseBody = new Document(
                lowerOperator,
                List.of(
                    getBoundOperatorForArray(path, $_MIN_N),
                    lower
                )
            );
            defaultBody = new Document(lowerOperator, List.of(path, lower));
        } else if (upper != null && !isInfinite(upper.doubleValue())) {
            caseBody = new Document(
                upperOperator,
                List.of(
                    getBoundOperatorForArray(path, $_MAX_N),
                    upper
                )
            );
            defaultBody = new Document(upperOperator, List.of(path, upper));
        }

        if (caseBody != null) {
            var caseBodyFull = caseBodyWithIsArrayCheck(path, caseBody);
            var sortFilter = switchWithOneCase(caseBodyFull, defaultBody);

            if (placeId == null) {
                return sortFilter;
            }

            return createInventorySortFilter(sortFilter, placeId);
        } else {
            throw new ProcessingException("No valid filtering expression found");
        }
    }

}
