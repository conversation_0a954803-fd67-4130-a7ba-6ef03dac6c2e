package com.groupbyinc.search.ssa.mongo.filtering.variants.filter;

import com.groupbyinc.search.ssa.application.core.search.filtering.filter.Filter;
import com.groupbyinc.search.ssa.application.core.search.filtering.filter.ToFilter;

import org.bson.Document;

import javax.annotation.Nonnull;
import java.util.ArrayList;
import java.util.List;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_AND;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_OR;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.FILTER;

/**
 * Used to accumulate all filters applied to the mongo search request.
 *
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/compound/">Compound operator Doc</a>
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/compound/#mongodb-data-filter">Filter clause Doc</a>
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/equals/">Equals operator Doc</a>
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/in/">In operator Doc</a>
 * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/range/">Range operator Doc</a>
 */
public class MongoVariantSortFilter extends Filter<Document> {

    public MongoVariantSortFilter() {
        super(new ArrayList<>(), new ArrayList<>());
    }

    /**
     * Transforms stored "and" and "or" expressions into a retail search filter string.
     * This method combines expressions with logical "AND" and "OR" conditions to construct
     * a query that can be used to filter search results in a retail context.
     * <p>
     * Process:
     * 1. All "and" expressions are joined using the logical "AND".
     * 2. All "or" expressions are individually wrapped with parentheses and then joined using the logical ""OR.
     * <p>
     * Finally, if both "and" and "or" expressions joined using the logical "AND".
     * This method ensures that the final filter string is properly formatted for use
     * in search queries, applying all specified filters to refine search results.
     *
     * @return A filter formatted for mongo search.
     */
    @Nonnull
    @Override
    public Document toFilter() {
        List<Document> filters = new ArrayList<>();

        if (!ands.isEmpty()) {
            addFilters(ands, filters, $_AND);
        }

        if (!ors.isEmpty()) {
            addFilters(ors, filters, $_OR);
        }

        return new Document(FILTER, filters);
    }

    private void addFilters(List<ToFilter<Document>> source, List<Document> filtersToAdd, String operator) {
        var filters = new ArrayList<Document>();
        source.forEach(filter -> {
            if (filter instanceof MongoVariantSortFilter mf) {
                filters.addAll(mf.toFilter().getList(FILTER, Document.class));
            } else {
                filters.add(filter.toFilter());
            }
        });

        addToFilters(filtersToAdd, filters, operator);
    }

    private void addToFilters(List<Document> filters, List<Document> filtersToAdd, String operator) {
        if (filtersToAdd.size() == 1) {
            filters.add(filtersToAdd.getFirst());
        } else {
            filters.add(
                new Document(
                    operator,
                    filtersToAdd
                )
            );
        }
    }

}
