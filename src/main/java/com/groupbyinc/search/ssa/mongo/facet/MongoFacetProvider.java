package com.groupbyinc.search.ssa.mongo.facet;

import com.groupbyinc.search.ssa.application.core.search.converter.ResolvedNavigationConfiguration;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.mongo.converter.MongoSearch;
import com.groupbyinc.search.ssa.mongo.facet.category.CategoryBucketsProvider;
import com.groupbyinc.search.ssa.mongo.filtering.AbstractMongoFilterService;
import com.groupbyinc.search.ssa.mongo.filtering.search.MongoSearchFilterService;
import com.groupbyinc.search.ssa.mongo.request.MongoPipeline;
import com.groupbyinc.search.ssa.mongo.search.MongoPartNumberSearch;
import com.groupbyinc.search.ssa.mongo.settings.MongoSettingsStorage;

import io.micronaut.context.annotation.Context;
import io.micronaut.core.util.CollectionUtils;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Predicate;
import java.util.stream.Stream;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.BOUNDARIES;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.BUCKETS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.COUNT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.DEFAULT_NUM_RANGES;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.DYNAMIC_FACETS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.FACET;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.FILTER;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.GTE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.ID_UNDERSCORE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.INDEXABLES_PREFIX;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.LOCAL_INVENTORIES_PREFIX;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.LTE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.META;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.NUMBER;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.NUM_BUCKETS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.PATH;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.STRING;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.TOTAL;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.TYPE;
import static com.groupbyinc.search.ssa.mongo.MongoSearchEngine.getSearchParamsWithExcludedRefinement;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.createDocumentForRangeSearchFilter;
import static com.groupbyinc.search.ssa.mongo.request.PipelineType.SEARCH_DYNAMIC_FACETS;
import static com.groupbyinc.search.ssa.mongo.request.PipelineType.SEARCH_DYNAMIC_FACETS_SAMPLE;
import static com.groupbyinc.search.ssa.mongo.request.PipelineType.SEARCH_FACETS;
import static com.groupbyinc.search.ssa.mongo.request.PipelineType.SEARCH_FACETS_SAMPLE;
import static com.groupbyinc.search.ssa.mongo.settings.FacetSamplePercentageSettings.SAMPLE_PRECISION_FIELD;
import static com.groupbyinc.search.ssa.mongo.util.DocumentUtils.getAsLong;
import static com.groupbyinc.search.ssa.mongo.util.DocumentUtils.getAsString;
import static com.groupbyinc.search.ssa.util.AttributeUtils.transformFieldForMongo;

import static io.micronaut.core.util.CollectionUtils.isNotEmpty;
import static java.lang.Math.abs;
import static java.lang.Math.min;
import static java.lang.Math.sqrt;
import static java.util.Objects.requireNonNullElse;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

@Slf4j
@Context
@RequiredArgsConstructor
public class MongoFacetProvider {
    private static final Predicate<ResolvedNavigationConfiguration> UNRESOLVED_RANGE_NAV_PREDICATE =
        nav -> nav.type() == NavigationType.RANGE && CollectionUtils.isEmpty(nav.ranges());

    private final MongoSettingsStorage mongoSettingsStorage;
    private final MongoSearchFilterService mongoSearchFilterService;
    private final CategoryBucketsProvider categoryBucketProvider;

    public Document buildDynamicFacet() {
        var facets = new Document();
        addDynamicFacetsFacet(facets);
        return facets;
    }

    public List<MongoPipeline> createFacetsSearchPipelinesForRefinements(
        SearchParameters searchParameters,
        Document baseFilter,
        Map<String, Set<String>> multiSelectNavScopedProductIds) {

        var facetPipelines = new ArrayList<MongoPipeline>();

        addNonFilteredFacets(searchParameters, facetPipelines, baseFilter);
        addFilteredFacets(searchParameters, facetPipelines, multiSelectNavScopedProductIds);

        return facetPipelines;
    }

    public List<String> extractDynamicFacetKeys(Document searchResults) {
        var meta = searchResults.get(META, Document.class);
        if (meta == null) {
            return List.of();
        }

        var facet = meta.get(FACET, Document.class);
        if (facet == null) {
            return List.of();
        }

        var dynamicFacets = facet.get(DYNAMIC_FACETS, Document.class);
        if (dynamicFacets == null) {
            return List.of();
        }

        var buckets = dynamicFacets.getList(BUCKETS, Document.class);
        if (buckets == null) {
            return List.of();
        }

        return buckets
            .stream()
            .map(bucket -> getAsString(ID_UNDERSCORE, bucket))
            .toList();
    }

    public MongoPipeline createDynamicFacetsSearchPipeline(SearchParameters searchParameters,
                                                           List<String> dynamicFacetsToRequest,
                                                           Document filter) {
        var facetConverter = searchParameters.getMongoFacetConverter();

        var facetsToRequest = resolveDynamicRanges(
            searchParameters,
            facetConverter.resolvedDynamicNavigations(dynamicFacetsToRequest)
        );

        var facets = buildFacets(
            facetsToRequest,
            searchParameters.getMerchandisingConfiguration().attributeConfigurations(),
            facetConverter.getFacetLimit()
        );

        var type = SEARCH_DYNAMIC_FACETS;
        var addSamplingFilter = isFacetSamplingUsed(searchParameters);

        var filterCopy = new Document(filter);

        if (addSamplingFilter) {
            addFacetSamplingFilter(filterCopy);
            type = SEARCH_DYNAMIC_FACETS_SAMPLE;
        }

        var searchMetaPipeline = buildSearchMetaStage(searchParameters, filterCopy, facets);
        return new MongoPipeline(
            List.of(searchMetaPipeline),
            type
        );
    }

    /**
     * Reorder the set of dynamic facets based on a specified formula that provides a score for a facet based on all of
     * its facet values. The facets that are most likely to narrow down a search are facets that have facet values with
     * relatively equal product counts. E.g.
     * <p>
     * 1. Each product count c is divided by the total number of products, and its square root is taken. 2. A penalty on
     * each facet value is produced, based on how far away from the ideal product count it should be.
     * <p>
     * With 100 thousand products and 4 facet values, each would ideally cover 25,000 products. 3. The penalty is
     * subtracted from the base score, and the scores for each facet value are summed for the facet.
     *
     * @param mainQueryResult           document with products and 'dynamic_facets' value
     * @param dynamicFacetsQueryResults document with unordered 'facet' result
     */
    public void setTopDynamicNavigations(@Nonnull Document mainQueryResult,
                                         @Nonnull Document dynamicFacetsQueryResults) {
        var meta = mainQueryResult.get(META, Document.class);
        var total = getAsLong(
            TOTAL,
            meta.get(COUNT, Document.class)
        );

        var dynamicFacets = dynamicFacetsQueryResults.get(FACET, Document.class);
        var scoreMap = new HashMap<String, Double>();

        dynamicFacets.forEach((key, value) -> {
            var buckets = ((Document) value).getList(BUCKETS, Document.class);
            // numValues:= the number of facet values for a given facet with existing values only.
            var numValues = (int) buckets
                .stream()
                .map(bucket -> getAsLong(COUNT, bucket))
                .filter(count -> count != 0)
                .count();

            var score = 0.0;

            if (numValues > 0) {
                // idealCount:= the total number of products divided by the number of facets
                var idealCount = total / numValues;

                // ci:= c1, c2, …, cn count of products for a given facet value
                // ri:= rooted ratio
                // pi:= p1, p2, …, pn penalty assigned to a specific facet value for being non-ideal
                //                 (for example not enough products, too many products)
                // si:= s1, s2, …, sn the score assigned to a given facet value within a facet
                for (var bucket : buckets) {
                    var ci = getAsLong(COUNT, bucket);
                    var ri = sqrt(ci.doubleValue() / total.doubleValue());
                    var pi = min(abs((double) idealCount - ci), ci) / total;
                    var si = (ri - pi) / 2;
                    score = score + si;
                }
                score = score / 10;
            }

            scoreMap.put(key, score);
        });
        var reorderedFacets = new Document();
        scoreMap.entrySet()
            .stream()
            .sorted((e1, e2) -> Double.compare(e2.getValue(), e1.getValue())) // descending order
            .map(Map.Entry::getKey)
            .limit(mongoSettingsStorage.getDynamicFacetsCount())
            .forEach(s -> reorderedFacets.append(s, dynamicFacets.get(s)));

        dynamicFacetsQueryResults.replace(FACET, reorderedFacets);
    }

    private void addNonFilteredFacets(SearchParameters searchParameters,
                                      List<MongoPipeline> facetPipelines,
                                      Document baseFilter) {
        var facets = getFacetsWithoutUsedInRefinements(searchParameters);
        if (facets.isEmpty()) {
            return;
        }

        addFacetPipeline(searchParameters, facetPipelines, baseFilter, facets);
    }


    private void addFilteredFacets(SearchParameters searchParameters,
                                   List<MongoPipeline> facetPipelines,
                                   Map<String, Set<String>> multiSelectNavScopedProductIds) {
        var facetConverter = searchParameters.getMongoFacetConverter();

        var facetsToRequest = facetConverter
            .getFacetsToRequestWithSearchRequest(s -> facetConverter.getExcludedFacets().contains(s))
            .stream()
            .toList();

        var resolvedNavigationConfigurations = resolveDynamicRanges(searchParameters, facetsToRequest)
            .stream()
            .collect(toMap(ResolvedNavigationConfiguration::field, identity()));

        List<ResolvedNavigationConfiguration> selectedDynamicFacets = new ArrayList<>();
        facetConverter.getExcludedFacets().forEach( facet -> {
            if (!resolvedNavigationConfigurations.containsKey(facet)) {
                var resolvedNavigationConfiguration = facetConverter.resolveNavigationConfiguration(facet);
                if (resolvedNavigationConfiguration != null) {
                    selectedDynamicFacets.add(resolvedNavigationConfiguration);
                }
            }
        });

        Stream
            .concat(
                resolvedNavigationConfigurations.values().stream(),
                resolveDynamicRanges(searchParameters, selectedDynamicFacets).stream()
            )
            .forEach(navigation -> {
                var field = transformFieldForMongo(
                    navigation.field(),
                    searchParameters.getMerchandisingConfiguration().attributeConfigurations()
                );

                if (!field.startsWith(LOCAL_INVENTORIES_PREFIX)) {
                    var facet = createFacet(
                        field,
                        navigation.type(),
                        searchParameters.getFacetLimit(),
                        navigation.ranges()
                    );

                    var facets = new Document(field, facet);

                    // Check if this is a multi-select navigation and if it has scoped product IDs
                    var navField = navigation.field();
                    Document filterToUse;

                    var searchParamsWithoutFilter = getSearchParamsWithExcludedRefinement(searchParameters, navField);
                    if (multiSelectNavScopedProductIds.containsKey(navField) &&
                        !multiSelectNavScopedProductIds.get(navField).isEmpty()) {
                        // This is an active multi-select navigation under PN expansion
                        filterToUse = mongoSearchFilterService.createFilterForMultiSelectFacet(
                            searchParamsWithoutFilter,
                            navField,
                            multiSelectNavScopedProductIds.get(navField)
                        );
                    } else {
                        filterToUse = mongoSearchFilterService.createFilter(searchParamsWithoutFilter);
                    }

                    addFacetPipeline(searchParamsWithoutFilter, facetPipelines, filterToUse, facets);
                }
            });
    }

    private void addFacetPipeline(SearchParameters searchParameters,
                                 List<MongoPipeline> facetPipelines,
                                 Document filter,
                                 Document facets) {
        var type = SEARCH_FACETS;
        var addSamplingFilter = isFacetSamplingUsed(searchParameters);
        if (addSamplingFilter) {
            addFacetSamplingFilter(filter);
            type = SEARCH_FACETS_SAMPLE;
        }

        var pipeline = buildSearchMetaStage(searchParameters, filter, facets);
        facetPipelines.add(
            new MongoPipeline(List.of(pipeline), type)
        );
    }

    private Document getFacetsWithoutUsedInRefinements(SearchParameters searchParameters) {
        var facetConverter = searchParameters.getMongoFacetConverter();
        var facets = facetConverter
            .getFacetsToRequestWithSearchRequest(s -> !facetConverter.getExcludedFacets().contains(s));

        if (facets.isEmpty()) {
            return new Document();
        }

        var facetsToRequest = resolveDynamicRanges(searchParameters, facets);

        return buildFacets(
            facetsToRequest,
            searchParameters.getMerchandisingConfiguration().attributeConfigurations(),
            facetConverter.getFacetLimit()
        );
    }

    private Document buildFacets(List<ResolvedNavigationConfiguration> facetsToRequest,
                                 Map<String, AttributeConfiguration> attributeConfigurations,
                                 Integer facetLimit) {
        var facets = new Document();

        facetsToRequest.forEach(navigation -> {
            var field = transformFieldForMongo(navigation.field(), attributeConfigurations);

            if (!field.startsWith(LOCAL_INVENTORIES_PREFIX)) {
                var facet = createFacet(field, navigation.type(), facetLimit, navigation.ranges());

                facets.put(field, facet);
            }
        });

        return facets;
    }

    private void addDynamicFacetsFacet(Document facets) {
        facets.append(
            DYNAMIC_FACETS,
            new Document(TYPE, STRING)
                .append(PATH, DYNAMIC_FACETS)
                .append(NUM_BUCKETS, mongoSettingsStorage.getDynamicFacetsCount() * 2)
        );
    }

    private Document createFacet(String field, NavigationType type, Integer facetLimit, List<Range> ranges) {
        var path = INDEXABLES_PREFIX + field;

        return switch (type) {
            case VALUE -> new Document(TYPE, STRING)
                .append(PATH, path)
                .append(NUM_BUCKETS, facetLimit);
            case RANGE -> new Document(TYPE, NUMBER)
                .append(PATH, path)
                .append(BOUNDARIES, rangesToBoundaries(ranges));
        };
    }

    private List<Double> rangesToBoundaries(List<Range> ranges) {
        var boundaries = new TreeSet<Double>();
        for (var range : ranges) {
            if (range.low() != null) {
                boundaries.add(range.low());
            }
            if (range.high() != null) {
                boundaries.add(range.high());
            }
        }

        return new ArrayList<>(boundaries);
    }

    private Document buildSearchMetaStage(SearchParameters searchParameters, Document filter, Document facets) {
        var searchIndex = mongoSettingsStorage.getSearchIndexName();

        var searchMeta = new MongoSearch(searchIndex);
        searchMeta.setConcurrent(mongoSettingsStorage.getMongoSettings().enabledMultiThreading());

        if (isNotEmpty(facets)) {
            searchMeta
                .facet()
                .appendFacets(facets);
        }

        var filters = filter.getList(FILTER, Document.class);
        if (isNotEmpty(filters)) {
            searchMeta
                .facet()
                .operator()
                .appendFilters(filters);
        }

        var partNumberSearch = new MongoPartNumberSearch(searchParameters, mongoSettingsStorage);
        if (Boolean.TRUE.equals(searchParameters.getPartNumberSearchEnabled()) && partNumberSearch.isApplicable()) {
            searchMeta
                .facet()
                .operator()
                .appendMust(partNumberSearch.getCompoundSearchClause());
        }

        return searchMeta.toSearchMetaDocument();
    }

    private boolean isFacetSamplingUsed(SearchParameters searchParameters) {
        var usedFields = AbstractMongoFilterService.getUsedFields(searchParameters);

        if (!searchParameters.getQuery().isEmpty()) {
            return false;
        }

        if (usedFields.isEmpty()) {
            return true;
        }

        if (usedFields.size() > 1) {
            return false;
        }

        return mongoSettingsStorage
            .getFacetSamplePercentageSettings()
            .sampleFields()
            .contains(usedFields.stream().findFirst().orElse(null));
    }

    private void addFacetSamplingFilter(Document filter) {
        var settings = mongoSettingsStorage.getFacetSamplePercentageSettings();

        var samplingFilter = createDocumentForRangeSearchFilter(
            SAMPLE_PRECISION_FIELD,
            settings.min(),
            settings.max(),
            GTE,
            LTE
        );

        filter.getList(FILTER, Document.class).add(samplingFilter);
    }

    private List<ResolvedNavigationConfiguration> resolveDynamicRanges(
        SearchParameters searchParameters,
        List<ResolvedNavigationConfiguration> facetsToRequest
    ) {
        // Numeric facets without range configurations
        var unresolvedRangeNavs = facetsToRequest.stream()
            .filter(UNRESOLVED_RANGE_NAV_PREDICATE)
            .map(ResolvedNavigationConfiguration::field)
            .toList();

        if (!unresolvedRangeNavs.isEmpty()) {
            var navRanges = categoryBucketProvider.resolveNumRanges(searchParameters, unresolvedRangeNavs);
            return facetsToRequest
                .stream()
                .map(nav -> {
                    if (UNRESOLVED_RANGE_NAV_PREDICATE.test(nav)) {
                        return nav.withRanges(requireNonNullElse(navRanges.get(nav.field()), DEFAULT_NUM_RANGES));
                    }
                    return nav;
                }).toList();
        }

        return facetsToRequest;
    }

}
