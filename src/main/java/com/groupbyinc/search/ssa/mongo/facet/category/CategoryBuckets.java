package com.groupbyinc.search.ssa.mongo.facet.category;

import com.groupbyinc.search.ssa.core.navigation.Range;

import io.micronaut.core.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.groupbyinc.search.ssa.mongo.MongoConstants.BUCKETS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.CATEGORY;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.COUNT;

@Slf4j
public record CategoryBuckets(String name, Integer count, Map<String, List<Range>> fieldRanges) {

    static List<CategoryBuckets> fromBson(List<Document> docs) {
        if (CollectionUtils.isEmpty(docs)) {
            return List.of();
        }
        return docs.stream().map(CategoryBuckets::fromBson).toList();
    }

    static CategoryBuckets fromBson(Document doc) {
        try {
            var name = doc.get(CATEGORY, String.class);
            var count = doc.get(COUNT, Number.class).intValue();
            var flattenedBuckets = new HashMap<String, List<Range>>();
            flatten("", doc.get(BUCKETS, Document.class), flattenedBuckets);
            return new CategoryBuckets(name, count, flattenedBuckets);
        } catch (Exception e) {
            log.error("Error parsing bson: {}", e.getMessage(), e);
            return new CategoryBuckets("", 0, Map.of());
        }
    }

    private static void flatten(String prefix, Document doc, Map<String, List<Range>> flattened) {
        for (var key : doc.keySet()) {
            var value = doc.get(key);
            var newKey = prefix.isEmpty() ? key : prefix + "." + key;
            if (value instanceof Document) {
                flatten(newKey, (Document) value, flattened);
            } else if (value instanceof List<?> buckets) {
                var ranges = new ArrayList<Range>();
                buckets.forEach(bucketDoc -> {
                    if (bucketDoc instanceof List<?> bucket) {
                        if (bucket.size() != 2) {
                            log.warn("Invalid bucket document size: {}. Expected 2 elements.", bucket.size());
                            return;
                        }
                        ranges.add(new Range(
                            ((Number) bucket.getFirst()).doubleValue(),
                            ((Number) bucket.getLast()).doubleValue()
                        ));
                    } else {
                        log.warn("Unexpected bucket document type: {}. Expected a List.", bucketDoc.getClass());
                    }
                });
                flattened.put(newKey, ranges);
            } else {
                log.warn("Unexpected bson field type: {}, expected Doc or List", value.getClass());
            }
        }
    }
}
