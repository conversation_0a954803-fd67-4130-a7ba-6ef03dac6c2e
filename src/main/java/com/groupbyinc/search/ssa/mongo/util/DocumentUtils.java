package com.groupbyinc.search.ssa.mongo.util;

import io.micronaut.core.annotation.Nullable;
import lombok.experimental.UtilityClass;
import org.bson.Document;

import java.util.List;

@UtilityClass
public class DocumentUtils {

    public static String getAsString(String field, @Nullable Document document) {
        if (document != null && document.containsKey(field)) {
            try {
                return document.getString(field);
            } catch (ClassCastException ignored) {
            }
        }
        return null;
    }

    public static Long getAsLong(String field, @Nullable Document document) {
        if (document != null && document.containsKey(field)) {
            try {
                return document.getLong(field);
            } catch (ClassCastException ignoredOnLongCast) {
                try {
                    return document.getInteger(field).longValue();
                } catch (ClassCastException ignoredOnIntegerCast) {
                }
            }
        }
        return null;
    }

    public static Double getAsDouble(String field, @Nullable Document document) {
        if (document != null && document.containsKey(field)) {
            try {
                return document.getDouble(field);
            } catch (ClassCastException ignored) {
            }
        }
        return null;
    }

    public static List<Document> toList(Document document) {
        return document.values()
            .stream()
            .map(Document.class::cast)
            .toList();
    }

}
