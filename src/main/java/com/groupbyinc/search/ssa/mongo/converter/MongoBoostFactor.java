package com.groupbyinc.search.ssa.mongo.converter;

import com.groupbyinc.search.ssa.core.biasing.Bias;

import java.util.HashMap;
import java.util.Map;

public class MongoBoostFactor {

    private static final Map<Integer, Integer> BOOST_BUCKETS = new HashMap<>();

    static {
        BOOST_BUCKETS.put(1, 1024);
        BOOST_BUCKETS.put(2, 512);
        BOOST_BUCKETS.put(3, 256);
        BOOST_BUCKETS.put(4, 128);
        BOOST_BUCKETS.put(5, 64);
        BOOST_BUCKETS.put(6, 32);
        BOOST_BUCKETS.put(7, 16);
        BOOST_BUCKETS.put(8, 8);
    }

    private static final int BURY_BUCKET = 1024;

    private static final Map<Bias.Strength, Integer> BIAS_BUCKETS = new HashMap<>();

    static {
        BIAS_BUCKETS.put(Bias.Strength.ABSOLUTE_INCREASE, 512);
        BIAS_BUCKETS.put(Bias.Strength.STRONG_INCREASE, 256);
        BIAS_BUCKETS.put(Bias.Strength.MEDIUM_INCREASE, 128);
        BIAS_BUCKETS.put(Bias.Strength.WEAK_INCREASE, 64);
        BIAS_BUCKETS.put(Bias.Strength.WEAK_DECREASE, 64);
        BIAS_BUCKETS.put(Bias.Strength.MEDIUM_DECREASE, 128);
        BIAS_BUCKETS.put(Bias.Strength.STRONG_DECREASE, 256);
        BIAS_BUCKETS.put(Bias.Strength.ABSOLUTE_DECREASE, 1024);
    }

    public static int getBoostFactor(int currentBucket) {
        return BOOST_BUCKETS.getOrDefault(currentBucket, 0);
    }

    public static int getBuryFactor() {
        return BURY_BUCKET;
    }

    public static int getBiasFactor(Bias.Strength strength) {
        return BIAS_BUCKETS.getOrDefault(strength, 0);
    }
}
