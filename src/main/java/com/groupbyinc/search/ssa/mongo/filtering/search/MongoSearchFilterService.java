package com.groupbyinc.search.ssa.mongo.filtering.search;

import com.groupbyinc.search.ssa.antlr.MongoSearchRawFilterVisitor;
import com.groupbyinc.search.ssa.application.core.search.filtering.expression.Expression;
import com.groupbyinc.search.ssa.application.core.search.filtering.filter.FilterItemWrapper;
import com.groupbyinc.search.ssa.application.core.search.filtering.filter.ToFilter;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.filter.ProductIdFilter;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.navigation.SelectedRefinement;
import com.groupbyinc.search.ssa.mongo.filtering.AbstractMongoFilterService;
import com.groupbyinc.search.ssa.mongo.filtering.search.expression.MongoSearchNotExpression;
import com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical.MongoSearchComparisonExpression;
import com.groupbyinc.search.ssa.mongo.filtering.search.expression.numerical.MongoSearchRangeExpression;
import com.groupbyinc.search.ssa.mongo.filtering.search.expression.text.MongoSearchTextExpression;
import com.groupbyinc.search.ssa.mongo.filtering.search.filter.MongoSearchFilter;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import jakarta.inject.Singleton;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.groupbyinc.search.ssa.application.core.search.filtering.expression.numerical.ComparisonExpression.EQUAL;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.FILTER;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.getFieldToPathFunction;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.getParsedRawFilter;
import static com.groupbyinc.search.ssa.mongo.filtering.MongoFilterUtils.parseInventoryIfExists;
import static com.groupbyinc.search.ssa.util.AttributeUtils.PRODUCT_ID_FIELD_FOR_MONGO;

import static io.micrometer.core.instrument.util.StringEscapeUtils.escapeJson;
import static java.util.Objects.requireNonNull;

@Slf4j
@Singleton
@RequiredArgsConstructor
public class MongoSearchFilterService extends AbstractMongoFilterService {

    /**
     * Used to create a filter for mongo search.
     * <p>
     * Created filter contains:
     * 1) Attribute filters from rule.
     * 2) Included productId filters from rule
     * 3) Excluded productId filters from rule
     * 4) Filters created based on user selected refinements
     * 5) Pre-filter passed in request
     * 6) Site-filter configured fo are associated with current request
     * <p>
     * The filter syntax consists of an expression language for constructing a
     * predicate from one or more fields of the products being filtered. Filter
     * expression is case-sensitive.
     * <p>
     *
     * @param searchParameters object which is representing user request.
     *
     * @return mongo representing full filter, which needs to be applied to the mongo search request.
     */
    @NonNull
    public Document createFilter(@NonNull SearchParameters searchParameters) {
       var fieldToPath = getFieldToPathFunction(searchParameters);

        var filter = new MongoSearchFilter();
        fillFilterWithFilters(filter, fieldToPath, searchParameters);

        var filterVisitor = new MongoSearchRawFilterVisitor(
            searchParameters.getMerchandisingConfiguration().attributeConfigurations()
        );
        var raw = getParsedRawFilter(searchParameters, filterVisitor, log);

        var result = filter.toFilter();
        raw.ifPresent(document -> result.getList(FILTER, Document.class).add(document));

        return result;
    }

    @NonNull
    @Override
    protected MongoSearchFilter createIncludedProductIdFilter(@NonNull ProductIdFilter productIdFilter) {
        var filter = new MongoSearchFilter();
        filter.addToOrs(
            new MongoSearchTextExpression(
                PRODUCT_ID_FIELD_FOR_MONGO,
                productIdFilter.includedProductIds()
            )
        );

        return filter;
    }

    @NonNull
    @Override
    protected MongoSearchNotExpression createExcludedProductIdsFilter(@NonNull ProductIdFilter productIdFilter) {
        return new MongoSearchNotExpression(
            new MongoSearchTextExpression(PRODUCT_ID_FIELD_FOR_MONGO, productIdFilter.excludedProductIds())
        );
    }

    @NonNull
    @Override
    protected MongoSearchFilter convertFilterItemsToFilter(@NonNull Map<String, FilterItemWrapper> filterItemsByField) {
        var filter = new MongoSearchFilter();
        addExpressionsToFilter(filter, filterItemsByField);

        return filter;
    }

    @NonNull
    @Override
    protected MongoSearchTextExpression buildTextExpression(@NonNull String field, @NonNull List<String> filters) {
        var parsed = parseInventoryIfExists(field, true);
        return new MongoSearchTextExpression(parsed.field(), parsed.placeId(), filters);
    }

    @NonNull
    @Override
    protected MongoSearchNotExpression buildNotExpression(Expression<Document> expression) {
        return new MongoSearchNotExpression(expression);
    }

    @NonNull
    @Override
    protected MongoSearchTextExpression buildSinglenessTextExpression(@NonNull String field, @NonNull String value) {
        var parsed = parseInventoryIfExists(field, true);
        return new MongoSearchTextExpression(
            parsed.field(),
            parsed.placeId(),
            List.of(requireNonNull(escapeJson(value)))
        );
    }

    @NonNull
    @Override
    protected MongoSearchRangeExpression buildRangeExpression(@NonNull String field, @NonNull Range range) {
        var parsed = parseInventoryIfExists(field, true);
        return MongoSearchRangeExpression.of(parsed.field(), parsed.placeId(), range.low(), range.high());
    }

    @NonNull
    @Override
    protected MongoSearchComparisonExpression buildComparisonExpression(@NonNull String field, @Nullable Double value) {
        var parsed = parseInventoryIfExists(field, true);
        return new MongoSearchComparisonExpression(parsed.field(), parsed.placeId(), EQUAL, requireNonNull(value));
    }

    @NonNull
    @Override
    protected MongoSearchFilter combineFiltersAsOrs(List<ToFilter<Document>> expressions) {
        var filter = new MongoSearchFilter();
        filter.addToOrs(expressions);
        return filter;
    }

    /**
     * Creates a filter for multi-select facets that:
     * 1. Filters by the dedicated product IDs
     * 2. Includes other filters from the original search parameters
     * 3. EXCLUDES refinements from the current navigation field
     *
     * @param originalSearchParams the original search parameters containing refinements and filters
     * @param navFieldToExclude    the current navigation field
     * @param dedicatedProductIds  the set of product IDs to filter by
     */
    @NonNull
    public Document createFilterForMultiSelectFacet(@NonNull SearchParameters originalSearchParams,
                                                    @NonNull String navFieldToExclude,
                                                    @NonNull Set<String> dedicatedProductIds) {
        var filteredRefinements = originalSearchParams.getRefinements() == null
            ? List.<SelectedRefinement>of()
            : originalSearchParams.getRefinements().stream()
            .filter(r -> !r.getField().equals(navFieldToExclude))
            .toList();

        var searchParamsBuilder = originalSearchParams.toBuilder();
        searchParamsBuilder.refinements(filteredRefinements);

        var originalProductIdFilter = originalSearchParams.getProductIdFilter();
        searchParamsBuilder.productIdFilter(
            originalProductIdFilter == null
                ? new ProductIdFilter(new ArrayList<>(dedicatedProductIds), null)
                : originalProductIdFilter.withAdditionalIncludedIds(dedicatedProductIds)
        );

        return createFilter(searchParamsBuilder.build());
    }

}
