package com.groupbyinc.search.ssa.mongo.filtering;

import com.groupbyinc.search.ssa.antlr.ErrorListener;
import com.groupbyinc.search.ssa.antlr.FilterBaseVisitor;
import com.groupbyinc.search.ssa.antlr.FilterLexer;
import com.groupbyinc.search.ssa.antlr.FilterParser;
import com.groupbyinc.search.ssa.antlr.MongoSearchRawFilterVisitor;
import com.groupbyinc.search.ssa.api.error.ProcessingException;
import com.groupbyinc.search.ssa.core.SearchParameters;

import io.micronaut.core.annotation.NonNull;
import io.micronaut.core.annotation.Nullable;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.misc.ParseCancellationException;
import org.bson.Document;
import org.slf4j.Logger;

import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Pattern;

import static com.groupbyinc.search.ssa.application.core.search.filtering.FilterServiceBase.mergePreAndSiteFilters;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_AND;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_ANY_ELEMENT_TRUE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_ARRAY_ELEM_AT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_EQ;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_IS_ARRAY;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_MAP;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.$_SWITCH;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.AS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.BRANCHES;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.CASE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.COMPOUND;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.DEFAULT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.EMBEDDED_DOCUMENT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.EQUALS;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.FILTER;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.IN;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.INDEXABLES_PREFIX;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.INPUT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.LOCAL_INVENTORIES_PLACE_ID_DOUBLE_DOLLAR_PATH;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.LOCAL_INVENTORIES_PREFIX;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.LOCAL_INVENTORIES_PREFIX_NO_DOT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.MUST_NOT;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.N;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.OPERATOR;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.PATH;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.PLACE_ID;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.RANGE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.SHOULD;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.THEN;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.VALUE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.VARIANT_INVENTORIES_DOUBLE_DOLLAR_PATH;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.VARIANT_LOCAL_INVENTORIES_SHORT;
import static com.groupbyinc.search.ssa.util.AttributeUtils.parseInventoryNavigations;
import static com.groupbyinc.search.ssa.util.AttributeUtils.transformFieldForMongo;
import static com.groupbyinc.search.ssa.util.StringUtils.EMPTY;

import static java.util.regex.Pattern.CASE_INSENSITIVE;

public class MongoFilterUtils {

    private static final String BOUND_REGEX = "(^[0-9]+(\\.[0-9]+)?([ei])?$)|(^\\*$)";
    private static final Pattern BOUND_VALIDATION_PATTERN = Pattern.compile(BOUND_REGEX, CASE_INSENSITIVE);
    private static final String EXCLUSIVE = "e";
    private static final String INCLUSIVE = "i";
    private static final String INFINITY = "*";
    private static final String BOUNDS_VALIDATION_ERROR_MESSAGE = """
      Invalid range. A lower_bound or upper_bound is either a double or '*', which represents infinity.
      Explicitly specify inclusive bound with the character 'i' or exclusive bound with the character 'e'.
      Example: price: IN(*, 100.0e)
    """;

    public static Function<String, String> getFieldToPathFunction(SearchParameters searchParameters) {
         return field -> transformFieldForMongo(
            field,
            searchParameters.getMerchandisingConfiguration().attributeConfigurations()
        );
    }

    // region Raw
    public static Optional<Document> getParsedRawFilter(SearchParameters searchParameters,
                                                        FilterBaseVisitor<Document> filterVisitor,
                                                        Logger log) {
        var rawFilter = mergePreAndSiteFilters(
            searchParameters.getPreFilter(),
            searchParameters.getMerchandisingConfiguration().siteFilter()
        );

        if (hasRawFilters(searchParameters)) {
            return buildRawFilters(rawFilter, filterVisitor, log);
        }
        return Optional.empty();
    }

    /**
     * Build a filter based on passed pre-filter and site-filter.
     * <p>
     * Actually parse raw representation of these filters using AntLR lib.
     *
     * @return mongo representing pre-filter and site-filter.
     */
    public static Optional<Document> buildRawFilters(Optional<String> rawFilter,
                                                     FilterBaseVisitor<Document> filterVisitor,
                                                     Logger log
    ) {
        try {
            return rawFilter.map(raw -> {
                var parsed = parseRawFilter(
                    rawFilter.get(),
                    filterVisitor
                );

                if (hasInternalFilter(parsed) || hasInternalShould(parsed)) {
                    return parsed;
                }

                return new Document(
                    COMPOUND,
                    new Document(
                        FILTER,
                        List.of(parsed)
                    )
                );
            });
        } catch (Exception e) {
            log.error("Could not parse the submitted raw filter '{}' error: {}", rawFilter.get(), e.getMessage(), e);
            throw new IllegalArgumentException("could not parse filter. error: %s".formatted(e.getMessage()), e);
        }
    }

    /**
     * Check is passed search parameters object contains raw (pre-filter and site-filter) filters or not.
     *
     * @param searchParameters object which is representing user request.
     *
     * @return true if the passed search parameters object contains raw filters, false – otherwise.
     */
    public static boolean hasRawFilters(SearchParameters searchParameters) {
        return !searchParameters.getPreFilter().isEmpty()
            || searchParameters.getMerchandisingConfiguration().siteFilter() != null;
    }

    /**
     * Given a raw-filter, transform the string into usable mongo query {@link MongoSearchRawFilterVisitor}
     * class transforms required nodes in the AST into usable mongo Document queries.
     * <p>
     * Note: the returned parent operator might need to be wrapped in another compound operator.
     *
     * @param rawFilter rawFilter string from request parameters (merged preFilter anf SiteFilter)
     *
     * @return mongo Document queries constructed from preFilters
     *
     * @see MongoSearchRawFilterVisitor
     * @see FilterParser
     * @see FilterLexer
     * @see ErrorListener
     *
     * @throws ParseCancellationException  if there is a parser exception, malformed filter that can't be parsed
     */
    public static <T> T parseRawFilter(String rawFilter,
                                       FilterBaseVisitor<T> visitor) throws ParseCancellationException {

        var tokens = new CommonTokenStream(new FilterLexer(CharStreams.fromString(rawFilter)));

        var parser = new FilterParser(tokens);
        parser.removeErrorListeners();
        parser.addErrorListener(ErrorListener.INSTANCE);

        return visitor.visit(parser.filter());
    }
    // endregion

    // region parsing for range from string
    public record ParsedRange(@Nullable Number lower,
                              @Nullable Number upper,
                              @Nullable String lowerOperator,
                              @Nullable String upperOperator) {
    }

    public static ParsedRange parseRange(String lowerBound,
                                         String upperBound,
                                         String inclusiveLowerBoundOperator,
                                         String exclusiveLowerBoundOperator,
                                         String inclusiveUpperBoundOperator,
                                         String exclusiveUpperBoundOperator) {
        validateBounds(lowerBound, upperBound);

        var lowerBoundValue = Double.POSITIVE_INFINITY;
        var upperBoundValue = Double.POSITIVE_INFINITY;

        // lower_bound is inclusive by default.
        var lowerBoundOperator = inclusiveLowerBoundOperator;
        // upper_bound is exclusive by default.
        var upperBoundOperator = inclusiveUpperBoundOperator;

        if (!INFINITY.equals(lowerBound)) {
            lowerBoundValue = toDouble(lowerBound);

            if (EXCLUSIVE.equals(getBoundChar(lowerBound))) {
                lowerBoundOperator = exclusiveLowerBoundOperator;
            }
        }

        if (!INFINITY.equals(upperBound)) {
            upperBoundValue = toDouble(upperBound);

            if (INCLUSIVE.equals(getBoundChar(upperBound))) {
                upperBoundOperator = exclusiveUpperBoundOperator;
            }
        }

        return new ParsedRange(
            lowerBoundValue,
            upperBoundValue,
            lowerBoundOperator,
            upperBoundOperator
        );
    }


    private static double toDouble(String bound) {
        if (INCLUSIVE.equals(getBoundChar(bound)) || EXCLUSIVE.equals(getBoundChar(bound))) {
            return Double.parseDouble(stripLastChar(bound));
        } else {
            return Double.parseDouble(bound);
        }
    }

    private static String getBoundChar(@NonNull String value) {
        return value.substring(value.length() - 1);
    }

    private static String stripLastChar(String value) {
        return value.substring(0, value.length() - 1);
    }

    private static void validateBounds(@Nullable String upperBound, @Nullable String lowerBound) {
        var isvalid = validateBound(lowerBound) && validateBound(upperBound);
        if (!isvalid) {
            throw new IllegalArgumentException(
                "The supplied lower (%s) or upper (%s) bound is not valid. %s".formatted(
                    lowerBound,
                    upperBound,
                    BOUNDS_VALIDATION_ERROR_MESSAGE
                )
            );
        }
    }

    private static boolean validateBound(@Nullable String bound) {
        if (bound == null || bound.isEmpty()) {
            return false;
        }

        return BOUND_VALIDATION_PATTERN.matcher(bound).matches();
    }
    // endregion

    // region Search Filtering

    /**
     * Create a document with 'compound' wrapper around 'should' clause with IN or EQUALS filters.
     * Example:
     * <pre>{@code
     * Usual:
     * 'equals
     * {
     *   "equals": {
     *     "value": "example",
     *     "path": "indexables.attribute"
     *   }
     * }
     *
     * 'in'
     * {
     *   "in": {
     *     "value": [
     *        "example",
     *        "example_1"
     *     ]
     *     "path": "indexables.attribute"
     *   }
     * }
     *
     * }</pre>
     *
     * @param operator operator to create a filter (IN, EQUALS).
     * @param path     actually path to the attribute in 'json' product.
     * @param value    value used in operator.
     *
     * @return {@link Document} with 'compound' wrapping around 'should' clause with IN or EQUALS filter inside.
     *
     * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/in/">IN filter doc</a>
     * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/equals/">EQUALS filter doc</a>
     * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/compound/">Compound doc</a>
     */
    @NonNull
    public static Document createDocumentForSearchFilter(@NonNull String operator,
                                                         @NonNull String path,
                                                         @NonNull Object value) {
        return new Document(
            operator,
            new Document()
                .append(VALUE, value)
                .append(PATH, path)
        );
    }

    /**
     * Create a document with range 'filter' operator inside.
     * Example:
     * <pre>{@code
     * {
     *   "range": {
     *     "gte": 100.0,
     *     "lt": 999998.99,
     *     "path": [
     *       "attribute",
     *       "variants.attribute"
     *     ]
     *   }
     * }
     * }</pre>
     *
     * @param path          actually path to the attribute in 'json' product
     * @param lower         range bound (inclusive)
     * @param upper         range bound (exclusive)
     * @param lowerOperator include | exclude operator for lower bound
     * @param upperOperator include | exclude operator for upper bound
     *
     * @return {@link Document} with range filter inside.
     *
     * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/range/">range doc</a>
     */
    @NonNull
    public static Document createDocumentForRangeSearchFilter(@NonNull String path,
                                                              @Nullable Number lower,
                                                              @Nullable Number upper,
                                                              @Nullable String lowerOperator,
                                                              @Nullable String upperOperator) {
        var document = new Document().append(PATH, path);

        if (lower != null && lowerOperator != null) {
            if (!Double.isInfinite(lower.doubleValue())) {
                document.put(lowerOperator, lower);
            }
        }

        if (upper != null && upperOperator != null) {
            if (!Double.isInfinite(upper.doubleValue())) {
                document = document.append(upperOperator, upper);
            }
        }

        return new Document(RANGE, document);
    }

    /**
     * Verify is document with 'compound' operator contains 'filter' operator inside.
     *
     * @param document to check.
     *
     * @return true if the document with 'compound' operator contains 'filter' operator inside, false – otherwise.
     */
    public static boolean hasInternalFilter(@NonNull Document document) {
        try {
            return document.get(COMPOUND, Document.class) != null
                && document.get(COMPOUND, Document.class).getList(FILTER, Document.class) != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Verify is document with 'compound' operator contains 'should' operator inside.
     *
     * @param document to check.
     *
     * @return true if the document with 'compound' operator contains 'should' operator inside, false – otherwise.
     */
    public static boolean hasInternalShould(@NonNull Document document) {
        try {
            return document.get(COMPOUND, Document.class) != null
                && document.get(COMPOUND, Document.class).getList(SHOULD, Document.class) != null;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Extract 'filter' operator from the document with 'compound' operator which contains 'filter' operator inside.
     *
     * @param document to extract filters.
     *
     * @return extracted filters.
     */
    @NonNull
    public static List<Document> getInternalFilter(@NonNull Document document) {
        var result = document
            .get(COMPOUND, Document.class)
            .getList(FILTER, Document.class);

        if (result == null) {
            throw new ProcessingException(
                "Document should contain filter to extract internal filter. Document: " + document.toJson()
            );
        }

        return result;
    }

    /**
     * Extract 'should' operator from the document with 'compound' operator which contains 'should' operator inside.
     *
     * @param document to extract filters.
     *
     * @return extracted should.
     *
     * @throws NullPointerException if passed document doesn't contain 'should' inside.
     */
    @NonNull
    public static List<Document> getInternalShould(@NonNull Document document) {
        var result = document
            .get(COMPOUND, Document.class)
            .getList(SHOULD, Document.class);

        if (result == null) {
            throw new ProcessingException(
                "Document should contain should to extract internal should. Document: " + document.toJson()
            );
        }

        return result;
    }

    /**
     * Used to wrap filters with "mustNot" condition.
     *
     * @param document with filters to negotiate.
     *
     * @return negotiated mongo filter.
     */
    public static Document wrapWithMustNotBasedOnInternalStructure(Document document) {
        if (hasInternalFilter(document)) {
            return wrapWithMustNot(getInternalFilter(document));
        }

        if (hasInternalShould(document)) {
            return wrapWithMustNot(getInternalShould(document));
        }

        return wrapWithMustNot(document);
    }

    /**
     * Wrap passed documents with "mustNot" clause under "compound" operator.
     *
     * @param documents to wrap.
     *
     * @return "compound" operator with "mustNot" clause.
     */
    @NonNull
    public static Document wrapWithMustNot(@NonNull List<Document> documents) {
        return new Document(
            COMPOUND,
            new Document(MUST_NOT, documents)
        );
    }

    /**
     * Wrap passed document with "mustNot" clause under "compound" operator.
     *
     * @param document to wrap.
     *
     * @return "compound" operator with "mustNot" clause.
     */
    @NonNull
    public static Document wrapWithMustNot(@NonNull Document document) {
        return wrapWithMustNot(List.of(document));
    }

    /**
     * In case when inventory attribute is used, it will create a special filter based on an embedded document.
     * For example:
     * <pre>{@code
     * {
     *    embeddedDocument: {
     *        path: "indexables.localInventories",
     *        operator: {
     *            compound: {
     *                filter: [
     *                    {
     *                        equals: {
     *                            path: "indexables.localInventories.placeId",
     *                            value: "placeId",
     *                        }
     *                    },
     *                    {
     *                        equals: {
     *                            path: "indexables.localInventories.attribute",
     *                            value: value,
     *                        }
     *                    }
     *                ]
     *            }
     *        }
     *    }
     * }
     * }</pre>
     *
     * @param placeId id of store to get inventory data
     * @param filter  filter created by inventory attribute.
     *
     * @return mongo filter object to filter by inventory attributes.
     *
     * @see <a href="https://www.mongodb.com/docs/atlas/atlas-search/embedded-document/">EMBEDDED DOCUMENT Doc</a>
     */
    public static Document createInventoryFilter(String placeId, Document filter) {
        return new Document(
            EMBEDDED_DOCUMENT,
            new Document(PATH, INDEXABLES_PREFIX + LOCAL_INVENTORIES_PREFIX_NO_DOT).append(
                OPERATOR,
                new Document(
                    COMPOUND,
                    new Document(
                        FILTER,
                        List.of(
                            new Document(
                                EQUALS,
                                new Document(
                                    PATH,
                                    INDEXABLES_PREFIX + LOCAL_INVENTORIES_PREFIX + PLACE_ID
                                ).append(VALUE, placeId)),
                            filter
                        )
                    )
                )
            )
        );
    }
    //endregion


    // region Variant sort Filtering

    /**
     * Used to construct switch case filter based on "isArray" check.
     * For example:
     * <pre>{@code
     * {
     *   "case": {
     *     "$isArray": "indexables.sizes"
     *   },
     *   "then": {
     *     "embeddedDocument": {
     *       "path": "indexables.localInventories",
     *       "operator": {
     *         "compound": {
     *           "filter": [
     *             {
     *               "equals": {
     *                 "path": "indexables.localInventories.placeId",
     *                 "value": "10"
     *               }
     *             },
     *             {
     *               "in": {
     *                 "value": [
     *                   "XL",
     *                   "X",
     *                   "L"
     *                 ],
     *                 "path": "indexables.localInventories.sizes"
     *               }
     *             }
     *           ]
     *         }
     *       }
     *     }
     *   }
     * }
     * }</pre>
     *
     * @param path to property to check
     * @param body a filter which should be applied if object in a passed path is array.
     *
     * @return a filter condition with "isArray" check.
     */
    public static Document caseBodyWithIsArrayCheck(String path, Document body) {
        return new Document(CASE, new Document($_IS_ARRAY, path)).append(THEN, body);
    }

    /**
     * Used to create a basic switch-case filter with one case and default section.
     * For example:
     * <pre>{@code
     * {
     *   "$switch": {
     *     "branches": [
     *       {
     *         "in": {
     *           "value": [
     *             "XL",
     *             "X",
     *             "L"
     *           ],
     *           "path": "indexables.sizes"
     *         }
     *       }
     *     ],
     *     "default": {
     *       "equals": {
     *         "value": "Puma",
     *         "path": "indexables.brands"
     *       }
     *     }
     *   }
     * }
     * }</pre>
     *
     * @param caseBody    case section filter
     * @param defaultBody default section filter
     *
     * @return basic switch-case filter
     */
    public static Document switchWithOneCase(Document caseBody, Document defaultBody) {
        return new Document(
            $_SWITCH,
            new Document(
                BRANCHES,
                List.of(caseBody)
            ).append(
                DEFAULT,
                defaultBody
            )
        );
    }

    /**
     * Used to create a bound-based filter for array properties.
     * For example:
     * <pre>{@code
     * {
     *   "$arrayElemAt": [
     *     {
     *       "$maxN": {
     *         "n": 1,
     *         "input": "indexables.brands"
     *       }
     *     },
     *     0
     *   ]
     * }
     * }</pre>
     *
     * @param path          to the array property
     * @param boundOperator a bound operator
     *
     * @return bounded filter for array element
     */
    public static Document getBoundOperatorForArray(String path, String boundOperator) {
        return new Document(
            $_ARRAY_ELEM_AT,
            List.of(
                new Document(boundOperator, new Document(N, 1L).append(INPUT, path)),
                0L
            )
        );
    }

    /**
     * Used to create a variant sort filter for local inventory filters.
     * For example:
     * <pre>{@code
     * {
     *   "$anyElementTrue": {
     *     "$map": {
     *       "input": "$$variant.localInventories",
     *       "as": "lv",
     *       "in": {
     *         "$and": [
     *           {
     *             "$eq": [
     *               "$$lv.placeId",
     *               "10"
     *             ]
     *           },
     *           {
     *             "embeddedDocument": {
     *               "path": "indexables.localInventories",
     *               "operator": {
     *                 "compound": {
     *                   "filter": [
     *                     {
     *                       "equals": {
     *                         "path": "indexables.localInventories.placeId",
     *                         "value": "10"
     *                       }
     *                     },
     *                     {
     *                       "in": {
     *                         "value": [
     *                           "XL",
     *                           "X",
     *                           "L"
     *                         ],
     *                         "path": "indexables.localInventories.sizes"
     *                       }
     *                     }
     *                   ]
     *                 }
     *               }
     *             }
     *           }
     *         ]
     *       }
     *     }
     *   }
     * }
     * }</pre>
     *
     * @param filter  filter to apply
     * @param placeId placeId of inventory attribute
     *
     * @return variant sort filter for local inventory filters
     */
    public static Document createInventorySortFilter(Document filter, String placeId) {
        return new Document(
            $_ANY_ELEMENT_TRUE,
            new Document(
                $_MAP,
                new Document(INPUT, VARIANT_INVENTORIES_DOUBLE_DOLLAR_PATH)
                    .append(AS, VARIANT_LOCAL_INVENTORIES_SHORT)
                    .append(
                        IN,
                        new Document(
                            $_AND,
                            List.of(
                                new Document($_EQ, List.of(LOCAL_INVENTORIES_PLACE_ID_DOUBLE_DOLLAR_PATH, placeId)),
                                filter
                            )
                        )
                    )
            )
        );
    }
    // endregion

    public record FieldWithPlaceId(String field, @Nullable String placeId) {}

    public static FieldWithPlaceId parseInventoryIfExists(String fieldToParse, boolean includePrefix) {
        var parsedField = fieldToParse;
        String parsedPlaceId = null;
        var matcher = parseInventoryNavigations(fieldToParse);
        if (matcher.isPresent()){
            parsedField = matcher.get().group(2);
            parsedPlaceId = matcher.get().group(1);
        }

        if (!includePrefix) {
            parsedField = parsedField.replace(LOCAL_INVENTORIES_PREFIX, EMPTY);
        }

        return new FieldWithPlaceId(parsedField, parsedPlaceId);
    }

}
