grammar Filter;

/*
 * This filter is constructed from EBNF defined
 * [here](https://cloud.google.com/retail/docs/filter-and-order#filter)
 *
 * ANTLR (ANother Tool for Language Recognition) is a powerful
 * parser generator for reading, processing, executing,
 * or translating structured text or binary files.
 * It's widely used to build languages, tools,
 * and frameworks. From a grammar, ANTLR generates
 * a parser that can build and walk parse trees.
 *
 * @see https://www.antlr.org/
 */

/*
 * Production Rules (non-terminal)
 *
 * Production rules define how the various components
 * of a language's grammar can be combined to form
 * valid expressions or statements.
 * Non-terminals are used in the production rules
 * to indicate the structure of the language's expressions or statements,
 * and to specify how the tokens and other symbols can be combined.
 */
filter
  : expression
  ;

/*
 * The order in which expressions are evaluated
 * is determined by the order in which possible
 * matching rules are defined.
 * Here, negations are dealt with first, then parentheses
 * and so on.
 *
 * an expression can be any expression, comparison expression
 * or in expression
 * all three expressions are on the
 * same precedence level, so they are grouped.
 *
 * Labels (e.g. "# negationExpression") are added to each rule
 * to provide context to which rule is being parsed.
 * This can be used in a Listener or Visitor
 * to allow for separate control over Listener or Visitor actions.
 *
 * Likewise, inner labels (e.g. "left=expression")
 * can be added to child nodes of the rule.
 * This makes them identifiable in a
 * Listener's or Visitor's parsing of the rule,
 * allowing for even more fine-grained control.
 * ------------------------------------------------------------
 * negationExpression
 * An expression which is negatiated. Mean logical "NOT"
 *
 * parenthesisExpression
 * An expression which is contains embeded expressions, like ((expressiuon) AND (expression))
 *
 * logicalExpression
 * A single expression or multiple expressions that are joined by "AND" or "OR".
 *
 * rangeExpression
 * A simple expression applying to a numerical field. Function "IN" returns true
 * if a field value is within the range. By default, lower_bound is inclusive and
 * upper_bound is exclusive.
 *
 * comparisonExpression
 * A simple expression that applies to a numerical field and compares with a double value.
 *
 * inventoryComparisonExpression
 * A simple expression that applies to a numerical field and compares with a double value. but fot inventory facets.
 * inventory(443, attribute) = 10
 *
 * textExpression
 * A simple expression applying to a text field.
 * Function "ANY" returns true if the field contains any of the literals.
 *
 * inventoryTextExpression
 * We also support expressions such as
 * inventory(443, attribute): ANY("PROMO TALON")
 *
 * inventoryRangeExpression
 * We also support expressions such as
 * inventory(443, attribute): IN(10i, 100e)
 */
expression
  : NEGATION expression # negationExpression
  | LEFT_PARENTHESIS expression RIGHT_PARENTHESIS # parenthesisExpression
  | left=expression operator=logicalOperators right=expression # logicalExpression
  | field=textField assign values=anyValues # textExpression
  | field=inventoryFacet assign values=anyValues # inventoryTextExpression
  | field=numericalField assign values=inValues # rangeExpression
  | field=inventoryFacet assign values=inValues # inventoryRangeExpression
  | field=numericalField comparison values=DECIMAL_VALUE # comparisonExpression
  | field=inventoryFacet comparison values=DECIMAL_VALUE # inventoryComparisonExpression
  ;

inValues
  : 'IN' LEFT_PARENTHESIS lowerBound COMMA upperBound RIGHT_PARENTHESIS
  ;

anyValues
  : 'ANY' LEFT_PARENTHESIS literal (COMMA literal)* RIGHT_PARENTHESIS
  ;

lowerBound
  : numberLiteral
  ;

upperBound
  : numberLiteral
  ;

comparison
  : op=(GREATER | GREATER_OR_EQUAL | LESS | LESS_OR_EQUAL | EQUALS | NOT_EQUAL)
  ;

textField
  : identifier
  ;

inventoryFacet
  : 'inventory' LEFT_PARENTHESIS placeId COMMA textField RIGHT_PARENTHESIS
  ;

numericalField
  : identifier
  ;

placeId
  : literal
  | numberLiteral
  ;

literal
  : ID
  | STRING
  ;

numberLiteral
  : (MINUS | PLUS)? INTEGER_VALUE (INCLUDED | EXCLUDED)?
  | (MINUS | PLUS)? DECIMAL_VALUE (INCLUDED | EXCLUDED)?
  | ASTERISK
  ;

identifier
  : ID DOT ID
  | (ID | DOT)+
  ;

assign
  : COLON
  ;

logicalOperators
  : AND
  | OR
  ;

OR: 'OR';
AND: 'AND';

NEGATION: 'NOT' | MINUS;

STRING
  : '\'' ( ~('\''|'\\') | ('\\' .) )* '\''
  | '"' ( ~('"'|'\\') | ('\\' .) )* '"'
  ;

DECIMAL_VALUE
  : DECIMAL_DIGITS
  | INTEGER_VALUE
  ;

INTEGER_VALUE
  : DIGIT+
  ;

ID
  : LETTER (LETTER | DIGIT | SPECIAL_CHARACTERS)+
  ;

/*
 * Tokens (terminal)
 *
 * Terminals are basic symbols that cannot
 * be broken down into smaller parts.
 * They're stated in generic terms as
 * their meaning can depend on the context
 * in which they're used, for example:
 * MINUS is used for subtraction, but also for negation.
 */

QUOTE
  : '"'
  | '\\"'
  | '\''
  | '\\\''
  ;

SPECIAL_CHARACTERS
  : ([_-] | '.')
  ;

COLON: ':';
DOT: '.';
COMMA: ',';
ASTERISK: '*';
LEFT_PARENTHESIS: '(';
RIGHT_PARENTHESIS: ')';
EQUALS: '=';
MINUS: '-';
PLUS: '+';
GREATER: '>';
GREATER_OR_EQUAL: '>=';
LESS: '<';
LESS_OR_EQUAL: '<=';
NOT_EQUAL: '!=';
INCLUDED: 'i';
EXCLUDED: 'e';

fragment DECIMAL_DIGITS
  : DIGIT+ '.' DIGIT*
  | '.' DIGIT+
  ;

fragment DIGIT
  : [0-9]
  ;

fragment LETTER
  : [a-zA-Z]
  ;

WS
  : [ \r\n\t]+ -> channel(HIDDEN)
  ;
