name: Create Release

on:
  workflow_dispatch:
    inputs:
      release_version:
        description: 'Tag to be released'
        required: true
      fix_version:
        description: 'JIRA fix version'
        required: true

env:
  REPO_NAME: ${{ github.event.repository.full_name }} # DO NOT Change or remove this value.
  GIT_TOKEN: ${{secrets.GITHUB_TOKEN}}                # DO NOT Change or remove this value.
  APP_NAME: auth                                      # Name of the application
  PROJECT_ID: "upper-tools-dev"
  PROJECT_NUMBER: "490875368535"
  IMAGE: us-docker.pkg.dev/upper-tools-dev/gcr.io/search/site-search-api
permissions: write-all
jobs:
  release:
    name: Create Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
          fetch-depth: 0
          token: ${{ secrets.GIT_TOKEN }}
      - name: Get Commit SHA
        run: echo "GIT_COMMIT_SHA=`echo $(git rev-list -n 1 ${{github.event.inputs.release_version}})`" >> $GITHUB_ENV

      - name: Print Release Tag
        run: |
          echo "${{github.event.inputs.release_version}}"
          echo "${{env.GIT_COMMIT_SHA}}"
      - name: Release
        uses: softprops/action-gh-release@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          prerelease: false
          target_commitish: ${{env.GIT_COMMIT_SHA}}
          tag_name: ${{github.event.inputs.release_version}}
          name: ${{github.event.inputs.fix_version}}
          generate_release_notes: true
          repository: ${{ github.event.repository.full_name }}
        env:
          GITHUB_REPOSITORY: ${{ github.event.repository.full_name }}
