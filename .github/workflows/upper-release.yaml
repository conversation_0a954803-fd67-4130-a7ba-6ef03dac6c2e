name: Deploy - Upper

on:
  workflow_dispatch:
    inputs:
      customer_name:
        # "all" <-- if you want the release to be published to all customers.
        description: 'Name of the customer'
        required: true
      release_version:
        # If not mentioned, the 'CI/CD' will pick the latest tag available automatically.
        description: 'Release name to be deployed'
        required: false
      all_except:
        description: 'Comma-separated list of customers to exclude when using "all"'
        required: false

env:
  REPO_NAME: ${{ github.event.repository.full_name }} # DO NOT Change or remove this value.
  GIT_TOKEN: ${{secrets.GITHUB_TOKEN}}                # DO NOT Change or remove this value.
  APP_NAME: search                                    # Name of the application
  ENV: upper                                          # lower or upper
  PROJECT_ID: "upper-tools-dev"
  PROJECT_NUMBER: "490875368535"
  IMAGE: us-docker.pkg.dev/upper-tools-dev/gcr.io/search/site-search-api
permissions: write-all
jobs:
  matrix-setup:
    name: Matrix Setup
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      output_matrix: ${{steps.release.outputs.output_matrix }}
      release_version: ${{steps.release.outputs.release_version }}
    steps:

      - name: Checkout GitHub Action Repo
        uses: actions/checkout@v3
        with:
          repository: groupby/app-release
          ref: v5
          token: ${{ secrets.GIT_TOKEN }}
          path: .github/actions/release
      - name: Run custom Action for release deployment.
        uses: ./.github/actions/release
        id: release
        with:
          vault_url: ${{ secrets.VAULT_URL_UPPER }} 
          vault_token: ${{ secrets.VAULT_TOKEN_UPPER }} 
          customer_name: ${{ github.event.inputs.customer_name }}
          release_version: ${{ github.event.inputs.release_version }}
          app_name: ${{ env.APP_NAME }}
          # Enable this to "true" if you want to lock a particular release to a customer.
          lock: 'false'
          all_except: ${{ github.event.inputs.all_except }}


      # DO NOT remove this section. This section creates a matrix out of cluster information from vault.
      - id: set-matrix
        name: setup-cluster-matrix
        run: |-
          JSON='${{steps.release.outputs.output_matrix }}'
          JSON="${JSON//'%'/'%25'}"
          JSON="${JSON//$'\n'/'%0A'}"
          JSON="${JSON//$'\r'/'%0D'}"
          echo "matrix={\"include\":"$JSON"}" >> $GITHUB_OUTPUT

  deploy-upper:
    name: Deploy ${{ needs.matrix-setup.outputs.release_version }} to ${{ matrix.project_id }}
    runs-on: ubuntu-latest
    needs: matrix-setup
    strategy:
      matrix: ${{fromJson(needs.matrix-setup.outputs.matrix)}}
      fail-fast: false
    steps:
      - name: Get latest release
        id: release-tag
        uses: cardinalby/git-get-release-action@v1
        env:
          GITHUB_TOKEN: ${{ github.token }}
        with:
          releaseName: ${{needs.matrix-setup.outputs.release_version }}
          draft: false
          prerelease: false

      ## fetching from vault gloabal and local and preparing the deployment values file
      - name: Fetch value from Vault
        uses: groupby/fetch-vault-app-vals@main
        with:
          vault_path_key_global: "Upper/Applications/Global/${{ env.APP_NAME }}"    
          vault_path_key_local: "Upper/Applications/${{ matrix.customer_name }}-${{ env.ENV }}/${{ env.APP_NAME }}" 
          cluster_name: ${{matrix.cluster_name }}
          vault_url: ${{ secrets.VAULT_URL_UPPER }} 
          vault_token: ${{ secrets.VAULT_TOKEN_UPPER }} 
          git_token: ${{ secrets.GIT_TOKEN }}

      ## Checking out the app repo again 
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: recursive
          ref: ${{steps.release-tag.outputs.tag_name}}
          token: ${{ secrets.GIT_TOKEN }}

      ## Starting the authentication with gcp and gke
      - name: 'Authenticate to GCP for IAP token'
        id: iap-auth
        uses: google-github-actions/auth@v1
        with:
          workload_identity_provider: 'projects/************/locations/global/workloadIdentityPools/github-actions-pool/providers/github-actions-pool-prvdr'
          service_account: '<EMAIL>'
          project_id: "upper-tools-sre"

      - name: Specify cluster for hpa config
        run: sed -i "s/__CLUSTER_NAME__/${{ matrix.cluster_name }}/" helm/templates/sitesearch/hpa.yaml

      - id: get-gke-credentials
        name: Get GKE Credentials
        uses: google-github-actions/get-gke-credentials@v1
        with:
          cluster_name: ${{matrix.cluster_name}}
          location: ${{matrix.region }}
          project_id: ${{matrix.project_id }}

      # Restore the artifact after the checkout
      - name: Restore generated values-deploy.yaml
        uses: actions/download-artifact@v4
        with:
          name: ${{ format('{0}-helm-values', matrix.cluster_name) }}
      - name: Helm Deploy --dry-run
        uses: WyriHaximus/github-action-helm3@v3
        with:
          exec: helm upgrade --install site-search-api helm/ --namespace site-search-api -f values-deploy.yaml --set configmap.project_id=${{matrix.project_id }} --set image.tag=${{steps.release-tag.outputs.tag_name}} --create-namespace --dry-run
      - name: Helm Deploy
        uses: WyriHaximus/github-action-helm3@v3
        with:
          exec: helm upgrade --install site-search-api helm/ --namespace site-search-api -f values-deploy.yaml --set configmap.project_id=${{matrix.project_id }} --set image.tag=${{steps.release-tag.outputs.tag_name}} --create-namespace
      # kubectl rollout restart
      - name: Restart Deployment
        run: |
          #kubectl rollout restart deployment search-sitesearch -n site-search-api
          NAMESPACE="site-search-api"
          DEPLOYMENT="search-sitesearch"
          # Check if the deployment exists
          if kubectl get deployment $DEPLOYMENT -n $NAMESPACE > /dev/null 2>&1; then
            echo "Deployment $DEPLOYMENT found in namespace $NAMESPACE. Proceeding with rollout restart."
            kubectl rollout restart deployment $DEPLOYMENT -n $NAMESPACE
          else
            echo "Deployment $DEPLOYMENT not found in namespace $NAMESPACE. Aborting rollout restart."
          fi
      - name: Label namespace for ASM
        run: |
          kubectl label namespace site-search-api "istio.io/rev"="asm-managed" --overwrite
