name: Build - Pull Request

on:
  pull_request:
    branches:
      - main

permissions: write-all

jobs:
  build-pr:
    name: Build and Test
    runs-on: ubuntu-latest
    env:
      IMAGE: us-docker.pkg.dev/upper-tools-dev/gcr.io/search/site-search-api
      PROJECT_ID: "upper-tools-dev"
      PROJECT_NUMBER: "490875368535"
    steps:
      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive
          fetch-depth: 0
          token: ${{ secrets.GIT_TOKEN }}
      - name: Grant execute permission for gradlew
        run: chmod +x gradlew
      - name: <PERSON><PERSON> Gradle packages
        uses: actions/cache@v4
        with:
          key: v1-dependencies-${{ hashFiles('build.gradle') }}
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
      - name: Download dependencies
        run: >-
          ./gradlew dependencies
          -PmavenUsername=${{secrets.GIT_PACKAGES_USER}}
          -PmavenPassword=${{secrets.GIT_PACKAGES_PASSWORD}}
      - name: Build and Test
        run: >-
          ./gradlew build
          -PmavenUsername=${{secrets.GIT_PACKAGES_USER}}
          -PmavenPassword=${{secrets.GIT_PACKAGES_PASSWORD}}
      - name: Set Tag
        run: echo "GIT_TAG=${{ github.head_ref }}" >> $GITHUB_ENV
      - name: JIB
        run: >-
          ./gradlew jibDockerBuild
          -PmavenUsername=${{secrets.GIT_PACKAGES_USER}}
          -PmavenPassword=${{secrets.GIT_PACKAGES_PASSWORD}}
      - id: auth
        name: 'Authenticate to GCP'
        uses: google-github-actions/auth@v1
        with:
          workload_identity_provider: 'projects/**********/locations/global/workloadIdentityPools/github-actions-pool/providers/github-actions-pool-prvdr'
          service_account: '<EMAIL>'
          project_id: "lower-tools-sre"
      - name: 'Set up Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v1'
      - name: gcloud auth configure-docker
        run: |
          gcloud auth configure-docker us-docker.pkg.dev --project ${{env.PROJECT_ID}} --quiet
      - name: Tag Push Image
        run: |-
          docker tag ${{env.IMAGE}}:latest ${{env.IMAGE}}:${{ env.GIT_TAG }}
          docker push ${{env.IMAGE}}:${{ env.GIT_TAG }}
