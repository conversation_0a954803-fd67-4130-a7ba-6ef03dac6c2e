name: Build

on:
  push:
    branches: [ main, hotfix  ]

env:
  REPO_NAME: ${{ github.event.repository.full_name }} # DO NOT Change or remove this value.
  GIT_TOKEN: ${{secrets.GITHUB_TOKEN}}                # DO NOT Change or remove this value.
  APP_NAME: search                                    # Name of the application
  ENV: lower                                          # lower or upper

permissions: write-all
jobs:
  build:
    name: Build and Test
    runs-on: ubuntu-latest
    env:
      IMAGE: us-docker.pkg.dev/upper-tools-dev/gcr.io/search/site-search-api
      PROJECT_ID: "upper-tools-dev"
      PROJECT_NUMBER: "490875368535"
      RECON_SCOPE: minor
    steps:
      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'
      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive
          fetch-depth: 0
          token: ${{ secrets.GIT_TOKEN }}
      - name: Grant execute permission for gradlew
        run: chmod +x gradlew

      - name: Cache Gradle packages
        uses: actions/cache@v4
        with:
          key: v1-dependencies-${{ hashFiles('build.gradle') }}
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
      - name: Set reckon scope to patch
        if: github.ref == 'refs/heads/hotfix'
        run: echo "RECON_SCOPE=patch" >> $GITHUB_ENV
      - name: Download dependencies
        run: >-
          ./gradlew dependencies -Preckon.stage=final -Preckon.scope=${{env.RECON_SCOPE}}
          -PmavenUsername=${{secrets.GIT_PACKAGES_USER}}
          -PmavenPassword=${{secrets.GIT_PACKAGES_PASSWORD}}
      - name: Build and Test
        run: >-
          ./gradlew build -Preckon.stage=final -Preckon.scope=${{env.RECON_SCOPE}}
          -PmavenUsername=${{secrets.GIT_PACKAGES_USER}}
          -PmavenPassword=${{secrets.GIT_PACKAGES_PASSWORD}}
      - name: ReckonTagPush
        id: ReckonTagPush
        run: ./gradlew reckonTagPush -Preckon.stage=final -Preckon.scope=${{env.RECON_SCOPE}}
      - name: Publish Unit Test Results
        uses: EnricoMi/publish-unit-test-result-action@v1
        if: success() || failure()
        with:
          files: build/test-results/**/*.xml
      - name: Set Tag
        run: echo "GIT_TAG=`echo $(git describe --tags --abbrev=0)`" >> $GITHUB_ENV
      - name: JIB
        run: >-
          ./gradlew jibDockerBuild
          -PmavenUsername=${{secrets.GIT_PACKAGES_USER}}
          -PmavenPassword=${{secrets.GIT_PACKAGES_PASSWORD}}
      - id: auth
        name: 'Authenticate to GCP'
        uses: google-github-actions/auth@v1
        with:
          workload_identity_provider: 'projects/**********/locations/global/workloadIdentityPools/github-actions-pool/providers/github-actions-pool-prvdr'
          service_account: '<EMAIL>'
          project_id: "lower-tools-sre"

      - name: 'Set up Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v1'

      - name: gcloud auth configure-docker
        run: |
          gcloud auth configure-docker us-docker.pkg.dev --project ${{env.PROJECT_ID}} --quiet
      - name: Tag Push Image
        run: |-
          docker tag ${{env.IMAGE}}:latest ${{env.IMAGE}}:${{ env.GIT_TAG }}
          docker push ${{env.IMAGE}}:${{ env.GIT_TAG }}
    outputs:
      git-tag: ${{ env.GIT_TAG }}

  # Automate deployment to gbiqa lower.
  auto-deploy:
    needs: build
    uses: ./.github/workflows/lower-release.yaml
    with:
      customer_name: gbiqa
      release_version: ${{ needs.build.outputs.git-tag }}
    secrets:
      GIT_TOKEN: ${{secrets.GIT_TOKEN}}
      VAULT_URL_LOWER: ${{ secrets.VAULT_URL_LOWER }} 
      VAULT_TOKEN_LOWER: ${{ secrets.VAULT_TOKEN_LOWER }} 

