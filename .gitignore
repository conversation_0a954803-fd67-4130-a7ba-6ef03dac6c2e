Thumbs.db
.DS_Store
.gradle
build/
bin/
target/
out/
.idea
.qodo
.vscode
.local/.hidden/
*.iml
*.ipr
*.iws
.project
.settings
.classpath
.factorypath
.env

*.private.env.json
.run/groupby-development-*.json

#Lombok
lombok.config

# Ignore generated credentials from google-github-actions/auth
gha-creds-*.json

# ignore vim temp files
*.swp
*.swo

#antlr
/src/main/java/com/groupbyinc/search/ssa/antlr/FilterVisitor.java
/src/main/java/com/groupbyinc/search/ssa/antlr/FilterParser.java
/src/main/java/com/groupbyinc/search/ssa/antlr/FilterListener.java
/src/main/java/com/groupbyinc/search/ssa/antlr/FilterLexer.tokens
/src/main/java/com/groupbyinc/search/ssa/antlr/FilterLexer.interp
/src/main/java/com/groupbyinc/search/ssa/antlr/Filter.tokens
/src/main/java/com/groupbyinc/search/ssa/antlr/FilterBaseListener.java
/src/main/java/com/groupbyinc/search/ssa/antlr/FilterBaseVisitor.java
/src/main/java/com/groupbyinc/search/ssa/antlr/FilterLexer.java
/src/main/java/com/groupbyinc/search/ssa/antlr/Filter.interp
