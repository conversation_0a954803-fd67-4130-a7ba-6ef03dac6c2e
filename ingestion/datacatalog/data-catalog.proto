syntax = "proto3";

option java_multiple_files = true;
package com.groupbyinc.proto.ingestion.datacatalog;

import "common-schemas/ingestion/base-ingestion.proto";

/**
 * Indicates that the ingestion task initiated and the uploaded files are persisted on GCS
 *
 * Published by:     Ingestion-API
 * Topic:            data-catalog-init
 * Known subscriber: Data-Catalog-API
 */
message InitMessage {

  IngestionTask task = 1;

}

/**
 * Represents a message which indicates that status of task have been changed
 *
 * Published by:     Data-Catalog-API
 * Topic:            data-catalog-status-change
 * Known subscriber: Ingestion-API
 */
message StatusChangeMessage {

  StatusDetails statusDetails = 1;

}

/**
 * Represents a message which indicates that task have been canceled
 *
 * Published by:     Ingestion-API
 * Topic:            data-catalog-cancel
 * Known subscriber: Data-Catalog-API
 */
message CancelTaskMessage {

  TaskInfo taskInfo = 1;
  UploadType uploadType = 2;

}

/**
 * Represents a message which indicates that catalog version changed
 *
 * Published by:     Data-Catalog-API
 * Topic:            data-catalog-collection-version-update
 * Known subscriber: Data-Catalog-Fetch-API
 */
message DataCatalogVersionMessage {

  string tenant = 1;
  string collection = 2;
  int32 version = 3;

}
