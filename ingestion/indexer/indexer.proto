syntax = "proto3";

option java_multiple_files = true;
package com.groupbyinc.proto.ingestion.indexer;

import "google/protobuf/timestamp.proto";
import "common-schemas/ingestion/base-ingestion.proto";

/**
 * Indicates that the ingestion task initiated and the uploaded files are persisted on GCS
 *
 * Published by:        Ingestion-API
 * Topic:               atlas-indexer-init
 * Known subscribers:   Search Indexer Service
 */
message InitMessage {

  IngestionTask task = 1;

}

/**
 * Represents a message which indicates that transformation has been initiated
 *
 * Published by:      Ingestion API service
 * Topic:             indexer-transform-init
 * Known subscribers: Atlas-Indexer
 */
message TransformInitMessage {

  IngestionTask task = 1;

  optional int32 transformTimeoutMinutes = 2;

}

/**
 * Represents a message which indicates that status of task have been changed
 *
 * Published by:      Atlas-Indexer
 * Topic:             atlas-indexer-status-change
 * Known subscribers: Ingestion API service
 */
message StatusChangeMessage {

  StatusDetails statusDetails = 1;
  string projectId = 2; // GCP Project
  optional string retailOperationName = 3; // Unique name of import operation processing in Retail

}

/**
 * Represents context for branch action executing
 * Could be used to apply next available actions or to track already applied action
 */
message BranchActionMessage {

  // Action to be run against provided branch
  message BranchAction {

    enum Action {
      CLEANING = 0;         // Cleanup the branch (i.e. import empty catalog data)
      IMPORTING = 1;        // Import catalog data to the branch
      SET_DEFAULT = 2;      // Mark the branch as `default`
      IMPORTING_LOCAL = 3;  // Importing local inventory data to the branch
      AUTOCOMPLETE = 4;     // Apply Autocomplete config upload. Branch-independent
    }


    Action action = 1;       // Action to be applied
    int32 branchNumber = 2;  // Branch number to be processed. Possible values: [0,1,2]

  }

  // Configuration context to be used during the indexing process
  message ImportConfig {
    /**
     * Indicates how imported products are reconciled with the existing products created or imported before.
     * Is a working duplicate of ImportProductsRequest.ReconciliationMode (google/cloud/retail/v2/import_config.proto)
     * Defaults to INCREMENTAL
     */
    enum ReconciliationMode {
      RECONCILIATION_MODE_UNSPECIFIED = 0;
      INCREMENTAL = 1;
      FULL = 2;
    }

    TaskInfo taskInfo = 1;
    string gcpProject = 2;
    string dataset = 3;
    string table = 4;
    string emptyTable = 5;
    /**
     * Ordered list of remaining branch actions to execute on.
     * After picking of action it has to be removed from the list.
     */
    repeated BranchAction branchActions = 6;
    ReconciliationMode mode = 7;
    string mask = 8;
    google.protobuf.Timestamp indexingStartTime = 9;
    google.protobuf.Timestamp indexingCompletionTime = 10;
    int64 numIndexedProducts = 11;
    int64 failureCount = 12;
    string message = 13;
    optional string bigQueryMessage = 14;
    optional FailureConfiguration failureConfiguration = 15;
    optional LocalInventoryConfiguration localInventoryConfiguration = 16;
    google.protobuf.Timestamp bqLoadStartTime = 17;

  }

  // Contains all configuration context for import data during the indexing process
  ImportConfig importConfig = 1;

  // Current action to be tracked while being in progress
  optional BranchAction action = 2;

  // Current retail operation name (unique) to be tracked while being in progress
  optional string operationName = 3;

}

message BigQueryUploadTrackingMessage {

  UploadTrackingDetails uploadTrackingDetails = 1;
  bool tableWithNullableTitle = 2;
  optional string tableWithNullableTitleName = 3;
  optional LocalInventoryConfiguration localInventoryConfiguration = 4;

}

message LocalInventoryUploadTrackingMessage {

  UploadTrackingDetails uploadTrackingDetails = 1;
  LocalInventoryConfiguration localInventoryConfiguration = 2;
  optional BranchActionMessage.ImportConfig importConfig = 3;

}

message BatchTransformTrackingMessage {

  UploadTrackingDetails.IngestionTaskData taskData = 1;
  optional FailureConfiguration failureConfiguration = 2;
  string projectId = 3;
  string region = 4;
}

/**
* Represents the context for tracking data loading into BigQuery.
*/
message UploadTrackingDetails {
  /**
   * Represents the ingestion task metadata.
   */
  message IngestionTaskData {
    TaskInfo taskInfo = 1;
    // Ingestion task status
    UploadType uploadType = 2;
    optional string partialPartialsMask = 3 [deprecated = true];
    optional string partialRecordMask = 4;
  }

  string jobId = 1;
  string projectId = 2;
  string sourceUri = 3;
  string datasetName = 4;
  string targetTableName = 5;
  IngestionTaskData taskData = 6;
  google.protobuf.Timestamp bqLoadStartTime = 7;
  optional FailureConfiguration failureConfiguration = 8;

}

/**
 * Encapsulate local inventory related fields.
 */
message LocalInventoryConfiguration {

  string localInvFilePath = 1;
  string localInvBucketName = 2;

}
