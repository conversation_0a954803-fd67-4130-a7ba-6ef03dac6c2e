syntax = "proto3";

option java_multiple_files = true;
package com.groupbyinc.proto.ingestion;

/**
 * Object representing base fields which are applicable for all ingestion tasks
 */
message TaskInfo {

  // UUID identifies the ingestion task which is used to track the ingestion process
  string taskId = 1;

  // Uniquely identifies the merchandiser (GroupBy's customer) across the platform
  string merchandiserId = 2;

  // Name of collection for which upload is started
  string collection = 3;

}

message IngestionTask {

  TaskInfo taskInfo = 1;

  // Type of ingestion operation
  UploadType uploadType = 2;

  // Upload fail settings
  optional FailureConfiguration failureConfiguration = 3;

  // Storage object for the raw catalog data file (ND-JSON, could be gzipped)
  optional StorageObject catalogData = 4;

  // Storage object for the raw catalog data file (ND-JSON, could be gzipped)
  optional StorageObject localInventoryData = 5;

  // Storage object for the autocomplete terms data file (CSV)
  optional StorageObject autocompleteData = 6;

  // Optional parameter for atlas-indexer, otherwise must be empty
  optional string partialPartialsMask = 7 [deprecated = true];

  //Optional parameter with attributes list (JSON) for google retail apply
  optional string attributes = 8;

  // Optional parameter for atlas-indexer, otherwise must be empty
  optional string partialRecordMask = 9;

}

message StatusDetails {

  message CollectionMetadata {
    string field = 1;
    string value = 2;
  }

  TaskInfo taskInfo = 1;

  // Ingestion task status
  string status = 2;

  // Summary message that describes processing status (messages from retail, failures and etc.)
  optional string message = 3;

  // Epoch milliseconds representing the time at which operation started
  optional uint64 startTime = 4;

  // Epoch milliseconds representing the time at which operation completed
  optional uint64 completionTime = 5;

  // Number of products indexed
  // This is expected to be equal to the number of valid top-level JSON records in a product catalog data file
  optional uint64 numIndexedProducts = 6;

  // Number of products that failed to be indexed
  optional uint64 numIndexFailedProducts = 7;

  // Catalog metadata for useful metrics ex. # of each type of product, # products in stock
  repeated CollectionMetadata primaryMetadata = 8;
  repeated CollectionMetadata variantMetadata = 9;
  repeated CollectionMetadata collectionsMetadata = 10;

}

message FailureConfiguration {

  // Percent of records which may fail until task will be determined as failed
  optional uint32 percentageThreshold = 1;

  // Count of records which may fail until task will be determined as failed
  optional uint32 countThreshold = 2;

  optional uint32 outputSizeDifferenceThreshold = 3;

}

/**
 * A wrapper object that can be used to refer to Google Cloud Storage (GCS) objects (files)
 */
message StorageObject {

  // Bucket name where the object is located
  string bucketName = 1;

  /**
   * File path under above mentioned bucket name
   * Example: 'bucket-name/sub-bucket-name/file.ndjson'
   */
  string filePath = 2;

  // Epoch milliseconds representing the time at which storage object was updated
  uint64 updatedTime = 3;

}

enum UploadType {
  BASELINE = 0;
  PARTIAL_RECORD = 1;
  BASELINE_CLEAN = 2;
  PARTIAL_CATALOG = 3;
  LOCAL_INVENTORIES = 4;
  BASELINE_INVENTORIES = 5;
  AUTOCOMPLETE_DENY_LIST = 6;
  BASELINE_INVENTORIES_CLEAN = 7;
  FITMENT_SPECIFICATION = 8;
  FITMENT_PRODUCT_MAP = 9;
}
