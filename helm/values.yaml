ambassador:
  listen: 80
  mappingname: ambassador-mapping
  hostname: some!valid!domain.groupbycloud.com
  bypass_auth: false
  prefix: '/api/v3/search'
  rewrite: '/api/search'
local:
  enabled: false

# corsPolicy:
#   allowOrigins:
#   - exact: https://example.com
#   allowMethods:
#   - GET
#   allowCredentials: false
#   allowHeaders:
#   - X-Foo-Bar
#   maxAge: "24h"
image:
  repository: us-docker.pkg.dev/upper-tools-dev/gcr.io/search/site-search-api

configmap:
  searchRedisURI: ''
  browseRedisURI: ''
  mongoRedisURI: ''
  ambassador: false
  project_id: groupby-development
  name: configmap-volume
  mountPath: /etc/configmap
  search_beacon_token: gcp:///************/DIRECT_SEARCH_BEACON_AUTH_TOKEN
  command_center_url: http://cc-api-atlas-app-svc.atlas-command-center-api/config
  authentication_jwks: http://authentication.authentication/.well-known/jwks.json
  search_beacon_url: https://us.groupbycloud.com
  bulkhead:
    search:
      parallelism: 8
    pdp:
      parallelism: 8
  mongo:
    account: ''
    key: ''
    host: ''
    uri: ''
    searchIndexSuffixOverride: ''
    analytics:
      defaultValue: 0.1
      multiplier: 1024
    partNumber:
      matchScoreMultiplier: 2
      boostScoreMultiplier: 4
      exactMatchMultiplier: 2048
      indexDelaySeconds: 14400  # 4 hours
    facets:
      dynamicFacetsCount: 5
      categoryBucketCacheTtl: 60m
    timeout:
      meta: 1
      search: 20
    readPreference: SECONDARY_PREFERRED
    minThreadsSize: 1
    maxThreadsSize: 3
    maxConnectingThreads: 2

prometheus:
  scrape: true
  endpoint: "/metrics"
  scheme: http
  port: 9090
  podMonitoring: false
  mergeMetrics: false

hpa: {}

envFileName: ""

sitesearch:
  enabled: false # If false, site-search-api will not be installed
  appLogLevel: INFO
  EgressTraffic:
    enabled: false
    excludeOutboundIPRanges: "0.0.0.0/0"
  container:
    port: 8080
    healthcheckport: 8080
    readinesspath: /health/readiness
    livenesspath: /health/liveness

    service:
      type: ClusterIP
      port: 8080

    nodeSelector: {}
    tolerations: []
    affinity: {}

  strategy:
    rollingUpdate:
      maxSurge: 10%
      maxUnavailable: 0
    type: RollingUpdate

  # pod hpa configuration including metrics, and scale policy
  hpa:
    replicas:
      min: 1
      max: 1
    metrics1:
      targetCpuUsage: 60
      name: cpu
    external:
      targetQPS: 25
      targetParallel: 4
    behavior:
      scaleUp:
        policies:
          pods:
            value: 10
            periodSeconds: 10
          percent:
            value: 30
            periodSeconds: 60
        selectPolicy: Max
      scaleDown:
        stabilizationWindowSeconds: 1800
        policies:
          percent:
            value: 20
            periodSeconds: 900

  resources:
    limits:
      cpu: 2
      memory: 4Gi
    requests:
      cpu: 1
      memory: 3.5Gi

initcontainer:
  name: secret-container
  image: 'us-docker.pkg.dev/upper-tools-dev/gcr.io/gcp-get-secret:dev3'
  mountpath: /etc/secrets
  args: env | grep 'gcp://'| awk -F'=' '{print $1}' | xargs -I {}  gcp-get-secret ash -c 'echo export {}=${}' >> /etc/secrets/values.sh
