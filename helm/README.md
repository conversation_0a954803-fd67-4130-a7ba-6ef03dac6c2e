# Site Search API

## Prerequisites

- Kubernetes 1.16+
- <PERSON><PERSON> 3+

## Install Chart

```console
# Helm Dry run
$ helm upgrade --install site-search-api helm/ -f helm/values/values-dev.yaml --set image.tag=latest --namespace site-search-api --atomic --dry-run --debug
# Helm Real run
 helm upgrade --install site-search-api helm/ -f helm/values/values-dev.yaml --set image.tag=latest --namespace site-search-api--atomic
$
```

## Test Chart

```console
# Helm
$  helm template site-search-api helm/ -f helm/values/values-dev.yaml --set image.tag=latest --namespace site-search-api --atomic --debug
```

_See [configuration](#configuration) below._

_See [helm install](https://helm.sh/docs/helm/helm_install/) for command documentation._

## Dependencies

By default this chart installs additional, dependent charts:

- [stable/kube-state-metrics](https://github.com/helm/charts/tree/master/stable/kube-state-metrics)

To disable the dependency during installation, set `kubeStateMetrics.enabled` to `false`.

_See [helm dependency](https://helm.sh/docs/helm/helm_dependency/) for command documentation._

## Uninstall Chart

```console
# Helm
$ helm uninstall [RELEASE_NAME]
```

This removes all the Kubernetes components associated with the chart and deletes the release.

_See [helm uninstall](https://helm.sh/docs/helm/helm_uninstall/) for command documentation._

## Upgrading Chart

```console
# Helm
$ helm upgrade [RELEASE_NAME] [CHART] --install
```

_See [helm upgrade](https://helm.sh/docs/helm/helm_upgrade/) for command documentation._


## Configuration

See [Customizing the Chart Before Installing](https://helm.sh/docs/intro/using_helm/#customizing-the-chart-before-installing). To see all configurable options with detailed comments, visit the chart's [values.yaml](values.yaml), or run these configuration commands:

```console
# Helm 3
$ helm show values site-search-api
```


### Setting Metrics and Alerts

```TODO:```
