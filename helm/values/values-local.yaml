ambassador:
  mappingname: ambassador-mapping
  hostname: site-search-api.dev.example.ca
local:
  enabled: true

nameOverride: ""
fullnameOverride: "ss-api"
k8sSvcAccount: "k8s-ss-api-runner-secret-sa"

### sitesearch
sitesearch:
  enabled: true
  envFileName: "config/sitesearch/local"

  replicaCount: 1

  image:
    pullPolicy: IfNotPresent

  nameOverride: ""
  fullnameOverride: "site-search-api-local"

  hpa:
    replicas:
      min: 1
      max: 1

  resources:
    limits:
      cpu: 200m
      memory: 536Mi
    requests:
      cpu: 100m
      memory: 256Mi

  resourcessql:
    limits:
      cpu: 200m
      memory: 536Mi
    requests:
      cpu: 100m
      memory: 256Mi
