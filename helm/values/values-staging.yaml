ambassador:
  mappingname: ambassador-mapping
  hostname: '^.*(.qa|.dev|.prod)?.groupbycloud.com'

nameOverride: ""
fullnameOverride: "ss-api-st"
k8sSvcAccount: "k8s-ss-api-st-runner-secret-sa"

configmap:
  ambassador: true
  environment: dev
  config_topic: config-update-dev

### sitesearch
sitesearch:
  enabled: true
  envFileName: "config/sitesearch/staging"

  replicaCount: 1

  image:
    pullPolicy: Always

  nameOverride: ""

  hpa:
    replicas:
      min: 1
      max: 1
