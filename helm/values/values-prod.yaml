ambassador:
  mappingname: site-search-api-prod
  hostname: '^.*(.qa|.dev|.prod)?.groupbycloud.com'

nameOverride: ""
fullnameOverride: "ss-api"
k8sSvcAccount: "k8s-ss-api-runner-secret-sa"

configmap:
  ambassador: true
  project_id: groupby-cloud-1701
  search_beacon_token: gcp:///************/DIRECT_SEARCH_BEACON_AUTH_TOKEN

sitesearch:
  enabled: true
  envFileName: "config/sitesearch/prod"

  replicaCount: 2

  image:
    pullPolicy: Always

  hpa:
    replicas:
      min: 65
      max: 150
  resources:
    limits:
      cpu: 2
      memory: 3Gi
    requests:
      cpu: 1
      memory: 1Gi
