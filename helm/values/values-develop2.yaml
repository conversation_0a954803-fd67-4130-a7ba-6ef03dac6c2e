ambassador:
  mappingname: ambassador-mapping
  hostname: 'site-search-api.dev.groupbycloud.com'
  bypass_auth: true
  prefix: '/api/dev/search'

nameOverride: ""
fullnameOverride: "ss-api"
k8sSvcAccount: "k8s-ss-api-runner-secret-sa"
configmap:
  ambassador: true
  environment: dev
  config_topic: config-update-dev
  security_enabled: "\"false\""
  search_beacon_url: https://us.dev.groupbycloud.com

### sitesearch
sitesearch:
  enabled: true
  envFileName: "config/sitesearch/dev"

  project_id:
  replicaCount: 1

  image:
    pullPolicy: Always

  nameOverride: ""
  fullnameOverride: "site-search-api-dev"

  hpa:
    replicas:
      min: 1
      max: 20
