{{/* vim: set filetype=mustache: */}}
{{/*
Expand the name of the chart.
*/}}
{{- define "helm.name" -}}
{{- printf "%s-%s" .Chart.Name .Values.nameOverride | replace "+" "_" | trunc 63 | trimSuffix "-" -}}
{{- end -}}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "helm.fullname" -}}
{{- if .Values.fullnameOverride -}}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" -}}-{{- template "subfolder" . -}}
{{- end -}}
{{- end -}}

{{- define "subfolder" -}}
{{- $list := regexSplit "/" $.Template.Name -1 | reverse  -}}
{{- $list | slice $list 1 2 | toStrings | quote | regexFind "[a-zA-Z]+"   -}}
{{- end -}}


{{- define "mychart.shared" -}}
heritage: {{ .Release.Service }}
date: {{ now | htmlDate }}
chart: {{ include "helm.chart" . }}
app.kubernetes.io/name: {{ include "helm.fullname" .  }}
app.kubernetes.io/release: {{ .Release.Name }}
{{- end -}}

{{- define "mychart.app-shared" -}}
app: {{ .Values.fullnameOverride }}
{{- end -}}

{{- define "mychart.app" -}}
app.kubernetes.io/name: {{ include "helm.fullname" . }}
app.kubernetes.io/component: api
{{- end -}}

{{- define "mychart.cm" -}}
configmap: {{ include "helm.fullname" . }}
{{- end -}}

{{- define "mychart.hpa" -}}
horizontalpodautoscaler: {{ include "helm.fullname" . }}
{{- end -}}

{{- define "mychart.svc" -}}
service: {{ include "helm.fullname" . }}-app-svc
{{- end -}}

{{- define "mychart.ambassador" -}}
ambassador: {{ include "helm.fullname" . }}-mapping
{{- end -}}

{{- define "mychart.virtualservice" -}}
virtualservice: {{ include "helm.fullname" . }}-virtualservice
{{- end -}}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "helm.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" -}}
{{- end -}}
