{{- if .Values.local.enabled -}}
---
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: {{ include "helm.fullname" . }}-gateway
spec:
  selector:
    istio: ingressgateway # use istio default ingress gateway
  servers:
    - port:
        number: 443
        name: https
        protocol: HTTPS
      tls:
        mode: SIMPLE
        credentialName: selfsigned-cert-tls # must be the same as secret
      hosts:
        - {{ .Values.ambassador.hostname }}
    - port:
        number: 80
        name: http
        protocol: HTTP
      hosts:
        - {{ .Values.ambassador.hostname }}
      tls:
        httpsRedirect: true
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{ include "helm.fullname" . }}-virtualservice
  labels:
{{ include "mychart.shared" . | indent 4 }}
{{ include "mychart.virtualservice" . | indent 4 }}
spec:
  gateways:
    - {{ include "helm.fullname" . }}-gateway
  hosts:
    - {{ .Values.ambassador.hostname }}
  http:
    - match:
        - uri:
            prefix: /
      route:
        - destination:
            host: {{ include "helm.fullname" . }}-app-svc
            port:
              number: 80
---
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: test-selfsigned
  namespace: istio-system
spec:
  selfSigned: {}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: selfsigned-cert
  namespace: istio-system
spec:
  dnsNames:
    - "*.dev.example.ca"
  secretName: selfsigned-cert-tls
  issuerRef:
    name: test-selfsigned
{{- end }}
