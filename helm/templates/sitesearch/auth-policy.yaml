{{- if .Values.sitesearch.enabled -}}
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: {{ include "helm.fullname" . }}-ext-authz
  labels:
{{ include "mychart.shared" . | indent 4 }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "helm.fullname" . }}
  action: CUSTOM
  provider:
    # The provider name must match the extension provider defined in the mesh config.
    # You can also replace this with sample-ext-authz-http to test the other external authorizer definition.
    name: ext-authz-grpc
  rules:
  # The rules specify when to trigger the external authorizer.
  - to:
    - operation:
        methods: ["GET", "POST", "DELETE", "PATCH", "PUT"]
        notPaths: ["/health/readiness", "/api/swagger*", "/internal/search*", "/metrics"]
{{- end }}
