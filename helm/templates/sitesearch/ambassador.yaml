{{ if and ( .Values.configmap.ambassador) (not .Values.local.enabled) }}
apiVersion: getambassador.io/v2
kind: Mapping
metadata:
  name: {{ include "helm.fullname" . }}-{{ .Values.ambassador.mappingname }}
  namespace: ambassador
  labels:
{{ include "mychart.shared" . | indent 4 }}
{{ include "mychart.ambassador" . | indent 4 }}
spec:
  service: http://{{ include "helm.fullname" . }}-app-svc.{{ .Release.Namespace }}:{{ .Values.ambassador.listen }}
  prefix: {{ .Values.ambassador.prefix }}
  rewrite: {{ .Values.ambassador.rewrite }}
  host: {{ .Values.ambassador.hostname }}
  host_regex: true
  bypass_auth: {{ .Values.ambassador.bypass_auth }}
{{ end }}

