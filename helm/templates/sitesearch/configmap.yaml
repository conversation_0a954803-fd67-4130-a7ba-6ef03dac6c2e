{{- if .Values.sitesearch.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "helm.fullname" . }}
  labels:
{{ include "mychart.shared" . | indent 4 }}
{{ include "mychart.cm" . | indent 4 }}
data:
{{ if .Values.sitesearch.appLogLevel }}
    APP_LOG_LEVEL: {{ .Values.sitesearch.appLogLevel }}
{{ end }}
{{ if .Values.configmap.defaultLogSamplingRate }}
    DEFAULT_LOG_SAMPLING_RATE: {{ .Values.configmap.defaultLogSamplingRate | quote }}
{{ end }}
{{ if .Values.configmap.environment }}
    MICRONAUT_ENVIRONMENTS: {{ .Values.configmap.environment }}
{{ end }}
{{ if .Values.configmap.config_topic }}
    GCP_PUBSUB_CONFIG_TOPIC: {{ .Values.configmap.config_topic }}
{{ end }}
    PROJECT_ID: {{ .Values.configmap.project_id }}
    GCP_PROJECT_ID: {{ .Values.configmap.project_id }}
    DIRECT_SEARCH_BEACON_AUTH_TOKEN: {{ .Values.configmap.search_beacon_token }}
    MICRONAUT_HTTP_SERVICES_BEACON_URL: {{ .Values.configmap.search_beacon_url }}
    COMMANDCENTER_CLIENT_URL: {{ .Values.configmap.command_center_url }}
    MICRONAUT_SECURITY_TOKEN_JWT_SIGNATURES_JWKS_GROUPBY_URL: {{ .Values.configmap.authentication_jwks }}
    REDIS_SERVERS_SEARCH_URI: {{ .Values.configmap.searchRedisURI }}
    REDIS_SERVERS_BROWSE_URI: {{ .Values.configmap.browseRedisURI }}
    REDIS_SERVERS_MONGO_URI: {{ .Values.configmap.mongoRedisURI }}
{{ if .Values.configmap.security_enabled }}
    MICRONAUT_SECURITY_ENABLED: {{ .Values.configmap.security_enabled }}
{{ end }}
{{ if .Values.configmap.http_client_read_timeout }}
    MICRONAUT_HTTP_CLIENT_READ_TIMEOUT: {{ .Values.configmap.http_client_read_timeout }}
{{ end }}
{{ if .Values.configmap.singletenant }}
    SINGLETENANT_ENABLED: {{ .Values.configmap.singletenant.enabled }}
    SINGLETENANT_NAME: {{ .Values.configmap.singletenant.name }}
{{ end }}
{{ if .Values.configmap.max_request_size }}
    MICRONAUT_SERVER_MAX_REQUEST_SIZE: {{ .Values.configmap.max_request_size }}
{{ end }}
{{ if .Values.configmap.max_content_length }}
    MICRONAUT_HTTP_CLIENT_MAX_CONTENT_LENGTH: {{ .Values.configmap.max_content_length }}
{{ end }}

{{ if .Values.configmap.bulkhead }}
  {{ if .Values.configmap.bulkhead.search.parallelism }}
    BULKHEAD_SEARCH_PARALLELISM: {{ .Values.configmap.bulkhead.search.parallelism | quote }}
  {{ end }}
  {{ if .Values.configmap.bulkhead.pdp.parallelism }}
    BULKHEAD_PDP_PARALLELISM: {{ .Values.configmap.bulkhead.pdp.parallelism | quote }}
  {{ end }}
{{ end }}

{{ if .Values.configmap.mongo }}
  {{ if .Values.configmap.mongo.key }}
    MONGO_KEY: {{ .Values.configmap.mongo.key }}
  {{ end }}
  {{ if .Values.configmap.mongo.account }}
    MONGO_ACCOUNT: {{ .Values.configmap.mongo.account }}
  {{ end }}
  {{ if .Values.configmap.mongo.host }}
    MONGO_HOST: {{ .Values.configmap.mongo.host }}
  {{ end }}
  {{ if .Values.configmap.mongo.uri }}
    MONGODB_URI: {{ .Values.configmap.mongo.uri }}
  {{ end }}
  {{ if .Values.configmap.mongo.readPreference }}
    MONGO_READ_PREFERENCE: {{ .Values.configmap.mongo.readPreference }}
  {{ end }}
  {{ if .Values.configmap.mongo.minThreadsSize }}
    MONGODB_CONNECTION_POOL_MIN_SIZE: {{ .Values.configmap.mongo.minThreadsSize | quote }}
  {{ end }}
  {{ if .Values.configmap.mongo.maxThreadsSize }}
    MONGODB_CONNECTION_POOL_MAX_SIZE: {{ .Values.configmap.mongo.maxThreadsSize | quote }}
  {{ end }}
  {{ if .Values.configmap.mongo.maxThreadsSize }}
    MONGODB_CONNECTION_POOL_MAX_CONNECTING: {{ .Values.configmap.mongo.maxConnectingThreads | quote }}
  {{ end }}
  {{ if .Values.configmap.mongo.searchIndexSuffixOverride }}
    MONGO_SEARCH_INDEX_SUFFIX_OVERRIDE: {{ .Values.configmap.mongo.searchIndexSuffixOverride }}
  {{ end }}
  {{ if .Values.configmap.mongo.analytics.defaultValue }}
    MONGO_ANALYTICS_DEFAULT_VALUE: {{ .Values.configmap.mongo.analytics.defaultValue | quote }}
  {{ end }}
  {{ if .Values.configmap.mongo.analytics.multiplier }}
    MONGO_ANALYTICS_MULTIPLIER: {{ .Values.configmap.mongo.analytics.multiplier | quote }}
  {{ end }}
  {{ if .Values.configmap.mongo.partNumber.matchScoreMultiplier }}
    MONGO_PART_NUMBER_MATCH_SCORE_MULTIPLIER: {{ .Values.configmap.mongo.partNumber.matchScoreMultiplier | quote }}
  {{ end }}
  {{ if .Values.configmap.mongo.partNumber.boostScoreMultiplier }}
    MONGO_PART_NUMBER_BOOST_SCORE_MULTIPLIER: {{ .Values.configmap.mongo.partNumber.boostScoreMultiplier | quote }}
  {{ end }}
  {{ if .Values.configmap.mongo.partNumber.exactMatchMultiplier }}
    MONGO_PART_NUMBER_EXACT_MATCH_MULTIPLIER: {{ .Values.configmap.mongo.partNumber.exactMatchMultiplier | quote }}
  {{ end }}
  {{ if .Values.configmap.mongo.partNumber.indexDelaySeconds }}
    MONGO_PART_NUMBER_INDEX_DELAY_SECONDS: {{ .Values.configmap.mongo.partNumber.indexDelaySeconds | quote }}
  {{ end }}
  {{ if .Values.configmap.mongo.facets.dynamicFacetsCount }}
    MONGO_FACETS_DYNAMIC_FACETS_COUNT: {{ .Values.configmap.mongo.facets.dynamicFacetsCount | quote }}
  {{ end }}
  {{ if .Values.configmap.mongo.facets.categoryBucketCacheTtl }}
    MONGO_FACETS_CATEGORY_BUCKET_CACHE_TTL: {{ .Values.configmap.mongo.facets.categoryBucketCacheTtl | quote }}
  {{ end }}

  {{ if .Values.configmap.mongo.timeout }}
  {{ if .Values.configmap.mongo.timeout.meta }}
    MONGO_TIMEOUT_META: {{ .Values.configmap.mongo.timeout.meta | quote }}
  {{ end }}
  {{ if .Values.configmap.mongo.timeout.search }}
    MONGO_TIMEOUT_SEARCH: {{ .Values.configmap.mongo.timeout.search | quote }}
  {{ end }}
  {{ end }}
{{ end }}

{{ if .Values.configmap.otel_metrics_enabled }}
    OTEL_METRICS_ENABLED: {{ .Values.configmap.otel_metrics_enabled | quote}}
{{ end }}
{{ if .Values.configmap.otel_metrics_counters }}
    OTEL_METRICS_COUNTERS: {{ .Values.configmap.otel_metrics_counters }}
{{ end }}
{{ if .Values.configmap.otel_metrics_histograms }}
    OTEL_METRICS_HISTOGRAMS: {{ .Values.configmap.otel_metrics_histograms }}
{{ end }}

{{ if .Values.configmap.retail }}{{ if .Values.configmap.retail.warmup }}
  {{ if .Values.configmap.retail.warmup.enabled }}
    RETAIL_WARMUP_ENABLED: {{ .Values.configmap.retail.warmup.enabled | quote }}
  {{ end }}
  {{ if .Values.configmap.retail.warmup.collection }}
    RETAIL_WARMUP_COLLECTION: {{ .Values.configmap.retail.warmup.collection }}
  {{ end }}
  {{ if .Values.configmap.retail.warmup.area }}
    RETAIL_WARMUP_AREA: {{ .Values.configmap.retail.warmup.area }}
  {{ end }}
  {{ if .Values.configmap.retail.warmup.merchandiserId }}
    RETAIL_WARMUP_MERCHANDISER_ID: {{ .Values.configmap.retail.warmup.merchandiserId }}
  {{ end }}
{{ end }}{{ end }}

{{ if .Values.configmap.features }}{{ if .Values.configmap.features.launchDarkly }}
  {{ if .Values.configmap.features.launchDarkly.sdkKey }}
    FEATURES_LAUNCH_DARKLY_SDK_KEY: {{ .Values.configmap.features.launchDarkly.sdkKey }}
  {{ end }}
  {{ if .Values.configmap.features.launchDarkly.polingIntervalMinutes }}
    FEATURES_LAUNCH_DARKLY_POLING_INTERVAL_MINUTES: {{ .Values.configmap.features.launchDarkly.polingIntervalMinutes }}
  {{ end }}
  {{ if .Values.configmap.features.launchDarkly.polingIntervalMinutes }}
    FEATURES_LAUNCH_DARKLY_EVENTS_FLUSH_MINUTES: {{ .Values.configmap.features.launchDarkly.eventsFlushMinutes }}
  {{ end }}
  {{ if .Values.configmap.features.launchDarkly.flags }}
    FEATURES_LAUNCH_DARKLY_FLAGS: {{ .Values.configmap.features.launchDarkly.flags | quote }}
  {{ end }}
    FEATURES_LAUNCH_DARKLY_ENVIRONMENT: {{ .Values.Full_Customer_Name }}
{{ end }}{{ end }}

{{ if .Values.configmap.search_beacon }}
  {{ if .Values.configmap.search_beacon.pb_topic }}
    GCP_PUBSUB_DIRECT_SEARCH_TOPIC: {{ .Values.configmap.search_beacon.pb_topic }}
  {{ end }}
  {{ if .Values.configmap.search_beacon.pb_gcp_project }}
    GCP_PUBSUB_DIRECT_SEARCH_PROJECT_ID: {{ .Values.configmap.search_beacon.pb_gcp_project }}
  {{ end }}
{{ end }}

{{ if .Values.configmap.topsort }}
  {{ if .Values.configmap.topsort.min_request_page_size }}
    TOPSORT_MIN_REQUEST_PAGE_SIZE: {{ .Values.configmap.topsort.min_request_page_size | quote }}
  {{ end }}
  {{ if .Values.configmap.topsort.client }}
    {{ if .Values.configmap.topsort.client.api_key }}
    TOPSORT_CLIENT_TOKEN: {{ .Values.configmap.topsort.client.api_key }}
    {{ end }}
    {{ if .Values.configmap.topsort.client.url }}
    MICRONAUT_HTTP_SERVICES_TOPSORT_URL: {{ .Values.configmap.topsort.client.url }}
    {{ end }}
    {{ if .Values.configmap.topsort.client.read_timeout }}
    MICRONAUT_HTTP_SERVICES_TOPSORT_READ_TIMEOUT: {{ .Values.configmap.topsort.client.read_timeout }}
    {{ end }}
  {{ end }}
{{ end }}

{{ if .Values.configmap.productcatalog }}
  {{ if .Values.configmap.productcatalog.client }}
    {{ if .Values.configmap.productcatalog.client.read_timeout }}
    MICRONAUT_HTTP_SERVICES_PRODUCTCATALOG_READ_TIMEOUT: {{ .Values.configmap.productcatalog.client.read_timeout | quote }}
    {{ end }}
  {{ end }}
{{ end }}

{{ if .Values.configmap.security }}{{ if .Values.configmap.security.crypto }}{{ if .Values.configmap.security.crypto.secretAes }}
    SECURITY_CRYPTO_SECRET_AES: {{ .Values.configmap.security.crypto.secretAes }}
{{ end }}{{ end }}{{ end }}

{{ .Files.Get .Values.sitesearch.envFileName  | indent 4 }}

---
{{- end }}
