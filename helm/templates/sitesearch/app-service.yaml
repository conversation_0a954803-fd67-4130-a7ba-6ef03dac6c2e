{{- if .Values.sitesearch.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "helm.fullname" . }}-app-svc
  labels:
{{ include "mychart.shared" . | indent 4 }}
{{ include "mychart.svc" . | indent 4 }}
spec:
  ports:
    - port: 80
      protocol: TCP
      name: http
      targetPort: {{ .Values.sitesearch.container.port }}
  selector:
{{ include "mychart.app" . | indent 4 }}
  type: ClusterIP
{{- end }}
