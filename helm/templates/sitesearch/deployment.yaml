{{- if .Values.sitesearch.enabled -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "helm.fullname" . }}
  labels:
{{ include "mychart.shared" . | indent 4 }}
{{ include "mychart.app" . | indent 4 }}
  {{- with .Values.sitesearch.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels:
{{ include "mychart.app" . | indent 6 }}
      release: {{ .Release.Name }}
  strategy:
{{ toYaml .Values.sitesearch.strategy | indent 4 }}
  template:
    metadata:
      labels:
{{ include "mychart.app" . | indent 8 }}
        release: {{ .Release.Name }}
      annotations:
        prometheus.io/scrape: {{ .Values.prometheus.scrape | quote }}
        prometheus.io/path: {{ .Values.prometheus.endpoint | quote }}
        prometheus.io/port: {{ .Values.prometheus.port | quote }}
        prometheus.io/scheme: {{ .Values.prometheus.scheme | quote }}
        prometheus.istio.io/merge-metrics: {{ .Values.prometheus.mergeMetrics | quote }}
        checksum/config: {{ .Files.Get .Values.sitesearch.envFileName | sha256sum }}
        {{- if .Values.sitesearch.EgressTraffic.enabled }}
        traffic.sidecar.istio.io/excludeOutboundIPRanges: {{ .Values.sitesearch.EgressTraffic.excludeOutboundIPRanges | quote }}
        {{- end }}
    spec:
      volumes:
        - name: secret-volume
          emptyDir:
            medium: Memory
            sizeLimit: "100Mi"
      initContainers:
        - name: {{ .Values.initcontainer.name }}-{{ include "subfolder" . }}
          image: us-docker.pkg.dev/upper-tools-dev/gcr.io/gcp-get-secret:dev3
          volumeMounts:
            - name: secret-volume
              mountPath: {{ .Values.initcontainer.mountpath }}
          command: ["/bin/ash"]
          args:
            - "-c"
            - {{ .Values.initcontainer.args | quote}} # debug: "sleep 600m"
          envFrom:
            - configMapRef:
                name: {{ include "helm.fullname" . }}
      serviceAccountName: k8s-{{ .Values.fullnameOverride  }}-runner-secret-sa
      containers:
        - name: {{ include "helm.fullname" . }}
          {{ if .Values.local.enabled }}
          image: "{{ .Values.image.repository }}"
          {{ else }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          {{ end }}
          imagePullPolicy: {{ .Values.sitesearch.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.sitesearch.container.port }}
              protocol: TCP
            - name: monitoring
              containerPort: {{ .Values.prometheus.port }}
              protocol: TCP
          envFrom:
            - configMapRef:
                name: {{ include "helm.fullname" . }}
          volumeMounts:
            - name: secret-volume
              mountPath: {{ .Values.initcontainer.mountpath }}
          env:
            - name: CONTAINER_NAME
              value:  {{ include "helm.fullname" . }}
            - name: NAMESPACE_NAME
              value:  {{ .Release.Namespace }}
          readinessProbe:
            httpGet:
              path: {{ .Values.sitesearch.container.readinesspath }}
              port: {{ .Values.sitesearch.container.healthcheckport }}
            initialDelaySeconds: 15
            periodSeconds: 5
            failureThreshold: 4
            successThreshold: 1
          livenessProbe:
            httpGet:
              path: {{ .Values.sitesearch.container.livenesspath }}
              port: {{ .Values.sitesearch.container.healthcheckport }}
            initialDelaySeconds: 60
            periodSeconds: 30
            failureThreshold: 5
            successThreshold: 1
          resources:
{{ toYaml .Values.sitesearch.resources | indent 12 }}
    {{- with .Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.sitesearch.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.sitesearch.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
    {{- end }}
{{- end }}
