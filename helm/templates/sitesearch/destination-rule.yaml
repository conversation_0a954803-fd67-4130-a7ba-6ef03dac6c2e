{{- if .Values.sitesearch.enabled -}}
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: {{ include "helm.fullname" . }}-destinationrule
  labels:
{{ include "mychart.shared" . | indent 4 }}
{{ include "mychart.svc" . | indent 4 }}
spec:
  host: {{ include "helm.fullname" . }}-app-svc
  trafficPolicy:
    loadBalancer:
      simple: LEAST_REQUEST
      localityLbSetting:
        enabled: true
        failoverPriority:
          - "topology.kubernetes.io/region"
    outlierDetection:
      consecutive5xxErrors: 3
      consecutiveGatewayErrors: 3
      interval: 30s
      maxEjectionPercent: 100
      baseEjectionTime: 1m
{{- end }}
