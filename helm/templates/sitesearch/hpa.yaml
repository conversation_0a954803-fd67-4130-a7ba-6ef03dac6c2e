{{- if .Values.sitesearch.enabled -}}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "helm.fullname" . }}-hpa
  labels:
{{ include "mychart.shared" . | indent 4 }}
{{ include "mychart.hpa" . | indent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "helm.fullname" . }}
  minReplicas: {{ .Values.sitesearch.hpa.replicas.min }}
  maxReplicas: {{ .Values.sitesearch.hpa.replicas.max }}
  behavior:
    scaleUp:
      policies:
        - type: Pods
          value: {{ .Values.sitesearch.hpa.behavior.scaleUp.policies.pods.value }}
          periodSeconds: {{ .Values.sitesearch.hpa.behavior.scaleUp.policies.pods.periodSeconds }}
        - type: Percent
          value: {{ .Values.sitesearch.hpa.behavior.scaleUp.policies.percent.value }}
          periodSeconds: {{ .Values.sitesearch.hpa.behavior.scaleUp.policies.percent.periodSeconds }}
      selectPolicy: {{ .Values.sitesearch.hpa.behavior.scaleUp.selectPolicy }}
    scaleDown:
      stabilizationWindowSeconds: {{ .Values.sitesearch.hpa.behavior.scaleDown.stabilizationWindowSeconds }}
      policies:
        - type: Percent
          value: {{ .Values.sitesearch.hpa.behavior.scaleDown.policies.percent.value }}
          periodSeconds: {{ .Values.sitesearch.hpa.behavior.scaleDown.policies.percent.periodSeconds }}
  metrics:
    - type: Resource
      resource:
        name: {{ .Values.sitesearch.hpa.metrics1.name }}
        target:
          type: Utilization
          averageUtilization: {{ .Values.sitesearch.hpa.metrics1.targetCpuUsage }}
    - type: External
      external:
        metric:
          name: "istio.io|service|server|request_count"
          selector:
            matchLabels:
              resource.labels.namespace_name: {{ .Release.Namespace }}
              resource.labels.cluster_name: __CLUSTER_NAME__
        target:
          type: AverageValue
          averageValue: {{ .Values.sitesearch.hpa.external.targetQPS }}
{{- end }}
