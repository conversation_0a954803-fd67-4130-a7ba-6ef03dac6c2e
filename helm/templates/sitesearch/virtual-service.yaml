{{- if not .Values.configmap.ambassador -}}
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{ include "helm.fullname" . }}-virtualservice
  labels:
{{ include "mychart.shared" . | indent 4 }}
{{ include "mychart.svc" . | indent 4 }}
spec:
  hosts:
{{- if eq .Values.configmap.environment "lower" }}
    - {{ .Values.fullnameOverride }}.{{ .Values.Full_Customer_Name }}-lo.groupbycloud.com
{{- else }}
    - {{ .Values.fullnameOverride }}.{{ .Values.Full_Customer_Name }}.groupbycloud.com
{{- end }}
  gateways:
    - asm-gateways/asm-ingress-gateway-xlb
  http:
    - route:
      - destination:
          host: {{ include "helm.fullname" . }}-app-svc
          port:
            number: 80
       {{- with .Values.corsPolicy }}
      corsPolicy:
{{ toYaml . | indent 8 }}
      {{- end }}
      match:
        - uri:
            prefix: /api
        - uri:
            prefix: /health
{{- end }}
