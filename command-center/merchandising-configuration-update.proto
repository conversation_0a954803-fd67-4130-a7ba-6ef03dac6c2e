syntax = "proto3";

// Generate top-level Java classes instead of nested ones.
option java_multiple_files = true;

package com.groupbyinc.proto.commandcenter;

/*
 * Represents an update to the command center merchandising configuration.
 *
 * Published by:        Command center API
 * Topic:               merchandising-configuration-update-<CLUSTER>
 * Known subscribers:   Site Search Caching Service
 */
message MerchandisingConfigurationUpdateMessage {

  // Unique identifier for the customerId Example: cvshealth
  string customerId = 1;

  // Name of the area
  string area = 2;

  // Name of the collection
  string collection = 3;

  // Names of the managers which were updated
  repeated string managers = 4;

  // Epoch milliseconds representing the time at which the configuration update has occurred.
  uint64 modificationTime = 5;
}
