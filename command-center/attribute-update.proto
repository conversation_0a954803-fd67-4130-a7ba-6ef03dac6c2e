syntax = "proto3";
import "common-schemas/command-center/config/base-configuration-update.proto";

// Generate top-level Java classes instead of nested ones.
option java_multiple_files = true;

package com.groupbyinc.proto.commandcenter;

/*
 * Represents an update to the tenant/collection attributes set.
 *
 * Published by:        Data Catalog Ingestion service
 * Topic:               command-center-attributes-update
 * Known subscribers:   Command Center
 */
message AttributesSyncMessage {
  string tenant = 1;
  string collection = 2;
  repeated AttributeOperation attributes = 3;
}

message AttributeOperation {
  string key = 1;
  config.AttributeMessage.AttributeType type = 2;
  config.MessageType messageType = 3;
}
