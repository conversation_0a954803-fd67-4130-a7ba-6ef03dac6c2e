syntax = "proto3";
import "common-schemas/command-center/config/base-configuration-update.proto";

// Generate top-level Java classes instead of nested ones.
option java_multiple_files = true;

package com.groupbyinc.proto.commandcenter.config;

/*
 * Represents an update sent from command center application.
 *
 * Published by:        Atlas Command Center API
 * Topic:               fetch-configuration-update
 * Known subscribers:   Data Catalog Fetch API
 */
message FetchConfigurationUpdateMessage {

  repeated TenantMessage tenant = 1;
  repeated ProjectConfigurationMessage projectConfiguration = 2;
  repeated AttributeMessage attribute = 3;

}

