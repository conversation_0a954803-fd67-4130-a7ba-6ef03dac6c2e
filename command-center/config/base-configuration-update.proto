syntax = "proto3";

// Generate top-level Java classes instead of nested ones.
option java_multiple_files = true;

package com.groupbyinc.proto.commandcenter.config;

enum MessageType {
  UPDATE = 0;
  DELETE = 1;
  CREATE = 2;
}

message TenantMessage {
  message TenantSettings {
    int32 facetLimit = 1;
    bool includeExpandedResults = 2;
  }

  bool enabled = 1;
  int32 id = 2;
  string name = 3;
  MessageType messageType = 4;
  TenantSettings tenantSettings = 5;
}

message ProjectConfigurationMessage {
  message AutocompleteConfiguration {
    bool extendedSuggestions = 1;
    repeated string extendedAttributes = 2;
  }

  int32 id = 1;
  int32 tenantId = 2;
  string collection = 3;
  string projectId = 4;
  bool saytEnabled = 5;
  MessageType messageType = 6;
  AutocompleteConfiguration autocompleteConfiguration = 7;
}

message AreaMessage {
  int32 id = 1;
  string name = 2;
  int32 tenantId = 3;
  MessageType messageType = 4;
  repeated MetadataMessage metadata = 5;
  optional int32 collectionId = 6;
  optional string servingConfigName = 7;
  optional int32 siteFilterId = 8;
}

message AttributeMessage {
  enum AttributeGroup {
    CUSTOM = 0;
    SYSTEM = 1;
    INVENTORY = 2;
  }
  enum AttributeType {
    UNKNOWN = 0;
    TEXTUAL = 1;
    NUMERICAL = 2;
  }

  string key = 1;
  string displayName = 2;
  MessageType messageType = 3;
  int32 collectionId = 4;
  AttributeType type = 5;
  AttributeGroup attributeGroup = 6;
  repeated MetadataMessage metadata = 7;
  bool filterable = 8;
  bool retrievable = 9;
  bool indexable = 10;
  bool searchable = 11;
  bool exactMatch = 12;
  string path = 13;
  bool partNumberSearchable = 14;
  bool dynamicFacetable = 15;
  optional int64 lastModifiedTimestamp = 16;
  optional string lastModifiedField = 17;
}

message MetadataMessage {
  string field = 1;
  string value = 2;
}

message SiteFilterMessage {
  int32 id = 1;
  int32 collectionId = 2;
  string name = 3;
  string rawFilter = 4;
  MessageType messageType = 5;
}
