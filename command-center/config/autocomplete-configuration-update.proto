syntax = "proto3";
import "common-schemas/command-center/config/base-configuration-update.proto";

// Generate top-level Java classes instead of nested ones.
option java_multiple_files = true;

package com.groupbyinc.proto.commandcenter.config;

/*
 * Represents an update sent from command center application.
 *
 * Published by:        Atlas Command Center API
 * Topic:               autocomplete-configuration-update
 * Known subscribers:   Atlas-Autocomplete API
 */
message AutocompleteConfigurationUpdateMessage {
  repeated TenantMessage tenant = 1;
  repeated AreaMessage area = 2;
  repeated ProjectConfigurationMessage projectConfiguration = 3;
  repeated SiteFilterMessage siteFilter = 4;
  repeated AttributeMessage attribute = 5;
}
