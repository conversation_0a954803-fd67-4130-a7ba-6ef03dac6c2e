syntax = "proto3";
import "common-schemas/command-center/config/base-configuration-update.proto";

// Generate top-level Java classes instead of nested ones.
option java_multiple_files = true;

package com.groupbyinc.proto.commandcenter.config;

/*
 * Represents an update sent from command center application.
 *
 * Published by:        Atlas Command Center API
 * Topic:               recommendations-configuration-update
 * Known subscribers:   Atlas-Recommendations API
 */
message RecommendationConfigurationUpdateMessage {
  repeated TenantMessage tenant = 1;
  repeated FilterSetMessage filterSet = 2;
  repeated AttributeMessage attribute = 3;
  repeated ProjectConfigurationMessage projectConfiguration = 4;
  repeated ProductRecommendationMessage productRecommendation = 5;
}

message ProductRecommendationMessage {
  int32 id = 1;
  int32 collectionId = 2;
  string name = 3;
  string modelId = 4;
  string modelName = 5;
  string eventType = 6;
  string servingConfigId = 7;
  optional int32 filterSetId = 8;
  MessageType messageType = 9;
  string modelType = 10;
  string optimizationObjective = 11;
}

message FilterSetMessage {
  message Filter {
    string field = 1;
    optional string value = 2;
    bool exclude = 3;
    bool derivedFromProduct = 5;
  }

  int32 id = 1;
  int32 collectionId = 2;
  string name = 3;
  optional string rawFilter = 4;
  repeated Filter filters = 5;
  MessageType messageType = 6;
}
