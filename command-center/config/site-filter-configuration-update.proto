syntax = "proto3";
import "common-schemas/command-center/config/base-configuration-update.proto";

// Generate top-level Java classes instead of nested ones.
option java_multiple_files = true;

package com.groupbyinc.proto.commandcenter.config;

/*
 * Represents an update sent from command center application.
 *
 * Published by:        Atlas Command Center API
 * Topic:               site-filter-configuration-update
 * Known subscribers:   SEO API
 */
message SiteFilterConfigurationUpdateMessage {
  repeated SiteFilterMessage siteFilter = 1;
}
