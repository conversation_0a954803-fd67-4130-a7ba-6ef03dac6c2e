syntax = "proto3";
import "common-schemas/command-center/config/base-configuration-update.proto";

// Generate top-level Java classes instead of nested ones.
option java_multiple_files = true;

package com.groupbyinc.proto.commandcenter.config;

/*
 * Represents an update sent from command center application.
 *
 * Published by:        Atlas Command Center API
 * Topic:               site-search-configuration-update
 * Known subscribers:   Site-Search API
 */
message SiteSearchConfigurationUpdateMessage {
  repeated TenantMessage tenant = 1;
  repeated AreaMessage area = 2;
  repeated BiasingProfileMessage biasingProfile = 3;
  repeated RedirectMessage redirect = 4;
  repeated NavigationMessage navigation = 5;
  repeated RuleMessage rule = 6;
  repeated ProjectConfigurationMessage projectConfiguration = 7;
  repeated ZoneMessage zone = 8;
  repeated AttributeMessage attribute = 9;
  repeated SiteFilterMessage siteFilter = 10;
  repeated TopsortConfigMessage topsortConfig = 11;
}

message BiasingProfileMessage {
  message BiasMessage {
    enum Strength {
      ABSOLUTE_INCREASE = 0;
      STRONG_INCREASE = 1;
      MEDIUM_INCREASE = 2;
      WEAK_INCREASE = 3;
      LEAVE_UNCHANGED = 4;
      WEAK_DECREASE = 5;
      MEDIUM_DECREASE = 6;
      STRONG_DECREASE = 7;
      ABSOLUTE_DECREASE = 8;
    }

    enum Type {
      TEXTUAL = 0;
      NUMERIC = 1;
    }

    message NumericContentMessage {
      repeated double values = 1;
      repeated RangeMessage ranges = 2;
    }

    string field = 1;
    optional string content = 2;
    Strength strength = 3;
    optional Type type = 4;
    optional NumericContentMessage numericContent = 5;
  }

  int32 id = 1;
  string name = 2;
  int32 areaId = 3;
  repeated BiasMessage biases = 4;
  bool areaDefault = 5;
  MessageType messageType = 6;
}

message RedirectMessage {
  message TriggerMessage {
    TriggerType type = 1;
    string value = 2;
  }

  int32 id = 1;
  int32 areaId = 2;
  string url = 3;
  int32 priority = 4;
  repeated TriggerMessage triggers = 5;
  ActiveTimeRangeMessage activeTimeRange = 6;
  MessageType messageType = 7;
  repeated MetadataMessage metadata = 8;
}

message NavigationMessage {
  enum NavigationType {
    VALUE = 0;
    RANGE = 1;
  }

  message NavigationSortMessage {
    enum SortField {
      COUNT = 0;
      VALUE = 1;
    }

    Order orderType = 1;
    SortField field = 2;
  }

  int32 id = 1;
  string field = 2;
  int32 areaId = 3;
  int32 priority = 4;
  NavigationType type = 5;
  repeated RangeMessage ranges = 6;
  bool multiSelect = 7;
  optional NavigationSortMessage sort = 8;
  MessageType messageType = 9;
  repeated MetadataMessage metadata = 10;
  repeated RefinementMessage pinnedRefinements = 11;
  string name = 12;
}

message RuleMessage {
  message ValueFilterMessage {
    enum ValueFilterType {
      TEXTUAL = 0;
      NUMERIC = 1;
    }

    string field = 1;
    optional string value = 2;
    bool exclude = 3;
    optional double numberValue = 4;
    ValueFilterType type = 5;
  }

  message AttributeFilterMessage {
    repeated ValueFilterMessage valueFilter = 1;
    repeated RangeFilterMessage rangeFilter = 2;
  }

  message ProductIdFilterMessage {
    repeated string includedProductIds = 1;
    repeated string excludedProductIds = 2;
  }

  message TriggerSetMessage {
    message QueryPatternMessage {
      string value = 1;
      TriggerType type = 2;
    }

    message SelectedRefinementMessage {
      enum Type {
        VALUE = 0;
        RANGE = 1;
        NAVIGATION_SELECTED = 2;
      }

      string field = 1;
      Type type = 2;
      optional RangeMessage range = 3;
      optional string value = 4;
    }

    message CustomParameterMessage {
      string key = 1;
      string value = 2;
    }

    repeated QueryPatternMessage queryPatternTrigger = 1;
    repeated CustomParameterMessage customParameterTrigger = 2;
    repeated SelectedRefinementMessage selectedRefinementTrigger = 3;
  }

  message SearchFilterMessage {
    string value = 1;
  }

  message RangeFilterMessage {
    string field = 1;
    RangeMessage range = 2;
  }

  message RuleTemplateMessage {
    message RuleTemplateSectionMessage {
      optional int32 zoneId = 1;
      string name = 2;
      optional string zoneContent = 3;
      ZoneType type = 4;
    }

    string name = 2;
    repeated RuleTemplateSectionMessage section = 3;
    optional bool enableExactMatching = 4;
  }

  message ProductBucketMessage {
    repeated string productIds = 1;
  }

  message PinnedRefinementMessage {
    string navigation = 1;
    repeated RefinementMessage refinement = 2;
  }

  message PinnedProductMessage {
    int32 position = 1;
    string productId = 2;
    bool includeOutOfStock = 3;
  }

  message VariantMessage {
    optional string biasingProfileName = 1;
    repeated string includedNavigations = 3;
    repeated SearchFilterMessage searchFilter = 5;
    optional RuleTemplateMessage template = 7;
    repeated ProductBucketMessage boostedProducts = 8;
    repeated ProductBucketMessage buriedProducts = 9;
    repeated PinnedRefinementMessage pinnedRefinements = 10;
    repeated PinnedProductMessage pinnedProducts = 11;
    optional ProductIdFilterMessage productIdFilter = 12;
    repeated AttributeFilterMessage attributeFilters = 13;
    optional ProductVisibilityBias productVisibilityBias = 14;
  }

  message ExperimentVariantMessage {
    string name = 1;
    VariantMessage variantMessage = 2;
    optional int32 variantTriggerPercentage = 3;
  }

  message ProductVisibilityBias {
    string attribute = 1;
    repeated string values = 2;
    repeated double numberValues = 3;
    double multiplier = 4;
    double percentageOffset = 5;
    optional double visibilityPercentageCap = 6;
  }

  enum RuleTypeType {
    REGULAR = 0;
    EXPERIMENT = 1;
  }

  int32 id = 1;
  string name = 2;
  int32 areaId = 3;
  int32 priority = 4;
  ActiveTimeRangeMessage activeTimeRange = 6;
  repeated TriggerSetMessage triggerSet = 7;
  optional string biasingProfileName = 8;
  repeated string includedNavigations = 10;
  repeated SearchFilterMessage searchFilter = 12;
  MessageType messageType = 14;
  optional RuleTemplateMessage template = 15;
  repeated ProductBucketMessage boostedProducts = 16;
  repeated PinnedRefinementMessage pinnedRefinements = 17;
  repeated ExperimentVariantMessage experimentVariants = 18;
  RuleTypeType type = 19;
  repeated ProductBucketMessage buriedProducts = 20;
  repeated PinnedProductMessage pinnedProducts = 21;
  optional ProductIdFilterMessage productIdFilter = 22;
  repeated AttributeFilterMessage attributeFilters = 23;
  optional ProductVisibilityBias productVisibilityBias = 24;
}

message ZoneMessage {
  int32 id = 1;
  int32 areaId = 2;
  string name = 3;
  string value = 4;
  ZoneType type = 5;
  MessageType messageType = 6;
}

enum ZoneType {
  QUERY = 0;
  CONTENT = 1;
  RICH_CONTENT = 2;
  EXPECT_QUERY = 3;
  GENERATED_CONTENT = 4;
  HTML = 5;
}

enum TriggerType {
  REGEX = 0;
  MATCHES = 1;
  CONTAINS = 2;
  ENDS_WITH = 3;
  STARTS_WITH = 4;
}

enum Order {
  ASCENDING = 0;
  DESCENDING = 1;
}

message RangeMessage {
  optional double low = 1;
  optional double high = 2;
  optional string description = 3;
  optional bool exclude = 4;
}

message ActiveTimeRangeMessage {
  optional int64 activeFrom = 1;
  optional int64 activeTo = 2;
  bool activeHoursEnabled = 3;
}

message RefinementMessage {
  string value = 1;
  int32 priority = 2;
}

message TopsortConfigMessage {
  int32 id = 1;
  int32 areaId = 2;
  bool enabled = 3;
  string apiKey = 4;
  MessageType messageType = 5;
}
