syntax = "proto3";

// Generate top-level Java classes instead of nested ones.
option java_multiple_files = true;

package com.groupbyinc.proto.commandcenter;

/*
 * Represents an update to the client key credentials or active status of a customer.
 * This could be a change to one or both primary and secondary client keys for a client or an update on the active status of the customer.
 *
 * Published by:        Command center API
 * Topic:               customer-credentials-update-<CLUSTER>
 * Known subscribers:   Authentication service
 */
message CustomerCredentialsUpdateMessage {
    enum Region {
        US = 0;
        UK = 1;
        EU = 2;
        JP = 3;
        AU = 4;
    }

    message TenantSettings {
        optional Region auth0Region = 1;
    }

    // Unique identifier for the customerId Example: cvshealth
    string customerId = 1;

    // New primary and active client key, which is typically a UUID.
    string primaryClientKey = 2;

    // New secondary and active client key, which is typically a UUID.
    string secondaryClientKey = 3;

    // Epoch milliseconds representing the time at which one or both of the keys have been updated.
    uint64 modificationTime = 4;

    // Whether or not the customer is active. Inactive customers must not be authenticated under any circumstances.
    bool active = 5;

    optional TenantSettings tenantSettings = 6;

}
