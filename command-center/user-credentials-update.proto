syntax = "proto3";

// Generate top-level Java classes instead of nested ones.
option java_multiple_files = true;

package com.groupbyinc.proto.commandcenter;

/*
 * Represents an update to the command center user email/password or when a successful login has occurred.
 * This is used to migrate username and password credentials from command center to the new authentication service.
 *
 * Published by:        Command center API
 * Topic:               user-credentials-update-<CLUSTER>
 * Known subscribers:   Authentication service
 */
message UserCredentialsUpdateMessage {

  // Unique identifier for the customerId Example: cvshealth
  string customerId = 1;

  // Email login associated with this user
  string email = 2;

  // Plain text password associated with this user
  string password = 3;

  // Epoch milliseconds representing the time at which a password change or a successful login has occurred.
  uint64 modificationTime = 4;
}
