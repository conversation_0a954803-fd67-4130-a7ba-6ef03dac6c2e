version: '3.7'
services:
  grafana:
    container_name: grafana_local
    image: grafana/grafana
    ports:
      - 3000:3000
    volumes:
      - ./.hidden/grafana:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
    networks:
      monitoring_network:
        aliases:
          - grafana
  prometheus:
    container_name: prometheus_local
    image: prom/prometheus
    ports:
      - 9090:9090
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - ./.hidden/prometheus:/prometheus
    networks:
      monitoring_network:
        aliases:
          - prometheus
networks:
  monitoring_network:
