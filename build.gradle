////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                    //
//                                                      VERSIONS                                                      //
//                                                                                                                    //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

buildscript {
    ext {
        utilsVersion = '12.5.0'

        micronautGcpTracing = '5.7.1'

        xnioApi = '3.8.7.Final'
        undertowVersion = '3.2.3'

        launchDarklyVersion = '7.6.0'

        logbackVersion = '1.5.12'
        logbackLogstashVersion = '8.0'

        lombokVersion = '1.18.36'

        openTelemetryVersion = '1.45.0'
        openTelemetryAlphaVersion = '1.30.1-alpha'

        protobufVersion = '3.25.5'

        apacheTextVersion = '1.12.0'

        zipkinBraveVersion = '6.0.3'
        zipkinReporterVersion = '3.4.3'

        awaitilityVersion = '4.2.0'
        wiremockVersion = '3.10.0'
        jupiterVerion = '5.14.2'
        jupiterParamsVerion = '5.11.3'
        hakkyVersion = '2.10.0'
        resilience4jVersion = '2.2.0'
        antlr4Version = '4.13.2'
    }
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                    //
//                                                      PLUGINS                                                       //
//                                                                                                                    //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
plugins {
    id 'application'
    id 'idea'
    id 'jacoco'

    id "io.micronaut.application" version "$micronautPluginVersion"
    id "io.micronaut.aot" version "$micronautPluginVersion"
    id 'com.google.cloud.tools.jib' version '3.4.4'
    id "com.google.protobuf" version "0.9.4"
    id "org.ajoberstar.reckon" version "0.18.2"
    id "antlr"
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                    //
//                                               VARIABLES / PROPERTIES                                               //
//                                                                                                                    //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
group = 'com.groupbyinc.search'

// These properties are either globally defined (in developer machines) or explicitly passed (in CI jobs).
def mavenUsername = project.ext.properties.mavenUsername
def mavenPassword = project.ext.properties.mavenPassword

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                    //
//                                                    DEPENDENCIES                                                    //
//                                                                                                                    //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
repositories {
    mavenLocal()
    mavenCentral()
    maven { url 'https://plugins.gradle.org/m2/' }
    maven {
        url 'https://maven.pkg.github.com/groupby/maven-releases/'

        credentials {
            username = mavenUsername
            password = mavenPassword
        }
    }
}

micronaut {
    runtime('netty')
    testRuntime('junit5')
    processing {
        incremental(true)
        annotations('com.groupbyinc.search.ssa.*')
    }
    aot {
        // Please review carefully the optimizations enabled below
        // Check https://micronaut-projects.github.io/micronaut-aot/latest/guide/ for more details
        optimizeServiceLoading = false
        convertYamlToJava = false
        precomputeOperations = true
        cacheEnvironment = true
        optimizeClassLoading = true
        deduceEnvironment = true
        optimizeNetty = true
        replaceLogbackXml = true
    }
}

dependencies {
    antlr "org.antlr:antlr4:$antlr4Version"
    annotationProcessor(
        "org.projectlombok:lombok:$lombokVersion",
        'io.micronaut.openapi:micronaut-openapi',
        'io.micronaut.security:micronaut-security-annotations',
        "io.micronaut:micronaut-inject-java:$micronautVersion",
    )

    compileOnly(
        "org.projectlombok:lombok:$lombokVersion",
        'org.slf4j:slf4j-api',
    )

    implementation(
        "com.groupbyinc.utils:validation:$utilsVersion",
        "com.groupbyinc.utils:crypto:$utilsVersion",

        "io.micronaut:micronaut-inject",
        'io.micronaut:micronaut-runtime',
        'io.micronaut:micronaut-management',
        'io.micronaut.validation:micronaut-validation',
        'io.micronaut:micronaut-jackson-databind',
        'io.micronaut:micronaut-http-client',
        "io.micronaut.rxjava2:micronaut-rxjava2",
        'io.micronaut.redis:micronaut-redis-lettuce',
        // the above redis-lettuce module does not provide io.micronaut.cache.CacheConfiguration implementation used for JWKS caching
        // so we need to include the caffeine cache module as well. We can remove the latter when the redis-lettuce module provides its own.
        // https://micronaut-projects.github.io/micronaut-security/4.11.2/guide/#jwksCache
        "io.micronaut.cache:micronaut-cache-caffeine",
        'io.micronaut.security:micronaut-security-jwt',
        'io.micronaut.micrometer:micronaut-micrometer-core',
        'io.micronaut.mongodb:micronaut-mongo-sync',
        "io.micronaut.gcp:micronaut-gcp-tracing:$micronautGcpTracing",
        "io.micronaut.gcp:micronaut-gcp-logging:$micronautGcpTracing",

        "io.github.resilience4j:resilience4j-micronaut:$resilience4jVersion",
        "io.github.resilience4j:resilience4j-reactor:$resilience4jVersion",
        "io.github.resilience4j:resilience4j-bulkhead:$resilience4jVersion",
        "io.github.resilience4j:resilience4j-consumer:$resilience4jVersion",

        "org.apache.commons:commons-text:$apacheTextVersion",

        'com.google.protobuf:protobuf-java-util',
        "com.google.protobuf:protobuf-java:$protobufVersion",

        'io.swagger.core.v3:swagger-annotations',
        'jakarta.validation:jakarta.validation-api',

        'io.projectreactor:reactor-core',
        'org.json:json:20240303',

        "org.antlr:antlr4-runtime:$antlr4Version",

        "io.opentelemetry:opentelemetry-api:$openTelemetryVersion",
        "io.opentelemetry:opentelemetry-sdk:$openTelemetryVersion",
        "io.opentelemetry:opentelemetry-semconv:$openTelemetryAlphaVersion",
        "io.opentelemetry:opentelemetry-exporter-prometheus:$openTelemetryAlphaVersion",

        "com.launchdarkly:launchdarkly-java-server-sdk:$launchDarklyVersion",

        "ch.qos.logback:logback-classic:${logbackVersion}",
    )

    implementation("com.groupbyinc.utils:micronaut-gcp:$utilsVersion") {
        exclude group: 'com.google.cloud', module: 'google-cloud-storage'
    }

    runtimeOnly(
        "net.logstash.logback:logstash-logback-encoder:${logbackLogstashVersion}",
        "io.zipkin.brave:brave-instrumentation-http:$zipkinBraveVersion",
        "io.zipkin.reporter2:zipkin-reporter:$zipkinReporterVersion",
        "org.yaml:snakeyaml"
    )

    testImplementation(
        'org.assertj:assertj-core',
        'org.mockito:mockito-core',
        "io.github.hakky54:logcaptor:$hakkyVersion",
        "org.awaitility:awaitility:$awaitilityVersion",
        "org.mockito:mockito-junit-jupiter:$jupiterVerion",
        "com.groupbyinc.utils:embedded-pubsub:$utilsVersion",
        "org.wiremock:wiremock:$wiremockVersion",
        "org.junit.jupiter:junit-jupiter-params:$jupiterParamsVerion",
    )

    testAnnotationProcessor(
        "org.projectlombok:lombok:$lombokVersion",
    )
    testCompileOnly(
        "org.projectlombok:lombok:$lombokVersion",
    )
}

// Antlr
generateGrammarSource {
    maxHeapSize = "64m"
    arguments += ["-visitor",
                  "-long-messages",
                  "-Werror",
                  "-package", "com.groupbyinc.search.ssa.antlr"]
    outputDirectory = file("${projectDir}/src/main/java")
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                    //
//                                          BUILD / RUN / TEST CONFIGURATION                                          //
//                                                                                                                    //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${protobufVersion}"
    }
}

sourceSets {
    main {
        proto {
            exclude 'common-schemas/tests/**/*.proto'
            exclude 'common-schemas/wisdom/**/*.proto'
            exclude 'common-schemas/indexer/**/*.proto'
        }
    }
}

reckon {
    defaultInferredScope = 'patch'
    stages 'rc', 'final'
    scopeCalc = calcScopeFromProp() | calcScopeFromCommitMessages()
    stageCalc = calcStageFromProp()
}

jar {
    exclude("**/common-schemas")
}

application {
    applicationDefaultJvmArgs = ['--enable-preview']
    mainClass.set('com.groupbyinc.search.ssa.Application')
}

tasks.withType(JavaCompile) {
    options.fork = true
    options.forkOptions.jvmArgs << '-Dmicronaut.openapi.views.spec=redoc.enabled=true,mapping.path=swagger'
}

configurations.all {
    exclude group: 'org.slf4j', module: 'slf4j-simple'
}

test {
    finalizedBy jacocoTestReport // report is always generated after tests run
}

jacocoTestReport {
    dependsOn test // tests are required to run before generating the report
}

wrapper {
    distributionType = Wrapper.DistributionType.ALL
    gradleVersion = '8.10.1'
}
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                    //
//                                               RELEASE AND VERSIONING                                               //
//                                                                                                                    //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//                                                                                                                    //
//                                                  CONTAINERIZATION                                                  //
//                                                                                                                    //
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
jib {
    from {
        image = 'eclipse-temurin:21-jre'
    }
    to {
        image = 'us-docker.pkg.dev/upper-tools-dev/gcr.io/search/site-search-api'
        tags = ['latest']
    }
    extraDirectories {
        paths = [file('env')]
        permissions = [
            '/entrypoint.sh': '755'
        ]
    }
    container {
        //noinspection GroovyAccessibility
        labels = [maintainer: 'Search']
        ports = ['8080']
        creationTime = 'USE_CURRENT_TIMESTAMP'
        entrypoint = ['sh', '/entrypoint.sh']

        // Netty issue causes JVM crash https://github.com/grpc/grpc-java/issues/8751
        jvmFlags = ['-Dio.grpc.netty.shaded.io.netty.transport.noNative=true']
    }
}
