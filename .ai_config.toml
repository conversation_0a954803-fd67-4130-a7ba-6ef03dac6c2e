# Qodo AI Configuration File
# See: https://docs.qodo.ai/qodo-documentation/qodo-gen/company-codebase-rag/configuration-file

[file_filters]
exclude = [
    # Files
    "**/.DS_Store",
    "**/*.log",
    "**/*.tmp",
    "**/Thumbs.db",
    "**/.run/groupby-development-*.json",
    "**/*.env",

    # Folders
    "dist/**",
    "build/**",
    "out/**",
    "bin/**",
    "target/**",
    ".gradle/**",
    ".qodo/**",
    ".env/**"
]

[tags]
project_type = "api"
languages = ["java"]
framework = ["micronaut"]
