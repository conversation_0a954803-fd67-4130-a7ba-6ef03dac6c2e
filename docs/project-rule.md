- Do not look at Linter errors until the user explicitly asks.
- Do not reformat existing code or imports that are not related to the implementation changes. Try to adhere to the existing file formating if you make changes in the existing file.
- Use `var` for new variables' declaration with new operator.
- Try to use the same naming and style patterns as in the existing code.
- Use Java 21 with preview features for all new code and refactoring.
- Micronaut is used as a Java framework, so check the Micronaut and Java 21 official documentation for actual APIs and libraries usage. (Use Context7 tool if available)
- Mongo Atlas Search is used as one of the Search Engines. Check official documentation and examples if needed. (Use Context7 tool if available)
