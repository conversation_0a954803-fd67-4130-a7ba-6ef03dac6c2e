syntax = "proto3";

package com.groupbyinc.datahub.proto;

option java_package = "com.groupbyinc.datahub.proto";
option java_outer_classname = "DatahubEventProto";

import "google/protobuf/struct.proto";

message DatahubEvent {
  // Collection identifier
  string collection = 1;

  // Complex JSON object containing product data
  // Using google.protobuf.Struct to handle arbitrary JSON structure
  google.protobuf.Struct data = 2;

  // Operation type (e.g., "create", "update", "delete")
  string op = 3;

  // Functional domain identifier
  string functional_domain = 4;
}