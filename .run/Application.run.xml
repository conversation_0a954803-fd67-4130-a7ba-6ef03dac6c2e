<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Application" type="MicronautRunConfigurationType" factoryName="Micronaut">
    <option name="envFilePaths">
      <option value="$PROJECT_DIR$/env/dev.env" />
    </option>
    <option name="MAIN_CLASS_NAME" value="com.groupbyinc.search.ssa.Application" />
    <module name="site-search-api.main" />
    <option name="shortenCommandLine" value="ARGS_FILE" />
    <option name="VM_PARAMETERS" value="-Dlogback.configurationFile=logback-local.xml" />
    <option name="alternativeJrePath" />
    <option name="alternativeJrePathEnabled" value="false" />
    <option name="mainClass" value="com.groupbyinc.search.ssa.Application" />
    <option name="passParentEnvs" value="true" />
    <option name="programParameters" value="" />
    <option name="vmParameters" value="-Dlogback.configurationFile=logback-local.xml" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>
