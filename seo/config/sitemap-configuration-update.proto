syntax = "proto3";

// Generate top-level Java classes instead of nested ones.
option java_multiple_files = true;

package com.groupbyinc.proto.seo.config;

/*
 * Represents an update sent from seo management application.
 *
 * Published by:        SEO Management API
 * Topic:               sitemap-configuration-update
 * Known subscribers:   SEO API
 */
message SiteMapConfigurationUpdateMessage {
  repeated SiteMap sitemap = 1;
}

message SiteMap {
    string collection = 1;
    string siteFilterId = 2;
    string siteUrl = 4;
    string parametersTemplate = 5;
    string titleTemplate = 6;
    bool active = 7;
    repeated Metadata metadata = 8;
    MessageType messageType = 9;
}

message Metadata {
  string field = 1;
  string value = 2;
}

enum MessageType {
  UPDATE = 0;
  DELETE = 1;
  CREATE = 2;
}
