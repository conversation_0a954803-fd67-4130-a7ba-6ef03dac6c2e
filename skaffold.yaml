apiVersion: skaffold/v2beta21
kind: Config
metadata:
  name: site-search-api
profiles:
  - name: local
    activation:
      - kubeContext: minikube
        command: dev
    build:
      artifacts:
        - image: gcr.io/groupby-cloud-1701/search/site-search-api
          jib:
            type: gradle
          sync:
            manual:
              - src: "build/classes/**/*.class"
                dest: /app/classes/
                strip: "build/classes"
    deploy:
      helm:
        releases:
          - name: site-search-api
            chartPath: helm
            artifactOverrides:
              image:
                repository: gcr.io/groupby-cloud-1701/search/site-search-api
            valuesFiles:
              - helm/values/values-local.yaml


  - name: staging
    activation:
      - kubeContext: gke_groupby-development_us-central1-a_development
        command: dev
    build:
      artifacts:
        - image: gcr.io/groupby-cloud-1701/search/site-search-api
          jib:
            type: gradle
          sync:
            manual:
              - src: "build/classes/**/*.class"
                dest: /app/classes/
                strip: "build/classes"
    deploy:
      helm:
        releases:
          - name: site-search-api
            chartPath: helm
            namespace: site-search-api
            createNamespace: true
            artifactOverrides:
              image:
                repository: gcr.io/groupby-cloud-1701/search/site-search-api
            valuesFiles:
              - helm/values/values-staging.yaml
