#!/usr/bin/env bash

# Tests the schemas by compiling them. Feel free to add tests for your schemas too.

# Needs to be ran from project root, i.e.: ./tests/compile.sh
# Requires protobuf installed in the system (replace brew with your package manager, e.g. apt-get, apk, etc):
# > brew install protobuf
# > brew install protoc-gen-go (If protoc-gen-go executable is missing from protobuf package)

DST_DIR_BASE="tests/generated"
rm -r $DST_DIR_BASE
mkdir -p $DST_DIR_BASE

DST_DIR_JAVA="$DST_DIR_BASE/java"
DST_DIR_GO="$DST_DIR_BASE/go"
mkdir -p $DST_DIR_JAVA
mkdir -p $DST_DIR_GO

# Java S4R schemas (configs, ingestion)
protoc \
  -I="../" \
  --java_out=$DST_DIR_JAVA \
  common-schemas/command-center/config/base-configuration-update.proto \
  common-schemas/command-center/config/fetch-configuration-update.proto \
  common-schemas/command-center/config/recommendation-configuration-update.proto \
  common-schemas/command-center/config/site-search-configuration-update.proto \
  common-schemas/command-center/config/tenant-configuration-update.proto \
  common-schemas/command-center/config/collection-configuration-update.proto \
  common-schemas/command-center/config/attribute-configuration-update.proto \
  common-schemas/command-center/config/area-configuration-update.proto \
  common-schemas/command-center/config/site-filter-configuration-update.proto \
  common-schemas/command-center/attribute-update.proto \
  common-schemas/command-center/customer-credentials-update.proto \
  common-schemas/command-center/merchandising-configuration-update.proto \
  common-schemas/command-center/user-credentials-update.proto \
  common-schemas/ingestion/base-ingestion.proto \
  common-schemas/ingestion/commandcenter/command-center.proto \
  common-schemas/ingestion/indexer/indexer.proto \
  common-schemas/ingestion/datacatalog/data-catalog.proto \
  common-schemas/seo/config/sitemap-configuration-update.proto

# Java and Go Wisdom schemas
protoc \
  -I="." \
  --java_out=$DST_DIR_JAVA \
  --go_out=$DST_DIR_GO \
  wisdom/events/**/*.proto
