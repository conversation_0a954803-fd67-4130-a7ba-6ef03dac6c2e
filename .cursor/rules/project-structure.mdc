---
description: Use this rule when you need to get the project structure overview and get the key directories and files in the project.
globs:
alwaysApply: false
---
# Project Structure Overview

This document outlines the key directories and files in the `site-search-api` project.

## Core Application

- **Main Source Code**: The primary application logic resides in `[src/main/java/com/groupbyinc/search/ssa/](mdc:src/main/java/com/groupbyinc/search/ssa)`.
    - **Application Entry Point**: The main entry point for Micronaut application `[Application.java](mdc:src/main/java/com/groupbyinc/search/ssa/Application.java)`.
    - **API Layer**: API definitions and controllers can be found in `[api/](mdc:src/main/java/com/groupbyinc/search/ssa/api)`.
    - **Core Logic**: Core business logic is expected in `[core/](mdc:src/main/java/com/groupbyinc/search/ssa/core)`.
    - **Data Access (MongoDB)**: Code related to MongoDB interactions is in `[mongo/](mdc:src/main/java/com/groupbyinc/search/ssa/mongo)`.
    - **Data Access (Redis)**: Code related to Redis interactions is in `[redis/](mdc:src/main/java/com/groupbyinc/search/ssa/redis)`.
    - **Messaging (Pub/Sub)**: Publish-subscribe logic is in `[pubsub/](mdc:src/main/java/com/groupbyinc/search/ssa/pubsub)`.
    - **Domain Modules**:
        - Product Catalog: `[productcatalog/](mdc:src/main/java/com/groupbyinc/search/ssa/productcatalog)`
        - Retail: `[retail/](mdc:src/main/java/com/groupbyinc/search/ssa/retail)`
        - Part Number: `[partnumber/](mdc:src/main/java/com/groupbyinc/search/ssa/partnumber)`
    - **Utilities**: Utility classes are located in `[util/](mdc:src/main/java/com/groupbyinc/search/ssa/util)`.
    - **Metrics**: Metrics-related code is in `[metrics/](mdc:src/main/java/com/groupbyinc/search/ssa/metrics)`.
    - **Features**: Feature-specific implementations are in `[features/](mdc:src/main/java/com/groupbyinc/search/ssa/features)`.
    - **Command Center**: Administration or control module: `[commandcenter/](mdc:src/main/java/com/groupbyinc/search/ssa/commandcenter)`
- **Resources**: Application resources like configuration files and templates are in `[src/main/resources/](mdc:src/main/resources)`.
- **Protocol Buffers**: Protobuf definitions are stored in `[src/main/proto/](mdc:src/main/proto)`.
- **ANTLR Grammars**: ANTLR grammar files are located in `[src/main/antlr/](mdc:src/main/antlr)` and related Java code in `[src/main/java/com/groupbyinc/search/ssa/antlr/](mdc:src/main/java/com/groupbyinc/search/ssa/antlr)`.

## Search Functionality

- **Main Search Service**: The primary service for handling search requests is `[SearchService.java](mdc:src/main/java/com/groupbyinc/search/ssa/application/core/search/SearchService.java)`.
    - Specialized search services include:
        - `[PartNumberSearchService.java](mdc:src/main/java/com/groupbyinc/search/ssa/partnumber/PartNumberSearchService.java)`
        - `[MultiProductSearchService.java](mdc:src/main/java/com/groupbyinc/search/ssa/application/core/search/strategy/multiproduct/MultiProductSearchService.java)`
- **Search Strategies**: The system employs various search strategies, defined by the `[SearchStrategy.java](mdc:src/main/java/com/groupbyinc/search/ssa/application/core/search/strategy/SearchStrategy.java)` interface.
    - **Factory**: `[SearchStrategyFactory.java](mdc:src/main/java/com/groupbyinc/search/ssa/application/core/search/strategy/SearchStrategyFactory.java)` is used to create strategy instances.
    - Key implementations include:
        - `[DefaultSearchStrategy.java](mdc:src/main/java/com/groupbyinc/search/ssa/application/core/search/strategy/base/DefaultSearchStrategy.java)`
        - `[PartNumberSearchStrategy.java](mdc:src/main/java/com/groupbyinc/search/ssa/application/core/search/strategy/partnumber/PartNumberSearchStrategy.java)`
        - `[MultiProductSearchStrategy.java](mdc:src/main/java/com/groupbyinc/search/ssa/application/core/search/strategy/multiproduct/MultiProductSearchStrategy.java)`
        - `[TopSortSearchStrategy.java](mdc:src/main/java/com/groupbyinc/search/ssa/application/core/search/strategy/topsort/TopSortSearchStrategy.java)`
        - `[FallbackSearchStrategy.java](mdc:src/main/java/com/groupbyinc/search/ssa/application/core/search/strategy/fallback/FallbackSearchStrategy.java)`
- **Search Engines**: The abstraction for different search backends is `[SearchEngine.java](mdc:src/main/java/com/groupbyinc/search/ssa/application/core/search/engine/SearchEngine.java)`.
    - **Selector**: `[SearchEngineSelector.java](mdc:src/main/java/com/groupbyinc/search/ssa/application/core/search/engine/SearchEngineSelector.java)` likely chooses the appropriate engine.
    - **Types**: `[SearchEngineType.java](mdc:src/main/java/com/groupbyinc/search/ssa/application/core/search/engine/SearchEngineType.java)` defines available engine types.
    - Known implementations:
        - `[MongoSearchEngine.java](mdc:src/main/java/com/groupbyinc/search/ssa/mongo/MongoSearchEngine.java)`
        - `[GoogleSearchEngine.java](mdc:src/main/java/com/groupbyinc/search/ssa/retail/GoogleSearchEngine.java)`

## Testing

- **Tests**: Unit and integration tests are in `[src/test/](mdc:src/test)`.

## Build and Deployment

- **Build Configuration**: The project is built using Gradle, configured in `[build.gradle](mdc:build.gradle)`.
- **Containerization**:
    - Docker Compose: `[docker-compose.yml](mdc:docker-compose.yml)`
- **Kubernetes**: Helm charts for Kubernetes deployment are in `[helm/](mdc:helm)`.

## Documentation and Data

- **Project Overview**: `[README.md](mdc:README.md)` provides general information about the project.
- **Data Files**: Sample or test data might be located in `[data/](mdc:data)`.





