syntax = "proto3";
package wisdom.events.assembled;

option java_package = "com.groupbyinc.wisdom.events.assembled";

option go_package = ".;goprotos";

import "google/protobuf/wrappers.proto";

import "wisdom/events/partials/cart.proto";
import "wisdom/events/partials/client_version.proto";
import "wisdom/events/partials/customer.proto";
import "wisdom/events/partials/experiment.proto";
import "wisdom/events/partials/metadata.proto";
import "wisdom/events/partials/product.proto";
import "wisdom/events/partials/search.proto";
import "wisdom/events/partials/visit.proto";

// An event that a shopper performed on a web page controlled by a GroupBy customer. The data stored in the message can
// be any of the GroupBy event types. Some fields will only be set for certain event types. See those field
// descriptions for details.
message Event {
    // Common to each event type:

    // The ID of the event generated by GroupBy's servers when they receive the event.
    string id = 1;
    // The type of the event. Can be "search", "viewProduct", "addToCart", or "order". Its value determines which of
    // the event type-specific fields are present.
    string event_type = 2;
    // The ClientVersion of the event. This field will always be present.
    wisdom.events.partials.ClientVersion client_version = 3;
    // The Customer of the event. This field will always be present.
    wisdom.events.partials.Customer customer = 4;
    // The Visit of the event. This field will always be present.
    wisdom.events.partials.Visit visit = 5;
    // The metadata for the event.
    //
    // An empty list means either no metadata was provided or it was provided
    // as an empty list. Either situation should be interpreted as no metadata
    // provided.
    repeated wisdom.events.partials.Metadata metadata = 6;
    // The experiments for the event.
    //
    // An empty list means either no experiments were provided or it was provided
    // as an empty list. Either situation should be interpreted as no experiments
    // provided.
    repeated wisdom.events.partials.Experiment experiments = 7;

    // Event type-specific fields:

    // The search component of search events.  This field will only be present if the event_type field is "search".
    wisdom.events.partials.Search search = 8;
    // The product component of viewProduct events.This field will only be present if the event_type field is
    // "viewProduct".
    wisdom.events.partials.Product product = 9;
    // The cart component of events related to carts. This field will only be present if the event_type field is
    // "addToCart" or "order".
    wisdom.events.partials.Cart cart = 10;

    // The event's search attribution token, which is a unique identifier for a
    // search performed using Google's Retail Search product. This is different
    // from the unique identifiers we generate in Searchandiser that are used
    // to join the "direct search" and "auto search" beacons. We instruct our
    // customers to set up their beacons so that this token is included in the
    // beacons that are sent after search beacons. For example, a viewProduct
    // beacon being sent after a search because the shopper clicked on a search
    // result and was brought to a product details page.
    //
    // This field will only be present if the event_type field is
    // "viewProduct", "addToCart", or "order". Also, it will only be present
    // for events of those types if the search preceeding those events was a
    // Google Retail Search search.
    google.protobuf.StringValue search_attribution_token = 11;
}
