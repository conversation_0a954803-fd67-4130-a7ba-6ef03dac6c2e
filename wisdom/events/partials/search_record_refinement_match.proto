syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

import "google/protobuf/wrappers.proto";
import "wisdom/events/partials/search_record_refinement_match_value.proto";

message SearchRecordRefinementMatch {
    google.protobuf.StringValue name = 1;
    repeated wisdom.events.partials.SearchRecordRefinementMatchValue values = 2;
}
