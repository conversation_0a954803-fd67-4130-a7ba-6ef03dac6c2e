syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

// Metadata related to the GroupBy customer that the event sender is claiming
// the event is for. Because beacon events are sent from websites via
// unauthenticated HTTP endpoints, this data may not match the website actually
// sending the beacon.
message Customer {
    // The name of the GroupBy customer. Often a short form. Never has spaces.
    string id = 1;
    // The area of the GroupBy customer. Areas are sets of configuration. One
    // product collection can be associated with one or more areas. Customers
    // will often set up an area for each domain they control or for different
    // parts of a site on one domain they control. Therefore, the beacons are
    // sent with this property to differentiate beacon events for the different
    // configurations.
    string area = 2;
}
