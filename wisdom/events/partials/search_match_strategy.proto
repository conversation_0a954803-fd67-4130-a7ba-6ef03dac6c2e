syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

import "google/protobuf/wrappers.proto";
import "wisdom/events/partials/search_match_strategy_rule.proto";

message SearchMatchStrategy {
    google.protobuf.StringValue name = 1;
    repeated wisdom.events.partials.SearchMatchStrategyRule rules = 2;
}
