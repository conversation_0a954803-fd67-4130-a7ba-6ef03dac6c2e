syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

import "wisdom/events/partials/product.proto";

// The main part of an Impression event.
message Impression {
    // The type of impression of the event. Possible values so far are "search"
    // and "recommendation".
    string impression_type = 1;
    // The products that received impressions. Each event allows more than one
    // product at a time to account for user interfaces where a shopper can
    // have multiple products revealed to them at a time, like a row of search
    // results as they scroll, or a recommendation shelf being rendered all at
    // once.
    repeated wisdom.events.partials.Product products = 2;
}
