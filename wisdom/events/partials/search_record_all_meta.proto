syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

import "google/protobuf/wrappers.proto";

// Information about the product that is the search record.
message SearchRecordAllMeta {
    // Sometimes contains the SKU of the product for the record.
    google.protobuf.StringValue sku = 1;
    // Sometimes contains the product iD of the product for the record.
    google.protobuf.StringValue product_id = 2;
}
