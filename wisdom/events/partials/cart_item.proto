syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

import "google/protobuf/wrappers.proto";
import "wisdom/events/partials/metadata.proto";

// An item entry in a cart, including info about the product and quantity of
// the product.
message CartItem {
    // The product's category.
    google.protobuf.StringValue category = 1;
    // The product's collection, which is a group of products uploaded to
    // GroupBy.
    string collection = 2;
    // The product's title.
    google.protobuf.StringValue title = 3;
    // The product's SKU. Many SKUs can be associated with one product.
    google.protobuf.StringValue sku = 4;
    // The product's product ID, which is used for attribution across event
    // types.
    string product_id = 5;
    // The product ID of the product that is the parent of this product
    // according to the client's product catalog.
    google.protobuf.StringValue parent_id = 6;
    // The margin of the product when it is sold.
    google.protobuf.DoubleValue margin = 7;
    // The price of the product.
    google.protobuf.DoubleValue price = 8;
    // The currency of the product, expressed as a three-character, uppercase,
    // alphanumeric string.
    google.protobuf.StringValue currency = 9;
    // The metadata for a product in the event.
    //
    // An empty list means either no metadata was provided or it was provided
    // as an empty list. Either situation should be interpreted as no metadata
    // provided.
    repeated Metadata metadata = 10;
    // The quantity of the product in the cart when the shopper performs
    // the action (for events of type ViewCart and Order) or the quantity
    // of the product added to or removed from the cart when the shopper
    // performs the action (for events of type AddToCart and
    // RemoveFromCart).
    int64 quantity = 11;
}
