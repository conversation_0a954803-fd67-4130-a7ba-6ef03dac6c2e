syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

import "google/protobuf/wrappers.proto";
import "wisdom/events/partials/search_record_all_meta.proto";
import "wisdom/events/partials/search_record_refinement_match.proto";

// An individual product in a set of search results. Note that a common use case for this message is to get the product
// ID of the product for the record. To do this, do not use the all_meta.product_id field of this message. It is not
// guaranteed to be present. The most reliable way to get the product ID is to parse it from the u field of this
// message. See the u field for details.
message SearchRecord {
    // Information sometimes provided about the product of the record.
    wisdom.events.partials.SearchRecordAllMeta all_meta = 1;
    repeated wisdom.events.partials.SearchRecordRefinementMatch refinement_matches = 2;
    // The ID of the record. Note that this is not the product ID of the product for the record.
    google.protobuf.StringValue id = 3;
    // A URL containing the product ID of the product for the record, or the product ID of the product for the record
    // (without being at the end of a URL). If the product ID is at the end of a URL, the URL will be one whose
    // protocol is HTTP and whose domain is always ".com" (and never has a subdomain). For example, if the product ID
    // were AbC123, then this field's value could be "http://example.com/AbC123". The product ID must be parsed from
    // the URL to be used.
    //
    // On rare occasions, this field may be not present. A customer with a good beacon implementation will always have
    // this field present.
    google.protobuf.StringValue u = 4;
    // The title of the product of the record.
    //
    // On rare occasions, this field may be not present. A customer with a good beacon implementation will always have
    // this field present.
    google.protobuf.StringValue t = 5;
    // The name of the collection this product is a part of in Searchandiser.
    //
    // On rare occasions, this field may be not present. A customer with a good beacon implementation will always have
    // this field present.
    google.protobuf.StringValue collection = 6;
}
