syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

import "google/protobuf/wrappers.proto";
import "wisdom/events/partials/search_match_strategy.proto";
import "wisdom/events/partials/search_navigation.proto";
import "wisdom/events/partials/search_origin.proto";
import "wisdom/events/partials/search_page_info.proto";
import "wisdom/events/partials/search_record.proto";
import "wisdom/events/partials/search_site_param.proto";
import "wisdom/events/partials/search_template.proto";

// Search is data related to the search in a Search event.
message Search {
    // The origin of the search. This field will always be present.
    wisdom.events.partials.SearchOrigin origin = 1;
    // The ID of the event generated by GroupBy's servers when they receive the event.
    string id = 2;
    // The total number of records in the search results in the event.
    int64 total_record_count = 3;
    // The area associated with the search, where area is a set of configuration the GroupBy customer has set up in Searchandiser. Sometimes not present.
    google.protobuf.StringValue area = 4;
    // The biasing profile active during the search. Sometimes not present.
    google.protobuf.StringValue biasing_profile = 5;
    // The query the shopper entered before any changes to it were made by Searchandiser. Examples of things that can be
    // removed from this field to form the query field are stop words like "for" and punctuation like ".".
    //
    // If field is not present, there were no changes to make to the query entered by the shopper due to rules being
    // triggered and the query field should be treated as the original query entered instead.
    google.protobuf.StringValue original_query = 6;
    // An intermediate form of the query the shopper entered for the search after the rules defined in Searchandiser's
    // rules engine that were triggered for the search changed the search query, but before changes such as spelling
    // mistake correction were made by Searchandiser.
    //
    // If field is an empty string, there was no query from the shopper. This happens when a shopper searches without
    // entering any query in or when Searchandiser is used to power a browse page, where queries are not relevant. This
    // field will always be present.
    string query = 7;
    // The query the shopper entered after all changes have been made by Searchandiser to the query. This includes
    // rules triggered and additional changes like spelling mistake corrections. Represents the final query
    // Searchandiser used for the search.
    //
    // If field is not present, there were no such changes to make to the query entered by the shopper beyond rules
    // being triggered and the query field should be considered the final query used instead.
    google.protobuf.StringValue corrected_query = 8;
    repeated string warnings = 9;
    google.protobuf.StringValue errors = 10;
    wisdom.events.partials.SearchTemplate template = 11;
    wisdom.events.partials.SearchPageInfo page_info = 12;
    repeated string related_queries = 13;
    repeated string rewrites = 14;
    google.protobuf.StringValue redirect = 15;
    repeated wisdom.events.partials.SearchSiteParam site_params = 16;
    wisdom.events.partials.SearchMatchStrategy match_strategy = 17;
    // The navigations that were displayed to the shopper when the search results were displayed to them.
    repeated wisdom.events.partials.SearchNavigation available_navigations = 18;
    // The navigations the shopper selected to contribute to the search results generated.
    repeated wisdom.events.partials.SearchNavigation selected_navigations = 19;
    repeated wisdom.events.partials.SearchRecord records = 20;
    repeated string did_you_mean = 21;
    repeated string page_categories = 22;
    google.protobuf.StringValue filter = 23;
}
