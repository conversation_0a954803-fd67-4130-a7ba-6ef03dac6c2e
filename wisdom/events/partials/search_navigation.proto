syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

import "google/protobuf/wrappers.proto";
import "wisdom/events/partials/metadata.proto";
import "wisdom/events/partials/search_navigation_refinement.proto";

// A navigation from a search event.
// 
// The navigation is the thing the shopper can use to refine their search (ex.
// brand in the case of value refinements or price in the case of range
// refinements). The refinements of that navigation were the options in that
// navigation that were available to the shopper (ex. brand A, or between $100
// and $200).
message SearchNavigation {
    // The type of the navigation, if this is an "available navigation" (as
    // opposed to a "selected navigation"). If this is an available navigation,
    // then this field will be set. Otherwise, this field will not be set, and
    // its default value should be ignored.
    //
    // Known types are "value" and "dynamic_range".
    string type = 1;
    // The ID of the navigation, if this is a "selected navigation" (as opposed
    // to an "available navigation"). If this is a selected navigation, then
    // this field will be set. Otherwise, this field will not be set, and its
    // default value should be ignored.
    string id = 2;
    // The navigation's name. This identifies the navigation on the web page
    // when paired with the customer ID and customer area. It is not required
    // that it be human-readable.
    string name = 3;
    // The navigation's name as displayed on the e-commerce web page.
    google.protobuf.StringValue display_name = 4;
    // If set, conveys whether or not the navigation is considered a "range
    // navigation".
    google.protobuf.BoolValue range = 5;
    // If set, conveys whether or not the navigation is considered an "or
    // navigation".
    google.protobuf.BoolValue or = 6;
    // This field will only be set if the value is true. If the value is false,
    // this field will not be set and its default value should be ignored.
    bool ignored = 7;
    // Whether or not the navigation has more refinements that can be
    // requested. The more refinements feature is when a navigation has some
    // refinements included automatically in the response from Searchandiser,
    // but there are more available. For example, for the navigation "brand",
    // there may have been "Brand A" and "Brand B" included in the response as
    // refinements, with this field set to true. if so, that means there are
    // more brands that the site can request in an additional API call to
    // Searchandiser, and maybe "Brand C" and "Brand D" would have be returned
    // in that response, with this field set to false, meaning that was all the
    // brands.
    //
    // This field will only be set if the value is true. If the value is false,
    // this field will not be set and its default value should be ignored.
    bool more_refinements = 8;
    // The refinements for the navigation in the search event.
    repeated wisdom.events.partials.SearchNavigationRefinement refinements = 9;
    // The metadata for the navigation. Most of the time, if metadata
    // is provided, it is provided at the event level, not at the level of any
    // partial model inside the event model such as this partial model.
    //
    // An empty list means either no metadata was provided or it was provided
    // as an empty list. Either situation should be interpreted as no metadata
    // was provided.
    repeated Metadata metadata = 10;

}
