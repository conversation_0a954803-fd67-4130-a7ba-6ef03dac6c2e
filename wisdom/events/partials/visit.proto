syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

import "google/protobuf/wrappers.proto";

// Data related to the shopper and their browser during the event.
message Visit {
    // Data related to a shopper performing the event. Note that this is not data related to a GroupBy customer.
    message CustomerData {
        // The shopper's unique identifier for a particular device. For example, if a shopper used a phone and then a
        // laptop, there would be two sets of events recorded for the shopper. Each set would have a unique visitor ID.
        string visitor_id = 1;
        // If present, a privacy-preserving hash of the shopper's unique identifier as stored in the GroupBy customer's
        // system. Available for events when a shopper logs in on their device. Used to join multiple sets of events
        // across devices. This is a typo.
        google.protobuf.StringValue login_id = 2;
    }

    // The generated part of Visit. Data collected in JS to supplement headers received by the server, plus parsed
    // versions of the headers received.
    message Generated {
        // Geographic data about the shopper that performed the event. May not be possible for all shoppers, so any of
        // the fields may be not present.
        message Geo {
            // The city the shopper was in.
            google.protobuf.StringValue city = 1;
            // The region (aka state, province, etc) the shopper was in.
            google.protobuf.StringValue region = 2;
            // The country the shopper was in.
            google.protobuf.StringValue country = 3;
        }

        // Data from a single language tag in a parsed Accept-Language header. Because parsing may fail, no fields in
        // this message are guaranteed to be present.
        // Example of a raw header value: da, en-gb;q=0.8, en;q=0.7
        message Language {
            // The base language of the language tag. If the base language is unspecified, an attempt will be made to
            // infer it from the context. It uses a variant of CLDR's Add Likely Subtags algorithm. This is subject to
            // change.
            google.protobuf.StringValue language  = 1;
            // If present in the language tag in the header value, the quality value contained in the language tag.
            google.protobuf.DoubleValue quality = 2;
            // The region for the language tag. If it was not explicitly given, it will infer a most likely candidate
            // from the context. It uses a variant of CLDR's Add Likely Subtags algorithm. This is subject to change.
            google.protobuf.StringValue region = 3;
            // The canonical string representation of the language tag.
            google.protobuf.StringValue value = 4;
        }

        // A parsed URI. Because parsing may fail, no fields in this message are guaranteed to be present.
        message ParsedUri {
            google.protobuf.StringValue auth = 1;
            google.protobuf.StringValue domain = 2;
            google.protobuf.StringValue hash = 3;
            google.protobuf.StringValue host = 4;
            google.protobuf.StringValue hostname = 5;
            google.protobuf.StringValue href = 6;
            google.protobuf.StringValue path = 7;
            google.protobuf.StringValue pathname = 8;
            google.protobuf.StringValue protocol = 9;
            google.protobuf.StringValue query = 10;
            google.protobuf.StringValue search = 11;
            google.protobuf.BoolValue slashes = 12;
            google.protobuf.StringValue subdomain = 13;
            google.protobuf.StringValue tld = 14;
        }

        // A parsed user agent. Because parsing may fail, no fields in this message are guaranteed to be present.
        message ParsedUserAgent {
            // The device from a parsed user agent. Because parsing may fail, no fields in this message are guaranteed
            // to be present.
            message Device {
                // The family component of the device version.
                google.protobuf.StringValue family = 1;
                // The major version component of the device version.
                google.protobuf.StringValue major = 2;
                // The minor version component of the device version.
                google.protobuf.StringValue minor = 3;
                // The patch version component of the device version.
                google.protobuf.StringValue patch = 4;
            }

            // The operating system from a parsed user agent. Because parsing may fail, no fields in this message are
            // guaranteed to be present.
            message Os {
                // The family component of the operating system.
                google.protobuf.StringValue family = 1;
                // The major version component of the operating system version.
                google.protobuf.StringValue major = 2;
                // The minor version component of the operating system version.
                google.protobuf.StringValue minor = 3;
                // The patch version component of the operating system version.
                google.protobuf.StringValue patch = 4;
            }

            // The device from the parsed user agent of the event.
            Device device = 1;
            // The family component of the user agent version.
            google.protobuf.StringValue family = 2;
            // The major component of the user agent version.
            google.protobuf.StringValue major = 3;
            // The minor component of the user agent version.
            google.protobuf.StringValue minor = 4;
            // The operating system from the parsed user agent of the event.
            Os os = 5;
            // The patch component of the user agent version.
            google.protobuf.StringValue patch = 6;
        }

        // A parsed referer. Because parsing may fail, no fields in this message are guaranteed to be present.
        message Referer {
            // A parsed URI for a referer URI. Because parsing may fail, no fields in this message are guaranteed to be
            // present.
            message ParsedUri {
                google.protobuf.StringValue domain = 2;
                google.protobuf.StringValue host = 4;
                google.protobuf.StringValue hostname = 5;
                google.protobuf.StringValue href = 6;
                google.protobuf.StringValue path = 7;
                google.protobuf.StringValue pathname = 8;
                google.protobuf.StringValue protocol = 9;
                google.protobuf.StringValue search = 11;
                google.protobuf.BoolValue slashes = 12;
                google.protobuf.StringValue subdomain = 13;
                google.protobuf.StringValue tld = 14;
                google.protobuf.StringValue port = 15;
            }

            // Whether or not the referer was known. This field is always set to "true".
            bool known = 1;
            // The classification of the type of referer. Known values so far include "search", "social", and "email".
            // If the medium cannot be determined, this will be set to "unknown".
            string medium = 2;
            // The parsed URI of the referer URI of the event.
            ParsedUri uri = 3;
        }
        
        // The shopper's device type. Depends on how user agent variety grows over time. Known values so far include
        // examples like "phone", "tablet", "desktop", and "tv".
        google.protobuf.StringValue device_type = 1;
        // The Geo part of the Generated.
        Geo geo = 2;
        // The Languages part of the Generated.
        repeated Language languages = 3;
        // The time in the shopper's local time zone when the event occurred (as opposed to the UTC time). Will be in a
        // form parsable by programming languages (ex. ISO8601) with millisecond accuracy.
        string local_time = 4;
        // The ParsedURI part of the Generated.
        ParsedUri parsed_uri = 5;
        // The ParsedUserAgent part of the Generated.
        ParsedUserAgent parsed_user_agent = 6;
        // The Referer part of the Generated.
        Referer referer = 7;
        // The UTC time when the event was received (as opposed to the shopper's local time zone). Will be in a form
        // parsable by programming languages (ex. ISO8601) with at least millisecond accuracy, but possibly more
        // accuracy, up to nanosecond accuracy.
        string server_time = 8;
        // The URI of the web page associated with the event performed by the shopper.
        string uri = 9;
        // The raw string collected for the user agent of the event.
        string user_agent_string = 10;
    }

    // The CustomerData part of the Visit. This field will always be present.
    CustomerData customer_data = 1;
    // The Generated part of the Visit. This field will always be present.
    Generated generated = 2;
}
