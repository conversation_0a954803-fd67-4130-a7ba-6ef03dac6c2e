syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

import "google/protobuf/wrappers.proto";

// Metadata about the tracker client (gb-tracker-client) used to send the
// event. All data except the raw version is only present in the message if the
// data was able to be parsed from the raw version when the event was received.
message ClientVersion {
    // The raw client version, unparsed. May or may not be parsable according
    // to specs like semver.
    string raw = 1;
    // The "version" portion of the raw version.
    google.protobuf.StringValue version = 2;
    // The "major" portion of the raw version if the raw version is compatible
    // with semver. For example, when the raw version is "1.2.3", this is 1.
    google.protobuf.Int64Value major = 3;
    // The "minor" portion of the raw version if the raw version is compatible
    // with semver. For example, when the raw version is "1.2.3", this is 2.
    google.protobuf.Int64Value minor = 4;
    // The "patch" portion of the raw version if the raw version is compatible
    // with semver. For example, when the raw version is "1.2.3", this is 3.
    google.protobuf.Int64Value patch = 5;
}
