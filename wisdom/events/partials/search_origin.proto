syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

// The origin of search results displayed to a shopper. More info in each field. Only one field will be set to "true"
// at a time.
message SearchOrigin {
    // A shopper seeing a full page of search results that replaces the page they were on when they performed the
    // search.
    bool search = 1;
    // A shopper seeing search results on the page they were performing the search on that were rendered as they typed.
    // Aka "search as you type".
    bool sayt = 2;
    // A shopper refining a search they've already done by choosing a refinement in a navigation, after performing an
    // initial search. For example, choosing a particular brand. This origin is set when it's that second (or third,
    // etc) search. Also used for when a shopper is viewing search results on a "browse page", where they didn't search
    // for anything, but Searchandiser is being used for rendering the products on that browse page.
    bool navigation = 3;
    bool dym = 4;
    bool recommendations = 5;
    bool autosearch = 6;
    bool collection_switcher = 7;
}
