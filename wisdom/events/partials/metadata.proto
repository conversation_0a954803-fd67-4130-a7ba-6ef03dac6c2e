syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

// A key-value pair of metadata. Used in many event types. GroupBy customers use it for custom reporting use cases.
// Because which keys are set cannot be known ahead of time except by those working directly with the customer,
// consumers of this message type are not normally expected to parse this. Instead, it's typically passed through the
// system to be written to the data warehouse.
message Metadata {
    // The key of the key-value pair.
    string key = 1;
    // The value of the key-value pair. Because it is always a string, no
    // validation except for string max length is done on it and it must be
    // carefully parsed to extract numeric or boolean data.
    string value = 2;
}
