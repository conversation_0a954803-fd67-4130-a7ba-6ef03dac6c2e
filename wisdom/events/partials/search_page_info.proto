syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

// Data about where the records in the search results were in terms of the total records from the search results.
// Records per page can be calculated by subtracting the record_start field from the record_end field. These counts are
// indexed starting from 1, not from 0.
message SearchPageInfo {
    // Where the records started. For example, 1 means the records started at the beginning of the search results, and
    // were displayed on the first page. 21 means the records were not from the first page, and could have been on the
    // 2nd page (if there were 20 records displayed per page).
    int64 record_start = 1;
    // Where the records ended. For example, 20 means the last record on the page of results was the 20th record in the
    // total search results.
    int64 record_end = 2;
}
