syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

import "google/protobuf/wrappers.proto";
import "wisdom/events/partials/cart_item.proto";
import "wisdom/events/partials/metadata.proto";

// A cart in an event with a cart. Fields of this type will only be present in
// events of type AddToCart, ViewCart, RemoveFromCart, and Order. In events of
// any other type, the field will be absent.
message Cart {
    // The cart's ID. This is present if the retailer has a concept of cart ID.
    google.protobuf.StringValue id = 1;
    // The cart's type. This is used a subcategory and is often used for
    // registries, gift baskets, etc. It can be any string value.
    google.protobuf.StringValue cart_type = 2;
    // The products in the cart when the shopper performs the action (for
    // events of type ViewCart and Order) or the products being added to or
    // removed from the cart (for events of type AddToCart and RemoveFromCart).
    repeated CartItem items = 3;
    // The metadata for the cart in the event.
    //
    // An empty list means either no metadata was provided or it was provided
    // as an empty list. Either situation should be interpreted as no metadata
    // provided.
    repeated Metadata metadata = 4;
}
