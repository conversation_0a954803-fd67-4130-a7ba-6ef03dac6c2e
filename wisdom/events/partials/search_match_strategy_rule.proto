syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

import "google/protobuf/wrappers.proto";

message SearchMatchStrategyRule {
    google.protobuf.Int64Value terms = 1;
    google.protobuf.Int64Value terms_greater_than = 2;
    google.protobuf.Int64Value must_match = 3;
    google.protobuf.BoolValue percentage = 4;
}
