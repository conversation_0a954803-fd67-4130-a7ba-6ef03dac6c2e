syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

import "google/protobuf/wrappers.proto";

// A refinement of a navigation from a search event.
// 
// The navigation is the thing the shopper can use to refine their search (ex.
// brand in the case of value refinements or price in the case of range
// refinements). The refinements of that navigation were the options in that
// navigation that were available to the shopper (ex. brand A, or between $100
// and $200).
message SearchNavigationRefinement {
    // The type of the refinement as defined by GroupBy Searchandiser. Possible
    // values so far are "value" and "range". This field must be read in order
    // to know which of the other fields of the message were set.
    string type = 1;
    google.protobuf.StringValue id = 2;
    // The refinement's value if the refinement's type is "value". If the type
    // field of this message is "value", then this field is set. Otherwise,
    // this field is not set, and its default value should be ignored.
    string value = 3;
    // The low component of the refinement's value if the refinement's type is
    // "range". If the type field of this message is "range", then this field
    // is set. Otherwise, this field is not set, and its default value should
    // be ignored.
    //
    // This is usually a numeric value (like 12.34) but is sometimes a string
    // such as "*". It must be carefully parsed.
    string low = 4;
    // The high component of the refinement's value if the refinement's type is
    // "range". If the type field of this message is "range", then this field
    // is set. Otherwise, this field is not set, and its default value should
    // be ignored.
    //
    // This is usually a numeric value (like 12.34) but is sometimes a string
    // such as "*". It must be carefully parsed.
    string high = 5;
    // The number of items that were available for this refinement if the
    // refinement is part of a navigation that is an "available navigation" (as
    // opposed to a "selected navigation"). If the this refinement is part of a
    // navigation that is an available navigation, then this field is set.
    // Otherwise, this field is not set, and its default value should be
    // ignored.
    int64 count = 6;
    google.protobuf.BoolValue exclude = 7;
}
