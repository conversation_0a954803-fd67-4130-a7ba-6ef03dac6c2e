syntax = "proto3";
package wisdom.events.partials;

option java_package = "com.groupbyinc.wisdom.events.partials";

option go_package = ".;goprotos";

// An A/B test experiment.
message Experiment {
    // The experiment's ID/name. For example "legacy-vs-new".GroupBy
    // experiments will always have "gbi-" as a prefix. Non-GroupBy experiments
    // are not required to have this prefix.
    string experiment_id = 1;
    // The experiment's variant. For example "legacy", or "new" (related to the
    // example experiment ID in the comment for experiment ID in this message).
    string experiment_variant = 2;
}
