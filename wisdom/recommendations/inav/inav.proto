syntax = "proto3";


option java_multiple_files = true;
option java_package = "inav";
option java_outer_classname = "INavProto";
option objc_class_prefix = "INAV";

package com.groupbyinc.recommendations.inav;

service INav {
    rpc GetINav (INavRequest) returns (INavResponse) {}
}

message INavRequest {
    string customer_id = 1;
    string area = 2;
    string query = 3;
    int32 maxNavigations = 4;
    int32 maxRefinementsPerNavigation = 5;
}

message INavResponse {
    message Navigation {
        message SelectedRefinement {
            string name = 1;
            int64 count = 2;
        }
        string name = 1;
        int64 selected_refinements_total = 2;
        repeated SelectedRefinement selected_refinements = 3;
        string display_name = 4;
    }
    repeated Navigation navigations = 1;
}
