### Project Vasco
# GroupBy Common Schemas

This repository hosts common schemas that are potentially used by multiple services. Currently, this mostly includes ProtoBuf definitions for Pub/Sub
message payloads.

## How to use this repository?

This repository is supposed to be added to projects (i.e. repositories for different services) as a
[Git submodule](https://git-scm.com/book/en/v2/Git-Tools-Submodules). Ideally, you use a plugin in your build system to generate code based on
these schemas upon every build. This is especially valuable if you're using a typed language, as doing so provides a guarantee that your code is
compatible with these schemas every time you compile it.

> **DO NOT** copy individual files from this repo into your projects. Doing so defeats the purpose of having these schemas centralized.

To use this repository for the first time in a project, run:

```bash
git submodule add https://github.com/groupby/common-schemas.git [PATH]
```

from your project repository root, with `[PATH]` pointing to the directory in which you want this repository to be cloned. `[PATH]` should be
relative to your repository root.

For Java projects, the directory structure should follow the GroupBy established standard (`src/main/proto/common-schemas`, so navigate to your
project and run:

```bash
git submodule add https://github.com/groupby/common-schemas.git src/main/proto/common-schemas
```

Git submodules have to be updated:
- Every time they are changed
- Every time a repository that includes them (e.g. your own project) is cloned

Therefore, when cloning a repository that uses `common-schemas`, or when you want to get the latest updates, you will have to run:

```bash
git submodule update --init --recursive --remote
```

from your project repository root.

Your CI pipeline should load submodules as well, typically as one of the first steps after checking out the code repository. For example, in
CircleCI, you'll need to add an additional step as follows:

```yaml
- run:
    name: Update submodules
    command: git submodule update --init --recursive
```

### Examples

See [Ingestion API](https://github.com/groupby/ingestion-api) or [Search Indexer](https://github.com/groupby/search-indexer) as examples of Java
projects that rely on schemas from this repository.

### Beacon event schemas and wrapper types

For beacon events, we need to decide on a per field basis whether to use a scalar type or its wrapper message type equivalent. This is so that we can convey a field being explicitly absent to a consumer when we have this requirement (ex. the field `price` in the `product` schema). If we use the scalar type `double` instead of the wrapper type `google.protobuf.DoubleValue`, and the sender doesn't set the field, the consumer will receive a message with the field set to `0.00` (the default value for doubles) and the consumer won't be able to tell whether the field value was meant to be `0.00` or unset (explicitly absent).

To solve this problem, we use the recommended approach until proto3 `optional` is generally available and implemented in all language-specific Protobuf tools we use. This approach is to use wrapper messages. We use the wrappers in the package `google/protobuf/wrappers.proto`.

To decide when to use scalar types and wrapper message types, we take into consideration how we want our schema to evolve over time. Schema consumers include our Rec AI integration and the system that ingests into BigQuery (currently called beacons-processor) if we ever choose to refactor it to use the schemas instead of JSON. It's important that we make our event schemas' APIs change as infrequently as possible so that the consumers have to be updated as infrequently as possible. Because we don't want to drop events, the event consumers would need to be updated to deal with those API changes first before the change in beacon data collected is made, so that no data is lost. This would become more and more complex as time goes by.

The following algorithm is used to decide whether we use a scalar or wrapper for a given field in beacon events:

```
Is the property required according to our validation (ex. product ID on a viewProduct event)?
Y: Is it provided by our back end server instead of the front end beacon implementation (ex. a server time)?
   Y: Will we possibly drop it in the future (like session ID because we're using SQL sessioning etc)?
      Y: Use wrapper.
      N: Use scalar.
   N: Is it a property that is on the Go model we use as data comes in but not in the BigQuery table schema?
      Y: Use wrapper, because we don't know enough about the data to judge. Err on side of caution.
      N: Will we never make it optional to beacon implementation (things core to our business like product IDs, search queries, etc)?
         Y: Use scalar.
         N: Use wrapper.
```

### Troubleshooting

After running `git submodule update --init --recursive --remote`, if you find Git unable to add the updated schema files to the staging area,
simply remove the root of the submodule from the staging area and add it back again. For example, in Java projects following the convention
mentioned above, you would do:

```bash
# Revert the submodule back to the commit as already specified in the project
git submodule update --init --recursive

# Remove and add back the parent directory to the staging area
git rm --cached -r src/main/proto
git add src/main/proto

# Update the submodule to point to the latest commit on the remote repository
git submodule update --init --recursive --remote
```
