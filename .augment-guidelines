## Project Overview

Site Search API is an application responsible for performing product searches against the Google Retail Search engine. It's built using Java 21 with Micronaut framework and uses MongoDB Atlas Search as one of the search engines.

## Project Core Components

- **Main Entry Point**: `src/main/java/com/groupbyinc/search/ssa/Application.java`
- **API Layer**: `src/main/java/com/groupbyinc/search/ssa/api/`
- **Core Search Functionality**:
  - Search Service: `src/main/java/com/groupbyinc/search/ssa/application/core/search/SearchService.java`
  - Search Strategies: `src/main/java/com/groupbyinc/search/ssa/application/core/search/strategy/`
  - Search Engines: `src/main/java/com/groupbyinc/search/ssa/application/core/search/engine/`
- **Domain Modules**:
  - Product Catalog: `src/main/java/com/groupbyinc/search/ssa/productcatalog/`
  - Retail: `src/main/java/com/groupbyinc/search/ssa/retail/`
  - Part Number: `src/main/java/com/groupbyinc/search/ssa/partnumber/`
- **Data Access**:
  - MongoDB: `src/main/java/com/groupbyinc/search/ssa/mongo/`
  - Redis: `src/main/java/com/groupbyinc/search/ssa/redis/`
- **Testing**
  - Unit and integration tests are located in `src/test/`
  - Test resources, including sample data, are in `src/test/resources/`

## Code Style Guidelines

When modifying or adding code to this project, follow these guidelines:

1. Use Java 21 with preview features for all new code and refactoring.
2. Use `var` for new variables' declaration with new operator.
3. Follow existing naming and style patterns in the codebase.
4. Do not reformat existing code or imports that are not related to the implementation changes.
5. Adhere to the existing file formatting when making changes to existing files.
6. Use Micronaut framework conventions for dependency injection and configuration.
7. Check Micronaut and Java 21 official documentation for APIs and libraries usage.
8. Consult MongoDB Atlas Search documentation when working with search functionality.

## Build Process

The project uses Gradle for building. Before submitting changes ensure the project builds successfully and all tests pass using the `test` command.


