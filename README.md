# Site Search API

This application has the primary responsibility of performing product searches against the Google Retail Search engine.

## Useful links
1) Main information located on related confluence [page]
2) Demo testing [tool]

## Pre-requirements
- Java 21
- IDEA
- GIT
- Docker
- docker-compose

## Application run
###Local:
1) Check that you have installed jdk at least 17 version.
2) Command center application is required to load all configurations. (Command center docs: [ccapi])
   - Local CC API: Run Command center application locally on port 8080
   - DEV CC API: update `application-local.yml` file and remove/comment property `commandcenter.client.url`
3) You will need to provide a service account credential file at `.run/groupby-development-51aac1fa93fd.json`
   which has already been ignored from Git.
    - credential file is required for communication with Google Retail API
4) Set environment variables:
   - MONGODB_URI env variable should be specified which includes all necessary information for a Java driver to connect.
     We are utilizing private link to connect to mongo, in order for local application to work the SOCKS5 proxy
     should be used, just append `/?proxyHost=localhost&proxyPort=1080` to your mongo URI. For more details
     see [Mongo: How to connect to mongo atlas from a local machine](https://groupby.atlassian.net/wiki/spaces/SRE/pages/**********/Mongo+How+to+connect+to+mongo+atlas+from+a+local+machine)

5) Run "micronaut" application:
   - Console:
     `MICRONAUT_ENVIRONMENTS=local MONGODB_URI=${MONGODB_URI} ./gradlew run -PmavenUsername=*** -PmavenPassword=***`
   - Idea:
     Use Idea runner configuration that was preinstalled automatically.

   Maven username and password could be stored at the `gradle.properties` (project or system-wide) file:
   ```properties
   mavenUsername=$USERNAME
   mavenPassword=$PASSWORD
   ```
6) Application is running on port: `8081`.
7) Check API is available: http://localhost:8081/redoc

## Branching and deployment

We have configured process based on GitHub actions which will trigger build and deploy automatically each time when branch related to the environment is updated.

Currently, we have 3 working environment:

| Branch     | URL                                                          |
|------------|--------------------------------------------------------------|
| `develop2` | https://asite-search-api.dev.groupbycloud.com/api/dev/search |
| `staging`  | https://apparel.dev.groupbycloud.com/api/v3/search           |
| `prod`     | https://apparel-atlas.groupbycloud.com/api/v3/search         |

##Notes:

<b>Develop</b> environment
<p>Mostly used internally, so each developer may use it as base branch.
But there we have no warranty that branch is stable.</p>
<br />
<b>Staging</b> environment
<p>It is "stable" develop, used internally.
Used as pre-production env where we do tests and shows demos. After each sprint this
branch will be updated with all completed tickets.</p>

<b>Staging</b> and <b>Prod</b> environments require _authorization_ header to communicate with available endpoints (```client-key **********```)
Endpoint domain is based on tenant name:
- _atlas_ -> `https://apparel.dev.groupbycloud.com/api/v3/search`
- _bedbathbeyond_ -> `https://bedbathbeyond.dev.groupbycloud.com/api/v3/search`

[page]: https://groupby.atlassian.net/wiki/spaces/GCSFR/overview
[ccapi]: https://github.com/groupby/atlas-command-center-api#readme
[tool]: https://github.com/groupby/google-search4retail
